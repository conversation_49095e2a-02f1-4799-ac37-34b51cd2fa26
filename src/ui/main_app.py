"""
量化交易系统主GUI应用程序
整合所有UI组件，提供统一的用户界面
"""

import sys
import asyncio
from typing import Dict, Any, Optional
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QMenuBar, QStatusBar, QToolBar, QAction, QSplitter,
    QMessageBox, QProgressBar, QLabel, QPushButton, QFrame
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QObject
from PyQt5.QtGui import QIcon, QFont, QPalette, QColor
import logging
from datetime import datetime

# 导入各个UI组件
from .charts.main_chart import MainChartWidget
from .strategy_management.main_interface import StrategyManagementWidget
from .data_source_management.main_interface import DataSourceManagementWidget
from .analysis_reporting.main_interface import AnalysisReportingWidget
from .monitoring.real_time_dashboard import RealTimeDashboard

# 导入系统核心组件
from ..data.data_manager import DataManager
from ..strategies.strategy_manager import StrategyManager
from ..backtest.backtest_engine import BacktestEngine
from ..monitoring.system_monitor import SystemMonitor

logger = logging.getLogger(__name__)


class MainApplication(QMainWindow):
    """主应用程序窗口"""
    
    # 信号定义
    status_message = pyqtSignal(str)
    progress_update = pyqtSignal(int)
    
    def __init__(self):
        super().__init__()
        
        # 初始化核心组件
        self.data_manager = None
        self.strategy_manager = None
        self.backtest_engine = None
        self.system_monitor = None
        
        # UI组件
        self.central_widget = None
        self.tab_widget = None
        self.status_bar = None
        self.progress_bar = None
        
        # 子组件
        self.chart_widget = None
        self.strategy_widget = None
        self.data_source_widget = None
        self.analysis_widget = None
        self.monitoring_widget = None
        
        # 初始化UI
        self.init_ui()
        self.init_core_components()
        self.setup_connections()
        
        logger.info("主应用程序初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("量化交易系统 - Quantitative Trading System")
        self.setGeometry(100, 100, 1600, 1000)
        
        # 设置应用程序样式
        self.setup_style()
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建中央窗口
        self.create_central_widget()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 设置窗口图标
        self.setWindowIcon(QIcon("assets/icons/app_icon.png"))
    
    def setup_style(self):
        """设置应用程序样式"""
        # 设置现代化的深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #3c3c3c;
            }
            
            QTabWidget::tab-bar {
                alignment: left;
            }
            
            QTabBar::tab {
                background-color: #404040;
                color: #ffffff;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            
            QTabBar::tab:selected {
                background-color: #0078d4;
            }
            
            QTabBar::tab:hover {
                background-color: #505050;
            }
            
            QMenuBar {
                background-color: #404040;
                color: #ffffff;
                border-bottom: 1px solid #555555;
            }
            
            QMenuBar::item {
                padding: 4px 8px;
            }
            
            QMenuBar::item:selected {
                background-color: #0078d4;
            }
            
            QToolBar {
                background-color: #404040;
                border: none;
                spacing: 2px;
            }
            
            QStatusBar {
                background-color: #404040;
                color: #ffffff;
                border-top: 1px solid #555555;
            }
            
            QPushButton {
                background-color: #0078d4;
                color: #ffffff;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
            }
            
            QPushButton:hover {
                background-color: #106ebe;
            }
            
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """)
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        new_action = QAction('新建策略(&N)', self)
        new_action.setShortcut('Ctrl+N')
        new_action.triggered.connect(self.new_strategy)
        file_menu.addAction(new_action)
        
        open_action = QAction('打开项目(&O)', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_project)
        file_menu.addAction(open_action)
        
        save_action = QAction('保存(&S)', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_project)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 数据菜单
        data_menu = menubar.addMenu('数据(&D)')
        
        import_data_action = QAction('导入数据(&I)', self)
        import_data_action.triggered.connect(self.import_data)
        data_menu.addAction(import_data_action)
        
        sync_data_action = QAction('同步数据(&S)', self)
        sync_data_action.triggered.connect(self.sync_data)
        data_menu.addAction(sync_data_action)
        
        # 策略菜单
        strategy_menu = menubar.addMenu('策略(&S)')
        
        run_backtest_action = QAction('运行回测(&R)', self)
        run_backtest_action.setShortcut('F5')
        run_backtest_action.triggered.connect(self.run_backtest)
        strategy_menu.addAction(run_backtest_action)
        
        optimize_action = QAction('参数优化(&O)', self)
        optimize_action.triggered.connect(self.optimize_strategy)
        strategy_menu.addAction(optimize_action)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')
        
        fullscreen_action = QAction('全屏(&F)', self)
        fullscreen_action.setShortcut('F11')
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setMovable(False)
        
        # 快速操作按钮
        run_btn = QPushButton('运行回测')
        run_btn.clicked.connect(self.run_backtest)
        toolbar.addWidget(run_btn)
        
        toolbar.addSeparator()
        
        sync_btn = QPushButton('同步数据')
        sync_btn.clicked.connect(self.sync_data)
        toolbar.addWidget(sync_btn)
        
        toolbar.addSeparator()
        
        # 状态指示器
        self.connection_status = QLabel('连接状态: 未连接')
        self.connection_status.setStyleSheet("color: #ff6b6b;")
        toolbar.addWidget(self.connection_status)
    
    def create_central_widget(self):
        """创建中央窗口部件"""
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(self.central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建选项卡窗口
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        
        # 添加各个功能模块
        self.create_chart_tab()
        self.create_strategy_tab()
        self.create_data_source_tab()
        self.create_analysis_tab()
        self.create_monitoring_tab()
        
        main_layout.addWidget(self.tab_widget)
    
    def create_chart_tab(self):
        """创建图表分析选项卡"""
        try:
            self.chart_widget = MainChartWidget()
            self.tab_widget.addTab(self.chart_widget, "📈 图表分析")
            logger.info("图表分析选项卡创建成功")
        except Exception as e:
            logger.error(f"创建图表选项卡失败: {str(e)}")
            # 创建占位符
            placeholder = QLabel("图表组件加载失败")
            placeholder.setAlignment(Qt.AlignCenter)
            self.tab_widget.addTab(placeholder, "📈 图表分析")
    
    def create_strategy_tab(self):
        """创建策略管理选项卡"""
        try:
            self.strategy_widget = StrategyManagementWidget()
            self.tab_widget.addTab(self.strategy_widget, "🎯 策略管理")
            logger.info("策略管理选项卡创建成功")
        except Exception as e:
            logger.error(f"创建策略管理选项卡失败: {str(e)}")
            placeholder = QLabel("策略管理组件加载失败")
            placeholder.setAlignment(Qt.AlignCenter)
            self.tab_widget.addTab(placeholder, "🎯 策略管理")
    
    def create_data_source_tab(self):
        """创建数据源管理选项卡"""
        try:
            self.data_source_widget = DataSourceManagementWidget()
            self.tab_widget.addTab(self.data_source_widget, "📊 数据管理")
            logger.info("数据源管理选项卡创建成功")
        except Exception as e:
            logger.error(f"创建数据源管理选项卡失败: {str(e)}")
            placeholder = QLabel("数据源管理组件加载失败")
            placeholder.setAlignment(Qt.AlignCenter)
            self.tab_widget.addTab(placeholder, "📊 数据管理")
    
    def create_analysis_tab(self):
        """创建分析报告选项卡"""
        try:
            self.analysis_widget = AnalysisReportingWidget()
            self.tab_widget.addTab(self.analysis_widget, "📋 分析报告")
            logger.info("分析报告选项卡创建成功")
        except Exception as e:
            logger.error(f"创建分析报告选项卡失败: {str(e)}")
            placeholder = QLabel("分析报告组件加载失败")
            placeholder.setAlignment(Qt.AlignCenter)
            self.tab_widget.addTab(placeholder, "📋 分析报告")
    
    def create_monitoring_tab(self):
        """创建实时监控选项卡"""
        try:
            self.monitoring_widget = RealTimeDashboard()
            self.tab_widget.addTab(self.monitoring_widget, "📱 实时监控")
            logger.info("实时监控选项卡创建成功")
        except Exception as e:
            logger.error(f"创建实时监控选项卡失败: {str(e)}")
            placeholder = QLabel("实时监控组件加载失败")
            placeholder.setAlignment(Qt.AlignCenter)
            self.tab_widget.addTab(placeholder, "📱 实时监控")
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        
        # 状态信息
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 时间显示
        self.time_label = QLabel()
        self.update_time()
        self.status_bar.addPermanentWidget(self.time_label)
        
        # 定时更新时间
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # 每秒更新
    
    def init_core_components(self):
        """初始化核心组件"""
        try:
            # 初始化数据管理器
            self.data_manager = DataManager()
            
            # 初始化策略管理器
            self.strategy_manager = StrategyManager()
            
            # 初始化回测引擎
            self.backtest_engine = BacktestEngine(
                initial_capital=100000,
                commission=0.001
            )
            
            # 初始化系统监控
            self.system_monitor = SystemMonitor()
            
            # 更新连接状态
            self.connection_status.setText("连接状态: 已连接")
            self.connection_status.setStyleSheet("color: #51cf66;")
            
            logger.info("核心组件初始化完成")
            
        except Exception as e:
            logger.error(f"核心组件初始化失败: {str(e)}")
            self.connection_status.setText("连接状态: 初始化失败")
            self.connection_status.setStyleSheet("color: #ff6b6b;")
    
    def setup_connections(self):
        """设置组件间的连接"""
        # 连接信号和槽
        self.status_message.connect(self.update_status_message)
        self.progress_update.connect(self.update_progress)
        
        # 连接选项卡切换事件
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
    
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def update_status_message(self, message: str):
        """更新状态消息"""
        self.status_label.setText(message)
        logger.info(f"状态更新: {message}")
    
    def update_progress(self, value: int):
        """更新进度条"""
        if value == 0:
            self.progress_bar.setVisible(False)
        else:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(value)
    
    def on_tab_changed(self, index: int):
        """选项卡切换事件处理"""
        tab_names = ["图表分析", "策略管理", "数据管理", "分析报告", "实时监控"]
        if 0 <= index < len(tab_names):
            self.status_message.emit(f"切换到 {tab_names[index]} 模块")
    
    # 菜单和工具栏事件处理
    def new_strategy(self):
        """新建策略"""
        self.tab_widget.setCurrentIndex(1)  # 切换到策略管理
        if self.strategy_widget:
            self.strategy_widget.create_new_strategy()
        self.status_message.emit("创建新策略")
    
    def open_project(self):
        """打开项目"""
        self.status_message.emit("打开项目功能开发中...")
    
    def save_project(self):
        """保存项目"""
        self.status_message.emit("保存项目功能开发中...")
    
    def import_data(self):
        """导入数据"""
        self.tab_widget.setCurrentIndex(2)  # 切换到数据管理
        if self.data_source_widget:
            self.data_source_widget.import_data()
        self.status_message.emit("导入数据")
    
    def sync_data(self):
        """同步数据"""
        self.status_message.emit("开始同步数据...")
        self.progress_update.emit(10)
        
        # 模拟数据同步过程
        QTimer.singleShot(2000, lambda: self.progress_update.emit(50))
        QTimer.singleShot(4000, lambda: self.progress_update.emit(100))
        QTimer.singleShot(5000, lambda: [
            self.progress_update.emit(0),
            self.status_message.emit("数据同步完成")
        ])
    
    def run_backtest(self):
        """运行回测"""
        self.status_message.emit("开始运行回测...")
        self.progress_update.emit(5)
        
        # 切换到图表分析查看结果
        self.tab_widget.setCurrentIndex(0)
        
        # 模拟回测过程
        QTimer.singleShot(1000, lambda: self.progress_update.emit(25))
        QTimer.singleShot(3000, lambda: self.progress_update.emit(75))
        QTimer.singleShot(5000, lambda: [
            self.progress_update.emit(0),
            self.status_message.emit("回测完成")
        ])
    
    def optimize_strategy(self):
        """优化策略"""
        self.status_message.emit("策略优化功能开发中...")
    
    def toggle_fullscreen(self):
        """切换全屏模式"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
                         "量化交易系统 v1.0\n\n"
                         "一个专业的量化交易策略开发和回测平台\n"
                         "支持多市场、多策略的量化分析\n\n"
                         "© 2024 Quantitative Trading System")
    
    def closeEvent(self, event):
        """关闭事件处理"""
        reply = QMessageBox.question(self, '确认退出', 
                                   '确定要退出量化交易系统吗？',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            # 清理资源
            if self.system_monitor:
                self.system_monitor.stop()
            
            logger.info("应用程序正常退出")
            event.accept()
        else:
            event.ignore()


def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("量化交易系统")
    app.setApplicationVersion("1.0.0")
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建主窗口
    main_window = MainApplication()
    main_window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()