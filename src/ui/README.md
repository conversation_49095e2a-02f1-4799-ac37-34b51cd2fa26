# 量化交易系统用户界面

本目录包含量化交易系统的图形用户界面组件，提供桌面GUI和Web界面两种模式。

## 功能模块

### 1. 主图表组件 (charts/)
- **主图表 (main_chart.py)**: K线图表显示和技术指标叠加
- **指标面板 (indicator_panel.py)**: 技术指标配置和显示
- **图表配置 (chart_config.py)**: 图表样式和参数配置
- **图表工具 (chart_utils.py)**: 图表辅助功能

### 2. 策略管理界面 (strategy_management/)
- **主界面 (main_interface.py)**: 策略管理主界面
- **策略列表 (strategy_list.py)**: 策略列表显示和操作
- **策略配置 (strategy_config.py)**: 策略参数配置
- **回测监控 (backtest_monitor.py)**: 回测进度监控
- **参数优化 (parameter_optimizer.py)**: 策略参数优化工具
- **组合管理 (portfolio_manager.py)**: 策略组合管理

### 3. 数据源管理界面 (data_source_management/)
- **主界面 (main_interface.py)**: 数据源管理主界面
- **数据源配置 (source_config.py)**: 数据源配置管理
- **同步监控 (sync_monitor.py)**: 数据同步状态监控
- **覆盖范围查看器 (coverage_viewer.py)**: 数据覆盖范围查看
- **质量监控 (quality_monitor.py)**: 数据质量监控
- **经济数据浏览器 (economic_browser.py)**: 经济数据浏览
- **关联分析器 (correlation_analyzer.py)**: 数据关联分析

### 4. 分析报告界面 (analysis_reporting/)
- **主界面 (main_interface.py)**: 分析报告主界面
- **绩效面板 (performance_panel.py)**: 策略绩效展示
- **图表面板 (chart_panel.py)**: 分析图表显示
- **对比面板 (comparison_panel.py)**: 策略对比分析
- **报告生成器 (report_generator.py)**: 报告生成和导出

### 5. 实时监控界面 (monitoring/)
- **实时仪表板 (real_time_dashboard.py)**: 实时监控仪表板
- **监控界面 (monitoring_interface.py)**: 系统监控界面
- **告警通知面板 (alert_notification_panel.py)**: 告警和通知显示

## 界面启动方式

### 方式1: 自动选择界面
```bash
python start_ui.py
```
系统会自动检测可用的依赖项并选择合适的界面。

### 方式2: 启动桌面GUI界面
```bash
python start_ui.py --gui
```
需要安装PyQt5:
```bash
pip install PyQt5
```

### 方式3: 启动Web界面
```bash
python start_ui.py --web
```
需要安装Flask:
```bash
pip install flask plotly
```

### 方式4: 指定Web端口和主机
```bash
python start_ui.py --web --host 0.0.0.0 --port 8080
```

## 界面特性

### 桌面GUI界面特性
- 原生桌面应用体验
- 高性能图表渲染
- 完整的键盘快捷键支持
- 多窗口和标签页管理
- 本地文件操作支持

### Web界面特性
- 跨平台浏览器访问
- 响应式设计，支持移动设备
- 实时数据更新
- 现代化的Bootstrap UI
- 交互式Plotly图表

## 依赖项

### 桌面GUI依赖
```
PyQt5>=5.15.0
matplotlib>=3.5.0
pandas>=1.3.0
numpy>=1.21.0
```

### Web界面依赖
```
Flask>=2.0.0
plotly>=5.0.0
pandas>=1.3.0
numpy>=1.21.0
```

## 界面截图和功能说明

### 主界面
- 统一的导航栏和菜单系统
- 模块化的标签页设计
- 实时状态显示
- 快速操作工具栏

### 图表分析模块
- 交互式K线图表
- 技术指标叠加显示
- 交易信号标记
- 图表缩放和平移
- 十字光标数据提示

### 策略管理模块
- 策略列表和状态管理
- 参数配置界面
- 回测启动和监控
- 策略组合管理
- 绩效对比分析

### 数据管理模块
- 多数据源配置
- 数据同步状态监控
- 数据质量检查
- 覆盖范围查看
- 经济数据浏览

### 分析报告模块
- 绩效指标展示
- 资金曲线图表
- 回撤分析
- 策略对比
- 报告导出功能

### 实时监控模块
- 系统状态监控
- 实时持仓显示
- 风险指标监控
- 告警通知
- 性能监控

## 开发说明

### 添加新的UI组件
1. 在相应的模块目录下创建新的Python文件
2. 继承相应的基类 (QWidget for GUI, Flask Blueprint for Web)
3. 实现必要的界面和业务逻辑
4. 在主应用程序中注册新组件

### 自定义样式
- GUI界面: 修改 `main_app.py` 中的样式表
- Web界面: 修改 `templates/base.html` 中的CSS样式

### 添加新的API端点
在 `web_app.py` 中添加新的路由和处理函数。

## 故障排除

### 常见问题

1. **PyQt5安装失败**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install python3-pyqt5
   
   # macOS
   brew install pyqt5
   
   # Windows
   pip install PyQt5
   ```

2. **Web界面无法访问**
   - 检查防火墙设置
   - 确认端口未被占用
   - 检查Flask依赖是否正确安装

3. **图表显示异常**
   - 确认matplotlib或plotly版本兼容性
   - 检查数据格式是否正确
   - 查看浏览器控制台错误信息

4. **界面响应缓慢**
   - 检查数据量大小
   - 优化图表渲染设置
   - 考虑使用数据分页

### 日志查看
界面运行日志保存在 `logs/` 目录下:
- `gui.log`: 桌面GUI界面日志
- `web.log`: Web界面日志
- `ui.log`: 通用界面日志

## 技术架构

### GUI架构 (PyQt5)
```
MainApplication (QMainWindow)
├── MenuBar (QMenuBar)
├── ToolBar (QToolBar)
├── CentralWidget (QTabWidget)
│   ├── ChartWidget (QWidget)
│   ├── StrategyWidget (QWidget)
│   ├── DataSourceWidget (QWidget)
│   ├── AnalysisWidget (QWidget)
│   └── MonitoringWidget (QWidget)
└── StatusBar (QStatusBar)
```

### Web架构 (Flask)
```
Flask Application
├── Templates (Jinja2)
├── Static Files (CSS/JS)
├── API Routes
├── WebSocket (实时数据)
└── Error Handlers
```

## 贡献指南

1. 遵循Python PEP 8编码规范
2. 为新功能添加相应的文档
3. 确保界面的响应式设计
4. 添加适当的错误处理
5. 编写单元测试

## 许可证

本项目采用MIT许可证，详见LICENSE文件。