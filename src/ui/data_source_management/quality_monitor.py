"""
数据质量监控和报告界面
"""

import streamlit as st
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np


class DataQualityMonitor:
    """数据质量监控器"""
    
    def __init__(self, api_base_url: str):
        self.api_base_url = api_base_url
        self.headers = {"Content-Type": "application/json"}
    
    def render(self):
        """渲染数据质量监控界面"""
        st.header("🔍 数据质量监控")
        
        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs(["质量概览", "实时监控", "质量报告", "异常检测"])
        
        with tab1:
            self._render_quality_overview()
        
        with tab2:
            self._render_real_time_monitor()
        
        with tab3:
            self._render_quality_reports()
        
        with tab4:
            self._render_anomaly_detection()
    
    def _render_quality_overview(self):
        """渲染质量概览"""
        st.subheader("数据质量总览")
        
        # 获取质量报告
        quality_report = self._get_quality_report()
        
        if not quality_report:
            st.error("无法获取质量报告")
            return
        
        # 总体质量指标
        summary = quality_report.get('summary', {})
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("数据源总数", summary.get('total_sources', 0))
        
        with col2:
            healthy_sources = summary.get('healthy_sources', 0)
            total_sources = summary.get('total_sources', 1)
            health_rate = (healthy_sources / total_sources * 100) if total_sources > 0 else 0
            st.metric("健康率", f"{health_rate:.1f}%")
        
        with col3:
            st.metric("品种总数", summary.get('total_symbols', 0))
        
        with col4:
            quality_score = summary.get('data_quality_score', 0)
            st.metric("质量评分", f"{quality_score:.1f}")
        
        # 质量分布图表
        col1, col2 = st.columns(2)
        
        with col1:
            # 数据源健康状态分布
            sources_data = quality_report.get('sources', {})
            if sources_data:
                health_status = []
                source_names = []
                
                for source_name, source_info in sources_data.items():
                    source_names.append(source_name)
                    health_status.append("健康" if source_info.get('healthy', False) else "异常")
                
                health_counts = pd.Series(health_status).value_counts()
                
                fig_health = px.pie(
                    values=health_counts.values,
                    names=health_counts.index,
                    title="数据源健康状态分布",
                    color_discrete_map={"健康": "green", "异常": "red"}
                )
                st.plotly_chart(fig_health, use_container_width=True)
        
        with col2:
            # 品种数量分布
            if sources_data:
                symbol_counts = []
                source_names = []
                
                for source_name, source_info in sources_data.items():
                    source_names.append(source_name)
                    symbol_counts.append(source_info.get('symbol_count', 0))
                
                fig_symbols = px.bar(
                    x=source_names,
                    y=symbol_counts,
                    title="各数据源品种数量",
                    labels={'x': '数据源', 'y': '品种数量'}
                )
                fig_symbols.update_xaxis(tickangle=45)
                st.plotly_chart(fig_symbols, use_container_width=True)
        
        # 详细质量表格
        st.subheader("数据源质量详情")
        
        if sources_data:
            quality_table = []
            for source_name, source_info in sources_data.items():
                perf_stats = source_info.get('performance', {})
                
                quality_table.append({
                    "数据源": source_name,
                    "市场": source_info.get('market', 'Unknown'),
                    "交易所": source_info.get('exchange', 'Unknown'),
                    "健康状态": "✅ 健康" if source_info.get('healthy', False) else "❌ 异常",
                    "品种数量": source_info.get('symbol_count', 0),
                    "响应时间": f"{perf_stats.get('avg_response_time', 0):.0f}ms",
                    "成功率": f"{perf_stats.get('success_rate', 0):.1f}%",
                    "最后更新": self._format_datetime(source_info.get('last_update')),
                    "健康信息": source_info.get('health_message', '')
                })
            
            df_quality = pd.DataFrame(quality_table)
            st.dataframe(df_quality, use_container_width=True)
        
        # 质量趋势图
        st.subheader("质量趋势")
        self._render_quality_trends()
    
    def _render_real_time_monitor(self):
        """渲染实时监控"""
        st.subheader("实时数据质量监控")
        
        # 监控控制面板
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            auto_refresh = st.checkbox("自动刷新", value=False)
        
        with col2:
            if auto_refresh:
                refresh_interval = st.selectbox("刷新间隔", [5, 10, 30, 60], index=1)
        
        with col3:
            if st.button("🔄 立即刷新"):
                st.rerun()
        
        with col4:
            if st.button("📊 生成报告"):
                self._generate_quality_report()
        
        # 实时指标仪表板
        st.subheader("实时指标")
        
        # 模拟实时数据
        current_metrics = self._get_real_time_metrics()
        
        # 创建仪表盘
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # 整体健康度仪表
            fig_health_gauge = go.Figure(go.Indicator(
                mode="gauge+number+delta",
                value=current_metrics.get('overall_health', 85),
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "整体健康度"},
                delta={'reference': 90},
                gauge={
                    'axis': {'range': [None, 100]},
                    'bar': {'color': "darkblue"},
                    'steps': [
                        {'range': [0, 50], 'color': "lightgray"},
                        {'range': [50, 80], 'color': "yellow"},
                        {'range': [80, 100], 'color': "green"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 90
                    }
                }
            ))
            fig_health_gauge.update_layout(height=300)
            st.plotly_chart(fig_health_gauge, use_container_width=True)
        
        with col2:
            # 数据完整性仪表
            fig_completeness_gauge = go.Figure(go.Indicator(
                mode="gauge+number+delta",
                value=current_metrics.get('data_completeness', 92),
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "数据完整性"},
                delta={'reference': 95},
                gauge={
                    'axis': {'range': [None, 100]},
                    'bar': {'color': "darkgreen"},
                    'steps': [
                        {'range': [0, 70], 'color': "lightgray"},
                        {'range': [70, 90], 'color': "yellow"},
                        {'range': [90, 100], 'color': "green"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 95
                    }
                }
            ))
            fig_completeness_gauge.update_layout(height=300)
            st.plotly_chart(fig_completeness_gauge, use_container_width=True)
        
        with col3:
            # 响应时间仪表
            fig_response_gauge = go.Figure(go.Indicator(
                mode="gauge+number+delta",
                value=current_metrics.get('avg_response_time', 250),
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "平均响应时间 (ms)"},
                delta={'reference': 200},
                gauge={
                    'axis': {'range': [0, 1000]},
                    'bar': {'color': "darkorange"},
                    'steps': [
                        {'range': [0, 200], 'color': "green"},
                        {'range': [200, 500], 'color': "yellow"},
                        {'range': [500, 1000], 'color': "red"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 500
                    }
                }
            ))
            fig_response_gauge.update_layout(height=300)
            st.plotly_chart(fig_response_gauge, use_container_width=True)
        
        # 实时数据流
        st.subheader("实时数据流监控")
        
        # 模拟实时数据流
        stream_data = self._generate_stream_data()
        
        if stream_data:
            # 创建实时图表
            fig_stream = go.Figure()
            
            for source_name, data in stream_data.items():
                fig_stream.add_trace(go.Scatter(
                    x=data['timestamps'],
                    y=data['response_times'],
                    mode='lines+markers',
                    name=source_name,
                    line=dict(width=2)
                ))
            
            fig_stream.update_layout(
                title="实时响应时间监控",
                xaxis_title="时间",
                yaxis_title="响应时间 (ms)",
                height=400
            )
            
            st.plotly_chart(fig_stream, use_container_width=True)
        
        # 告警信息
        st.subheader("实时告警")
        self._render_real_time_alerts()
        
        # 自动刷新逻辑
        if auto_refresh:
            import time
            time.sleep(refresh_interval)
            st.rerun()
    
    def _render_quality_reports(self):
        """渲染质量报告"""
        st.subheader("数据质量报告")
        
        # 报告类型选择
        report_type = st.selectbox(
            "选择报告类型",
            ["综合质量报告", "数据源对比报告", "品种质量报告", "历史趋势报告"]
        )
        
        if report_type == "综合质量报告":
            self._render_comprehensive_report()
        elif report_type == "数据源对比报告":
            self._render_source_comparison_report()
        elif report_type == "品种质量报告":
            self._render_symbol_quality_report()
        else:  # 历史趋势报告
            self._render_historical_trend_report()
    
    def _render_comprehensive_report(self):
        """渲染综合质量报告"""
        st.subheader("综合数据质量报告")
        
        # 报告时间范围
        col1, col2 = st.columns(2)
        
        with col1:
            start_date = st.date_input(
                "开始日期",
                value=datetime.now().date() - timedelta(days=30)
            )
        
        with col2:
            end_date = st.date_input(
                "结束日期",
                value=datetime.now().date()
            )
        
        if st.button("生成报告", type="primary"):
            with st.spinner("正在生成综合质量报告..."):
                # 模拟报告生成
                report_data = self._generate_comprehensive_report(start_date, end_date)
                
                # 显示报告
                st.success("报告生成完成！")
                
                # 执行摘要
                st.subheader("执行摘要")
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("总体评分", f"{report_data['overall_score']:.1f}/100")
                
                with col2:
                    st.metric("数据完整性", f"{report_data['completeness']:.1f}%")
                
                with col3:
                    st.metric("数据准确性", f"{report_data['accuracy']:.1f}%")
                
                with col4:
                    st.metric("及时性", f"{report_data['timeliness']:.1f}%")
                
                # 详细分析
                st.subheader("详细分析")
                
                # 质量维度分析
                dimensions = ['完整性', '准确性', '一致性', '及时性', '有效性']
                scores = [report_data['completeness'], report_data['accuracy'], 
                         report_data['consistency'], report_data['timeliness'], 
                         report_data['validity']]
                
                fig_radar = go.Figure()
                
                fig_radar.add_trace(go.Scatterpolar(
                    r=scores,
                    theta=dimensions,
                    fill='toself',
                    name='质量评分'
                ))
                
                fig_radar.update_layout(
                    polar=dict(
                        radialaxis=dict(
                            visible=True,
                            range=[0, 100]
                        )),
                    showlegend=True,
                    title="数据质量维度分析"
                )
                
                st.plotly_chart(fig_radar, use_container_width=True)
                
                # 问题分析
                st.subheader("主要问题")
                issues = report_data.get('issues', [])
                
                for i, issue in enumerate(issues):
                    severity_color = {"高": "🔴", "中": "🟡", "低": "🟢"}
                    st.write(f"{severity_color.get(issue['severity'], '🔵')} **{issue['title']}**")
                    st.write(f"   影响: {issue['impact']}")
                    st.write(f"   建议: {issue['recommendation']}")
                    st.write("")
                
                # 导出报告
                if st.button("📥 导出报告"):
                    report_json = pd.Series(report_data).to_json(indent=2)
                    st.download_button(
                        label="下载JSON报告",
                        data=report_json,
                        file_name=f"quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                        mime="application/json"
                    )
    
    def _render_source_comparison_report(self):
        """渲染数据源对比报告"""
        st.subheader("数据源对比分析")
        
        # 选择要对比的数据源
        available_sources = ["Yahoo Finance", "Alpha Vantage", "FRED", "Binance"]
        selected_sources = st.multiselect("选择数据源", available_sources, default=available_sources[:2])
        
        if len(selected_sources) < 2:
            st.warning("请至少选择两个数据源进行对比")
            return
        
        if st.button("生成对比报告"):
            # 模拟对比数据
            comparison_data = []
            
            for source in selected_sources:
                comparison_data.append({
                    "数据源": source,
                    "可用性": np.random.uniform(95, 99.5),
                    "响应时间": np.random.uniform(100, 500),
                    "数据完整性": np.random.uniform(90, 99),
                    "更新频率": np.random.choice(["实时", "每分钟", "每小时", "每日"]),
                    "品种数量": np.random.randint(100, 5000),
                    "质量评分": np.random.uniform(80, 95)
                })
            
            df_comparison = pd.DataFrame(comparison_data)
            
            # 显示对比表格
            st.dataframe(df_comparison, use_container_width=True)
            
            # 对比图表
            col1, col2 = st.columns(2)
            
            with col1:
                fig_availability = px.bar(
                    df_comparison,
                    x="数据源",
                    y="可用性",
                    title="可用性对比",
                    text="可用性"
                )
                fig_availability.update_traces(texttemplate='%{text:.1f}%', textposition='outside')
                st.plotly_chart(fig_availability, use_container_width=True)
            
            with col2:
                fig_response = px.bar(
                    df_comparison,
                    x="数据源",
                    y="响应时间",
                    title="响应时间对比",
                    text="响应时间"
                )
                fig_response.update_traces(texttemplate='%{text:.0f}ms', textposition='outside')
                st.plotly_chart(fig_response, use_container_width=True)
    
    def _render_symbol_quality_report(self):
        """渲染品种质量报告"""
        st.subheader("品种数据质量分析")
        
        # 品种选择
        symbol_input = st.text_input("输入品种代码", placeholder="例如: AAPL")
        
        if symbol_input and st.button("分析品种质量"):
            symbol = symbol_input.upper()
            
            # 模拟品种质量数据
            quality_data = {
                "基本信息": {
                    "品种代码": symbol,
                    "数据源数量": np.random.randint(2, 5),
                    "数据范围": "2020-01-01 至 2024-01-15",
                    "总记录数": np.random.randint(1000, 5000)
                },
                "质量指标": {
                    "完整性": np.random.uniform(95, 99.5),
                    "准确性": np.random.uniform(90, 98),
                    "一致性": np.random.uniform(85, 95),
                    "及时性": np.random.uniform(92, 99)
                },
                "数据问题": {
                    "缺失值": f"{np.random.uniform(0.1, 2.0):.1f}%",
                    "异常值": f"{np.random.uniform(0.05, 1.0):.2f}%",
                    "重复值": f"{np.random.uniform(0, 0.5):.2f}%",
                    "格式错误": f"{np.random.uniform(0, 0.1):.2f}%"
                }
            }
            
            # 显示质量信息
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.write("**基本信息**")
                for key, value in quality_data["基本信息"].items():
                    st.write(f"{key}: {value}")
            
            with col2:
                st.write("**质量指标**")
                for key, value in quality_data["质量指标"].items():
                    st.write(f"{key}: {value:.1f}%")
            
            with col3:
                st.write("**数据问题**")
                for key, value in quality_data["数据问题"].items():
                    st.write(f"{key}: {value}")
            
            # 质量趋势图
            st.subheader(f"{symbol} 质量趋势")
            
            # 模拟质量趋势数据
            dates = pd.date_range(start='2023-01-01', end='2024-01-15', freq='D')
            quality_trend = np.random.uniform(90, 99, len(dates))
            
            df_trend = pd.DataFrame({
                'date': dates,
                'quality_score': quality_trend
            })
            
            fig_trend = px.line(
                df_trend,
                x='date',
                y='quality_score',
                title=f'{symbol} 数据质量趋势',
                labels={'quality_score': '质量评分', 'date': '日期'}
            )
            
            st.plotly_chart(fig_trend, use_container_width=True)
    
    def _render_historical_trend_report(self):
        """渲染历史趋势报告"""
        st.subheader("历史质量趋势分析")
        
        # 时间范围选择
        col1, col2 = st.columns(2)
        
        with col1:
            start_date = st.date_input(
                "开始日期",
                value=datetime.now().date() - timedelta(days=90)
            )
        
        with col2:
            end_date = st.date_input(
                "结束日期",
                value=datetime.now().date()
            )
        
        if st.button("生成趋势报告"):
            # 模拟历史趋势数据
            dates = pd.date_range(start=start_date, end=end_date, freq='D')
            
            trend_data = []
            metrics = ['整体质量', '数据完整性', '响应时间', '可用性']
            
            for metric in metrics:
                base_value = np.random.uniform(80, 95)
                noise = np.random.normal(0, 2, len(dates))
                trend = np.linspace(0, 5, len(dates))  # 轻微上升趋势
                values = base_value + trend + noise
                
                for i, date in enumerate(dates):
                    trend_data.append({
                        'date': date,
                        'metric': metric,
                        'value': max(0, min(100, values[i]))  # 限制在0-100之间
                    })
            
            df_trends = pd.DataFrame(trend_data)
            
            # 趋势图表
            fig_trends = px.line(
                df_trends,
                x='date',
                y='value',
                color='metric',
                title='数据质量历史趋势',
                labels={'value': '评分', 'date': '日期', 'metric': '指标'}
            )
            
            st.plotly_chart(fig_trends, use_container_width=True)
            
            # 趋势分析
            st.subheader("趋势分析")
            
            # 计算趋势统计
            trend_stats = df_trends.groupby('metric')['value'].agg(['mean', 'std', 'min', 'max']).round(2)
            st.dataframe(trend_stats, use_container_width=True)
    
    def _render_anomaly_detection(self):
        """渲染异常检测"""
        st.subheader("数据异常检测")
        
        # 异常检测配置
        col1, col2 = st.columns(2)
        
        with col1:
            detection_method = st.selectbox(
                "检测方法",
                ["统计异常检测", "机器学习检测", "规则基础检测"]
            )
        
        with col2:
            sensitivity = st.slider("敏感度", 0.1, 1.0, 0.5, 0.1)
        
        if st.button("开始异常检测"):
            with st.spinner("正在进行异常检测..."):
                # 模拟异常检测结果
                anomalies = self._detect_anomalies(detection_method, sensitivity)
                
                if anomalies:
                    st.subheader("检测到的异常")
                    
                    for anomaly in anomalies:
                        severity_color = {"高": "🔴", "中": "🟡", "低": "🟢"}
                        
                        with st.expander(f"{severity_color.get(anomaly['severity'], '🔵')} {anomaly['title']}"):
                            st.write(f"**数据源:** {anomaly['source']}")
                            st.write(f"**品种:** {anomaly['symbol']}")
                            st.write(f"**异常类型:** {anomaly['type']}")
                            st.write(f"**检测时间:** {anomaly['detected_at']}")
                            st.write(f"**描述:** {anomaly['description']}")
                            st.write(f"**建议措施:** {anomaly['recommendation']}")
                            
                            # 异常数据可视化
                            if anomaly.get('chart_data'):
                                fig = px.line(
                                    anomaly['chart_data'],
                                    title=f"{anomaly['symbol']} 异常数据"
                                )
                                st.plotly_chart(fig, use_container_width=True)
                else:
                    st.success("未检测到异常数据")
        
        # 异常历史
        st.subheader("异常历史记录")
        self._render_anomaly_history()
    
    def _render_quality_trends(self):
        """渲染质量趋势图"""
        # 模拟质量趋势数据
        dates = pd.date_range(start='2023-01-01', end='2024-01-15', freq='D')
        
        metrics = ['整体质量', '数据完整性', '响应时间', '可用性']
        trend_data = []
        
        for metric in metrics:
            base_value = np.random.uniform(80, 95)
            noise = np.random.normal(0, 2, len(dates))
            trend = np.linspace(0, 5, len(dates))
            values = base_value + trend + noise
            
            for i, date in enumerate(dates):
                trend_data.append({
                    'date': date,
                    'metric': metric,
                    'value': max(0, min(100, values[i]))
                })
        
        df_trends = pd.DataFrame(trend_data)
        
        fig_trends = px.line(
            df_trends,
            x='date',
            y='value',
            color='metric',
            title='数据质量趋势',
            labels={'value': '评分', 'date': '日期', 'metric': '指标'}
        )
        
        st.plotly_chart(fig_trends, use_container_width=True)
    
    def _render_real_time_alerts(self):
        """渲染实时告警"""
        # 模拟告警数据
        alerts = [
            {
                "时间": "2024-01-15 14:30:00",
                "级别": "🔴 高",
                "数据源": "Yahoo Finance",
                "问题": "响应时间超过阈值",
                "详情": "平均响应时间达到800ms，超过500ms阈值"
            },
            {
                "时间": "2024-01-15 14:25:00",
                "级别": "🟡 中",
                "数据源": "Alpha Vantage",
                "问题": "数据更新延迟",
                "详情": "最新数据时间戳延迟15分钟"
            },
            {
                "时间": "2024-01-15 14:20:00",
                "级别": "🟢 低",
                "数据源": "FRED",
                "问题": "连接不稳定",
                "详情": "连接重试3次后成功"
            }
        ]
        
        if alerts:
            df_alerts = pd.DataFrame(alerts)
            st.dataframe(df_alerts, use_container_width=True)
        else:
            st.success("当前无告警信息")
    
    def _get_quality_report(self) -> Optional[Dict[str, Any]]:
        """获取质量报告"""
        try:
            response = requests.get(
                f"{self.api_base_url}/data-sources/quality/report",
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            st.error(f"获取质量报告失败: {str(e)}")
            return None
    
    def _get_real_time_metrics(self) -> Dict[str, Any]:
        """获取实时指标"""
        return {
            'overall_health': np.random.uniform(80, 95),
            'data_completeness': np.random.uniform(90, 99),
            'avg_response_time': np.random.uniform(150, 400)
        }
    
    def _generate_stream_data(self) -> Dict[str, Dict[str, List]]:
        """生成实时数据流"""
        sources = ['Yahoo Finance', 'Alpha Vantage', 'FRED']
        stream_data = {}
        
        current_time = datetime.now()
        
        for source in sources:
            timestamps = [current_time - timedelta(minutes=i) for i in range(30, 0, -1)]
            response_times = [np.random.uniform(100, 500) for _ in timestamps]
            
            stream_data[source] = {
                'timestamps': timestamps,
                'response_times': response_times
            }
        
        return stream_data
    
    def _generate_comprehensive_report(self, start_date, end_date) -> Dict[str, Any]:
        """生成综合报告"""
        return {
            'overall_score': np.random.uniform(80, 95),
            'completeness': np.random.uniform(90, 99),
            'accuracy': np.random.uniform(85, 98),
            'consistency': np.random.uniform(80, 95),
            'timeliness': np.random.uniform(88, 99),
            'validity': np.random.uniform(85, 96),
            'issues': [
                {
                    'severity': '中',
                    'title': 'Alpha Vantage响应时间偶尔超时',
                    'impact': '影响约5%的数据获取请求',
                    'recommendation': '增加重试机制和备用数据源'
                },
                {
                    'severity': '低',
                    'title': '部分品种数据更新延迟',
                    'impact': '影响实时性要求高的应用',
                    'recommendation': '优化数据同步频率'
                }
            ]
        }
    
    def _detect_anomalies(self, method: str, sensitivity: float) -> List[Dict[str, Any]]:
        """检测异常"""
        # 模拟异常检测结果
        anomalies = []
        
        if np.random.random() < sensitivity:
            anomalies.append({
                'severity': '高',
                'title': '价格数据异常波动',
                'source': 'Yahoo Finance',
                'symbol': 'AAPL',
                'type': '数值异常',
                'detected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'description': '检测到价格数据出现异常波动，可能存在数据质量问题',
                'recommendation': '检查数据源，验证数据准确性'
            })
        
        if np.random.random() < sensitivity * 0.7:
            anomalies.append({
                'severity': '中',
                'title': '数据更新频率异常',
                'source': 'Alpha Vantage',
                'symbol': 'GOOGL',
                'type': '时间异常',
                'detected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'description': '数据更新频率低于预期，可能影响数据时效性',
                'recommendation': '检查API限制和网络连接'
            })
        
        return anomalies
    
    def _render_anomaly_history(self):
        """渲染异常历史记录"""
        # 模拟历史异常数据
        history_data = [
            {
                "时间": "2024-01-15 10:30:00",
                "数据源": "Yahoo Finance",
                "品种": "AAPL",
                "异常类型": "价格异常",
                "严重程度": "🔴 高",
                "状态": "已解决"
            },
            {
                "时间": "2024-01-14 15:20:00",
                "数据源": "Alpha Vantage",
                "品种": "GOOGL",
                "异常类型": "更新延迟",
                "严重程度": "🟡 中",
                "状态": "已解决"
            },
            {
                "时间": "2024-01-14 09:15:00",
                "数据源": "FRED",
                "品种": "GDP",
                "异常类型": "数据缺失",
                "严重程度": "🟢 低",
                "状态": "已解决"
            }
        ]
        
        df_history = pd.DataFrame(history_data)
        st.dataframe(df_history, use_container_width=True)
    
    def _generate_quality_report(self):
        """生成质量报告"""
        with st.spinner("正在生成质量报告..."):
            import time
            time.sleep(2)
            
            st.success("质量报告生成完成！")
            st.info("报告已保存到系统，可在质量报告页面查看详情。")
    
    def _format_datetime(self, dt_str: Optional[str]) -> str:
        """格式化日期时间"""
        if not dt_str:
            return "未知"
        
        try:
            dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return dt_str