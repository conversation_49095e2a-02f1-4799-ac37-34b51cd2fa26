"""
数据源配置管理面板
"""

import streamlit as st
import requests
import json
from typing import Dict, List, Any, Optional
import pandas as pd


class DataSourceConfigPanel:
    """数据源配置管理面板"""
    
    def __init__(self, api_base_url: str):
        self.api_base_url = api_base_url
        self.headers = {"Content-Type": "application/json"}
    
    def render(self):
        """渲染配置面板"""
        st.header("🔧 数据源配置管理")
        
        # 创建标签页
        tab1, tab2, tab3 = st.tabs(["数据源列表", "添加数据源", "批量操作"])
        
        with tab1:
            self._render_source_list()
        
        with tab2:
            self._render_add_source()
        
        with tab3:
            self._render_batch_operations()
    
    def _render_source_list(self):
        """渲染数据源列表"""
        st.subheader("当前数据源配置")
        
        # 获取数据源列表
        sources = self._get_data_sources()
        if not sources:
            st.info("暂无数据源配置")
            return
        
        # 显示数据源表格
        for i, source in enumerate(sources):
            with st.expander(f"📡 {source.get('name', 'Unknown')} - {source.get('market', 'Unknown')}"):
                col1, col2 = st.columns([2, 1])
                
                with col1:
                    # 基本信息
                    st.write("**基本信息**")
                    st.write(f"名称: {source.get('name', 'N/A')}")
                    st.write(f"市场: {source.get('market', 'N/A')}")
                    st.write(f"交易所: {source.get('exchange', 'N/A')}")
                    st.write(f"优先级: {source.get('priority', 'N/A')}")
                    
                    # 状态信息
                    st.write("**状态信息**")
                    status_color = "🟢" if source.get('enabled') else "🔴"
                    health_color = "✅" if source.get('healthy') else "❌"
                    st.write(f"启用状态: {status_color} {'启用' if source.get('enabled') else '禁用'}")
                    st.write(f"健康状态: {health_color} {'健康' if source.get('healthy') else '异常'}")
                    
                    # 性能统计
                    perf_stats = source.get('performance_stats', {})
                    if perf_stats:
                        st.write("**性能统计**")
                        st.write(f"请求数/小时: {perf_stats.get('requests_last_hour', 0)}")
                        st.write(f"请求数/天: {perf_stats.get('requests_last_day', 0)}")
                        st.write(f"平均响应时间: {perf_stats.get('avg_response_time', 0):.2f}ms")
                        st.write(f"错误率: {perf_stats.get('error_rate', 0):.2f}%")
                
                with col2:
                    # 操作按钮
                    st.write("**操作**")
                    
                    # 启用/禁用按钮
                    if source.get('enabled'):
                        if st.button(f"🔴 禁用", key=f"disable_{i}"):
                            if self._disable_source(source['name']):
                                st.success(f"已禁用数据源: {source['name']}")
                                st.rerun()
                            else:
                                st.error("禁用失败")
                    else:
                        if st.button(f"🟢 启用", key=f"enable_{i}"):
                            if self._enable_source(source['name']):
                                st.success(f"已启用数据源: {source['name']}")
                                st.rerun()
                            else:
                                st.error("启用失败")
                    
                    # 健康检查按钮
                    if st.button(f"🏥 健康检查", key=f"health_{i}"):
                        with st.spinner("检查中..."):
                            health_result = self._check_source_health(source['name'])
                            if health_result:
                                if health_result.get('healthy'):
                                    st.success("数据源健康")
                                else:
                                    st.error(f"数据源异常: {health_result.get('message', '未知错误')}")
                            else:
                                st.error("健康检查失败")
                    
                    # 编辑按钮
                    if st.button(f"✏️ 编辑", key=f"edit_{i}"):
                        st.session_state[f"edit_source_{i}"] = True
                    
                    # 删除按钮
                    if st.button(f"🗑️ 删除", key=f"delete_{i}", type="secondary"):
                        if st.session_state.get(f"confirm_delete_{i}", False):
                            if self._delete_source(source['name']):
                                st.success(f"已删除数据源: {source['name']}")
                                st.rerun()
                            else:
                                st.error("删除失败")
                        else:
                            st.session_state[f"confirm_delete_{i}"] = True
                            st.warning("再次点击确认删除")
                
                # 编辑表单
                if st.session_state.get(f"edit_source_{i}", False):
                    st.write("**编辑配置**")
                    self._render_edit_form(source, i)
    
    def _render_add_source(self):
        """渲染添加数据源表单"""
        st.subheader("添加新数据源")
        
        with st.form("add_source_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                name = st.text_input("数据源名称*", placeholder="例如: yahoo_finance")
                adapter_class = st.selectbox(
                    "适配器类型*",
                    [
                        "src.data.adapters.yahoo.YahooFinanceAdapter",
                        "src.data.adapters.alpha_vantage.AlphaVantageAdapter",
                        "src.data.adapters.fred.FREDAdapter",
                        "src.data.adapters.binance.BinanceAdapter",
                        "src.data.adapters.base.MockDataSourceAdapter"
                    ]
                )
                enabled = st.checkbox("启用", value=True)
                priority = st.number_input("优先级", min_value=1, max_value=1000, value=100)
            
            with col2:
                st.write("**速率限制配置**")
                requests_per_minute = st.number_input("每分钟请求数", min_value=1, value=60)
                requests_per_hour = st.number_input("每小时请求数", min_value=1, value=1000)
                
                st.write("**认证信息**")
                api_key = st.text_input("API密钥", type="password")
                api_secret = st.text_input("API密钥", type="password")
            
            # 默认参数配置
            st.write("**默认参数**")
            default_params_json = st.text_area(
                "默认参数 (JSON格式)",
                value='{"timeout": 30, "retries": 3}',
                height=100
            )
            
            submitted = st.form_submit_button("添加数据源", type="primary")
            
            if submitted:
                if not name or not adapter_class:
                    st.error("请填写必填字段")
                    return
                
                # 解析默认参数
                try:
                    default_params = json.loads(default_params_json) if default_params_json else {}
                except json.JSONDecodeError:
                    st.error("默认参数JSON格式错误")
                    return
                
                # 构建配置
                config = {
                    "name": name,
                    "adapter_class": adapter_class,
                    "enabled": enabled,
                    "priority": priority,
                    "rate_limit": {
                        "requests_per_minute": requests_per_minute,
                        "requests_per_hour": requests_per_hour
                    },
                    "credentials": {},
                    "default_params": default_params
                }
                
                # 添加认证信息
                if api_key:
                    config["credentials"]["api_key"] = api_key
                if api_secret:
                    config["credentials"]["api_secret"] = api_secret
                
                # 提交配置
                if self._add_source(config):
                    st.success(f"成功添加数据源: {name}")
                    st.rerun()
                else:
                    st.error("添加数据源失败")
    
    def _render_batch_operations(self):
        """渲染批量操作"""
        st.subheader("批量操作")
        
        # 获取数据源列表
        sources = self._get_data_sources()
        if not sources:
            st.info("暂无数据源可操作")
            return
        
        # 选择数据源
        source_names = [s.get('name', '') for s in sources]
        selected_sources = st.multiselect("选择数据源", source_names)
        
        if not selected_sources:
            st.info("请选择要操作的数据源")
            return
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("🟢 批量启用", type="primary"):
                success_count = 0
                for source_name in selected_sources:
                    if self._enable_source(source_name):
                        success_count += 1
                st.success(f"成功启用 {success_count}/{len(selected_sources)} 个数据源")
                st.rerun()
        
        with col2:
            if st.button("🔴 批量禁用"):
                success_count = 0
                for source_name in selected_sources:
                    if self._disable_source(source_name):
                        success_count += 1
                st.success(f"成功禁用 {success_count}/{len(selected_sources)} 个数据源")
                st.rerun()
        
        with col3:
            if st.button("🏥 批量健康检查"):
                with st.spinner("检查中..."):
                    results = {}
                    for source_name in selected_sources:
                        result = self._check_source_health(source_name)
                        results[source_name] = result
                    
                    # 显示结果
                    for source_name, result in results.items():
                        if result and result.get('healthy'):
                            st.success(f"{source_name}: 健康")
                        else:
                            message = result.get('message', '未知错误') if result else '检查失败'
                            st.error(f"{source_name}: {message}")
        
        with col4:
            if st.button("🔄 重新加载配置"):
                with st.spinner("重新加载中..."):
                    if self._reload_config():
                        st.success("配置重新加载成功")
                        st.rerun()
                    else:
                        st.error("配置重新加载失败")
        
        # 导出配置
        st.subheader("配置导出")
        if st.button("📥 导出选中数据源配置"):
            export_data = []
            for source in sources:
                if source.get('name') in selected_sources:
                    export_data.append({
                        "name": source.get('name'),
                        "market": source.get('market'),
                        "exchange": source.get('exchange'),
                        "enabled": source.get('enabled'),
                        "priority": source.get('priority'),
                        "performance": source.get('performance_stats', {})
                    })
            
            if export_data:
                df = pd.DataFrame(export_data)
                csv = df.to_csv(index=False)
                st.download_button(
                    label="下载CSV文件",
                    data=csv,
                    file_name=f"data_sources_config_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
    
    def _render_edit_form(self, source: Dict[str, Any], index: int):
        """渲染编辑表单"""
        with st.form(f"edit_form_{index}"):
            col1, col2 = st.columns(2)
            
            with col1:
                new_priority = st.number_input(
                    "优先级", 
                    min_value=1, 
                    max_value=1000, 
                    value=source.get('priority', 100)
                )
                new_enabled = st.checkbox("启用", value=source.get('enabled', True))
            
            with col2:
                # 这里可以添加更多可编辑的字段
                st.write("更多配置选项...")
            
            col1, col2 = st.columns(2)
            with col1:
                if st.form_submit_button("保存更改", type="primary"):
                    # 构建更新配置
                    updated_config = {
                        "name": source.get('name'),
                        "adapter_class": source.get('adapter_class', ''),
                        "enabled": new_enabled,
                        "priority": new_priority,
                        "rate_limit": source.get('rate_limit', {}),
                        "credentials": source.get('credentials', {}),
                        "default_params": source.get('default_params', {})
                    }
                    
                    if self._update_source(source.get('name'), updated_config):
                        st.success("配置更新成功")
                        st.session_state[f"edit_source_{index}"] = False
                        st.rerun()
                    else:
                        st.error("配置更新失败")
            
            with col2:
                if st.form_submit_button("取消"):
                    st.session_state[f"edit_source_{index}"] = False
                    st.rerun()
    
    def _get_data_sources(self) -> List[Dict[str, Any]]:
        """获取数据源列表"""
        try:
            response = requests.get(
                f"{self.api_base_url}/data-sources/sources",
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            return []
        except Exception as e:
            st.error(f"获取数据源列表失败: {str(e)}")
            return []
    
    def _add_source(self, config: Dict[str, Any]) -> bool:
        """添加数据源"""
        try:
            response = requests.post(
                f"{self.api_base_url}/data-sources/sources",
                headers=self.headers,
                json=config
            )
            return response.status_code == 200
        except Exception as e:
            st.error(f"添加数据源失败: {str(e)}")
            return False
    
    def _update_source(self, source_name: str, config: Dict[str, Any]) -> bool:
        """更新数据源配置"""
        try:
            response = requests.put(
                f"{self.api_base_url}/data-sources/sources/{source_name}",
                headers=self.headers,
                json=config
            )
            return response.status_code == 200
        except Exception as e:
            st.error(f"更新数据源失败: {str(e)}")
            return False
    
    def _delete_source(self, source_name: str) -> bool:
        """删除数据源"""
        try:
            response = requests.delete(
                f"{self.api_base_url}/data-sources/sources/{source_name}",
                headers=self.headers
            )
            return response.status_code == 200
        except Exception as e:
            st.error(f"删除数据源失败: {str(e)}")
            return False
    
    def _enable_source(self, source_name: str) -> bool:
        """启用数据源"""
        try:
            response = requests.post(
                f"{self.api_base_url}/data-sources/sources/{source_name}/enable",
                headers=self.headers
            )
            return response.status_code == 200
        except Exception as e:
            st.error(f"启用数据源失败: {str(e)}")
            return False
    
    def _disable_source(self, source_name: str) -> bool:
        """禁用数据源"""
        try:
            response = requests.post(
                f"{self.api_base_url}/data-sources/sources/{source_name}/disable",
                headers=self.headers
            )
            return response.status_code == 200
        except Exception as e:
            st.error(f"禁用数据源失败: {str(e)}")
            return False
    
    def _check_source_health(self, source_name: str) -> Optional[Dict[str, Any]]:
        """检查数据源健康状态"""
        try:
            response = requests.get(
                f"{self.api_base_url}/data-sources/sources/{source_name}/health",
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            st.error(f"健康检查失败: {str(e)}")
            return None
    
    def _reload_config(self) -> bool:
        """重新加载配置"""
        try:
            response = requests.post(
                f"{self.api_base_url}/data-sources/reload",
                headers=self.headers
            )
            return response.status_code == 200
        except Exception as e:
            st.error(f"重新加载配置失败: {str(e)}")
            return False