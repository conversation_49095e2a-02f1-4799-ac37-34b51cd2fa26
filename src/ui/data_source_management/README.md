# 数据源管理界面模块

## 概述

数据源管理界面模块提供了一个完整的数据源管理和监控解决方案，包括数据源配置、同步任务管理、数据覆盖范围查看、数据质量监控、经济数据浏览和相关性分析等功能。

## 模块结构

```
src/ui/data_source_management/
├── __init__.py                 # 模块初始化
├── main_interface.py          # 主界面
├── source_config.py           # 数据源配置管理
├── sync_monitor.py            # 同步任务监控
├── coverage_viewer.py         # 数据覆盖范围查看
├── quality_monitor.py         # 数据质量监控
├── economic_browser.py        # 经济数据浏览器
├── correlation_analyzer.py    # 相关性分析工具
└── README.md                  # 说明文档
```

## 功能模块

### 1. 主界面 (main_interface.py)

**功能特性:**
- 数据源概览仪表板
- 实时状态监控
- 性能指标展示
- 快速操作面板

**主要组件:**
- `DataSourceManagementInterface`: 主界面类
- 数据源状态统计
- 性能监控图表
- 实时活动日志

### 2. 数据源配置 (source_config.py)

**功能特性:**
- 数据源添加/编辑/删除
- 配置参数管理
- 批量操作支持
- 配置导入/导出

**主要组件:**
- `DataSourceConfigPanel`: 配置管理面板
- 数据源列表展示
- 配置表单验证
- 批量操作工具

### 3. 同步任务监控 (sync_monitor.py)

**功能特性:**
- 创建同步任务
- 任务状态监控
- 调度管理
- 性能分析

**主要组件:**
- `SyncTaskMonitor`: 同步监控器
- 任务创建向导
- 实时状态更新
- 调度配置管理

### 4. 数据覆盖范围查看 (coverage_viewer.py)

**功能特性:**
- 总体覆盖分析
- 按数据源查看
- 按市场分析
- 品种详情查询

**主要组件:**
- `DataCoverageViewer`: 覆盖范围查看器
- 覆盖统计图表
- 交互式数据探索
- 详细信息展示

### 5. 数据质量监控 (quality_monitor.py)

**功能特性:**
- 质量概览仪表板
- 实时监控
- 质量报告生成
- 异常检测

**主要组件:**
- `DataQualityMonitor`: 质量监控器
- 质量指标仪表盘
- 实时告警系统
- 趋势分析图表

### 6. 经济数据浏览器 (economic_browser.py)

**功能特性:**
- 数据搜索和浏览
- 热门指标展示
- 数据对比分析
- 自定义分析工具

**主要组件:**
- `EconomicDataBrowser`: 经济数据浏览器
- 分类浏览系统
- 搜索引擎
- 对比分析工具

### 7. 相关性分析工具 (correlation_analyzer.py)

**功能特性:**
- 快速关联分析
- 自定义分析
- 高级统计分析
- 分析报告生成

**主要组件:**
- `CorrelationAnalyzer`: 相关性分析器
- 相关性计算引擎
- 可视化图表
- 统计检验工具

## API 依赖

模块依赖以下API端点：

### 数据源管理API
- `GET /api/v1/data-sources/sources` - 获取数据源列表
- `POST /api/v1/data-sources/sources` - 添加数据源
- `PUT /api/v1/data-sources/sources/{name}` - 更新数据源
- `DELETE /api/v1/data-sources/sources/{name}` - 删除数据源
- `POST /api/v1/data-sources/sources/{name}/enable` - 启用数据源
- `POST /api/v1/data-sources/sources/{name}/disable` - 禁用数据源

### 健康监控API
- `GET /api/v1/data-sources/sources/{name}/health` - 检查单个数据源健康状态
- `GET /api/v1/data-sources/sources/health/all` - 检查所有数据源健康状态

### 数据同步API
- `POST /api/v1/data-sources/sync` - 创建同步任务
- `GET /api/v1/data-sources/sync/status` - 获取同步状态

### 数据覆盖API
- `GET /api/v1/data-sources/coverage` - 获取数据覆盖范围
- `GET /api/v1/data-sources/symbols` - 获取可用品种

### 质量监控API
- `GET /api/v1/data-sources/quality/report` - 获取质量报告
- `GET /api/v1/data-sources/performance` - 获取性能报告

### 经济数据API
- `GET /api/v1/data-sources/economic/series` - 获取经济数据序列
- `POST /api/v1/data-sources/analysis/correlation` - 相关性分析

## 使用方法

### 基本使用

```python
import streamlit as st
from src.ui.data_source_management import DataSourceManagementInterface

# 创建主界面实例
interface = DataSourceManagementInterface(api_base_url="http://localhost:8000/api/v1")

# 渲染界面
interface.render()
```

### 独立组件使用

```python
from src.ui.data_source_management import DataSourceConfigPanel

# 创建配置面板
config_panel = DataSourceConfigPanel(api_base_url="http://localhost:8000/api/v1")
config_panel.render()
```

### 自定义配置

```python
# 自定义API基础URL
interface = DataSourceManagementInterface(api_base_url="https://your-api-server.com/api/v1")

# 自定义组件
interface.config_panel = CustomConfigPanel(api_base_url)
```

## 配置选项

### 环境变量
- `API_BASE_URL`: API服务器基础URL
- `REFRESH_INTERVAL`: 自动刷新间隔（秒）
- `MAX_RETRY_ATTEMPTS`: 最大重试次数

### 界面配置
- 支持深色/浅色主题切换
- 可配置图表颜色方案
- 自定义刷新频率
- 可调整数据显示精度

## 依赖库

### 核心依赖
- `streamlit`: Web界面框架
- `pandas`: 数据处理
- `numpy`: 数值计算
- `requests`: HTTP请求

### 可视化依赖
- `plotly`: 交互式图表
- `plotly.express`: 快速图表生成
- `plotly.graph_objects`: 高级图表对象

### 统计分析依赖
- `scipy`: 科学计算
- `scikit-learn`: 机器学习（可选）
- `statsmodels`: 统计模型（可选）

## 性能优化

### 数据缓存
- 使用Streamlit缓存机制
- 实现数据预加载
- 支持增量更新

### 异步处理
- 后台任务处理
- 非阻塞UI更新
- 批量数据操作

### 内存管理
- 大数据集分页显示
- 及时释放不用的数据
- 优化图表渲染

## 错误处理

### API错误处理
- 网络连接错误重试
- API响应错误提示
- 超时处理机制

### 数据验证
- 输入参数验证
- 数据格式检查
- 异常数据过滤

### 用户体验
- 友好的错误提示
- 操作确认对话框
- 进度指示器

## 扩展开发

### 添加新功能模块

1. 创建新的组件类
2. 实现render()方法
3. 添加到主界面
4. 更新API依赖

### 自定义图表类型

1. 继承基础图表类
2. 实现数据处理逻辑
3. 定义图表样式
4. 集成到相应模块

### 添加新的分析方法

1. 在相应分析器中添加方法
2. 实现数据处理逻辑
3. 创建可视化组件
4. 更新用户界面

## 测试

### 单元测试
```bash
python -m pytest tests/ui/data_source_management/
```

### 集成测试
```bash
python -m pytest tests/integration/ui/
```

### 界面测试
```bash
streamlit run src/ui/data_source_management/main_interface.py
```

## 部署

### 本地部署
```bash
# 启动数据源管理界面
python src/ui/data_source_management/main_interface.py

# 或作为主应用的一部分
python src/ui/main_app.py
```

## 维护和更新

### 定期维护任务
- 更新依赖库版本
- 检查API兼容性
- 优化性能瓶颈
- 修复已知问题

### 版本更新流程
1. 更新版本号
2. 测试新功能
3. 更新文档
4. 发布更新

## 故障排除

### 常见问题
1. **API连接失败**: 检查网络连接和API服务状态
2. **数据加载缓慢**: 优化查询参数，使用缓存
3. **图表显示异常**: 检查数据格式和浏览器兼容性
4. **内存使用过高**: 减少数据量，优化算法

### 调试工具
- Streamlit调试模式
- 浏览器开发者工具
- 日志记录系统
- 性能分析工具

## 贡献指南

### 代码规范
- 遵循PEP 8编码规范
- 添加类型注解
- 编写文档字符串
- 保持代码简洁

### 提交流程
1. Fork项目
2. 创建功能分支
3. 编写测试用例
4. 提交Pull Request

## 许可证

本模块遵循项目主许可证。详见LICENSE文件。