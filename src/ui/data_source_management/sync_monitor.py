"""
数据同步任务管理和监控界面
"""

import streamlit as st
import requests
import pandas as pd
from datetime import datetime, timedelta, date
from typing import Dict, List, Any, Optional
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import time


class SyncTaskMonitor:
    """数据同步任务监控器"""
    
    def __init__(self, api_base_url: str):
        self.api_base_url = api_base_url
        self.headers = {"Content-Type": "application/json"}
    
    def render(self):
        """渲染同步任务监控界面"""
        st.header("🔄 数据同步任务管理")
        
        # 创建标签页
        tab1, tab2, tab3 = st.tabs(["创建同步任务", "任务监控", "同步调度"])
        
        with tab1:
            self._render_create_task()
        
        with tab2:
            self._render_task_monitor()
        
        with tab3:
            self._render_sync_scheduler()
    
    def _render_create_task(self):
        """渲染创建同步任务界面"""
        st.subheader("创建新的同步任务")
        
        # 获取可用品种
        available_symbols = self._get_available_symbols()
        
        with st.form("create_sync_task"):
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**基本配置**")
                
                # 品种选择方式
                symbol_input_method = st.radio(
                    "品种选择方式",
                    ["从列表选择", "手动输入", "批量导入"]
                )
                
                if symbol_input_method == "从列表选择":
                    if available_symbols:
                        selected_symbols = st.multiselect(
                            "选择品种",
                            available_symbols.get('all_symbols', []),
                            help="可以选择多个品种进行同步"
                        )
                    else:
                        st.error("无法获取可用品种列表")
                        selected_symbols = []
                
                elif symbol_input_method == "手动输入":
                    symbols_text = st.text_area(
                        "输入品种代码",
                        placeholder="每行一个品种代码，例如:\nAAPL\nGOOGL\nTSLA",
                        height=100
                    )
                    selected_symbols = [s.strip() for s in symbols_text.split('\n') if s.strip()]
                
                else:  # 批量导入
                    uploaded_file = st.file_uploader(
                        "上传品种列表文件",
                        type=['csv', 'txt'],
                        help="CSV文件第一列为品种代码，或TXT文件每行一个品种"
                    )
                    selected_symbols = []
                    if uploaded_file:
                        try:
                            if uploaded_file.name.endswith('.csv'):
                                df = pd.read_csv(uploaded_file)
                                selected_symbols = df.iloc[:, 0].astype(str).tolist()
                            else:
                                content = uploaded_file.read().decode('utf-8')
                                selected_symbols = [s.strip() for s in content.split('\n') if s.strip()]
                            st.success(f"已导入 {len(selected_symbols)} 个品种")
                        except Exception as e:
                            st.error(f"文件导入失败: {str(e)}")
                
                # 显示选中的品种
                if selected_symbols:
                    st.write(f"**已选择 {len(selected_symbols)} 个品种:**")
                    st.write(", ".join(selected_symbols[:10]) + ("..." if len(selected_symbols) > 10 else ""))
            
            with col2:
                st.write("**时间配置**")
                
                # 日期范围选择
                date_range_method = st.radio(
                    "日期范围选择",
                    ["预设范围", "自定义范围"]
                )
                
                if date_range_method == "预设范围":
                    preset_range = st.selectbox(
                        "选择预设范围",
                        ["最近1周", "最近1个月", "最近3个月", "最近6个月", "最近1年"]
                    )
                    
                    # 计算日期范围
                    end_date = datetime.now().date()
                    if preset_range == "最近1周":
                        start_date = end_date - timedelta(days=7)
                    elif preset_range == "最近1个月":
                        start_date = end_date - timedelta(days=30)
                    elif preset_range == "最近3个月":
                        start_date = end_date - timedelta(days=90)
                    elif preset_range == "最近6个月":
                        start_date = end_date - timedelta(days=180)
                    else:  # 最近1年
                        start_date = end_date - timedelta(days=365)
                
                else:  # 自定义范围
                    start_date = st.date_input(
                        "开始日期",
                        value=datetime.now().date() - timedelta(days=30)
                    )
                    end_date = st.date_input(
                        "结束日期",
                        value=datetime.now().date()
                    )
                
                # 数据间隔
                interval = st.selectbox(
                    "数据间隔",
                    ["1d", "1h", "4h", "1w", "1M"],
                    index=0,
                    help="选择数据的时间间隔"
                )
                
                st.write("**高级选项**")
                
                # 优先数据源
                preferred_source = st.selectbox(
                    "优先数据源（可选）",
                    ["自动选择"] + list(available_symbols.get('sources', {}).keys()) if available_symbols else ["自动选择"]
                )
                
                # 并发设置
                max_concurrent = st.slider(
                    "最大并发数",
                    min_value=1,
                    max_value=10,
                    value=3,
                    help="同时处理的品种数量"
                )
                
                # 重试设置
                max_retries = st.slider(
                    "最大重试次数",
                    min_value=0,
                    max_value=5,
                    value=2
                )
            
            # 提交按钮
            submitted = st.form_submit_button("🚀 创建同步任务", type="primary")
            
            if submitted:
                if not selected_symbols:
                    st.error("请选择要同步的品种")
                    return
                
                if start_date >= end_date:
                    st.error("开始日期必须早于结束日期")
                    return
                
                # 创建同步任务
                task_config = {
                    "symbols": selected_symbols,
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "interval": interval
                }
                
                with st.spinner("正在创建同步任务..."):
                    task_result = self._create_sync_task(task_config)
                    
                    if task_result:
                        st.success(f"同步任务创建成功！任务ID: {task_result.get('task_id')}")
                        st.json(task_result)
                        
                        # 自动跳转到监控页面
                        if st.button("查看任务状态"):
                            st.session_state['active_tab'] = 1
                            st.rerun()
                    else:
                        st.error("同步任务创建失败")
    
    def _render_task_monitor(self):
        """渲染任务监控界面"""
        st.subheader("同步任务监控")
        
        # 获取同步状态
        sync_status = self._get_sync_status()
        
        if not sync_status:
            st.error("无法获取同步状态")
            return
        
        # 显示总体统计
        col1, col2, col3, col4 = st.columns(4)
        
        last_sync_times = sync_status.get('last_sync_times', {})
        adapters_status = sync_status.get('adapters_status', {})
        
        with col1:
            st.metric("已同步品种", sync_status.get('total_synced_symbols', 0))
        
        with col2:
            healthy_adapters = sum(1 for status in adapters_status.values() 
                                 if status.get('healthy', False))
            st.metric("健康数据源", f"{healthy_adapters}/{len(adapters_status)}")
        
        with col3:
            # 计算最近同步时间
            if last_sync_times:
                latest_sync = max(last_sync_times.values())
                latest_sync_dt = datetime.fromisoformat(latest_sync)
                time_diff = datetime.now() - latest_sync_dt
                if time_diff.total_seconds() < 3600:
                    sync_status_text = f"{int(time_diff.total_seconds() / 60)}分钟前"
                elif time_diff.total_seconds() < 86400:
                    sync_status_text = f"{int(time_diff.total_seconds() / 3600)}小时前"
                else:
                    sync_status_text = f"{int(time_diff.days)}天前"
            else:
                sync_status_text = "无记录"
            
            st.metric("最近同步", sync_status_text)
        
        with col4:
            if st.button("🔄 刷新状态"):
                st.rerun()
        
        # 同步历史图表
        st.subheader("同步活动时间线")
        
        if last_sync_times:
            # 准备图表数据
            sync_data = []
            for symbol, sync_time in last_sync_times.items():
                sync_dt = datetime.fromisoformat(sync_time)
                sync_data.append({
                    'symbol': symbol,
                    'sync_time': sync_dt,
                    'hour': sync_dt.hour
                })
            
            df_sync = pd.DataFrame(sync_data)
            
            # 创建时间分布图
            fig_timeline = px.scatter(
                df_sync,
                x='sync_time',
                y='symbol',
                title='品种同步时间线',
                labels={'sync_time': '同步时间', 'symbol': '品种'}
            )
            fig_timeline.update_layout(height=400)
            st.plotly_chart(fig_timeline, use_container_width=True)
            
            # 同步活动热力图
            col1, col2 = st.columns(2)
            
            with col1:
                # 按小时统计
                hourly_counts = df_sync.groupby('hour').size().reset_index(name='count')
                fig_hourly = px.bar(
                    hourly_counts,
                    x='hour',
                    y='count',
                    title='按小时同步活动分布',
                    labels={'hour': '小时', 'count': '同步次数'}
                )
                st.plotly_chart(fig_hourly, use_container_width=True)
            
            with col2:
                # 最活跃品种
                symbol_counts = df_sync['symbol'].value_counts().head(10)
                fig_symbols = px.bar(
                    x=symbol_counts.values,
                    y=symbol_counts.index,
                    orientation='h',
                    title='最活跃品种 (Top 10)',
                    labels={'x': '同步次数', 'y': '品种'}
                )
                st.plotly_chart(fig_symbols, use_container_width=True)
        
        # 数据源状态详情
        st.subheader("数据源状态详情")
        
        if adapters_status:
            status_data = []
            for source_name, status in adapters_status.items():
                status_data.append({
                    "数据源": source_name,
                    "状态": "🟢 健康" if status.get('healthy', False) else "🔴 异常",
                    "最后更新": self._format_datetime(status.get('last_update')),
                    "错误信息": status.get('last_error', '') or '无'
                })
            
            df_status = pd.DataFrame(status_data)
            st.dataframe(df_status, use_container_width=True)
        
        # 实时监控控制
        st.subheader("实时监控")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            auto_refresh = st.checkbox("自动刷新", value=False)
            if auto_refresh:
                refresh_interval = st.slider("刷新间隔(秒)", 5, 60, 10)
        
        with col2:
            if st.button("📊 生成报告"):
                self._generate_sync_report()
        
        with col3:
            if st.button("🧹 清理历史"):
                if st.session_state.get('confirm_cleanup', False):
                    # 这里可以添加清理历史记录的逻辑
                    st.success("历史记录已清理")
                    st.session_state['confirm_cleanup'] = False
                else:
                    st.session_state['confirm_cleanup'] = True
                    st.warning("再次点击确认清理")
        
        # 自动刷新逻辑
        if auto_refresh:
            time.sleep(refresh_interval)
            st.rerun()
    
    def _render_sync_scheduler(self):
        """渲染同步调度界面"""
        st.subheader("数据同步调度管理")
        
        # 创建调度任务
        with st.expander("📅 创建定时同步任务", expanded=True):
            with st.form("schedule_form"):
                col1, col2 = st.columns(2)
                
                with col1:
                    schedule_name = st.text_input("调度任务名称")
                    symbols_for_schedule = st.text_area(
                        "品种列表",
                        placeholder="每行一个品种代码",
                        height=100
                    )
                    
                with col2:
                    schedule_type = st.selectbox(
                        "调度类型",
                        ["每日", "每周", "每月", "自定义间隔"]
                    )
                    
                    if schedule_type == "每日":
                        schedule_time = st.time_input("执行时间", value=datetime.now().time())
                    elif schedule_type == "每周":
                        weekday = st.selectbox("星期", 
                                             ["周一", "周二", "周三", "周四", "周五", "周六", "周日"])
                        schedule_time = st.time_input("执行时间", value=datetime.now().time())
                    elif schedule_type == "每月":
                        day_of_month = st.number_input("每月第几天", min_value=1, max_value=28, value=1)
                        schedule_time = st.time_input("执行时间", value=datetime.now().time())
                    else:  # 自定义间隔
                        interval_minutes = st.number_input("间隔(分钟)", min_value=5, value=60)
                
                # 高级选项
                st.write("**高级选项**")
                col1, col2 = st.columns(2)
                
                with col1:
                    days_back = st.number_input("回溯天数", min_value=1, max_value=365, value=1)
                    enabled = st.checkbox("启用调度", value=True)
                
                with col2:
                    retry_on_failure = st.checkbox("失败时重试", value=True)
                    if retry_on_failure:
                        max_retries = st.number_input("最大重试次数", min_value=1, max_value=5, value=3)
                
                if st.form_submit_button("创建调度任务", type="primary"):
                    if not schedule_name or not symbols_for_schedule:
                        st.error("请填写任务名称和品种列表")
                    else:
                        symbols_list = [s.strip() for s in symbols_for_schedule.split('\n') if s.strip()]
                        
                        # 这里可以调用API创建调度任务
                        st.success(f"调度任务 '{schedule_name}' 创建成功，包含 {len(symbols_list)} 个品种")
        
        # 现有调度任务列表
        st.subheader("现有调度任务")
        
        # 模拟调度任务数据
        schedule_tasks = [
            {
                "名称": "每日美股同步",
                "品种数量": 50,
                "调度类型": "每日",
                "执行时间": "09:00",
                "状态": "🟢 启用",
                "下次执行": "2024-01-16 09:00:00",
                "最后执行": "2024-01-15 09:00:00",
                "执行结果": "✅ 成功"
            },
            {
                "名称": "周末加密货币同步",
                "品种数量": 20,
                "调度类型": "每周",
                "执行时间": "周日 10:00",
                "状态": "🟢 启用",
                "下次执行": "2024-01-21 10:00:00",
                "最后执行": "2024-01-14 10:00:00",
                "执行结果": "✅ 成功"
            }
        ]
        
        if schedule_tasks:
            df_schedules = pd.DataFrame(schedule_tasks)
            
            # 添加操作列
            for i, task in enumerate(schedule_tasks):
                with st.expander(f"📋 {task['名称']}"):
                    col1, col2, col3 = st.columns([2, 1, 1])
                    
                    with col1:
                        st.write(f"**品种数量:** {task['品种数量']}")
                        st.write(f"**调度类型:** {task['调度类型']}")
                        st.write(f"**执行时间:** {task['执行时间']}")
                        st.write(f"**下次执行:** {task['下次执行']}")
                        st.write(f"**最后执行:** {task['最后执行']}")
                        st.write(f"**执行结果:** {task['执行结果']}")
                    
                    with col2:
                        if st.button(f"▶️ 立即执行", key=f"run_{i}"):
                            st.success(f"任务 '{task['名称']}' 已开始执行")
                        
                        if task['状态'] == "🟢 启用":
                            if st.button(f"⏸️ 暂停", key=f"pause_{i}"):
                                st.info(f"任务 '{task['名称']}' 已暂停")
                        else:
                            if st.button(f"▶️ 启用", key=f"enable_{i}"):
                                st.success(f"任务 '{task['名称']}' 已启用")
                    
                    with col3:
                        if st.button(f"✏️ 编辑", key=f"edit_{i}"):
                            st.info("编辑功能开发中...")
                        
                        if st.button(f"🗑️ 删除", key=f"delete_{i}"):
                            if st.session_state.get(f'confirm_delete_schedule_{i}', False):
                                st.success(f"任务 '{task['名称']}' 已删除")
                                st.session_state[f'confirm_delete_schedule_{i}'] = False
                            else:
                                st.session_state[f'confirm_delete_schedule_{i}'] = True
                                st.warning("再次点击确认删除")
        else:
            st.info("暂无调度任务")
    
    def _get_available_symbols(self) -> Optional[Dict[str, Any]]:
        """获取可用品种列表"""
        try:
            response = requests.get(
                f"{self.api_base_url}/data-sources/symbols",
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            st.error(f"获取可用品种失败: {str(e)}")
            return None
    
    def _create_sync_task(self, task_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """创建同步任务"""
        try:
            response = requests.post(
                f"{self.api_base_url}/data-sources/sync",
                headers=self.headers,
                json=task_config
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            st.error(f"创建同步任务失败: {str(e)}")
            return None
    
    def _get_sync_status(self) -> Optional[Dict[str, Any]]:
        """获取同步状态"""
        try:
            response = requests.get(
                f"{self.api_base_url}/data-sources/sync/status",
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            st.error(f"获取同步状态失败: {str(e)}")
            return None
    
    def _format_datetime(self, dt_str: Optional[str]) -> str:
        """格式化日期时间"""
        if not dt_str:
            return "未知"
        
        try:
            dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return dt_str
    
    def _generate_sync_report(self):
        """生成同步报告"""
        with st.spinner("正在生成同步报告..."):
            # 模拟报告生成
            time.sleep(2)
            
            report_data = {
                "报告生成时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "总同步品种数": 150,
                "成功同步": 145,
                "失败同步": 5,
                "成功率": "96.7%",
                "平均同步时间": "2.3秒",
                "数据源使用情况": {
                    "Yahoo Finance": "60%",
                    "Alpha Vantage": "25%",
                    "FRED": "10%",
                    "其他": "5%"
                }
            }
            
            st.success("同步报告生成完成！")
            st.json(report_data)
            
            # 提供下载选项
            report_json = pd.Series(report_data).to_json(indent=2)
            st.download_button(
                label="📥 下载报告",
                data=report_json,
                file_name=f"sync_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )