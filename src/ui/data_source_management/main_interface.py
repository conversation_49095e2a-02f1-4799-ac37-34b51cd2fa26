"""
数据源管理主界面
"""

import streamlit as st
import pandas as pd
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from .source_config import DataSourceConfigPanel
from .sync_monitor import SyncTaskMonitor
from .coverage_viewer import DataCoverageViewer
from .quality_monitor import DataQualityMonitor
from .economic_browser import EconomicDataBrowser
from .correlation_analyzer import CorrelationAnalyzer


class DataSourceManagementInterface:
    """数据源管理主界面"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000/api/v1"):
        self.api_base_url = api_base_url
        self.headers = {"Content-Type": "application/json"}
        
        # 初始化子组件
        self.config_panel = DataSourceConfigPanel(api_base_url)
        self.sync_monitor = SyncTaskMonitor(api_base_url)
        self.coverage_viewer = DataCoverageViewer(api_base_url)
        self.quality_monitor = DataQualityMonitor(api_base_url)
        self.economic_browser = EconomicDataBrowser(api_base_url)
        self.correlation_analyzer = CorrelationAnalyzer(api_base_url)
    
    def render(self):
        """渲染主界面"""
        st.title("📊 数据源管理中心")
        
        # 侧边栏导航
        with st.sidebar:
            st.header("功能导航")
            selected_tab = st.selectbox(
                "选择功能模块",
                [
                    "数据源概览",
                    "数据源配置",
                    "同步任务管理",
                    "数据覆盖范围",
                    "数据质量监控",
                    "经济数据浏览",
                    "相关性分析"
                ]
            )
        
        # 根据选择的标签页渲染对应内容
        if selected_tab == "数据源概览":
            self._render_overview()
        elif selected_tab == "数据源配置":
            self.config_panel.render()
        elif selected_tab == "同步任务管理":
            self.sync_monitor.render()
        elif selected_tab == "数据覆盖范围":
            self.coverage_viewer.render()
        elif selected_tab == "数据质量监控":
            self.quality_monitor.render()
        elif selected_tab == "经济数据浏览":
            self.economic_browser.render()
        elif selected_tab == "相关性分析":
            self.correlation_analyzer.render()
    
    def _render_overview(self):
        """渲染数据源概览页面"""
        st.header("数据源概览")
        
        # 获取数据源状态
        sources_data = self._get_data_sources()
        if not sources_data:
            st.error("无法获取数据源信息")
            return
        
        # 显示总体统计
        col1, col2, col3, col4 = st.columns(4)
        
        total_sources = len(sources_data)
        enabled_sources = sum(1 for s in sources_data if s.get('enabled', False))
        healthy_sources = sum(1 for s in sources_data if s.get('healthy', False))
        
        with col1:
            st.metric("总数据源", total_sources)
        with col2:
            st.metric("已启用", enabled_sources)
        with col3:
            st.metric("健康状态", healthy_sources)
        with col4:
            health_rate = (healthy_sources / total_sources * 100) if total_sources > 0 else 0
            st.metric("健康率", f"{health_rate:.1f}%")
        
        # 数据源状态表格
        st.subheader("数据源状态")
        
        # 准备表格数据
        table_data = []
        for source in sources_data:
            perf_stats = source.get('performance_stats', {})
            table_data.append({
                "名称": source.get('name', ''),
                "市场": source.get('market', ''),
                "交易所": source.get('exchange', ''),
                "状态": "🟢 启用" if source.get('enabled') else "🔴 禁用",
                "健康": "✅ 健康" if source.get('healthy') else "❌ 异常",
                "优先级": source.get('priority', 0),
                "请求数/小时": perf_stats.get('requests_last_hour', 0),
                "平均响应时间": f"{perf_stats.get('avg_response_time', 0):.2f}ms",
                "最后检查": self._format_datetime(source.get('last_health_check'))
            })
        
        if table_data:
            df = pd.DataFrame(table_data)
            st.dataframe(df, use_container_width=True)
        
        # 性能图表
        st.subheader("性能监控")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 请求量图表
            if table_data:
                fig_requests = px.bar(
                    df,
                    x="名称",
                    y="请求数/小时",
                    title="各数据源请求量（过去1小时）",
                    color="健康",
                    color_discrete_map={"✅ 健康": "green", "❌ 异常": "red"}
                )
                fig_requests.update_layout(height=400)
                st.plotly_chart(fig_requests, use_container_width=True)
        
        with col2:
            # 响应时间图表
            if table_data:
                # 提取数值部分
                response_times = []
                source_names = []
                for item in table_data:
                    try:
                        time_str = item["平均响应时间"].replace("ms", "")
                        response_times.append(float(time_str))
                        source_names.append(item["名称"])
                    except:
                        continue
                
                if response_times:
                    fig_response = px.bar(
                        x=source_names,
                        y=response_times,
                        title="平均响应时间",
                        labels={"x": "数据源", "y": "响应时间 (ms)"}
                    )
                    fig_response.update_layout(height=400)
                    st.plotly_chart(fig_response, use_container_width=True)
        
        # 实时健康检查
        st.subheader("实时操作")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🔄 刷新状态", type="primary"):
                st.rerun()
        
        with col2:
            if st.button("🏥 健康检查"):
                with st.spinner("正在检查所有数据源健康状态..."):
                    health_results = self._check_all_health()
                    if health_results:
                        st.success("健康检查完成")
                        st.json(health_results)
                    else:
                        st.error("健康检查失败")
        
        with col3:
            if st.button("⚙️ 重新加载配置"):
                with st.spinner("正在重新加载配置..."):
                    if self._reload_config():
                        st.success("配置重新加载成功")
                        st.rerun()
                    else:
                        st.error("配置重新加载失败")
        
        # 最近活动日志
        st.subheader("最近活动")
        self._render_recent_activities()
    
    def _get_data_sources(self) -> List[Dict[str, Any]]:
        """获取数据源列表"""
        try:
            response = requests.get(
                f"{self.api_base_url}/data-sources/sources",
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            else:
                st.error(f"获取数据源失败: {response.status_code}")
                return []
        except Exception as e:
            st.error(f"请求失败: {str(e)}")
            return []
    
    def _check_all_health(self) -> Optional[Dict[str, Any]]:
        """检查所有数据源健康状态"""
        try:
            response = requests.get(
                f"{self.api_base_url}/data-sources/sources/health/all",
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            else:
                st.error(f"健康检查失败: {response.status_code}")
                return None
        except Exception as e:
            st.error(f"健康检查请求失败: {str(e)}")
            return None
    
    def _reload_config(self) -> bool:
        """重新加载配置"""
        try:
            response = requests.post(
                f"{self.api_base_url}/data-sources/reload",
                headers=self.headers
            )
            return response.status_code == 200
        except Exception as e:
            st.error(f"重新加载配置失败: {str(e)}")
            return False
    
    def _format_datetime(self, dt_str: Optional[str]) -> str:
        """格式化日期时间"""
        if not dt_str:
            return "未知"
        
        try:
            dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return dt_str
    
    def _render_recent_activities(self):
        """渲染最近活动日志"""
        # 这里可以显示最近的同步任务、配置更改等活动
        # 暂时显示模拟数据
        
        activities = [
            {
                "时间": "2024-01-15 14:30:00",
                "类型": "同步任务",
                "描述": "完成AAPL等10个品种的数据同步",
                "状态": "成功"
            },
            {
                "时间": "2024-01-15 14:25:00", 
                "类型": "健康检查",
                "描述": "Yahoo Finance数据源健康检查",
                "状态": "成功"
            },
            {
                "时间": "2024-01-15 14:20:00",
                "类型": "配置更新",
                "描述": "更新Alpha Vantage API密钥",
                "状态": "成功"
            }
        ]
        
        df_activities = pd.DataFrame(activities)
        st.dataframe(df_activities, use_container_width=True)
    
    def get_performance_data(self) -> Optional[Dict[str, Any]]:
        """获取性能数据"""
        try:
            response = requests.get(
                f"{self.api_base_url}/data-sources/performance",
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            st.error(f"获取性能数据失败: {str(e)}")
            return None
    
    def get_sync_status(self) -> Optional[Dict[str, Any]]:
        """获取同步状态"""
        try:
            response = requests.get(
                f"{self.api_base_url}/data-sources/sync/status",
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            st.error(f"获取同步状态失败: {str(e)}")
            return None


def main():
    """主函数，用于独立运行此界面"""
    st.set_page_config(
        page_title="数据源管理",
        page_icon="📊",
        layout="wide"
    )
    
    interface = DataSourceManagementInterface()
    interface.render()


if __name__ == "__main__":
    main()