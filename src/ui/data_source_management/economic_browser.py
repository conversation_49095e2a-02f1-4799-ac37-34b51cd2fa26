"""
经济数据浏览和搜索界面
"""

import streamlit as st
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np


class EconomicDataBrowser:
    """经济数据浏览器"""
    
    def __init__(self, api_base_url: str):
        self.api_base_url = api_base_url
        self.headers = {"Content-Type": "application/json"}
        
        # 经济数据分类
        self.categories = {
            "宏观经济": ["GDP", "通胀", "就业", "消费", "投资"],
            "货币政策": ["利率", "货币供应量", "央行政策", "汇率"],
            "财政政策": ["政府支出", "税收", "财政赤字", "债务"],
            "国际贸易": ["进出口", "贸易平衡", "关税", "汇率"],
            "金融市场": ["股市指数", "债券收益率", "商品价格", "波动率"],
            "行业数据": ["制造业", "服务业", "房地产", "能源", "科技"]
        }
        
        # 热门经济指标
        self.popular_indicators = [
            {"id": "GDP", "name": "国内生产总值", "category": "宏观经济", "frequency": "季度"},
            {"id": "UNRATE", "name": "失业率", "category": "就业", "frequency": "月度"},
            {"id": "CPIAUCSL", "name": "消费者价格指数", "category": "通胀", "frequency": "月度"},
            {"id": "FEDFUNDS", "name": "联邦基金利率", "category": "货币政策", "frequency": "月度"},
            {"id": "PAYEMS", "name": "非农就业人数", "category": "就业", "frequency": "月度"},
            {"id": "HOUST", "name": "新屋开工数", "category": "房地产", "frequency": "月度"},
            {"id": "INDPRO", "name": "工业生产指数", "category": "制造业", "frequency": "月度"},
            {"id": "RETAILSALES", "name": "零售销售", "category": "消费", "frequency": "月度"}
        ]
    
    def render(self):
        """渲染经济数据浏览界面"""
        st.header("📊 经济数据浏览器")
        
        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs(["数据搜索", "热门指标", "数据对比", "自定义分析"])
        
        with tab1:
            self._render_data_search()
        
        with tab2:
            self._render_popular_indicators()
        
        with tab3:
            self._render_data_comparison()
        
        with tab4:
            self._render_custom_analysis()
    
    def _render_data_search(self):
        """渲染数据搜索界面"""
        st.subheader("经济数据搜索")
        
        # 搜索界面
        col1, col2 = st.columns([3, 1])
        
        with col1:
            search_query = st.text_input(
                "搜索经济指标",
                placeholder="输入关键词，如：GDP、失业率、通胀等",
                help="支持中英文搜索，可以搜索指标名称、描述或分类"
            )
        
        with col2:
            if st.button("🔍 搜索", type="primary"):
                if search_query:
                    self._perform_search(search_query)
        
        # 高级搜索选项
        with st.expander("🔧 高级搜索选项"):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                selected_category = st.selectbox(
                    "数据分类",
                    ["全部"] + list(self.categories.keys())
                )
            
            with col2:
                frequency_filter = st.selectbox(
                    "数据频率",
                    ["全部", "日度", "周度", "月度", "季度", "年度"]
                )
            
            with col3:
                data_source = st.selectbox(
                    "数据源",
                    ["全部", "FRED", "BEA", "BLS", "Census"]
                )
            
            if st.button("应用筛选"):
                self._apply_filters(selected_category, frequency_filter, data_source)
        
        # 分类浏览
        st.subheader("按分类浏览")
        
        # 创建分类标签
        category_tabs = st.tabs(list(self.categories.keys()))
        
        for i, (category, subcategories) in enumerate(self.categories.items()):
            with category_tabs[i]:
                st.write(f"**{category}相关指标**")
                
                # 显示子分类
                cols = st.columns(len(subcategories))
                for j, subcategory in enumerate(subcategories):
                    with cols[j]:
                        if st.button(f"📈 {subcategory}", key=f"{category}_{subcategory}"):
                            self._show_subcategory_indicators(category, subcategory)
        
        # 最近搜索历史
        st.subheader("最近搜索")
        self._render_search_history()
    
    def _render_popular_indicators(self):
        """渲染热门指标界面"""
        st.subheader("热门经济指标")
        
        # 热门指标卡片
        cols = st.columns(2)
        
        for i, indicator in enumerate(self.popular_indicators):
            with cols[i % 2]:
                with st.container():
                    st.markdown(f"""
                    <div style="border: 1px solid #ddd; border-radius: 5px; padding: 10px; margin: 5px 0;">
                        <h4>{indicator['name']}</h4>
                        <p><strong>ID:</strong> {indicator['id']}</p>
                        <p><strong>分类:</strong> {indicator['category']}</p>
                        <p><strong>频率:</strong> {indicator['frequency']}</p>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button(f"📊 查看数据", key=f"view_{indicator['id']}"):
                            self._show_indicator_data(indicator['id'])
                    
                    with col2:
                        if st.button(f"📈 添加到对比", key=f"add_{indicator['id']}"):
                            self._add_to_comparison(indicator['id'])
        
        # 指标排行榜
        st.subheader("指标热度排行")
        
        # 模拟热度数据
        popularity_data = []
        for indicator in self.popular_indicators:
            popularity_data.append({
                "指标名称": indicator['name'],
                "指标ID": indicator['id'],
                "分类": indicator['category'],
                "查看次数": np.random.randint(100, 1000),
                "收藏次数": np.random.randint(10, 100),
                "热度评分": np.random.uniform(7.0, 9.5)
            })
        
        df_popularity = pd.DataFrame(popularity_data)
        df_popularity = df_popularity.sort_values('热度评分', ascending=False)
        
        st.dataframe(df_popularity, use_container_width=True)
        
        # 热度趋势图
        st.subheader("热度趋势")
        
        # 模拟趋势数据
        dates = pd.date_range(start='2023-01-01', end='2024-01-15', freq='D')
        trend_data = []
        
        top_indicators = df_popularity.head(3)['指标名称'].tolist()
        
        for indicator in top_indicators:
            base_popularity = np.random.uniform(50, 100)
            noise = np.random.normal(0, 5, len(dates))
            trend = np.sin(np.linspace(0, 4*np.pi, len(dates))) * 10  # 周期性变化
            popularity = base_popularity + trend + noise
            
            for i, date in enumerate(dates):
                trend_data.append({
                    'date': date,
                    'indicator': indicator,
                    'popularity': max(0, popularity[i])
                })
        
        df_trend = pd.DataFrame(trend_data)
        
        fig_trend = px.line(
            df_trend,
            x='date',
            y='popularity',
            color='indicator',
            title='热门指标关注度趋势',
            labels={'popularity': '关注度', 'date': '日期', 'indicator': '指标'}
        )
        
        st.plotly_chart(fig_trend, use_container_width=True)
    
    def _render_data_comparison(self):
        """渲染数据对比界面"""
        st.subheader("经济数据对比分析")
        
        # 指标选择
        st.write("**选择要对比的指标**")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 主要指标
            primary_indicators = st.multiselect(
                "主要指标",
                [f"{ind['name']} ({ind['id']})" for ind in self.popular_indicators],
                help="选择要对比的主要经济指标"
            )
        
        with col2:
            # 自定义指标
            custom_indicators = st.text_area(
                "自定义指标ID",
                placeholder="每行输入一个FRED序列ID，例如:\nGDP\nUNRATE\nCPIAUCSL",
                height=100
            )
        
        # 时间范围设置
        st.write("**时间范围设置**")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            start_date = st.date_input(
                "开始日期",
                value=datetime.now().date() - timedelta(days=365*2)
            )
        
        with col2:
            end_date = st.date_input(
                "结束日期",
                value=datetime.now().date()
            )
        
        with col3:
            comparison_type = st.selectbox(
                "对比方式",
                ["原始数值", "同比增长率", "环比增长率", "标准化数值"]
            )
        
        # 生成对比分析
        if st.button("📊 生成对比分析", type="primary"):
            # 处理选中的指标
            selected_indicators = []
            
            # 处理主要指标
            for indicator_str in primary_indicators:
                indicator_id = indicator_str.split('(')[-1].split(')')[0]
                selected_indicators.append(indicator_id)
            
            # 处理自定义指标
            if custom_indicators:
                custom_list = [s.strip() for s in custom_indicators.split('\n') if s.strip()]
                selected_indicators.extend(custom_list)
            
            if not selected_indicators:
                st.warning("请至少选择一个指标进行对比")
                return
            
            # 生成对比分析
            self._generate_comparison_analysis(selected_indicators, start_date, end_date, comparison_type)
    
    def _render_custom_analysis(self):
        """渲染自定义分析界面"""
        st.subheader("自定义经济数据分析")
        
        # 分析类型选择
        analysis_type = st.selectbox(
            "选择分析类型",
            ["相关性分析", "趋势分析", "周期性分析", "预测分析", "异常检测"]
        )
        
        if analysis_type == "相关性分析":
            self._render_correlation_analysis()
        elif analysis_type == "趋势分析":
            self._render_trend_analysis()
        elif analysis_type == "周期性分析":
            self._render_cyclical_analysis()
        elif analysis_type == "预测分析":
            self._render_forecast_analysis()
        else:  # 异常检测
            self._render_anomaly_detection()
    
    def _render_correlation_analysis(self):
        """渲染相关性分析"""
        st.subheader("经济指标相关性分析")
        
        # 指标选择
        selected_indicators = st.multiselect(
            "选择指标进行相关性分析",
            [f"{ind['name']} ({ind['id']})" for ind in self.popular_indicators],
            default=[f"{ind['name']} ({ind['id']})" for ind in self.popular_indicators[:4]]
        )
        
        if len(selected_indicators) < 2:
            st.warning("请至少选择两个指标进行相关性分析")
            return
        
        # 时间范围
        col1, col2 = st.columns(2)
        
        with col1:
            start_date = st.date_input("开始日期", value=datetime.now().date() - timedelta(days=365*3))
        
        with col2:
            end_date = st.date_input("结束日期", value=datetime.now().date())
        
        if st.button("计算相关性"):
            # 模拟相关性矩阵
            indicator_names = [ind.split('(')[0].strip() for ind in selected_indicators]
            n = len(indicator_names)
            
            # 生成随机相关性矩阵
            correlation_matrix = np.random.rand(n, n)
            correlation_matrix = (correlation_matrix + correlation_matrix.T) / 2  # 对称化
            np.fill_diagonal(correlation_matrix, 1)  # 对角线为1
            correlation_matrix = correlation_matrix * 2 - 1  # 调整到-1到1范围
            
            df_corr = pd.DataFrame(correlation_matrix, 
                                 index=indicator_names, 
                                 columns=indicator_names)
            
            # 相关性热力图
            fig_heatmap = px.imshow(
                df_corr,
                title="经济指标相关性矩阵",
                color_continuous_scale="RdBu",
                aspect="auto"
            )
            
            st.plotly_chart(fig_heatmap, use_container_width=True)
            
            # 相关性表格
            st.subheader("相关性系数表")
            st.dataframe(df_corr.round(3), use_container_width=True)
            
            # 强相关性分析
            st.subheader("强相关性分析")
            
            strong_correlations = []
            for i in range(n):
                for j in range(i+1, n):
                    corr_value = correlation_matrix[i, j]
                    if abs(corr_value) > 0.7:
                        strong_correlations.append({
                            "指标1": indicator_names[i],
                            "指标2": indicator_names[j],
                            "相关系数": f"{corr_value:.3f}",
                            "相关性": "强正相关" if corr_value > 0.7 else "强负相关"
                        })
            
            if strong_correlations:
                df_strong = pd.DataFrame(strong_correlations)
                st.dataframe(df_strong, use_container_width=True)
            else:
                st.info("未发现强相关性（|r| > 0.7）的指标对")
    
    def _render_trend_analysis(self):
        """渲染趋势分析"""
        st.subheader("经济指标趋势分析")
        
        # 指标选择
        selected_indicator = st.selectbox(
            "选择要分析的指标",
            [f"{ind['name']} ({ind['id']})" for ind in self.popular_indicators]
        )
        
        # 分析参数
        col1, col2, col3 = st.columns(3)
        
        with col1:
            trend_method = st.selectbox(
                "趋势分析方法",
                ["移动平均", "线性回归", "多项式拟合", "季节性分解"]
            )
        
        with col2:
            window_size = st.slider("窗口大小", 3, 24, 12)
        
        with col3:
            forecast_periods = st.slider("预测期数", 1, 12, 6)
        
        if st.button("开始趋势分析"):
            # 模拟时间序列数据
            dates = pd.date_range(start='2020-01-01', end='2024-01-15', freq='M')
            
            # 生成模拟数据（包含趋势、季节性和噪声）
            trend = np.linspace(100, 120, len(dates))
            seasonal = 10 * np.sin(2 * np.pi * np.arange(len(dates)) / 12)
            noise = np.random.normal(0, 2, len(dates))
            values = trend + seasonal + noise
            
            df_data = pd.DataFrame({
                'date': dates,
                'value': values
            })
            
            # 趋势分析
            if trend_method == "移动平均":
                df_data['trend'] = df_data['value'].rolling(window=window_size).mean()
            elif trend_method == "线性回归":
                from sklearn.linear_model import LinearRegression
                X = np.arange(len(df_data)).reshape(-1, 1)
                model = LinearRegression().fit(X, df_data['value'])
                df_data['trend'] = model.predict(X)
            
            # 绘制趋势图
            fig_trend = go.Figure()
            
            fig_trend.add_trace(go.Scatter(
                x=df_data['date'],
                y=df_data['value'],
                mode='lines',
                name='原始数据',
                line=dict(color='blue', width=1)
            ))
            
            if 'trend' in df_data.columns:
                fig_trend.add_trace(go.Scatter(
                    x=df_data['date'],
                    y=df_data['trend'],
                    mode='lines',
                    name='趋势线',
                    line=dict(color='red', width=2)
                ))
            
            fig_trend.update_layout(
                title=f"{selected_indicator.split('(')[0]} 趋势分析",
                xaxis_title="日期",
                yaxis_title="数值",
                height=500
            )
            
            st.plotly_chart(fig_trend, use_container_width=True)
            
            # 趋势统计
            st.subheader("趋势统计")
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("平均值", f"{df_data['value'].mean():.2f}")
            
            with col2:
                st.metric("标准差", f"{df_data['value'].std():.2f}")
            
            with col3:
                # 计算趋势斜率
                if 'trend' in df_data.columns:
                    slope = (df_data['trend'].iloc[-1] - df_data['trend'].iloc[0]) / len(df_data)
                    st.metric("趋势斜率", f"{slope:.4f}")
            
            with col4:
                # 计算变异系数
                cv = df_data['value'].std() / df_data['value'].mean() * 100
                st.metric("变异系数", f"{cv:.2f}%")
    
    def _render_cyclical_analysis(self):
        """渲染周期性分析"""
        st.subheader("经济指标周期性分析")
        
        selected_indicator = st.selectbox(
            "选择要分析的指标",
            [f"{ind['name']} ({ind['id']})" for ind in self.popular_indicators]
        )
        
        if st.button("开始周期性分析"):
            # 模拟周期性数据
            dates = pd.date_range(start='2015-01-01', end='2024-01-15', freq='M')
            
            # 生成包含多个周期的数据
            t = np.arange(len(dates))
            trend = 100 + 0.1 * t
            business_cycle = 15 * np.sin(2 * np.pi * t / 48)  # 4年商业周期
            seasonal_cycle = 8 * np.sin(2 * np.pi * t / 12)   # 年度季节周期
            noise = np.random.normal(0, 3, len(dates))
            
            values = trend + business_cycle + seasonal_cycle + noise
            
            df_cycle = pd.DataFrame({
                'date': dates,
                'value': values,
                'trend': trend,
                'business_cycle': business_cycle,
                'seasonal_cycle': seasonal_cycle
            })
            
            # 周期分解图
            fig_decomp = make_subplots(
                rows=4, cols=1,
                subplot_titles=['原始数据', '趋势', '商业周期', '季节周期'],
                vertical_spacing=0.08
            )
            
            fig_decomp.add_trace(
                go.Scatter(x=df_cycle['date'], y=df_cycle['value'], name='原始数据'),
                row=1, col=1
            )
            
            fig_decomp.add_trace(
                go.Scatter(x=df_cycle['date'], y=df_cycle['trend'], name='趋势', line=dict(color='red')),
                row=2, col=1
            )
            
            fig_decomp.add_trace(
                go.Scatter(x=df_cycle['date'], y=df_cycle['business_cycle'], name='商业周期', line=dict(color='green')),
                row=3, col=1
            )
            
            fig_decomp.add_trace(
                go.Scatter(x=df_cycle['date'], y=df_cycle['seasonal_cycle'], name='季节周期', line=dict(color='orange')),
                row=4, col=1
            )
            
            fig_decomp.update_layout(height=800, title_text="周期性分解")
            st.plotly_chart(fig_decomp, use_container_width=True)
            
            # 周期统计
            st.subheader("周期性统计")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.write("**商业周期特征**")
                st.write(f"周期长度: ~4年")
                st.write(f"振幅: {np.std(df_cycle['business_cycle']):.2f}")
                st.write(f"当前阶段: {'扩张期' if df_cycle['business_cycle'].iloc[-1] > 0 else '收缩期'}")
            
            with col2:
                st.write("**季节性特征**")
                st.write(f"周期长度: 12个月")
                st.write(f"振幅: {np.std(df_cycle['seasonal_cycle']):.2f}")
                st.write(f"季节性强度: {np.std(df_cycle['seasonal_cycle'])/np.std(df_cycle['value'])*100:.1f}%")
            
            with col3:
                st.write("**整体特征**")
                st.write(f"趋势方向: {'上升' if df_cycle['trend'].iloc[-1] > df_cycle['trend'].iloc[0] else '下降'}")
                st.write(f"波动性: {np.std(df_cycle['value']):.2f}")
                st.write(f"信噪比: {np.std(df_cycle['trend'])/np.std(noise):.2f}")
    
    def _render_forecast_analysis(self):
        """渲染预测分析"""
        st.subheader("经济指标预测分析")
        
        st.info("预测分析功能正在开发中，敬请期待！")
        
        # 预测参数设置
        col1, col2 = st.columns(2)
        
        with col1:
            selected_indicator = st.selectbox(
                "选择要预测的指标",
                [f"{ind['name']} ({ind['id']})" for ind in self.popular_indicators]
            )
        
        with col2:
            forecast_method = st.selectbox(
                "预测方法",
                ["ARIMA", "指数平滑", "神经网络", "随机森林"]
            )
        
        forecast_horizon = st.slider("预测期数（月）", 1, 24, 12)
        
        if st.button("开始预测"):
            st.warning("预测功能正在开发中...")
    
    def _render_anomaly_detection(self):
        """渲染异常检测"""
        st.subheader("经济数据异常检测")
        
        st.info("异常检测功能正在开发中，敬请期待！")
    
    def _perform_search(self, query: str):
        """执行搜索"""
        st.subheader(f"搜索结果: '{query}'")
        
        # 模拟搜索结果
        search_results = []
        
        for indicator in self.popular_indicators:
            if (query.lower() in indicator['name'].lower() or 
                query.lower() in indicator['id'].lower() or
                query.lower() in indicator['category'].lower()):
                search_results.append(indicator)
        
        if search_results:
            for result in search_results:
                with st.expander(f"📊 {result['name']} ({result['id']})"):
                    col1, col2 = st.columns([3, 1])
                    
                    with col1:
                        st.write(f"**分类:** {result['category']}")
                        st.write(f"**频率:** {result['frequency']}")
                        st.write(f"**描述:** 这是一个重要的{result['category']}指标，用于衡量...")
                    
                    with col2:
                        if st.button(f"查看数据", key=f"search_{result['id']}"):
                            self._show_indicator_data(result['id'])
        else:
            st.warning(f"未找到与 '{query}' 相关的指标")
            
            # 搜索建议
            st.write("**搜索建议:**")
            st.write("• 尝试使用更通用的关键词")
            st.write("• 检查拼写是否正确")
            st.write("• 使用英文缩写，如GDP、CPI等")
    
    def _show_subcategory_indicators(self, category: str, subcategory: str):
        """显示子分类指标"""
        st.subheader(f"{category} - {subcategory}")
        
        # 模拟子分类指标
        subcategory_indicators = [ind for ind in self.popular_indicators 
                                if subcategory.lower() in ind['category'].lower()]
        
        if subcategory_indicators:
            for indicator in subcategory_indicators:
                with st.expander(f"📈 {indicator['name']}"):
                    st.write(f"ID: {indicator['id']}")
                    st.write(f"频率: {indicator['frequency']}")
                    
                    if st.button(f"查看数据", key=f"subcat_{indicator['id']}"):
                        self._show_indicator_data(indicator['id'])
        else:
            st.info(f"暂无 {subcategory} 相关指标")
    
    def _show_indicator_data(self, indicator_id: str):
        """显示指标数据"""
        st.subheader(f"指标数据: {indicator_id}")
        
        # 模拟数据获取
        with st.spinner("正在获取数据..."):
            # 生成模拟数据
            dates = pd.date_range(start='2020-01-01', end='2024-01-15', freq='M')
            values = np.random.uniform(50, 150, len(dates))
            
            # 添加趋势
            trend = np.linspace(0, 20, len(dates))
            values += trend
            
            # 添加季节性
            seasonal = 10 * np.sin(2 * np.pi * np.arange(len(dates)) / 12)
            values += seasonal
            
            df_data = pd.DataFrame({
                'date': dates,
                'value': values
            })
            
            # 绘制数据图表
            fig = px.line(
                df_data,
                x='date',
                y='value',
                title=f'{indicator_id} 历史数据',
                labels={'value': '数值', 'date': '日期'}
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # 数据统计
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("最新值", f"{values[-1]:.2f}")
            
            with col2:
                st.metric("平均值", f"{np.mean(values):.2f}")
            
            with col3:
                change = values[-1] - values[-2]
                st.metric("月度变化", f"{change:.2f}", delta=f"{change:.2f}")
            
            with col4:
                yoy_change = (values[-1] - values[-13]) / values[-13] * 100
                st.metric("同比变化", f"{yoy_change:.1f}%")
            
            # 数据表格
            st.subheader("数据详情")
            st.dataframe(df_data.tail(12), use_container_width=True)
    
    def _add_to_comparison(self, indicator_id: str):
        """添加到对比列表"""
        if 'comparison_list' not in st.session_state:
            st.session_state.comparison_list = []
        
        if indicator_id not in st.session_state.comparison_list:
            st.session_state.comparison_list.append(indicator_id)
            st.success(f"已添加 {indicator_id} 到对比列表")
        else:
            st.warning(f"{indicator_id} 已在对比列表中")
    
    def _generate_comparison_analysis(self, indicators: List[str], start_date, end_date, comparison_type: str):
        """生成对比分析"""
        st.subheader("对比分析结果")
        
        # 模拟多个指标的数据
        dates = pd.date_range(start=start_date, end=end_date, freq='M')
        
        comparison_data = []
        colors = px.colors.qualitative.Set1
        
        fig = go.Figure()
        
        for i, indicator in enumerate(indicators):
            # 生成模拟数据
            base_value = np.random.uniform(50, 150)
            trend = np.linspace(0, np.random.uniform(-20, 20), len(dates))
            seasonal = np.random.uniform(5, 15) * np.sin(2 * np.pi * np.arange(len(dates)) / 12)
            noise = np.random.normal(0, 2, len(dates))
            values = base_value + trend + seasonal + noise
            
            # 根据对比类型处理数据
            if comparison_type == "同比增长率":
                if len(values) > 12:
                    values = [(values[j] - values[j-12]) / values[j-12] * 100 
                             for j in range(12, len(values))]
                    dates_adj = dates[12:]
                else:
                    values = [0] * len(values)
                    dates_adj = dates
            elif comparison_type == "环比增长率":
                values = [(values[j] - values[j-1]) / values[j-1] * 100 
                         for j in range(1, len(values))]
                dates_adj = dates[1:]
            elif comparison_type == "标准化数值":
                values = (values - np.mean(values)) / np.std(values)
                dates_adj = dates
            else:  # 原始数值
                dates_adj = dates
            
            # 添加到图表
            fig.add_trace(go.Scatter(
                x=dates_adj,
                y=values,
                mode='lines+markers',
                name=indicator,
                line=dict(color=colors[i % len(colors)], width=2)
            ))
            
            # 保存数据用于统计
            for j, date in enumerate(dates_adj):
                comparison_data.append({
                    'date': date,
                    'indicator': indicator,
                    'value': values[j]
                })
        
        # 更新图表布局
        fig.update_layout(
            title=f"经济指标对比分析 ({comparison_type})",
            xaxis_title="日期",
            yaxis_title="数值" if comparison_type == "原始数值" else comparison_type,
            height=500,
            hovermode='x unified'
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # 统计分析
        if comparison_data:
            df_comparison = pd.DataFrame(comparison_data)
            
            st.subheader("统计摘要")
            
            # 计算统计指标
            stats_summary = df_comparison.groupby('indicator')['value'].agg([
                'mean', 'std', 'min', 'max'
            ]).round(3)
            
            stats_summary.columns = ['平均值', '标准差', '最小值', '最大值']
            st.dataframe(stats_summary, use_container_width=True)
            
            # 相关性分析
            if len(indicators) > 1:
                st.subheader("相关性分析")
                
                pivot_data = df_comparison.pivot(index='date', columns='indicator', values='value')
                correlation_matrix = pivot_data.corr()
                
                fig_corr = px.imshow(
                    correlation_matrix,
                    title="指标相关性矩阵",
                    color_continuous_scale="RdBu",
                    aspect="auto"
                )
                
                st.plotly_chart(fig_corr, use_container_width=True)
    
    def _render_search_history(self):
        """渲染搜索历史"""
        # 模拟搜索历史
        search_history = [
            {"查询": "GDP", "时间": "2024-01-15 14:30", "结果数": 3},
            {"查询": "失业率", "时间": "2024-01-15 14:25", "结果数": 2},
            {"查询": "通胀", "时间": "2024-01-15 14:20", "结果数": 4}
        ]
        
        if search_history:
            df_history = pd.DataFrame(search_history)
            st.dataframe(df_history, use_container_width=True)
        else:
            st.info("暂无搜索历史")
    
    def _apply_filters(self, category: str, frequency: str, source: str):
        """应用筛选条件"""
        st.subheader("筛选结果")
        
        filtered_indicators = self.popular_indicators.copy()
        
        # 应用分类筛选
        if category != "全部":
            filtered_indicators = [ind for ind in filtered_indicators 
                                 if category in ind['category']]
        
        # 应用频率筛选
        if frequency != "全部":
            filtered_indicators = [ind for ind in filtered_indicators 
                                 if frequency in ind['frequency']]
        
        if filtered_indicators:
            for indicator in filtered_indicators:
                with st.expander(f"📊 {indicator['name']}"):
                    st.write(f"ID: {indicator['id']}")
                    st.write(f"分类: {indicator['category']}")
                    st.write(f"频率: {indicator['frequency']}")
                    
                    if st.button(f"查看数据", key=f"filter_{indicator['id']}"):
                        self._show_indicator_data(indicator['id'])
        else:
            st.warning("没有符合筛选条件的指标")