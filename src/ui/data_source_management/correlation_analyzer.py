"""
经济指标与市场数据的关联分析工具
"""

import streamlit as st
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
from scipy import stats
import seaborn as sns


class CorrelationAnalyzer:
    """相关性分析器"""
    
    def __init__(self, api_base_url: str):
        self.api_base_url = api_base_url
        self.headers = {"Content-Type": "application/json"}
        
        # 预定义的分析组合
        self.analysis_templates = {
            "宏观经济与股市": {
                "economic_indicators": ["GDP", "UNRATE", "CPIAUCSL", "FEDFUNDS"],
                "market_symbols": ["SPY", "QQQ", "DIA", "IWM"],
                "description": "分析宏观经济指标与主要股指的关联性"
            },
            "货币政策与债券": {
                "economic_indicators": ["FEDFUNDS", "DGS10", "DGS2", "T10Y2Y"],
                "market_symbols": ["TLT", "IEF", "SHY", "TIP"],
                "description": "分析货币政策对债券市场的影响"
            },
            "通胀与商品": {
                "economic_indicators": ["CPIAUCSL", "CPILFESL", "PPIACO", "GASREGW"],
                "market_symbols": ["GLD", "SLV", "USO", "DBA"],
                "description": "分析通胀指标与大宗商品的关系"
            },
            "就业与消费": {
                "economic_indicators": ["UNRATE", "PAYEMS", "AWHMAN", "HOUST"],
                "market_symbols": ["XLY", "XLP", "XRT", "HD"],
                "description": "分析就业数据与消费相关股票的关联"
            },
            "国际贸易与汇率": {
                "economic_indicators": ["BOPGSTB", "EXUSEU", "EXJPUS", "EXCAUS"],
                "market_symbols": ["UUP", "FXE", "FXY", "FXC"],
                "description": "分析贸易数据与汇率的关系"
            }
        }
    
    def render(self):
        """渲染相关性分析界面"""
        st.header("🔗 经济指标与市场数据关联分析")
        
        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs(["快速分析", "自定义分析", "高级分析", "分析报告"])
        
        with tab1:
            self._render_quick_analysis()
        
        with tab2:
            self._render_custom_analysis()
        
        with tab3:
            self._render_advanced_analysis()
        
        with tab4:
            self._render_analysis_reports()
    
    def _render_quick_analysis(self):
        """渲染快速分析"""
        st.subheader("快速关联分析")
        
        # 选择分析模板
        selected_template = st.selectbox(
            "选择分析模板",
            list(self.analysis_templates.keys()),
            help="选择预定义的分析组合，快速开始分析"
        )
        
        template_info = self.analysis_templates[selected_template]
        
        # 显示模板信息
        st.info(f"📋 {template_info['description']}")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**经济指标:**")
            for indicator in template_info['economic_indicators']:
                st.write(f"• {indicator}")
        
        with col2:
            st.write("**市场品种:**")
            for symbol in template_info['market_symbols']:
                st.write(f"• {symbol}")
        
        # 分析参数
        st.subheader("分析参数")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            start_date = st.date_input(
                "开始日期",
                value=datetime.now().date() - timedelta(days=365*2)
            )
        
        with col2:
            end_date = st.date_input(
                "结束日期",
                value=datetime.now().date()
            )
        
        with col3:
            correlation_method = st.selectbox(
                "相关性方法",
                ["皮尔逊相关", "斯皮尔曼相关", "肯德尔相关"]
            )
        
        # 开始分析
        if st.button("🚀 开始快速分析", type="primary"):
            self._perform_quick_analysis(
                template_info['economic_indicators'],
                template_info['market_symbols'],
                start_date,
                end_date,
                correlation_method
            )
    
    def _render_custom_analysis(self):
        """渲染自定义分析"""
        st.subheader("自定义关联分析")
        
        # 经济指标选择
        st.write("**选择经济指标**")
        
        # 预定义经济指标
        available_indicators = [
            "GDP", "UNRATE", "CPIAUCSL", "FEDFUNDS", "PAYEMS",
            "HOUST", "INDPRO", "RETAILSALES", "DGS10", "DGS2"
        ]
        
        col1, col2 = st.columns(2)
        
        with col1:
            selected_indicators = st.multiselect(
                "从列表选择",
                available_indicators,
                help="选择要分析的经济指标"
            )
        
        with col2:
            custom_indicators = st.text_area(
                "自定义指标ID",
                placeholder="每行输入一个FRED序列ID",
                height=100
            )
        
        # 市场品种选择
        st.write("**选择市场品种**")
        
        # 预定义市场品种
        available_symbols = [
            "SPY", "QQQ", "DIA", "IWM", "GLD", "SLV", "TLT", "IEF",
            "XLF", "XLE", "XLK", "XLV", "XLY", "XLP", "XLI", "XLU"
        ]
        
        col1, col2 = st.columns(2)
        
        with col1:
            selected_symbols = st.multiselect(
                "从列表选择",
                available_symbols,
                help="选择要分析的市场品种"
            )
        
        with col2:
            custom_symbols = st.text_area(
                "自定义品种代码",
                placeholder="每行输入一个品种代码",
                height=100
            )
        
        # 分析设置
        st.subheader("分析设置")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            start_date = st.date_input(
                "开始日期",
                value=datetime.now().date() - timedelta(days=365*3)
            )
        
        with col2:
            end_date = st.date_input(
                "结束日期",
                value=datetime.now().date()
            )
        
        with col3:
            data_frequency = st.selectbox(
                "数据频率",
                ["月度", "季度", "年度"]
            )
        
        with col4:
            lag_periods = st.slider(
                "滞后期数",
                0, 12, 0,
                help="分析经济指标对市场的滞后影响"
            )
        
        # 高级选项
        with st.expander("🔧 高级选项"):
            col1, col2 = st.columns(2)
            
            with col1:
                correlation_method = st.selectbox(
                    "相关性方法",
                    ["皮尔逊相关", "斯皮尔曼相关", "肯德尔相关"]
                )
                
                significance_level = st.selectbox(
                    "显著性水平",
                    [0.01, 0.05, 0.10],
                    index=1
                )
            
            with col2:
                rolling_window = st.slider(
                    "滚动窗口大小",
                    6, 36, 12,
                    help="用于计算滚动相关性"
                )
                
                min_correlation = st.slider(
                    "最小相关性阈值",
                    0.0, 1.0, 0.3,
                    help="只显示超过此阈值的相关性"
                )
        
        # 开始分析
        if st.button("📊 开始自定义分析", type="primary"):
            # 合并指标和品种
            all_indicators = selected_indicators.copy()
            if custom_indicators:
                all_indicators.extend([s.strip() for s in custom_indicators.split('\n') if s.strip()])
            
            all_symbols = selected_symbols.copy()
            if custom_symbols:
                all_symbols.extend([s.strip() for s in custom_symbols.split('\n') if s.strip()])
            
            if not all_indicators or not all_symbols:
                st.error("请至少选择一个经济指标和一个市场品种")
                return
            
            self._perform_custom_analysis(
                all_indicators, all_symbols, start_date, end_date,
                correlation_method, lag_periods, rolling_window, min_correlation
            )
    
    def _render_advanced_analysis(self):
        """渲染高级分析"""
        st.subheader("高级关联分析")
        
        # 分析类型选择
        analysis_type = st.selectbox(
            "选择分析类型",
            [
                "格兰杰因果检验",
                "协整分析",
                "向量自回归(VAR)",
                "脉冲响应分析",
                "方差分解",
                "滚动相关性分析"
            ]
        )
        
        if analysis_type == "格兰杰因果检验":
            self._render_granger_causality()
        elif analysis_type == "协整分析":
            self._render_cointegration_analysis()
        elif analysis_type == "向量自回归(VAR)":
            self._render_var_analysis()
        elif analysis_type == "脉冲响应分析":
            self._render_impulse_response()
        elif analysis_type == "方差分解":
            self._render_variance_decomposition()
        else:  # 滚动相关性分析
            self._render_rolling_correlation()
    
    def _render_analysis_reports(self):
        """渲染分析报告"""
        st.subheader("分析报告")
        
        # 报告类型选择
        report_type = st.selectbox(
            "选择报告类型",
            ["综合关联报告", "行业影响分析", "政策影响评估", "市场预警报告"]
        )
        
        if report_type == "综合关联报告":
            self._render_comprehensive_report()
        elif report_type == "行业影响分析":
            self._render_sector_impact_analysis()
        elif report_type == "政策影响评估":
            self._render_policy_impact_assessment()
        else:  # 市场预警报告
            self._render_market_alert_report()
    
    def _perform_quick_analysis(self, indicators: List[str], symbols: List[str], 
                               start_date, end_date, method: str):
        """执行快速分析"""
        st.subheader("快速分析结果")
        
        with st.spinner("正在进行关联分析..."):
            # 模拟数据生成
            analysis_data = self._generate_correlation_data(indicators, symbols, start_date, end_date)
            
            # 计算相关性矩阵
            correlation_matrix = self._calculate_correlation_matrix(analysis_data, method)
            
            # 显示相关性热力图
            self._plot_correlation_heatmap(correlation_matrix, "经济指标与市场数据相关性")
            
            # 显示强相关性对
            self._show_strong_correlations(correlation_matrix, indicators, symbols)
            
            # 时间序列对比图
            self._plot_time_series_comparison(analysis_data, indicators[:2], symbols[:2])
            
            # 散点图分析
            self._plot_scatter_analysis(analysis_data, indicators[0], symbols[0])
    
    def _perform_custom_analysis(self, indicators: List[str], symbols: List[str],
                                start_date, end_date, method: str, lag_periods: int,
                                rolling_window: int, min_correlation: float):
        """执行自定义分析"""
        st.subheader("自定义分析结果")
        
        with st.spinner("正在进行自定义关联分析..."):
            # 生成分析数据
            analysis_data = self._generate_correlation_data(indicators, symbols, start_date, end_date)
            
            # 应用滞后
            if lag_periods > 0:
                analysis_data = self._apply_lag(analysis_data, indicators, lag_periods)
            
            # 计算相关性
            correlation_matrix = self._calculate_correlation_matrix(analysis_data, method)
            
            # 过滤低相关性
            filtered_matrix = correlation_matrix.copy()
            filtered_matrix[abs(filtered_matrix) < min_correlation] = np.nan
            
            # 显示结果
            self._plot_correlation_heatmap(filtered_matrix, f"相关性分析 (阈值: {min_correlation})")
            
            # 滚动相关性分析
            if rolling_window > 0:
                self._plot_rolling_correlation(analysis_data, indicators[0], symbols[0], rolling_window)
            
            # 统计显著性检验
            self._perform_significance_test(correlation_matrix, len(analysis_data))
    
    def _render_granger_causality(self):
        """渲染格兰杰因果检验"""
        st.subheader("格兰杰因果检验")
        
        col1, col2 = st.columns(2)
        
        with col1:
            x_variable = st.selectbox("选择X变量（原因）", ["GDP", "UNRATE", "CPIAUCSL", "FEDFUNDS"])
        
        with col2:
            y_variable = st.selectbox("选择Y变量（结果）", ["SPY", "QQQ", "GLD", "TLT"])
        
        max_lags = st.slider("最大滞后期", 1, 12, 4)
        
        if st.button("执行格兰杰因果检验"):
            # 模拟格兰杰因果检验结果
            results = {
                "F统计量": np.random.uniform(2, 10),
                "p值": np.random.uniform(0.001, 0.1),
                "临界值(5%)": 2.45,
                "结论": "拒绝原假设" if np.random.random() > 0.5 else "接受原假设"
            }
            
            st.subheader("检验结果")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("F统计量", f"{results['F统计量']:.3f}")
            
            with col2:
                st.metric("p值", f"{results['p值']:.4f}")
            
            with col3:
                st.metric("临界值(5%)", f"{results['临界值(5%)']:.2f}")
            
            if results['结论'] == "拒绝原假设":
                st.success(f"✅ {x_variable} 对 {y_variable} 存在格兰杰因果关系")
            else:
                st.info(f"ℹ️ {x_variable} 对 {y_variable} 不存在格兰杰因果关系")
    
    def _render_cointegration_analysis(self):
        """渲染协整分析"""
        st.subheader("协整分析")
        
        st.info("协整分析功能正在开发中，敬请期待！")
        
        # 变量选择
        variables = st.multiselect(
            "选择变量进行协整分析",
            ["GDP", "UNRATE", "SPY", "GLD", "TLT"],
            default=["GDP", "SPY"]
        )
        
        if len(variables) >= 2 and st.button("执行协整分析"):
            st.warning("协整分析功能正在开发中...")
    
    def _render_var_analysis(self):
        """渲染VAR分析"""
        st.subheader("向量自回归(VAR)分析")
        
        st.info("VAR分析功能正在开发中，敬请期待！")
    
    def _render_impulse_response(self):
        """渲染脉冲响应分析"""
        st.subheader("脉冲响应分析")
        
        st.info("脉冲响应分析功能正在开发中，敬请期待！")
    
    def _render_variance_decomposition(self):
        """渲染方差分解"""
        st.subheader("方差分解")
        
        st.info("方差分解功能正在开发中，敬请期待！")
    
    def _render_rolling_correlation(self):
        """渲染滚动相关性分析"""
        st.subheader("滚动相关性分析")
        
        col1, col2 = st.columns(2)
        
        with col1:
            indicator = st.selectbox("选择经济指标", ["GDP", "UNRATE", "CPIAUCSL", "FEDFUNDS"])
        
        with col2:
            symbol = st.selectbox("选择市场品种", ["SPY", "QQQ", "GLD", "TLT"])
        
        window_size = st.slider("滚动窗口大小", 6, 36, 12)
        
        if st.button("计算滚动相关性"):
            # 生成模拟数据
            dates = pd.date_range(start='2020-01-01', end='2024-01-15', freq='M')
            
            # 模拟滚动相关性数据
            rolling_corr = np.random.uniform(-0.8, 0.8, len(dates) - window_size + 1)
            rolling_dates = dates[window_size-1:]
            
            df_rolling = pd.DataFrame({
                'date': rolling_dates,
                'correlation': rolling_corr
            })
            
            # 绘制滚动相关性图
            fig = px.line(
                df_rolling,
                x='date',
                y='correlation',
                title=f'{indicator} 与 {symbol} 滚动相关性 (窗口: {window_size}个月)',
                labels={'correlation': '相关系数', 'date': '日期'}
            )
            
            # 添加零线
            fig.add_hline(y=0, line_dash="dash", line_color="gray")
            fig.add_hline(y=0.5, line_dash="dot", line_color="green", annotation_text="强正相关")
            fig.add_hline(y=-0.5, line_dash="dot", line_color="red", annotation_text="强负相关")
            
            st.plotly_chart(fig, use_container_width=True)
            
            # 统计摘要
            st.subheader("滚动相关性统计")
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("平均相关性", f"{np.mean(rolling_corr):.3f}")
            
            with col2:
                st.metric("标准差", f"{np.std(rolling_corr):.3f}")
            
            with col3:
                st.metric("最大值", f"{np.max(rolling_corr):.3f}")
            
            with col4:
                st.metric("最小值", f"{np.min(rolling_corr):.3f}")
    
    def _render_comprehensive_report(self):
        """渲染综合关联报告"""
        st.subheader("综合关联分析报告")
        
        # 报告参数
        col1, col2 = st.columns(2)
        
        with col1:
            report_period = st.selectbox(
                "报告期间",
                ["最近1年", "最近2年", "最近3年", "最近5年"]
            )
        
        with col2:
            focus_area = st.selectbox(
                "关注领域",
                ["全市场", "股票市场", "债券市场", "商品市场", "外汇市场"]
            )
        
        if st.button("生成综合报告"):
            with st.spinner("正在生成综合关联分析报告..."):
                # 模拟报告生成
                report_data = self._generate_comprehensive_report_data(report_period, focus_area)
                
                # 执行摘要
                st.subheader("执行摘要")
                
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("分析指标数", report_data['total_indicators'])
                
                with col2:
                    st.metric("市场品种数", report_data['total_symbols'])
                
                with col3:
                    st.metric("强相关对数", report_data['strong_correlations'])
                
                with col4:
                    st.metric("平均相关性", f"{report_data['avg_correlation']:.3f}")
                
                # 主要发现
                st.subheader("主要发现")
                
                findings = report_data['key_findings']
                for i, finding in enumerate(findings):
                    st.write(f"{i+1}. {finding}")
                
                # 相关性网络图
                st.subheader("关联网络图")
                self._plot_correlation_network(report_data['network_data'])
                
                # 行业影响分析
                st.subheader("行业影响分析")
                self._plot_sector_impact(report_data['sector_impact'])
    
    def _render_sector_impact_analysis(self):
        """渲染行业影响分析"""
        st.subheader("行业影响分析")
        
        selected_sector = st.selectbox(
            "选择行业",
            ["金融", "科技", "医疗", "消费", "工业", "能源", "房地产", "公用事业"]
        )
        
        if st.button("分析行业影响"):
            # 模拟行业影响分析
            st.success(f"正在分析{selected_sector}行业的经济指标影响...")
            
            # 这里可以添加具体的行业分析逻辑
            st.info("行业影响分析功能正在完善中...")
    
    def _render_policy_impact_assessment(self):
        """渲染政策影响评估"""
        st.subheader("政策影响评估")
        
        policy_type = st.selectbox(
            "选择政策类型",
            ["货币政策", "财政政策", "监管政策", "贸易政策"]
        )
        
        if st.button("评估政策影响"):
            st.success(f"正在评估{policy_type}对市场的影响...")
            st.info("政策影响评估功能正在完善中...")
    
    def _render_market_alert_report(self):
        """渲染市场预警报告"""
        st.subheader("市场预警报告")
        
        alert_sensitivity = st.slider("预警敏感度", 0.1, 1.0, 0.5)
        
        if st.button("生成预警报告"):
            # 模拟预警报告
            alerts = [
                {
                    "时间": "2024-01-15",
                    "类型": "相关性异常",
                    "描述": "GDP与SPY相关性突然下降至-0.3",
                    "风险等级": "中等",
                    "建议": "关注宏观经济数据变化"
                },
                {
                    "时间": "2024-01-14",
                    "类型": "滞后效应",
                    "描述": "失业率变化对消费股影响延迟增强",
                    "风险等级": "低",
                    "建议": "监控就业数据发布"
                }
            ]
            
            st.subheader("当前预警")
            
            for alert in alerts:
                severity_color = {"高": "🔴", "中等": "🟡", "低": "🟢"}
                
                with st.expander(f"{severity_color.get(alert['风险等级'], '🔵')} {alert['类型']} - {alert['时间']}"):
                    st.write(f"**描述:** {alert['描述']}")
                    st.write(f"**风险等级:** {alert['风险等级']}")
                    st.write(f"**建议:** {alert['建议']}")
    
    def _generate_correlation_data(self, indicators: List[str], symbols: List[str], 
                                  start_date, end_date) -> pd.DataFrame:
        """生成相关性分析数据"""
        dates = pd.date_range(start=start_date, end=end_date, freq='M')
        
        data = {'date': dates}
        
        # 生成经济指标数据
        for indicator in indicators:
            base_value = np.random.uniform(50, 150)
            trend = np.linspace(0, np.random.uniform(-20, 20), len(dates))
            seasonal = np.random.uniform(5, 15) * np.sin(2 * np.pi * np.arange(len(dates)) / 12)
            noise = np.random.normal(0, 3, len(dates))
            data[indicator] = base_value + trend + seasonal + noise
        
        # 生成市场数据（与部分经济指标相关）
        for symbol in symbols:
            base_value = np.random.uniform(100, 300)
            
            # 与第一个经济指标建立相关性
            if indicators:
                correlation_strength = np.random.uniform(0.3, 0.8)
                correlated_component = correlation_strength * (data[indicators[0]] - np.mean(data[indicators[0]]))
            else:
                correlated_component = 0
            
            trend = np.linspace(0, np.random.uniform(-50, 50), len(dates))
            noise = np.random.normal(0, 10, len(dates))
            data[symbol] = base_value + trend + correlated_component + noise
        
        return pd.DataFrame(data)
    
    def _calculate_correlation_matrix(self, data: pd.DataFrame, method: str) -> pd.DataFrame:
        """计算相关性矩阵"""
        numeric_data = data.select_dtypes(include=[np.number])
        
        if method == "皮尔逊相关":
            return numeric_data.corr(method='pearson')
        elif method == "斯皮尔曼相关":
            return numeric_data.corr(method='spearman')
        else:  # 肯德尔相关
            return numeric_data.corr(method='kendall')
    
    def _plot_correlation_heatmap(self, correlation_matrix: pd.DataFrame, title: str):
        """绘制相关性热力图"""
        fig = px.imshow(
            correlation_matrix,
            title=title,
            color_continuous_scale="RdBu",
            aspect="auto",
            zmin=-1,
            zmax=1
        )
        
        fig.update_layout(height=500)
        st.plotly_chart(fig, use_container_width=True)
    
    def _show_strong_correlations(self, correlation_matrix: pd.DataFrame, 
                                 indicators: List[str], symbols: List[str]):
        """显示强相关性对"""
        st.subheader("强相关性分析")
        
        strong_correlations = []
        
        for indicator in indicators:
            for symbol in symbols:
                if indicator in correlation_matrix.index and symbol in correlation_matrix.columns:
                    corr_value = correlation_matrix.loc[indicator, symbol]
                    if abs(corr_value) > 0.5:
                        strong_correlations.append({
                            "经济指标": indicator,
                            "市场品种": symbol,
                            "相关系数": f"{corr_value:.3f}",
                            "相关性强度": "强正相关" if corr_value > 0.5 else "强负相关",
                            "显著性": "是" if abs(corr_value) > 0.7 else "否"
                        })
        
        if strong_correlations:
            df_strong = pd.DataFrame(strong_correlations)
            st.dataframe(df_strong, use_container_width=True)
        else:
            st.info("未发现强相关性（|r| > 0.5）的指标对")
    
    def _plot_time_series_comparison(self, data: pd.DataFrame, indicators: List[str], symbols: List[str]):
        """绘制时间序列对比图"""
        if len(indicators) > 0 and len(symbols) > 0:
            st.subheader("时间序列对比")
            
            fig = make_subplots(
                rows=2, cols=1,
                subplot_titles=[f'经济指标: {indicators[0]}', f'市场品种: {symbols[0]}'],
                vertical_spacing=0.1
            )
            
            # 标准化数据用于对比
            indicator_data = (data[indicators[0]] - data[indicators[0]].mean()) / data[indicators[0]].std()
            symbol_data = (data[symbols[0]] - data[symbols[0]].mean()) / data[symbols[0]].std()
            
            fig.add_trace(
                go.Scatter(x=data['date'], y=indicator_data, name=indicators[0]),
                row=1, col=1
            )
            
            fig.add_trace(
                go.Scatter(x=data['date'], y=symbol_data, name=symbols[0]),
                row=2, col=1
            )
            
            fig.update_layout(height=500, title_text="标准化时间序列对比")
            st.plotly_chart(fig, use_container_width=True)
    
    def _plot_scatter_analysis(self, data: pd.DataFrame, indicator: str, symbol: str):
        """绘制散点图分析"""
        if indicator in data.columns and symbol in data.columns:
            st.subheader("散点图分析")
            
            fig = px.scatter(
                data,
                x=indicator,
                y=symbol,
                title=f'{indicator} vs {symbol}',
                trendline="ols"
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # 计算回归统计
            correlation = data[indicator].corr(data[symbol])
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.metric("相关系数", f"{correlation:.3f}")
            
            with col2:
                r_squared = correlation ** 2
                st.metric("决定系数 (R²)", f"{r_squared:.3f}")
    
    def _apply_lag(self, data: pd.DataFrame, indicators: List[str], lag_periods: int) -> pd.DataFrame:
        """应用滞后"""
        lagged_data = data.copy()
        
        for indicator in indicators:
            if indicator in lagged_data.columns:
                lagged_data[f"{indicator}_lag{lag_periods}"] = lagged_data[indicator].shift(lag_periods)
                lagged_data.drop(indicator, axis=1, inplace=True)
        
        return lagged_data.dropna()
    
    def _plot_rolling_correlation(self, data: pd.DataFrame, indicator: str, symbol: str, window: int):
        """绘制滚动相关性"""
        if indicator in data.columns and symbol in data.columns:
            rolling_corr = data[indicator].rolling(window=window).corr(data[symbol])
            
            fig = px.line(
                x=data['date'][window-1:],
                y=rolling_corr[window-1:],
                title=f'{indicator} 与 {symbol} 滚动相关性 (窗口: {window})',
                labels={'x': '日期', 'y': '相关系数'}
            )
            
            fig.add_hline(y=0, line_dash="dash", line_color="gray")
            st.plotly_chart(fig, use_container_width=True)
    
    def _perform_significance_test(self, correlation_matrix: pd.DataFrame, n_observations: int):
        """执行显著性检验"""
        st.subheader("统计显著性检验")
        
        # 计算临界值
        alpha = 0.05
        critical_value = stats.t.ppf(1 - alpha/2, n_observations - 2)
        critical_r = critical_value / np.sqrt(critical_value**2 + n_observations - 2)
        
        st.write(f"**显著性水平:** {alpha}")
        st.write(f"**临界相关系数:** ±{critical_r:.3f}")
        
        # 统计显著相关性
        significant_count = (abs(correlation_matrix) > critical_r).sum().sum() - len(correlation_matrix)
        total_pairs = len(correlation_matrix) * (len(correlation_matrix) - 1)
        
        st.write(f"**显著相关对数:** {significant_count}/{total_pairs}")
    
    def _generate_comprehensive_report_data(self, period: str, focus_area: str) -> Dict[str, Any]:
        """生成综合报告数据"""
        return {
            'total_indicators': np.random.randint(10, 20),
            'total_symbols': np.random.randint(15, 30),
            'strong_correlations': np.random.randint(5, 15),
            'avg_correlation': np.random.uniform(0.2, 0.6),
            'key_findings': [
                "GDP增长与股市表现呈现强正相关关系",
                "失业率上升对消费类股票产生负面影响",
                "通胀预期与黄金价格相关性增强",
                "利率变化对债券市场影响显著",
                "国际贸易数据与汇率波动关联度较高"
            ],
            'network_data': {},  # 网络图数据
            'sector_impact': {}  # 行业影响数据
        }
    
    def _plot_correlation_network(self, network_data: Dict[str, Any]):
        """绘制相关性网络图"""
        st.info("相关性网络图功能正在开发中...")
    
    def _plot_sector_impact(self, sector_data: Dict[str, Any]):
        """绘制行业影响图"""
        # 模拟行业影响数据
        sectors = ['金融', '科技', '医疗', '消费', '工业', '能源']
        impact_scores = np.random.uniform(-1, 1, len(sectors))
        
        fig = px.bar(
            x=sectors,
            y=impact_scores,
            title="各行业受经济指标影响程度",
            labels={'x': '行业', 'y': '影响评分'},
            color=impact_scores,
            color_continuous_scale="RdYlGn"
        )
        
        st.plotly_chart(fig, use_container_width=True)