"""
数据覆盖范围查看工具
"""

import streamlit as st
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np


class DataCoverageViewer:
    """数据覆盖范围查看器"""
    
    def __init__(self, api_base_url: str):
        self.api_base_url = api_base_url
        self.headers = {"Content-Type": "application/json"}
    
    def render(self):
        """渲染数据覆盖范围界面"""
        st.header("📈 数据覆盖范围分析")
        
        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs(["总体覆盖", "按数据源", "按市场", "品种详情"])
        
        with tab1:
            self._render_overall_coverage()
        
        with tab2:
            self._render_source_coverage()
        
        with tab3:
            self._render_market_coverage()
        
        with tab4:
            self._render_symbol_details()
    
    def _render_overall_coverage(self):
        """渲染总体覆盖范围"""
        st.subheader("总体数据覆盖情况")
        
        # 获取覆盖数据
        coverage_data = self._get_coverage_data()
        
        if not coverage_data:
            st.error("无法获取覆盖数据")
            return
        
        # 总体统计
        col1, col2, col3, col4 = st.columns(4)
        
        total_sources = len(coverage_data)
        total_symbols = sum(source.get('symbol_count', 0) for source in coverage_data)
        active_sources = sum(1 for source in coverage_data if source.get('symbol_count', 0) > 0)
        
        with col1:
            st.metric("数据源总数", total_sources)
        
        with col2:
            st.metric("品种总数", total_symbols)
        
        with col3:
            st.metric("活跃数据源", active_sources)
        
        with col4:
            coverage_rate = (active_sources / total_sources * 100) if total_sources > 0 else 0
            st.metric("覆盖率", f"{coverage_rate:.1f}%")
        
        # 数据源分布饼图
        col1, col2 = st.columns(2)
        
        with col1:
            if coverage_data:
                # 按数据源的品种分布
                source_symbols = {}
                for source in coverage_data:
                    source_name = source.get('source_name', 'Unknown')
                    symbol_count = source.get('symbol_count', 0)
                    if symbol_count > 0:
                        source_symbols[source_name] = symbol_count
                
                if source_symbols:
                    fig_pie = px.pie(
                        values=list(source_symbols.values()),
                        names=list(source_symbols.keys()),
                        title="各数据源品种分布"
                    )
                    st.plotly_chart(fig_pie, use_container_width=True)
        
        with col2:
            if coverage_data:
                # 按市场的品种分布
                market_symbols = {}
                for source in coverage_data:
                    market = source.get('market', 'Unknown')
                    symbol_count = source.get('symbol_count', 0)
                    if symbol_count > 0:
                        if market in market_symbols:
                            market_symbols[market] += symbol_count
                        else:
                            market_symbols[market] = symbol_count
                
                if market_symbols:
                    fig_market = px.pie(
                        values=list(market_symbols.values()),
                        names=list(market_symbols.keys()),
                        title="各市场品种分布"
                    )
                    st.plotly_chart(fig_market, use_container_width=True)
        
        # 详细覆盖表格
        st.subheader("详细覆盖信息")
        
        if coverage_data:
            table_data = []
            for source in coverage_data:
                table_data.append({
                    "数据源": source.get('source_name', 'Unknown'),
                    "市场": source.get('market', 'Unknown'),
                    "交易所": source.get('exchange', 'Unknown'),
                    "品种数量": source.get('symbol_count', 0),
                    "示例品种": ", ".join(source.get('sample_symbols', [])[:5])
                })
            
            df_coverage = pd.DataFrame(table_data)
            st.dataframe(df_coverage, use_container_width=True)
        
        # 覆盖趋势图（模拟数据）
        st.subheader("覆盖趋势分析")
        self._render_coverage_trends()
    
    def _render_source_coverage(self):
        """渲染按数据源的覆盖情况"""
        st.subheader("按数据源分析")
        
        # 获取数据源列表
        sources_data = self._get_data_sources()
        coverage_data = self._get_coverage_data()
        
        if not sources_data or not coverage_data:
            st.error("无法获取数据源信息")
            return
        
        # 选择数据源
        source_names = [source.get('name', '') for source in sources_data]
        selected_source = st.selectbox("选择数据源", source_names)
        
        if not selected_source:
            return
        
        # 找到对应的覆盖数据
        source_coverage = None
        for coverage in coverage_data:
            if coverage.get('source_name') == selected_source:
                source_coverage = coverage
                break
        
        if not source_coverage:
            st.warning(f"未找到数据源 {selected_source} 的覆盖信息")
            return
        
        # 显示数据源详细信息
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("品种数量", source_coverage.get('symbol_count', 0))
        
        with col2:
            st.metric("市场", source_coverage.get('market', 'Unknown'))
        
        with col3:
            st.metric("交易所", source_coverage.get('exchange', 'Unknown'))
        
        # 获取该数据源的详细品种信息
        symbols_data = self._get_source_symbols(selected_source)
        
        if symbols_data:
            st.subheader("品种列表")
            
            # 搜索和筛选
            col1, col2 = st.columns(2)
            
            with col1:
                search_term = st.text_input("搜索品种", placeholder="输入品种代码或名称")
            
            with col2:
                show_count = st.selectbox("显示数量", [50, 100, 200, 500], index=1)
            
            # 处理品种数据
            symbols_list = symbols_data.get('symbols', [])
            
            if search_term:
                symbols_list = [s for s in symbols_list if search_term.upper() in s.upper()]
            
            symbols_list = symbols_list[:show_count]
            
            # 显示品种
            if symbols_list:
                # 分列显示
                cols = st.columns(4)
                for i, symbol in enumerate(symbols_list):
                    with cols[i % 4]:
                        st.write(f"• {symbol}")
            
            # 导出功能
            if st.button("📥 导出品种列表"):
                df_symbols = pd.DataFrame(symbols_list, columns=['Symbol'])
                csv = df_symbols.to_csv(index=False)
                st.download_button(
                    label="下载CSV文件",
                    data=csv,
                    file_name=f"{selected_source}_symbols_{datetime.now().strftime('%Y%m%d')}.csv",
                    mime="text/csv"
                )
        
        # 数据源性能分析
        st.subheader("性能分析")
        self._render_source_performance(selected_source)
    
    def _render_market_coverage(self):
        """渲染按市场的覆盖情况"""
        st.subheader("按市场分析")
        
        # 市场选择
        markets = ["US", "CN", "CRYPTO", "FOREX"]
        selected_market = st.selectbox("选择市场", markets)
        
        # 获取市场覆盖数据
        market_coverage = self._get_market_coverage(selected_market)
        
        if not market_coverage:
            st.warning(f"未找到市场 {selected_market} 的覆盖数据")
            return
        
        # 市场总体统计
        col1, col2, col3, col4 = st.columns(4)
        
        total_sources = len(market_coverage)
        total_symbols = sum(source.get('symbol_count', 0) for source in market_coverage)
        
        with col1:
            st.metric("数据源数量", total_sources)
        
        with col2:
            st.metric("品种总数", total_symbols)
        
        with col3:
            # 计算平均品种数
            avg_symbols = total_symbols / total_sources if total_sources > 0 else 0
            st.metric("平均品种数", f"{avg_symbols:.0f}")
        
        with col4:
            # 最大品种数的数据源
            if market_coverage:
                max_source = max(market_coverage, key=lambda x: x.get('symbol_count', 0))
                st.metric("最大数据源", max_source.get('source_name', 'Unknown'))
        
        # 市场内数据源对比
        if market_coverage:
            st.subheader("数据源对比")
            
            # 准备对比数据
            comparison_data = []
            for source in market_coverage:
                comparison_data.append({
                    "数据源": source.get('source_name', 'Unknown'),
                    "交易所": source.get('exchange', 'Unknown'),
                    "品种数量": source.get('symbol_count', 0),
                    "示例品种": ", ".join(source.get('sample_symbols', [])[:3])
                })
            
            df_comparison = pd.DataFrame(comparison_data)
            
            # 品种数量对比图
            fig_comparison = px.bar(
                df_comparison,
                x="数据源",
                y="品种数量",
                title=f"{selected_market} 市场各数据源品种数量对比",
                text="品种数量"
            )
            fig_comparison.update_traces(texttemplate='%{text}', textposition='outside')
            st.plotly_chart(fig_comparison, use_container_width=True)
            
            # 详细表格
            st.dataframe(df_comparison, use_container_width=True)
        
        # 市场热力图
        st.subheader("市场活跃度热力图")
        self._render_market_heatmap(selected_market)
    
    def _render_symbol_details(self):
        """渲染品种详情"""
        st.subheader("品种详细信息")
        
        # 品种搜索
        col1, col2 = st.columns(2)
        
        with col1:
            symbol_input = st.text_input("输入品种代码", placeholder="例如: AAPL")
        
        with col2:
            if st.button("🔍 查询", type="primary"):
                if symbol_input:
                    self._show_symbol_details(symbol_input.upper())
        
        # 批量查询
        st.subheader("批量查询")
        
        symbols_text = st.text_area(
            "输入多个品种代码",
            placeholder="每行一个品种代码，例如:\nAAPL\nGOOGL\nTSLA",
            height=100
        )
        
        if st.button("📊 批量查询"):
            if symbols_text:
                symbols_list = [s.strip().upper() for s in symbols_text.split('\n') if s.strip()]
                self._show_batch_symbol_details(symbols_list)
    
    def _show_symbol_details(self, symbol: str):
        """显示单个品种的详细信息"""
        st.subheader(f"品种详情: {symbol}")
        
        # 模拟品种详情数据
        symbol_info = {
            "基本信息": {
                "品种代码": symbol,
                "品种名称": f"{symbol} Inc." if symbol != "BTC" else "Bitcoin",
                "市场": "US" if symbol not in ["BTC", "ETH"] else "CRYPTO",
                "交易所": "NASDAQ" if symbol not in ["BTC", "ETH"] else "Binance",
                "货币": "USD",
                "行业": "Technology" if symbol == "AAPL" else "Unknown"
            },
            "数据覆盖": {
                "可用数据源": ["Yahoo Finance", "Alpha Vantage"],
                "最早数据": "2020-01-01",
                "最新数据": "2024-01-15",
                "数据完整性": "98.5%",
                "总记录数": 1250
            },
            "数据质量": {
                "缺失值": "1.5%",
                "异常值": "0.2%",
                "重复值": "0.0%",
                "质量评分": "A+"
            }
        }
        
        # 显示基本信息
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.write("**基本信息**")
            for key, value in symbol_info["基本信息"].items():
                st.write(f"{key}: {value}")
        
        with col2:
            st.write("**数据覆盖**")
            for key, value in symbol_info["数据覆盖"].items():
                if isinstance(value, list):
                    st.write(f"{key}: {', '.join(value)}")
                else:
                    st.write(f"{key}: {value}")
        
        with col3:
            st.write("**数据质量**")
            for key, value in symbol_info["数据质量"].items():
                st.write(f"{key}: {value}")
        
        # 数据覆盖时间线
        st.subheader("数据覆盖时间线")
        
        # 模拟时间线数据
        dates = pd.date_range(start='2020-01-01', end='2024-01-15', freq='D')
        coverage = np.random.choice([0, 1], size=len(dates), p=[0.02, 0.98])  # 98%覆盖率
        
        df_timeline = pd.DataFrame({
            'date': dates,
            'coverage': coverage
        })
        
        # 按月聚合
        df_monthly = df_timeline.set_index('date').resample('M')['coverage'].mean().reset_index()
        
        fig_timeline = px.line(
            df_monthly,
            x='date',
            y='coverage',
            title=f'{symbol} 数据覆盖率时间线',
            labels={'coverage': '覆盖率', 'date': '日期'}
        )
        fig_timeline.update_yaxis(tickformat='.1%')
        st.plotly_chart(fig_timeline, use_container_width=True)
    
    def _show_batch_symbol_details(self, symbols: List[str]):
        """显示批量品种的详细信息"""
        st.subheader(f"批量查询结果 ({len(symbols)} 个品种)")
        
        # 模拟批量查询结果
        batch_results = []
        for symbol in symbols:
            batch_results.append({
                "品种": symbol,
                "市场": "US" if symbol not in ["BTC", "ETH"] else "CRYPTO",
                "数据源数量": np.random.randint(1, 4),
                "最早数据": "2020-01-01",
                "最新数据": "2024-01-15",
                "完整性": f"{np.random.uniform(95, 99.5):.1f}%",
                "质量评分": np.random.choice(["A+", "A", "B+", "B"], p=[0.4, 0.3, 0.2, 0.1])
            })
        
        df_batch = pd.DataFrame(batch_results)
        st.dataframe(df_batch, use_container_width=True)
        
        # 批量统计图表
        col1, col2 = st.columns(2)
        
        with col1:
            # 质量评分分布
            quality_counts = df_batch['质量评分'].value_counts()
            fig_quality = px.pie(
                values=quality_counts.values,
                names=quality_counts.index,
                title="质量评分分布"
            )
            st.plotly_chart(fig_quality, use_container_width=True)
        
        with col2:
            # 数据源数量分布
            fig_sources = px.histogram(
                df_batch,
                x="数据源数量",
                title="数据源数量分布",
                nbins=5
            )
            st.plotly_chart(fig_sources, use_container_width=True)
    
    def _render_coverage_trends(self):
        """渲染覆盖趋势图"""
        # 模拟趋势数据
        dates = pd.date_range(start='2023-01-01', end='2024-01-15', freq='D')
        
        # 模拟不同数据源的覆盖趋势
        sources = ['Yahoo Finance', 'Alpha Vantage', 'FRED', 'Binance']
        
        trend_data = []
        for source in sources:
            base_coverage = np.random.uniform(80, 95)
            noise = np.random.normal(0, 2, len(dates))
            trend = np.linspace(0, 5, len(dates))  # 轻微上升趋势
            coverage = base_coverage + trend + noise
            coverage = np.clip(coverage, 0, 100)  # 限制在0-100之间
            
            for i, date in enumerate(dates):
                trend_data.append({
                    'date': date,
                    'source': source,
                    'coverage': coverage[i]
                })
        
        df_trends = pd.DataFrame(trend_data)
        
        fig_trends = px.line(
            df_trends,
            x='date',
            y='coverage',
            color='source',
            title='数据源覆盖率趋势',
            labels={'coverage': '覆盖率 (%)', 'date': '日期', 'source': '数据源'}
        )
        
        st.plotly_chart(fig_trends, use_container_width=True)
    
    def _render_source_performance(self, source_name: str):
        """渲染数据源性能分析"""
        # 模拟性能数据
        performance_data = {
            "响应时间": {
                "平均": f"{np.random.uniform(100, 500):.0f}ms",
                "最小": f"{np.random.uniform(50, 100):.0f}ms",
                "最大": f"{np.random.uniform(1000, 2000):.0f}ms",
                "P95": f"{np.random.uniform(800, 1200):.0f}ms"
            },
            "可用性": {
                "今日": f"{np.random.uniform(98, 100):.2f}%",
                "本周": f"{np.random.uniform(97, 99.5):.2f}%",
                "本月": f"{np.random.uniform(96, 99):.2f}%",
                "总体": f"{np.random.uniform(95, 98.5):.2f}%"
            },
            "请求统计": {
                "今日请求": f"{np.random.randint(1000, 5000):,}",
                "成功请求": f"{np.random.randint(950, 4950):,}",
                "失败请求": f"{np.random.randint(10, 100):,}",
                "成功率": f"{np.random.uniform(95, 99.5):.2f}%"
            }
        }
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.write("**响应时间**")
            for key, value in performance_data["响应时间"].items():
                st.write(f"{key}: {value}")
        
        with col2:
            st.write("**可用性**")
            for key, value in performance_data["可用性"].items():
                st.write(f"{key}: {value}")
        
        with col3:
            st.write("**请求统计**")
            for key, value in performance_data["请求统计"].items():
                st.write(f"{key}: {value}")
    
    def _render_market_heatmap(self, market: str):
        """渲染市场热力图"""
        # 模拟热力图数据
        hours = list(range(24))
        days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        
        # 生成随机活跃度数据
        activity_data = []
        for day in days:
            for hour in hours:
                activity = np.random.uniform(0, 100)
                # 模拟交易时间的高活跃度
                if market == "US" and 9 <= hour <= 16:
                    activity *= 1.5
                elif market == "CN" and (9 <= hour <= 11 or 13 <= hour <= 15):
                    activity *= 1.5
                elif market == "CRYPTO":  # 24小时交易
                    activity *= 1.2
                
                activity_data.append({
                    'day': day,
                    'hour': hour,
                    'activity': min(activity, 100)
                })
        
        df_heatmap = pd.DataFrame(activity_data)
        
        # 创建热力图
        pivot_data = df_heatmap.pivot(index='day', columns='hour', values='activity')
        
        fig_heatmap = px.imshow(
            pivot_data,
            title=f'{market} 市场活跃度热力图',
            labels={'x': '小时', 'y': '星期', 'color': '活跃度'},
            aspect='auto'
        )
        
        st.plotly_chart(fig_heatmap, use_container_width=True)
    
    def _get_coverage_data(self) -> List[Dict[str, Any]]:
        """获取覆盖数据"""
        try:
            response = requests.get(
                f"{self.api_base_url}/data-sources/coverage",
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            return []
        except Exception as e:
            st.error(f"获取覆盖数据失败: {str(e)}")
            return []
    
    def _get_data_sources(self) -> List[Dict[str, Any]]:
        """获取数据源列表"""
        try:
            response = requests.get(
                f"{self.api_base_url}/data-sources/sources",
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            return []
        except Exception as e:
            st.error(f"获取数据源列表失败: {str(e)}")
            return []
    
    def _get_source_symbols(self, source_name: str) -> Optional[Dict[str, Any]]:
        """获取数据源的品种列表"""
        try:
            response = requests.get(
                f"{self.api_base_url}/data-sources/symbols",
                params={"source": source_name},
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            st.error(f"获取数据源品种失败: {str(e)}")
            return None
    
    def _get_market_coverage(self, market: str) -> List[Dict[str, Any]]:
        """获取市场覆盖数据"""
        try:
            response = requests.get(
                f"{self.api_base_url}/data-sources/coverage",
                params={"market": market},
                headers=self.headers
            )
            if response.status_code == 200:
                return response.json()
            return []
        except Exception as e:
            st.error(f"获取市场覆盖数据失败: {str(e)}")
            return []