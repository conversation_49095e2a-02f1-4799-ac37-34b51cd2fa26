"""
策略管理主界面

整合所有策略管理组件的主界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, Optional
import threading

from .strategy_list import StrategyListWidget
from .strategy_config import StrategyConfigWidget
from .parameter_optimizer import ParameterOptimizerWidget
from .backtest_monitor import BacktestMonitorWidget
from .portfolio_manager import PortfolioManagerWidget


class StrategyManagementInterface:
    """策略管理主界面"""
    
    def __init__(self, parent: tk.Widget, strategy_manager=None):
        self.parent = parent
        self.strategy_manager = strategy_manager
        
        # 当前选中的策略
        self.current_strategy_id: Optional[str] = None
        
        # 创建界面
        self._create_widgets()
        self._setup_layout()
        self._bind_events()
        
        # 初始化数据
        self._initialize_components()
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        
        # 创建主要的PanedWindow用于分割界面
        self.main_paned = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        
        # 左侧面板 - 策略列表和配置
        self.left_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.left_frame, weight=1)
        
        # 右侧面板 - 功能选项卡
        self.right_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.right_frame, weight=2)
        
        # 创建左侧组件
        self._create_left_panel()
        
        # 创建右侧组件
        self._create_right_panel()
    
    def _create_left_panel(self):
        """创建左侧面板"""
        # 左侧使用垂直PanedWindow分割
        self.left_paned = ttk.PanedWindow(self.left_frame, orient=tk.VERTICAL)
        self.left_paned.pack(fill=tk.BOTH, expand=True)
        
        # 策略列表框架
        self.strategy_list_frame = ttk.LabelFrame(self.left_paned, text="策略列表")
        self.left_paned.add(self.strategy_list_frame, weight=1)
        
        # 创建策略列表组件
        self.strategy_list_widget = StrategyListWidget(
            self.strategy_list_frame,
            self.strategy_manager
        )
        
        # 策略配置框架
        self.strategy_config_frame = ttk.LabelFrame(self.left_paned, text="策略配置")
        self.left_paned.add(self.strategy_config_frame, weight=1)
        
        # 创建策略配置组件
        self.strategy_config_widget = StrategyConfigWidget(
            self.strategy_config_frame,
            self.strategy_manager
        )
    
    def _create_right_panel(self):
        """创建右侧面板"""
        # 创建选项卡
        self.notebook = ttk.Notebook(self.right_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 参数优化页面
        self.optimizer_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.optimizer_frame, text="参数优化")
        
        self.parameter_optimizer_widget = ParameterOptimizerWidget(
            self.optimizer_frame,
            self.strategy_manager
        )
        
        # 回测监控页面
        self.backtest_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.backtest_frame, text="回测监控")
        
        self.backtest_monitor_widget = BacktestMonitorWidget(
            self.backtest_frame,
            self.strategy_manager
        )
        
        # 组合管理页面
        self.portfolio_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.portfolio_frame, text="组合管理")
        
        self.portfolio_manager_widget = PortfolioManagerWidget(
            self.portfolio_frame,
            self.strategy_manager
        )
    
    def _setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def _bind_events(self):
        """绑定事件"""
        # 策略列表选择事件
        self.strategy_list_widget.on_strategy_select = self._on_strategy_select
        self.strategy_list_widget.on_strategy_edit = self._on_strategy_edit
        self.strategy_list_widget.on_strategy_delete = self._on_strategy_delete
        
        # 策略配置事件
        self.strategy_config_widget.on_config_save = self._on_config_save
        self.strategy_config_widget.on_config_cancel = self._on_config_cancel
        
        # 参数优化事件
        self.parameter_optimizer_widget.on_optimization_complete = self._on_optimization_complete
        
        # 回测监控事件
        self.backtest_monitor_widget.on_backtest_complete = self._on_backtest_complete
        
        # 组合管理事件
        self.portfolio_manager_widget.on_portfolio_save = self._on_portfolio_save
        self.portfolio_manager_widget.on_rebalance_complete = self._on_rebalance_complete
    
    def _initialize_components(self):
        """初始化组件"""
        try:
            # 加载策略列表到各个组件
            self.parameter_optimizer_widget.load_strategies()
            self.backtest_monitor_widget.load_strategies()
            self.portfolio_manager_widget.load_strategies()
            
        except Exception as e:
            messagebox.showerror("初始化错误", f"初始化组件失败: {str(e)}")
    
    def _on_strategy_select(self, strategy_id: str):
        """策略选择事件处理"""
        self.current_strategy_id = strategy_id
        
        # 加载策略配置
        self.strategy_config_widget.load_strategy_config(strategy_id)
        
        # 更新状态栏或其他相关组件
        self._update_status(f"已选择策略: {strategy_id}")
    
    def _on_strategy_edit(self, strategy_id: str):
        """策略编辑事件处理"""
        self.current_strategy_id = strategy_id
        
        # 切换到配置页面并加载策略配置
        self.strategy_config_widget.load_strategy_config(strategy_id)
        
        # 可以添加其他编辑相关的逻辑
        self._update_status(f"正在编辑策略: {strategy_id}")
    
    def _on_strategy_delete(self, strategy_id: str):
        """策略删除事件处理"""
        # 如果删除的是当前选中的策略，清空配置
        if self.current_strategy_id == strategy_id:
            self.current_strategy_id = None
            # 可以清空配置界面
        
        # 刷新相关组件
        self.parameter_optimizer_widget.load_strategies()
        self.backtest_monitor_widget.load_strategies()
        self.portfolio_manager_widget.load_strategies()
        
        self._update_status(f"策略 {strategy_id} 已删除")
    
    def _on_config_save(self, strategy_id: str, config_data: Dict[str, Any]):
        """策略配置保存事件处理"""
        try:
            # 刷新策略列表
            self.strategy_list_widget.refresh_strategies()
            
            # 刷新其他组件
            self.parameter_optimizer_widget.load_strategies()
            self.backtest_monitor_widget.load_strategies()
            self.portfolio_manager_widget.load_strategies()
            
            self._update_status(f"策略 {strategy_id} 配置已保存")
            
        except Exception as e:
            messagebox.showerror("保存错误", f"保存策略配置后更新界面失败: {str(e)}")
    
    def _on_config_cancel(self):
        """策略配置取消事件处理"""
        # 可以添加取消编辑的逻辑
        self._update_status("取消策略配置编辑")
    
    def _on_optimization_complete(self, strategy_id: str, best_parameters: Dict[str, Any]):
        """参数优化完成事件处理"""
        try:
            # 询问是否应用优化结果到策略配置
            result = messagebox.askyesno(
                "优化完成",
                f"策略 {strategy_id} 的参数优化已完成。\n是否将最优参数应用到策略配置？"
            )
            
            if result:
                # 加载策略配置并更新参数
                self.strategy_config_widget.load_strategy_config(strategy_id)
                # 这里可以添加自动更新参数的逻辑
                
                # 刷新策略列表
                self.strategy_list_widget.refresh_strategies()
            
            self._update_status(f"策略 {strategy_id} 参数优化完成")
            
        except Exception as e:
            messagebox.showerror("优化错误", f"处理优化结果失败: {str(e)}")
    
    def _on_backtest_complete(self, backtest_results: Dict[str, Any]):
        """回测完成事件处理"""
        try:
            strategy_id = backtest_results.get('config', {}).get('strategy_id', 'Unknown')
            metrics = backtest_results.get('metrics', {})
            
            # 显示回测结果摘要
            summary = f"""
回测完成摘要

策略: {strategy_id}
总收益率: {metrics.get('total_return', 0):.2%}
年化收益率: {metrics.get('annual_return', 0):.2%}
夏普比率: {metrics.get('sharpe_ratio', 0):.3f}
最大回撤: {metrics.get('max_drawdown', 0):.2%}
总交易次数: {metrics.get('total_trades', 0)}
"""
            
            messagebox.showinfo("回测完成", summary)
            
            self._update_status(f"策略 {strategy_id} 回测完成")
            
        except Exception as e:
            messagebox.showerror("回测错误", f"处理回测结果失败: {str(e)}")
    
    def _on_portfolio_save(self, portfolio_config: Dict[str, Any]):
        """组合保存事件处理"""
        try:
            portfolio_name = portfolio_config.get('name', 'Unknown')
            strategy_count = len(portfolio_config.get('strategies', []))
            
            messagebox.showinfo(
                "组合保存成功",
                f"组合 '{portfolio_name}' 已保存\n包含 {strategy_count} 个策略"
            )
            
            self._update_status(f"组合 {portfolio_name} 已保存")
            
        except Exception as e:
            messagebox.showerror("保存错误", f"处理组合保存失败: {str(e)}")
    
    def _on_rebalance_complete(self):
        """重平衡完成事件处理"""
        try:
            messagebox.showinfo("重平衡完成", "策略组合重平衡已完成")
            self._update_status("组合重平衡完成")
            
        except Exception as e:
            messagebox.showerror("重平衡错误", f"处理重平衡结果失败: {str(e)}")
    
    def _update_status(self, message: str):
        """更新状态信息"""
        # 这里可以更新状态栏或日志
        print(f"[策略管理] {message}")
    
    def refresh_all_components(self):
        """刷新所有组件"""
        """刷新所有组件的数据"""
        try:
            # 刷新策略列表
            self.strategy_list_widget.refresh_strategies()
            
            # 刷新其他组件的策略列表
            self.parameter_optimizer_widget.load_strategies()
            self.backtest_monitor_widget.load_strategies()
            self.portfolio_manager_widget.load_strategies()
            
            self._update_status("所有组件已刷新")
            
        except Exception as e:
            messagebox.showerror("刷新错误", f"刷新组件失败: {str(e)}")
    
    def get_current_strategy(self) -> Optional[str]:
        """获取当前选中的策略ID"""
        return self.current_strategy_id
    
    def select_strategy(self, strategy_id: str):
        """选中指定策略"""
        self.strategy_list_widget.select_strategy(strategy_id)
    
    def switch_to_optimizer(self):
        """切换到参数优化页面"""
        self.notebook.select(0)
    
    def switch_to_backtest(self):
        """切换到回测监控页面"""
        self.notebook.select(1)
    
    def switch_to_portfolio(self):
        """切换到组合管理页面"""
        self.notebook.select(2)
    
    def export_strategy_data(self, strategy_id: str) -> Dict[str, Any]:
        """导出策略数据"""
        try:
            # 这里可以收集策略的所有相关数据
            strategy_data = {
                'strategy_id': strategy_id,
                'config': {},  # 从配置组件获取
                'optimization_results': {},  # 从优化组件获取
                'backtest_results': {},  # 从回测组件获取
                'portfolio_allocation': {}  # 从组合组件获取
            }
            
            return strategy_data
            
        except Exception as e:
            messagebox.showerror("导出��误", f"导出策略数据失败: {str(e)}")
            return {}
    
    def import_strategy_data(self, strategy_data: Dict[str, Any]):
        """导入策略数据"""
        try:
            strategy_id = strategy_data.get('strategy_id')
            if not strategy_id:
                raise ValueError("无效的策略数据")
            
            # 这里可以将数据导入到各个组件
            # 例如：更新配置、优化结果、回测结果等
            
            # 刷新界面
            self.refresh_all_components()
            
            # 选中导入的策略
            self.select_strategy(strategy_id)
            
            messagebox.showinfo("导入成功", f"策略 {strategy_id} 数据已导入")
            
        except Exception as e:
            messagebox.showerror("导入错误", f"导入策略数据失败: {str(e)}")


def create_strategy_management_window(strategy_manager=None):
    """创建策略管理窗口的便捷函数"""
    root = tk.Tk()
    root.title("量化交易系统 - 策略管理")
    root.geometry("1400x900")
    
    # 设置窗口图标（如果有的话）
    # root.iconbitmap("icon.ico")
    
    # 创建策略管理界面
    strategy_interface = StrategyManagementInterface(root, strategy_manager)
    
    # 创建菜单栏
    menubar = tk.Menu(root)
    root.config(menu=menubar)
    
    # 文件菜单
    file_menu = tk.Menu(menubar, tearoff=0)
    menubar.add_cascade(label="文件", menu=file_menu)
    file_menu.add_command(label="新建策略", command=lambda: None)
    file_menu.add_command(label="导入策略", command=lambda: None)
    file_menu.add_command(label="导出策略", command=lambda: None)
    file_menu.add_separator()
    file_menu.add_command(label="退出", command=root.quit)
    
    # 视图菜单
    view_menu = tk.Menu(menubar, tearoff=0)
    menubar.add_cascade(label="视图", menu=view_menu)
    view_menu.add_command(label="参数优化", command=strategy_interface.switch_to_optimizer)
    view_menu.add_command(label="回测监控", command=strategy_interface.switch_to_backtest)
    view_menu.add_command(label="组合管理", command=strategy_interface.switch_to_portfolio)
    view_menu.add_separator()
    view_menu.add_command(label="刷新", command=strategy_interface.refresh_all_components)
    
    # 工具菜单
    tools_menu = tk.Menu(menubar, tearoff=0)
    menubar.add_cascade(label="工具", menu=tools_menu)
    tools_menu.add_command(label="数据管理", command=lambda: messagebox.showinfo("提示", "数据管理功能待实现"))
    tools_menu.add_command(label="系统设置", command=lambda: messagebox.showinfo("提示", "系统设置功能待实现"))
    
    # 帮助菜单
    help_menu = tk.Menu(menubar, tearoff=0)
    menubar.add_cascade(label="帮助", menu=help_menu)
    help_menu.add_command(label="使用说明", command=lambda: messagebox.showinfo("使用说明", "策略管理系统使用说明"))
    help_menu.add_command(label="关于", command=lambda: messagebox.showinfo("关于", "量化交易系统 v1.0"))
    
    return root, strategy_interface


if __name__ == "__main__":
    # 测试运行
    root, interface = create_strategy_management_window()
    root.mainloop()