"""
策略管理界面演示脚本

用于演示策略管理界面的功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

import tkinter as tk
from tkinter import ttk, messagebox
from src.ui.strategy_management.main_interface import create_strategy_management_window


def main():
    """主函数"""
    try:
        # 创建策略管理窗口
        root, strategy_interface = create_strategy_management_window()
        
        # 设置窗口关闭事件
        def on_closing():
            if messagebox.askokcancel("退出", "确定要退出策略管理系统吗？"):
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        # 显示欢迎信息
        messagebox.showinfo(
            "欢迎",
            "欢迎使用量化交易系统策略管理界面！\n\n"
            "功能包括：\n"
            "• 策略列表和配置管理\n"
            "• 策略参数优化工具\n"
            "• 回测启动和监控\n"
            "• 策略组合管理和权重设置\n\n"
            "请从左侧策略列表开始使用。"
        )
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("启动错误", f"启动策略管理界面失败: {str(e)}")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()