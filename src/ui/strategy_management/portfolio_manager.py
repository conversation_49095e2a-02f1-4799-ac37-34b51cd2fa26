"""
策略组合管理组件

提供策略组合管理和权重设置功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, List, Optional, Callable
import json
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import pandas as pd

try:
    from ...strategies.multi_strategy_manager import StrategyPortfolioManager, WeightingMethod, RebalanceFrequency
    from ...strategies.base import BaseStrategy
except ImportError:
    # Fallback for testing
    pass


class PortfolioManagerWidget:
    """策略组合管理组件"""
    
    def __init__(self, parent: tk.Widget, strategy_manager=None):
        self.parent = parent
        self.strategy_manager = strategy_manager
        self.portfolio_manager = self._create_portfolio_manager()
        
        # 当前组合配置
        self.current_portfolio: Dict[str, Any] = {}
        self.strategy_allocations: Dict[str, Dict[str, Any]] = {}
        
        # 回调函数
        self.on_portfolio_save: Optional[Callable] = None
        self.on_rebalance_complete: Optional[Callable] = None
        
        # 创建界面
        self._create_widgets()
        self._setup_layout()
        self._bind_events()
        
        # 初始化数据
        self._load_available_strategies()
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        
        # 创建Notebook用于分页显示
        self.notebook = ttk.Notebook(self.main_frame)
        
        # 组合配置页面
        self.config_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.config_frame, text="组合配置")
        
        # 权重管理页面
        self.weights_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.weights_frame, text="权重管理")
        
        # 风险分析页面
        self.risk_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.risk_frame, text="风险分析")
        
        # 创建各页面内容
        self._create_config_widgets()
        self._create_weights_widgets()
        self._create_risk_widgets()
    
    def _create_config_widgets(self):
        """创建组合配置组件"""
        # 基本设置框架
        basic_frame = ttk.LabelFrame(self.config_frame, text="基本设置")
        basic_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 组合名称
        ttk.Label(basic_frame, text="组合名称:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.portfolio_name_var = tk.StringVar(value="默认策略组合")
        self.portfolio_name_entry = ttk.Entry(basic_frame, textvariable=self.portfolio_name_var, width=30)
        self.portfolio_name_entry.grid(row=0, column=1, padx=5, pady=5, sticky='ew')
        
        # 初始资金
        ttk.Label(basic_frame, text="初始资金:").grid(row=0, column=2, sticky='w', padx=5, pady=5)
        self.initial_capital_var = tk.DoubleVar(value=1000000.0)
        self.initial_capital_spin = ttk.Spinbox(
            basic_frame,
            from_=100000,
            to=100000000,
            increment=100000,
            textvariable=self.initial_capital_var,
            width=15
        )
        self.initial_capital_spin.grid(row=0, column=3, padx=5, pady=5)
        
        basic_frame.grid_columnconfigure(1, weight=1)
        
        # 权重方法框架
        method_frame = ttk.LabelFrame(self.config_frame, text="权重分配方法")
        method_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.weighting_method_var = tk.StringVar(value="equal_weight")
        
        methods = [
            ("equal_weight", "等权重"),
            ("performance_based", "基于绩效"),
            ("risk_parity", "风险平价"),
            ("sharpe_ratio", "基于夏普比率"),
            ("inverse_volatility", "反波动率"),
            ("custom", "自定义")
        ]
        
        for i, (value, text) in enumerate(methods):
            row, col = divmod(i, 3)
            ttk.Radiobutton(
                method_frame,
                text=text,
                variable=self.weighting_method_var,
                value=value,
                command=self._on_weighting_method_change
            ).grid(row=row, column=col, sticky='w', padx=10, pady=5)
        
        # 重平衡设置框架
        rebalance_frame = ttk.LabelFrame(self.config_frame, text="重平衡设置")
        rebalance_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 重平衡频率
        ttk.Label(rebalance_frame, text="重平衡频率:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.rebalance_frequency_var = tk.StringVar(value="monthly")
        self.rebalance_frequency_combo = ttk.Combobox(
            rebalance_frame,
            textvariable=self.rebalance_frequency_var,
            values=['daily', 'weekly', 'monthly', 'quarterly'],
            state='readonly',
            width=15
        )
        self.rebalance_frequency_combo.grid(row=0, column=1, padx=5, pady=5)
        
        # 重平衡阈值
        ttk.Label(rebalance_frame, text="重平衡阈值:").grid(row=0, column=2, sticky='w', padx=5, pady=5)
        self.rebalance_threshold_var = tk.DoubleVar(value=0.05)
        self.rebalance_threshold_spin = ttk.Spinbox(
            rebalance_frame,
            from_=0.01,
            to=0.5,
            increment=0.01,
            textvariable=self.rebalance_threshold_var,
            width=10
        )
        self.rebalance_threshold_spin.grid(row=0, column=3, padx=5, pady=5)
        
        # 策略选择框架
        strategy_selection_frame = ttk.LabelFrame(self.config_frame, text="策略选择")
        strategy_selection_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 可用策略列表
        available_frame = ttk.Frame(strategy_selection_frame)
        available_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        ttk.Label(available_frame, text="可用策略").pack()
        
        self.available_strategies_listbox = tk.Listbox(available_frame, selectmode=tk.EXTENDED)
        available_scrollbar = ttk.Scrollbar(available_frame, orient="vertical", command=self.available_strategies_listbox.yview)
        self.available_strategies_listbox.configure(yscrollcommand=available_scrollbar.set)
        
        self.available_strategies_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        available_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 操作按钮
        buttons_frame = ttk.Frame(strategy_selection_frame)
        buttons_frame.pack(side=tk.LEFT, padx=10, pady=5)
        
        self.add_strategy_btn = ttk.Button(
            buttons_frame,
            text="添加 →",
            command=self._on_add_strategy
        )
        self.add_strategy_btn.pack(pady=5)
        
        self.remove_strategy_btn = ttk.Button(
            buttons_frame,
            text="← 移除",
            command=self._on_remove_strategy
        )
        self.remove_strategy_btn.pack(pady=5)
        
        self.add_all_btn = ttk.Button(
            buttons_frame,
            text="全部添加",
            command=self._on_add_all_strategies
        )
        self.add_all_btn.pack(pady=5)
        
        self.remove_all_btn = ttk.Button(
            buttons_frame,
            text="全部移除",
            command=self._on_remove_all_strategies
        )
        self.remove_all_btn.pack(pady=5)
        
        # 已选策略列表
        selected_frame = ttk.Frame(strategy_selection_frame)
        selected_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        ttk.Label(selected_frame, text="已选策略").pack()
        
        self.selected_strategies_listbox = tk.Listbox(selected_frame, selectmode=tk.EXTENDED)
        selected_scrollbar = ttk.Scrollbar(selected_frame, orient="vertical", command=self.selected_strategies_listbox.yview)
        self.selected_strategies_listbox.configure(yscrollcommand=selected_scrollbar.set)
        
        self.selected_strategies_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        selected_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 配置操作按钮
        config_buttons_frame = ttk.Frame(self.config_frame)
        config_buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.save_portfolio_btn = ttk.Button(
            config_buttons_frame,
            text="保存组合",
            command=self._on_save_portfolio
        )
        self.save_portfolio_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.load_portfolio_btn = ttk.Button(
            config_buttons_frame,
            text="加载组合",
            command=self._on_load_portfolio
        )
        self.load_portfolio_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.reset_portfolio_btn = ttk.Button(
            config_buttons_frame,
            text="重置",
            command=self._on_reset_portfolio
        )
        self.reset_portfolio_btn.pack(side=tk.LEFT)
    
    def _create_weights_widgets(self):
        """创建权重管理组件"""
        # 权重配置框架
        weights_config_frame = ttk.LabelFrame(self.weights_frame, text="权重配置")
        weights_config_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 权重表格
        self.weights_tree = ttk.Treeview(
            weights_config_frame,
            columns=('current_weight', 'target_weight', 'min_weight', 'max_weight', 'allocated_capital', 'performance'),
            show='tree headings',
            height=10
        )
        
        self.weights_tree.heading('#0', text='策略名称')
        self.weights_tree.heading('current_weight', text='当前权重')
        self.weights_tree.heading('target_weight', text='目标权重')
        self.weights_tree.heading('min_weight', text='最小权重')
        self.weights_tree.heading('max_weight', text='最大权重')
        self.weights_tree.heading('allocated_capital', text='分配资金')
        self.weights_tree.heading('performance', text='绩效')
        
        self.weights_tree.column('#0', width=120, minwidth=80)
        self.weights_tree.column('current_weight', width=80, minwidth=60)
        self.weights_tree.column('target_weight', width=80, minwidth=60)
        self.weights_tree.column('min_weight', width=80, minwidth=60)
        self.weights_tree.column('max_weight', width=80, minwidth=60)
        self.weights_tree.column('allocated_capital', width=100, minwidth=80)
        self.weights_tree.column('performance', width=80, minwidth=60)
        
        weights_scrollbar = ttk.Scrollbar(
            weights_config_frame,
            orient="vertical",
            command=self.weights_tree.yview
        )
        self.weights_tree.configure(yscrollcommand=weights_scrollbar.set)
        
        self.weights_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        weights_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 权重操作按钮
        weights_buttons_frame = ttk.Frame(self.weights_frame)
        weights_buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.calculate_weights_btn = ttk.Button(
            weights_buttons_frame,
            text="计算最优权重",
            command=self._on_calculate_optimal_weights
        )
        self.calculate_weights_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.apply_weights_btn = ttk.Button(
            weights_buttons_frame,
            text="应用权重",
            command=self._on_apply_weights
        )
        self.apply_weights_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.rebalance_btn = ttk.Button(
            weights_buttons_frame,
            text="执行重平衡",
            command=self._on_execute_rebalance
        )
        self.rebalance_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.equal_weights_btn = ttk.Button(
            weights_buttons_frame,
            text="等权重分配",
            command=self._on_set_equal_weights
        )
        self.equal_weights_btn.pack(side=tk.LEFT)
        
        # 权重图表框架
        chart_frame = ttk.LabelFrame(self.weights_frame, text="权重分布图")
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建饼图
        self.weights_fig, self.weights_ax = plt.subplots(figsize=(8, 6))
        self.weights_canvas = FigureCanvasTkAgg(self.weights_fig, chart_frame)
        self.weights_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def _create_risk_widgets(self):
        """创建风险分析组件"""
        # 风险指标框架
        risk_metrics_frame = ttk.LabelFrame(self.risk_frame, text="风险指标")
        risk_metrics_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 风险指标网格
        metrics_grid = ttk.Frame(risk_metrics_frame)
        metrics_grid.pack(fill=tk.X, padx=5, pady=5)
        
        # 第一行指标
        ttk.Label(metrics_grid, text="组合波动率:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.portfolio_volatility_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.portfolio_volatility_var).grid(row=0, column=1, sticky='w', padx=5, pady=2)
        
        ttk.Label(metrics_grid, text="VaR (95%):").grid(row=0, column=2, sticky='w', padx=5, pady=2)
        self.var_95_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.var_95_var).grid(row=0, column=3, sticky='w', padx=5, pady=2)
        
        # 第二行指标
        ttk.Label(metrics_grid, text="最大权重:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.max_weight_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.max_weight_var).grid(row=1, column=1, sticky='w', padx=5, pady=2)
        
        ttk.Label(metrics_grid, text="集中度指数:").grid(row=1, column=2, sticky='w', padx=5, pady=2)
        self.concentration_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.concentration_var).grid(row=1, column=3, sticky='w', padx=5, pady=2)
        
        # 相关性分析框架
        correlation_frame = ttk.LabelFrame(self.risk_frame, text="策略相关性分析")
        correlation_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 相关性矩阵图
        self.corr_fig, self.corr_ax = plt.subplots(figsize=(8, 6))
        self.corr_canvas = FigureCanvasTkAgg(self.corr_fig, correlation_frame)
        self.corr_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 风险分析按钮
        risk_buttons_frame = ttk.Frame(self.risk_frame)
        risk_buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.analyze_risk_btn = ttk.Button(
            risk_buttons_frame,
            text="分析风险",
            command=self._on_analyze_risk
        )
        self.analyze_risk_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stress_test_btn = ttk.Button(
            risk_buttons_frame,
            text="压力测试",
            command=self._on_stress_test
        )
        self.stress_test_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.monte_carlo_btn = ttk.Button(
            risk_buttons_frame,
            text="蒙特卡洛模拟",
            command=self._on_monte_carlo_simulation
        )
        self.monte_carlo_btn.pack(side=tk.LEFT)
    
    def _setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def _bind_events(self):
        """绑定事件"""
        self.weights_tree.bind('<Double-1>', self._on_weight_double_click)
        self.selected_strategies_listbox.bind('<Double-1>', self._on_strategy_double_click)
    
    def _create_portfolio_manager(self):
        """创建组合管理器"""
        # 模拟创建组合管理器
        class MockPortfolioManager:
            def __init__(self):
                self.strategies = {}
                self.allocations = {}
            
            def add_strategy(self, strategy_id, weight, min_weight=0.0, max_weight=1.0):
                self.allocations[strategy_id] = {
                    'weight': weight,
                    'min_weight': min_weight,
                    'max_weight': max_weight
                }
                return strategy_id
            
            def calculate_optimal_weights(self):
                # 模拟最优权重计算
                if not self.allocations:
                    return {}
                
                n_strategies = len(self.allocations)
                equal_weight = 1.0 / n_strategies
                
                return {sid: equal_weight for sid in self.allocations.keys()}
            
            def update_weights(self, new_weights, trigger_reason="manual"):
                for strategy_id, weight in new_weights.items():
                    if strategy_id in self.allocations:
                        self.allocations[strategy_id]['weight'] = weight
        
        return MockPortfolioManager()
    
    def _load_available_strategies(self):
        """加载可用策略列表"""
        try:
            # 模拟策略列表
            strategies = [
                {'id': 'strategy_1', 'name': '移动平均策略'},
                {'id': 'strategy_2', 'name': 'RSI均值回归'},
                {'id': 'strategy_3', 'name': 'MACD趋势跟踪'},
                {'id': 'strategy_4', 'name': '布林带策略'},
                {'id': 'strategy_5', 'name': '动量策略'}
            ]
            
            # 清空列表
            self.available_strategies_listbox.delete(0, tk.END)
            
            # 添加策略到列表
            for strategy in strategies:
                display_text = f"{strategy['name']} ({strategy['id']})"
                self.available_strategies_listbox.insert(tk.END, display_text)
                
        except Exception as e:
            messagebox.showerror("错误", f"加载策略列表失败: {str(e)}")
    
    def _on_weighting_method_change(self):
        """权重方法变化事件"""
        method = self.weighting_method_var.get()
        
        # 根据不同方法调整界面
        if method == "custom":
            # 自定义权重时启用手动编辑
            pass
        else:
            # 自动计算权重
            self._calculate_weights_by_method(method)
    
    def _calculate_weights_by_method(self, method: str):
        """根据方法计算权重"""
        selected_strategies = list(self.selected_strategies_listbox.get(0, tk.END))
        if not selected_strategies:
            return
        
        n_strategies = len(selected_strategies)
        
        if method == "equal_weight":
            weight = 1.0 / n_strategies
            weights = {strategy: weight for strategy in selected_strategies}
        elif method == "performance_based":
            # 模拟基于绩效的权重
            weights = self._calculate_performance_weights(selected_strategies)
        elif method == "risk_parity":
            # 模拟风险平价权重
            weights = self._calculate_risk_parity_weights(selected_strategies)
        else:
            # 默认等权重
            weight = 1.0 / n_strategies
            weights = {strategy: weight for strategy in selected_strategies}
        
        # 更新权重显示
        self._update_weights_display(weights)
    
    def _calculate_performance_weights(self, strategies: List[str]) -> Dict[str, float]:
        """计算基于绩效的权重"""
        # 模拟绩效数据
        performance_scores = {}
        for strategy in strategies:
            # 随机生成绩效分数
            performance_scores[strategy] = np.random.uniform(0.5, 2.0)
        
        # 计算权重
        total_score = sum(performance_scores.values())
        weights = {strategy: score / total_score for strategy, score in performance_scores.items()}
        
        return weights
    
    def _calculate_risk_parity_weights(self, strategies: List[str]) -> Dict[str, float]:
        """计算风险平价权重"""
        # 模拟风险数据
        risk_scores = {}
        for strategy in strategies:
            # 随机生成风险分数（波动率）
            risk_scores[strategy] = np.random.uniform(0.1, 0.4)
        
        # 计算反风险权重
        inv_risk_sum = sum(1 / risk for risk in risk_scores.values())
        weights = {strategy: (1 / risk) / inv_risk_sum for strategy, risk in risk_scores.items()}
        
        return weights
    
    def _on_add_strategy(self):
        """添加策略到组合"""
        selection = self.available_strategies_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请选择要添加的策略")
            return
        
        for index in selection:
            strategy_text = self.available_strategies_listbox.get(index)
            
            # 检查是否已存在
            current_strategies = list(self.selected_strategies_listbox.get(0, tk.END))
            if strategy_text not in current_strategies:
                self.selected_strategies_listbox.insert(tk.END, strategy_text)
        
        # 重新计算权重
        self._on_weighting_method_change()
    
    def _on_remove_strategy(self):
        """从组合中移除策略"""
        selection = self.selected_strategies_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请选择要移除的策略")
            return
        
        # 从后往前删除，避免索引变化
        for index in reversed(selection):
            self.selected_strategies_listbox.delete(index)
        
        # 重新计算权重
        self._on_weighting_method_change()
    
    def _on_add_all_strategies(self):
        """添加所有策略"""
        all_strategies = list(self.available_strategies_listbox.get(0, tk.END))
        
        self.selected_strategies_listbox.delete(0, tk.END)
        for strategy in all_strategies:
            self.selected_strategies_listbox.insert(tk.END, strategy)
        
        # 重新计算权重
        self._on_weighting_method_change()
    
    def _on_remove_all_strategies(self):
        """移除所有策略"""
        self.selected_strategies_listbox.delete(0, tk.END)
        
        # 清空权重显示
        for item in self.weights_tree.get_children():
            self.weights_tree.delete(item)
        
        # 清空图表
        self._clear_charts()
    
    def _update_weights_display(self, weights: Dict[str, float]):
        """更新权重显示"""
        # 清空现有项目
        for item in self.weights_tree.get_children():
            self.weights_tree.delete(item)
        
        # 添加权重信息
        total_capital = self.initial_capital_var.get()
        
        for strategy_text, weight in weights.items():
            # 提取策略ID
            strategy_id = strategy_text.split('(')[-1].rstrip(')')
            
            allocated_capital = total_capital * weight
            
            # 模拟绩效数据
            performance = np.random.uniform(-0.1, 0.3)
            
            self.weights_tree.insert(
                '',
                'end',
                text=strategy_text,
                values=(
                    f"{weight:.2%}",
                    f"{weight:.2%}",  # 目标权重初始等于当前权重
                    "0.00%",  # 最小权重
                    "100.00%",  # 最大权重
                    f"${allocated_capital:,.0f}",
                    f"{performance:.2%}"
                )
            )
        
        # 更新权重图表
        self._update_weights_chart(weights)
    
    def _update_weights_chart(self, weights: Dict[str, float]):
        """更新权重分布图"""
        try:
            self.weights_ax.clear()
            
            if weights:
                # 提取策略名称（去掉ID部分）
                labels = [name.split(' (')[0] for name in weights.keys()]
                sizes = list(weights.values())
                
                # 创建饼图
                wedges, texts, autotexts = self.weights_ax.pie(
                    sizes,
                    labels=labels,
                    autopct='%1.1f%%',
                    startangle=90
                )
                
                self.weights_ax.set_title("策略权重分布")
                
                # 设置文本属性
                for autotext in autotexts:
                    autotext.set_color('white')
                    autotext.set_fontweight('bold')
            
            self.weights_canvas.draw()
            
        except Exception as e:
            print(f"更新权重图表失败: {str(e)}")
    
    def _clear_charts(self):
        """清空图表"""
        self.weights_ax.clear()
        self.weights_ax.set_title("策略权重分布")
        self.weights_canvas.draw()
        
        self.corr_ax.clear()
        self.corr_ax.set_title("策略相关性矩阵")
        self.corr_canvas.draw()
    
    def _on_calculate_optimal_weights(self):
        """计算最优权重"""
        selected_strategies = list(self.selected_strategies_listbox.get(0, tk.END))
        if not selected_strategies:
            messagebox.showwarning("警告", "请先选择策略")
            return
        
        try:
            # 使用当前选择的权重方法计算
            method = self.weighting_method_var.get()
            self._calculate_weights_by_method(method)
            
            messagebox.showinfo("成功", "最优权重计算完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"计算最优权重失败: {str(e)}")
    
    def _on_apply_weights(self):
        """应用权重设置"""
        try:
            # 收集当前权重设置
            weights = {}
            for item in self.weights_tree.get_children():
                strategy_text = self.weights_tree.item(item, 'text')
                target_weight_str = self.weights_tree.item(item, 'values')[1]
                target_weight = float(target_weight_str.rstrip('%')) / 100
                weights[strategy_text] = target_weight
            
            # 验证权重总和
            total_weight = sum(weights.values())
            if abs(total_weight - 1.0) > 0.01:
                messagebox.showerror("错误", f"权重总和必须为100%，当前为{total_weight:.1%}")
                return
            
            # 应用权重到组合管理器
            for strategy_text, weight in weights.items():
                strategy_id = strategy_text.split('(')[-1].rstrip(')')
                self.portfolio_manager.add_strategy(strategy_id, weight)
            
            messagebox.showinfo("成功", "权重设置已应用")
            
        except Exception as e:
            messagebox.showerror("错误", f"应用权重失败: {str(e)}")
    
    def _on_execute_rebalance(self):
        """执行重平衡"""
        try:
            # 模拟重平衡过程
            result = messagebox.askyesno("确认重平衡", "确定要执行组合重平衡吗？")
            if result:
                # 这里应该调用实际的重平衡逻辑
                messagebox.showinfo("成功", "重平衡执行完成")
                
                if self.on_rebalance_complete:
                    self.on_rebalance_complete()
                    
        except Exception as e:
            messagebox.showerror("错误", f"执行重平衡失败: {str(e)}")
    
    def _on_set_equal_weights(self):
        """设置等权重"""
        selected_strategies = list(self.selected_strategies_listbox.get(0, tk.END))
        if not selected_strategies:
            messagebox.showwarning("警告", "请先选择策略")
            return
        
        n_strategies = len(selected_strategies)
        equal_weight = 1.0 / n_strategies
        
        weights = {strategy: equal_weight for strategy in selected_strategies}
        self._update_weights_display(weights)
    
    def _on_weight_double_click(self, event):
        """权重双击编辑事件"""
        item = self.weights_tree.selection()[0] if self.weights_tree.selection() else None
        if item:
            strategy_text = self.weights_tree.item(item, 'text')
            current_values = self.weights_tree.item(item, 'values')
            
            # 打开权重编辑对话框
            dialog = WeightEditDialog(self.parent, strategy_text, current_values)
            if dialog.result:
                # 更新权重
                self.weights_tree.item(item, values=dialog.result)
    
    def _on_strategy_double_click(self, event):
        """策略双击事件"""
        selection = self.selected_strategies_listbox.curselection()
        if selection:
            strategy_text = self.selected_strategies_listbox.get(selection[0])
            strategy_id = strategy_text.split('(')[-1].rstrip(')')
            
            # 显示策略详情
            messagebox.showinfo("策略详情", f"策略ID: {strategy_id}\n策略名称: {strategy_text}")
    
    def _on_analyze_risk(self):
        """分析风险"""
        selected_strategies = list(self.selected_strategies_listbox.get(0, tk.END))
        if not selected_strategies:
            messagebox.showwarning("警告", "请先选择策略")
            return
        
        try:
            # 模拟风险分析
            portfolio_vol = np.random.uniform(0.15, 0.25)
            var_95 = np.random.uniform(0.02, 0.05)
            max_weight = 1.0 / len(selected_strategies)
            concentration = np.random.uniform(0.3, 0.7)
            
            # 更新风险指标显示
            self.portfolio_volatility_var.set(f"{portfolio_vol:.2%}")
            self.var_95_var.set(f"{var_95:.2%}")
            self.max_weight_var.set(f"{max_weight:.2%}")
            self.concentration_var.set(f"{concentration:.2f}")
            
            # 生成相关性矩阵
            self._generate_correlation_matrix(selected_strategies)
            
            messagebox.showinfo("成功", "风险分析完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"风险分析失败: {str(e)}")
    
    def _generate_correlation_matrix(self, strategies: List[str]):
        """生成相关性矩阵图"""
        try:
            n_strategies = len(strategies)
            if n_strategies < 2:
                return
            
            # 生成随机相关性矩阵
            corr_matrix = np.random.uniform(-0.5, 0.8, (n_strategies, n_strategies))
            
            # 确保对角线为1，矩阵对称
            for i in range(n_strategies):
                corr_matrix[i, i] = 1.0
                for j in range(i):
                    corr_matrix[j, i] = corr_matrix[i, j]
            
            # 清空图表
            self.corr_ax.clear()
            
            # 绘制热力图
            im = self.corr_ax.imshow(corr_matrix, cmap='coolwarm', vmin=-1, vmax=1)
            
            # 设置标签
            strategy_names = [name.split(' (')[0] for name in strategies]
            self.corr_ax.set_xticks(range(n_strategies))
            self.corr_ax.set_yticks(range(n_strategies))
            self.corr_ax.set_xticklabels(strategy_names, rotation=45, ha='right')
            self.corr_ax.set_yticklabels(strategy_names)
            
            # 添加数值标注
            for i in range(n_strategies):
                for j in range(n_strategies):
                    text = self.corr_ax.text(j, i, f'{corr_matrix[i, j]:.2f}',
                                           ha="center", va="center", color="black")
            
            self.corr_ax.set_title("策略相关性矩阵")
            
            # 添加颜色条
            self.corr_fig.colorbar(im, ax=self.corr_ax)
            
            self.corr_canvas.draw()
            
        except Exception as e:
            print(f"生成相关性矩阵失败: {str(e)}")
    
    def _on_stress_test(self):
        """压力测试"""
        messagebox.showinfo("提示", "压力测试功能待实现")
    
    def _on_monte_carlo_simulation(self):
        """蒙特卡洛模拟"""
        messagebox.showinfo("提示", "蒙特卡洛模拟功能待实现")
    
    def _on_save_portfolio(self):
        """保存组合配置"""
        try:
            # 收集组合配置
            portfolio_config = {
                'name': self.portfolio_name_var.get(),
                'initial_capital': self.initial_capital_var.get(),
                'weighting_method': self.weighting_method_var.get(),
                'rebalance_frequency': self.rebalance_frequency_var.get(),
                'rebalance_threshold': self.rebalance_threshold_var.get(),
                'strategies': list(self.selected_strategies_listbox.get(0, tk.END)),
                'weights': {},
                'created_at': datetime.now().isoformat()
            }
            
            # 收集权重信息
            for item in self.weights_tree.get_children():
                strategy_text = self.weights_tree.item(item, 'text')
                values = self.weights_tree.item(item, 'values')
                portfolio_config['weights'][strategy_text] = {
                    'current_weight': values[0],
                    'target_weight': values[1],
                    'min_weight': values[2],
                    'max_weight': values[3]
                }
            
            # 模拟保存
            filename = f"portfolio_{portfolio_config['name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            print(f"保存组合配置到文件: {filename}")
            print(json.dumps(portfolio_config, indent=2, ensure_ascii=False))
            
            messagebox.showinfo("成功", f"组合配置已保存到 {filename}")
            
            if self.on_portfolio_save:
                self.on_portfolio_save(portfolio_config)
                
        except Exception as e:
            messagebox.showerror("错误", f"保存组合配置失败: {str(e)}")
    
    def _on_load_portfolio(self):
        """加载组合配置"""
        messagebox.showinfo("提示", "加载组合配置功能待实现")
    
    def _on_reset_portfolio(self):
        """重置组合配置"""
        result = messagebox.askyesno("确认重置", "确定要重置所有组合配置吗？")
        if result:
            # 重置所有设置
            self.portfolio_name_var.set("默认策略组合")
            self.initial_capital_var.set(1000000.0)
            self.weighting_method_var.set("equal_weight")
            self.rebalance_frequency_var.set("monthly")
            self.rebalance_threshold_var.set(0.05)
            
            # 清空策略选择
            self.selected_strategies_listbox.delete(0, tk.END)
            
            # 清空权重表格
            for item in self.weights_tree.get_children():
                self.weights_tree.delete(item)
            
            # 清空图表
            self._clear_charts()
            
            # 重置风险指标
            self.portfolio_volatility_var.set("N/A")
            self.var_95_var.set("N/A")
            self.max_weight_var.set("N/A")
            self.concentration_var.set("N/A")


class WeightEditDialog:
    """权重编辑对话框"""
    
    def __init__(self, parent, strategy_name: str, current_values: tuple):
        self.parent = parent
        self.strategy_name = strategy_name
        self.current_values = current_values
        self.result = None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"编辑权重: {strategy_name}")
        self.dialog.geometry("350x250")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 100,
            parent.winfo_rooty() + 100
        ))
        
        self._create_widgets()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def _create_widgets(self):
        """创建对话框组件"""
        # 当前权重
        ttk.Label(self.dialog, text="当前权重:").grid(row=0, column=0, sticky='w', padx=10, pady=5)
        self.current_var = tk.StringVar(value=self.current_values[0])
        ttk.Entry(self.dialog, textvariable=self.current_var, width=15, state='readonly').grid(row=0, column=1, padx=10, pady=5)
        
        # 目标权重
        ttk.Label(self.dialog, text="目标权重:").grid(row=1, column=0, sticky='w', padx=10, pady=5)
        self.target_var = tk.StringVar(value=self.current_values[1])
        ttk.Entry(self.dialog, textvariable=self.target_var, width=15).grid(row=1, column=1, padx=10, pady=5)
        
        # 最小权重
        ttk.Label(self.dialog, text="最小权重:").grid(row=2, column=0, sticky='w', padx=10, pady=5)
        self.min_var = tk.StringVar(value=self.current_values[2])
        ttk.Entry(self.dialog, textvariable=self.min_var, width=15).grid(row=2, column=1, padx=10, pady=5)
        
        # 最大权重
        ttk.Label(self.dialog, text="最大权重:").grid(row=3, column=0, sticky='w', padx=10, pady=5)
        self.max_var = tk.StringVar(value=self.current_values[3])
        ttk.Entry(self.dialog, textvariable=self.max_var, width=15).grid(row=3, column=1, padx=10, pady=5)
        
        # 按钮
        button_frame = ttk.Frame(self.dialog)
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="确定", command=self._on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self._on_cancel).pack(side=tk.LEFT, padx=5)
    
    def _on_ok(self):
        """确定"""
        try:
            # 验证输入
            target_weight = float(self.target_var.get().rstrip('%'))
            min_weight = float(self.min_var.get().rstrip('%'))
            max_weight = float(self.max_var.get().rstrip('%'))
            
            if min_weight > max_weight:
                raise ValueError("最小权重不能大于最大权重")
            
            if not (min_weight <= target_weight <= max_weight):
                raise ValueError("目标权重必须在最小权重和最大权重之间")
            
            self.result = (
                self.current_var.get(),
                f"{target_weight:.2f}%",
                f"{min_weight:.2f}%",
                f"{max_weight:.2f}%",
                self.current_values[4],  # 分配资金
                self.current_values[5]   # 绩效
            )
            
            self.dialog.destroy()
            
        except ValueError as e:
            messagebox.showerror("输入错误", str(e))
    
    def _on_cancel(self):
        """取消"""
        self.dialog.destroy()