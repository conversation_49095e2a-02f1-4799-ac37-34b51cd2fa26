"""
策略配置管理组件

提供策略参数配置、验证和保存功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, Optional, List, Union
import json
from datetime import datetime

try:
    from ...strategies.parameters import StrategyParameter, ParameterType, ParameterValidator
    from ...strategies.base import BaseStrategy
except ImportError:
    # Fallback for testing
    pass


class StrategyConfigWidget:
    """策略配置管理组件"""
    
    def __init__(self, parent: tk.Widget, strategy_manager=None):
        self.parent = parent
        self.strategy_manager = strategy_manager
        
        # 当前编辑的策略
        self.current_strategy_id: Optional[str] = None
        self.current_strategy_config: Dict[str, Any] = {}
        self.parameter_widgets: Dict[str, tk.Widget] = {}
        self.parameter_vars: Dict[str, tk.Variable] = {}
        
        # 回调函数
        self.on_config_save: Optional[callable] = None
        self.on_config_cancel: Optional[callable] = None
        
        # 创建界面
        self._create_widgets()
        self._setup_layout()
        self._bind_events()
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        
        # 标题栏
        self.title_frame = ttk.Frame(self.main_frame)
        self.title_label = ttk.Label(
            self.title_frame,
            text="策略配置",
            font=('Arial', 12, 'bold')
        )
        
        # 基本信息框架
        self.info_frame = ttk.LabelFrame(self.main_frame, text="基本信息")
        
        # 策略名称
        ttk.Label(self.info_frame, text="策略名称:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.name_var = tk.StringVar()
        self.name_entry = ttk.Entry(self.info_frame, textvariable=self.name_var, width=30)
        self.name_entry.grid(row=0, column=1, padx=5, pady=5, sticky='ew')
        
        # 策略类型
        ttk.Label(self.info_frame, text="策略类型:").grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.type_var = tk.StringVar()
        self.type_label = ttk.Label(self.info_frame, textvariable=self.type_var)
        self.type_label.grid(row=1, column=1, padx=5, pady=5, sticky='w')
        
        # 描述
        ttk.Label(self.info_frame, text="描述:").grid(row=2, column=0, sticky='nw', padx=5, pady=5)
        self.desc_text = tk.Text(self.info_frame, width=40, height=3)
        self.desc_text.grid(row=2, column=1, padx=5, pady=5, sticky='ew')
        
        # 参数配置框架
        self.params_frame = ttk.LabelFrame(self.main_frame, text="参数配置")
        
        # 参数滚动区域
        self.params_canvas = tk.Canvas(self.params_frame)
        self.params_scrollbar = ttk.Scrollbar(
            self.params_frame,
            orient="vertical",
            command=self.params_canvas.yview
        )
        self.params_scrollable_frame = ttk.Frame(self.params_canvas)
        
        self.params_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.params_canvas.configure(scrollregion=self.params_canvas.bbox("all"))
        )
        
        self.params_canvas.create_window((0, 0), window=self.params_scrollable_frame, anchor="nw")
        self.params_canvas.configure(yscrollcommand=self.params_scrollbar.set)
        
        # 交易品种配置框架
        self.symbols_frame = ttk.LabelFrame(self.main_frame, text="交易品种")
        
        # 品种列表
        self.symbols_listbox = tk.Listbox(self.symbols_frame, height=6)
        self.symbols_scrollbar = ttk.Scrollbar(
            self.symbols_frame,
            orient="vertical",
            command=self.symbols_listbox.yview
        )
        self.symbols_listbox.configure(yscrollcommand=self.symbols_scrollbar.set)
        
        # 品种操作按钮
        self.symbols_btn_frame = ttk.Frame(self.symbols_frame)
        self.add_symbol_btn = ttk.Button(
            self.symbols_btn_frame,
            text="添加",
            command=self._on_add_symbol
        )
        self.remove_symbol_btn = ttk.Button(
            self.symbols_btn_frame,
            text="移除",
            command=self._on_remove_symbol
        )
        
        # 操作按钮框架
        self.button_frame = ttk.Frame(self.main_frame)
        
        self.save_btn = ttk.Button(
            self.button_frame,
            text="保存配置",
            command=self._on_save_config
        )
        self.cancel_btn = ttk.Button(
            self.button_frame,
            text="取消",
            command=self._on_cancel_config
        )
        self.reset_btn = ttk.Button(
            self.button_frame,
            text="重置",
            command=self._on_reset_config
        )
        self.validate_btn = ttk.Button(
            self.button_frame,
            text="验证参数",
            command=self._on_validate_params
        )
    
    def _setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题栏
        self.title_frame.pack(fill=tk.X, padx=10, pady=5)
        self.title_label.pack(side=tk.LEFT)
        
        # 基本信息
        self.info_frame.pack(fill=tk.X, padx=10, pady=5)
        self.info_frame.grid_columnconfigure(1, weight=1)
        
        # 参数配置
        self.params_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        self.params_canvas.pack(side="left", fill="both", expand=True)
        self.params_scrollbar.pack(side="right", fill="y")
        
        # 交易品种
        self.symbols_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 品种列表布局
        symbols_list_frame = ttk.Frame(self.symbols_frame)
        symbols_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.symbols_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.symbols_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 品种按钮布局
        self.symbols_btn_frame.pack(fill=tk.X, padx=5, pady=5)
        self.add_symbol_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.remove_symbol_btn.pack(side=tk.LEFT)
        
        # 操作按钮
        self.button_frame.pack(fill=tk.X, padx=10, pady=10)
        self.save_btn.pack(side=tk.RIGHT, padx=(5, 0))
        self.cancel_btn.pack(side=tk.RIGHT, padx=(5, 0))
        self.reset_btn.pack(side=tk.RIGHT, padx=(5, 0))
        self.validate_btn.pack(side=tk.LEFT)
    
    def _bind_events(self):
        """绑定事件"""
        # 鼠标滚轮事件
        self.params_canvas.bind("<MouseWheel>", self._on_mousewheel)
        
        # 参数变化事件
        self.name_var.trace('w', self._on_param_change)
    
    def _on_mousewheel(self, event):
        """鼠标滚轮事件"""
        self.params_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    def _on_param_change(self, *args):
        """参数变化事件"""
        # 标记配置已修改
        pass
    
    def load_strategy_config(self, strategy_id: str):
        """加载策略配置"""
        try:
            self.current_strategy_id = strategy_id
            
            # 获取策略信息（模拟数据）
            strategy_info = self._get_strategy_info(strategy_id)
            
            if not strategy_info:
                messagebox.showerror("错误", f"策略 {strategy_id} 不存在")
                return
            
            self.current_strategy_config = strategy_info
            
            # 更新基本信息
            self.name_var.set(strategy_info.get('name', ''))
            self.type_var.set(strategy_info.get('type', ''))
            
            description = strategy_info.get('description', '')
            self.desc_text.delete('1.0', tk.END)
            self.desc_text.insert('1.0', description)
            
            # 更新参数配置
            self._update_parameter_widgets(strategy_info.get('parameters', {}))
            
            # 更新交易品种
            self._update_symbols_list(strategy_info.get('symbols', []))
            
            # 更新标题
            self.title_label.config(text=f"策略配置 - {strategy_info.get('name', strategy_id)}")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载策略配置失败: {str(e)}")
    
    def _get_strategy_info(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """获取策略信息（模拟实现）"""
        # 模拟策略信息
        mock_strategies = {
            'strategy_1': {
                'id': 'strategy_1',
                'name': '移动平均策略',
                'type': 'moving_average',
                'description': '基于移动平均线交叉的趋势跟踪策略',
                'parameters': {
                    'fast_period': 10,
                    'slow_period': 30,
                    'signal_threshold': 0.02
                },
                'symbols': ['AAPL', 'MSFT', 'GOOGL']
            },
            'strategy_2': {
                'id': 'strategy_2',
                'name': 'RSI均值回归',
                'type': 'rsi_mean_reversion',
                'description': 'RSI指标的均值回归策略',
                'parameters': {
                    'rsi_period': 14,
                    'oversold_threshold': 30,
                    'overbought_threshold': 70,
                    'position_size': 0.1
                },
                'symbols': ['SPY', 'QQQ']
            }
        }
        
        return mock_strategies.get(strategy_id)
    
    def _update_parameter_widgets(self, parameters: Dict[str, Any]):
        """更新参数配置组件"""
        # 清空现有组件
        for widget in self.params_scrollable_frame.winfo_children():
            widget.destroy()
        
        self.parameter_widgets.clear()
        self.parameter_vars.clear()
        
        # 获取参数定义（模拟）
        param_definitions = self._get_parameter_definitions()
        
        row = 0
        for param_name, param_value in parameters.items():
            param_def = param_definitions.get(param_name, {})
            
            # 创建参数标签
            label = ttk.Label(
                self.params_scrollable_frame,
                text=f"{param_def.get('display_name', param_name)}:"
            )
            label.grid(row=row, column=0, sticky='w', padx=5, pady=5)
            
            # 创建参数输入组件
            widget, var = self._create_parameter_widget(
                self.params_scrollable_frame,
                param_name,
                param_value,
                param_def
            )
            widget.grid(row=row, column=1, padx=5, pady=5, sticky='ew')
            
            # 添加描述标签
            if param_def.get('description'):
                desc_label = ttk.Label(
                    self.params_scrollable_frame,
                    text=param_def['description'],
                    font=('Arial', 8),
                    foreground='gray'
                )
                desc_label.grid(row=row, column=2, sticky='w', padx=5, pady=5)
            
            self.parameter_widgets[param_name] = widget
            self.parameter_vars[param_name] = var
            
            row += 1
        
        # 配置列权重
        self.params_scrollable_frame.grid_columnconfigure(1, weight=1)
    
    def _get_parameter_definitions(self) -> Dict[str, Dict[str, Any]]:
        """获取参数定义（模拟实现）"""
        return {
            'fast_period': {
                'type': 'int',
                'display_name': '快速周期',
                'description': '快速移动平均线周期',
                'min_value': 1,
                'max_value': 100,
                'default': 10
            },
            'slow_period': {
                'type': 'int',
                'display_name': '慢速周期',
                'description': '慢速移动平均线周期',
                'min_value': 1,
                'max_value': 200,
                'default': 30
            },
            'signal_threshold': {
                'type': 'float',
                'display_name': '信号阈值',
                'description': '信号触发阈值',
                'min_value': 0.0,
                'max_value': 1.0,
                'default': 0.02
            },
            'rsi_period': {
                'type': 'int',
                'display_name': 'RSI周期',
                'description': 'RSI指标计算周期',
                'min_value': 2,
                'max_value': 50,
                'default': 14
            },
            'oversold_threshold': {
                'type': 'float',
                'display_name': '超卖阈值',
                'description': 'RSI超卖阈值',
                'min_value': 0.0,
                'max_value': 50.0,
                'default': 30.0
            },
            'overbought_threshold': {
                'type': 'float',
                'display_name': '超买阈值',
                'description': 'RSI超买阈值',
                'min_value': 50.0,
                'max_value': 100.0,
                'default': 70.0
            },
            'position_size': {
                'type': 'float',
                'display_name': '仓位大小',
                'description': '单次交易仓位比例',
                'min_value': 0.01,
                'max_value': 1.0,
                'default': 0.1
            }
        }
    
    def _create_parameter_widget(self, parent: tk.Widget, param_name: str, 
                               param_value: Any, param_def: Dict[str, Any]) -> tuple:
        """创建参数输入组件"""
        param_type = param_def.get('type', 'str')
        
        if param_type == 'int':
            var = tk.IntVar(value=param_value)
            widget = ttk.Spinbox(
                parent,
                from_=param_def.get('min_value', 0),
                to=param_def.get('max_value', 100),
                textvariable=var,
                width=15
            )
        elif param_type == 'float':
            var = tk.DoubleVar(value=param_value)
            widget = ttk.Entry(parent, textvariable=var, width=15)
        elif param_type == 'bool':
            var = tk.BooleanVar(value=param_value)
            widget = ttk.Checkbutton(parent, variable=var)
        elif param_type == 'choice':
            var = tk.StringVar(value=param_value)
            widget = ttk.Combobox(
                parent,
                textvariable=var,
                values=param_def.get('choices', []),
                state='readonly',
                width=12
            )
        else:  # str
            var = tk.StringVar(value=str(param_value))
            widget = ttk.Entry(parent, textvariable=var, width=15)
        
        return widget, var
    
    def _update_symbols_list(self, symbols: List[str]):
        """更新交易品种列表"""
        self.symbols_listbox.delete(0, tk.END)
        for symbol in symbols:
            self.symbols_listbox.insert(tk.END, symbol)
    
    def _on_add_symbol(self):
        """添加交易品种"""
        dialog = SymbolAddDialog(self.parent)
        if dialog.result:
            symbol = dialog.result.upper()
            
            # 检查是否已存在
            current_symbols = list(self.symbols_listbox.get(0, tk.END))
            if symbol not in current_symbols:
                self.symbols_listbox.insert(tk.END, symbol)
            else:
                messagebox.showwarning("警告", f"品种 {symbol} 已存在")
    
    def _on_remove_symbol(self):
        """移除交易品种"""
        selection = self.symbols_listbox.curselection()
        if selection:
            self.symbols_listbox.delete(selection[0])
        else:
            messagebox.showwarning("警告", "请选择要移除的品种")
    
    def _on_save_config(self):
        """保存配置"""
        try:
            # 验证参数
            if not self._validate_all_parameters():
                return
            
            # 收集配置数据
            config_data = self._collect_config_data()
            
            # 保存配置（模拟）
            success = self._save_strategy_config(config_data)
            
            if success:
                messagebox.showinfo("成功", "策略配置已保存")
                if self.on_config_save:
                    self.on_config_save(self.current_strategy_id, config_data)
            else:
                messagebox.showerror("错误", "保存配置失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
    
    def _on_cancel_config(self):
        """取消配置"""
        if self.on_config_cancel:
            self.on_config_cancel()
    
    def _on_reset_config(self):
        """重置配置"""
        if self.current_strategy_id:
            result = messagebox.askyesno("确认重置", "确定要重置所有参数到默认值吗？")
            if result:
                self.load_strategy_config(self.current_strategy_id)
    
    def _on_validate_params(self):
        """验证参数"""
        if self._validate_all_parameters():
            messagebox.showinfo("验证结果", "所有参数验证通过")
    
    def _validate_all_parameters(self) -> bool:
        """验证所有参数"""
        param_definitions = self._get_parameter_definitions()
        
        for param_name, var in self.parameter_vars.items():
            param_def = param_definitions.get(param_name, {})
            
            try:
                value = var.get()
                
                # 类型验证
                param_type = param_def.get('type', 'str')
                if param_type == 'int' and not isinstance(value, int):
                    raise ValueError(f"参数 {param_name} 必须是整数")
                elif param_type == 'float' and not isinstance(value, (int, float)):
                    raise ValueError(f"参数 {param_name} 必须是数字")
                
                # 范围验证
                if param_type in ['int', 'float']:
                    min_val = param_def.get('min_value')
                    max_val = param_def.get('max_value')
                    
                    if min_val is not None and value < min_val:
                        raise ValueError(f"参数 {param_name} 不能小于 {min_val}")
                    if max_val is not None and value > max_val:
                        raise ValueError(f"参数 {param_name} 不能大于 {max_val}")
                
            except Exception as e:
                messagebox.showerror("参数验证失败", str(e))
                return False
        
        return True
    
    def _collect_config_data(self) -> Dict[str, Any]:
        """收集配置数据"""
        config_data = {
            'id': self.current_strategy_id,
            'name': self.name_var.get(),
            'type': self.type_var.get(),
            'description': self.desc_text.get('1.0', tk.END).strip(),
            'parameters': {},
            'symbols': list(self.symbols_listbox.get(0, tk.END)),
            'updated_at': datetime.now().isoformat()
        }
        
        # 收集参数值
        for param_name, var in self.parameter_vars.items():
            config_data['parameters'][param_name] = var.get()
        
        return config_data
    
    def _save_strategy_config(self, config_data: Dict[str, Any]) -> bool:
        """保存策略配置（模拟实现）"""
        # 这里应该调用实际的保存逻辑
        print(f"保存策略配置: {json.dumps(config_data, indent=2, ensure_ascii=False)}")
        return True


class SymbolAddDialog:
    """添加交易品种对话框"""
    
    def __init__(self, parent):
        self.parent = parent
        self.result = None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("添加交易品种")
        self.dialog.geometry("300x150")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 100,
            parent.winfo_rooty() + 100
        ))
        
        self._create_widgets()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def _create_widgets(self):
        """创建对话框组件"""
        # 输入框
        ttk.Label(self.dialog, text="交易品种代码:").pack(pady=10)
        
        self.symbol_var = tk.StringVar()
        self.symbol_entry = ttk.Entry(
            self.dialog,
            textvariable=self.symbol_var,
            width=20
        )
        self.symbol_entry.pack(pady=5)
        self.symbol_entry.focus()
        
        # 按钮
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(pady=20)
        
        ttk.Button(
            button_frame,
            text="添加",
            command=self._on_add
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            button_frame,
            text="取消",
            command=self._on_cancel
        ).pack(side=tk.LEFT, padx=5)
        
        # 绑定回车键
        self.symbol_entry.bind('<Return>', lambda e: self._on_add())
    
    def _on_add(self):
        """添加品种"""
        symbol = self.symbol_var.get().strip().upper()
        if symbol:
            self.result = symbol
            self.dialog.destroy()
        else:
            messagebox.showerror("错误", "请输入交易品种代码")
    
    def _on_cancel(self):
        """取消"""
        self.dialog.destroy()