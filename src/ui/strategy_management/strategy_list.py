"""
策略列表管理组件

提供策略的列表显示、创建、编辑、删除等功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
import json

try:
    from ...strategies.multi_strategy_manager import StrategyPortfolioManager
    from ...strategies.base import BaseStrategy
    from ...api.routers.strategies import StrategyConfig
except ImportError:
    # Fallback for testing
    pass


class StrategyListWidget:
    """策略列表管理组件"""
    
    def __init__(self, parent: tk.Widget, strategy_manager=None):
        self.parent = parent
        self.strategy_manager = strategy_manager or self._create_mock_manager()
        
        # 回调函数
        self.on_strategy_select: Optional[Callable[[str], None]] = None
        self.on_strategy_edit: Optional[Callable[[str], None]] = None
        self.on_strategy_delete: Optional[Callable[[str], None]] = None
        
        # 当前选中的策略
        self.selected_strategy_id: Optional[str] = None
        
        # 创建界面
        self._create_widgets()
        self._setup_layout()
        self._bind_events()
        
        # 加载策略列表
        self.refresh_strategies()
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        
        # 工具栏
        self.toolbar_frame = ttk.Frame(self.main_frame)
        
        # 创建策略按钮
        self.create_btn = ttk.Button(
            self.toolbar_frame,
            text="创建策略",
            command=self._on_create_strategy
        )
        
        # 刷新按钮
        self.refresh_btn = ttk.Button(
            self.toolbar_frame,
            text="刷新",
            command=self.refresh_strategies
        )
        
        # 搜索框
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(
            self.toolbar_frame,
            textvariable=self.search_var,
            placeholder_text="搜索策略..."
        )
        self.search_var.trace('w', self._on_search_change)
        
        # 策略列表
        self.tree_frame = ttk.Frame(self.main_frame)
        
        # 创建Treeview
        columns = ('name', 'type', 'status', 'return', 'sharpe', 'updated')
        self.tree = ttk.Treeview(
            self.tree_frame,
            columns=columns,
            show='tree headings',
            selectmode='extended'
        )
        
        # 设置列标题
        self.tree.heading('#0', text='ID')
        self.tree.heading('name', text='策略名称')
        self.tree.heading('type', text='策略类型')
        self.tree.heading('status', text='状态')
        self.tree.heading('return', text='收益率')
        self.tree.heading('sharpe', text='夏普比率')
        self.tree.heading('updated', text='更新时间')
        
        # 设置列宽
        self.tree.column('#0', width=80, minwidth=50)
        self.tree.column('name', width=150, minwidth=100)
        self.tree.column('type', width=120, minwidth=80)
        self.tree.column('status', width=80, minwidth=60)
        self.tree.column('return', width=100, minwidth=80)
        self.tree.column('sharpe', width=100, minwidth=80)
        self.tree.column('updated', width=150, minwidth=100)
        
        # 滚动条
        self.v_scrollbar = ttk.Scrollbar(
            self.tree_frame,
            orient=tk.VERTICAL,
            command=self.tree.yview
        )
        self.tree.configure(yscrollcommand=self.v_scrollbar.set)
        
        self.h_scrollbar = ttk.Scrollbar(
            self.tree_frame,
            orient=tk.HORIZONTAL,
            command=self.tree.xview
        )
        self.tree.configure(xscrollcommand=self.h_scrollbar.set)
        
        # 右键菜单
        self.context_menu = tk.Menu(self.main_frame, tearoff=0)
        self.context_menu.add_command(label="编辑", command=self._on_edit_strategy)
        self.context_menu.add_command(label="复制", command=self._on_copy_strategy)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="启动", command=self._on_start_strategy)
        self.context_menu.add_command(label="停止", command=self._on_stop_strategy)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除", command=self._on_delete_strategy)
        
        # 状态栏
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_label = ttk.Label(
            self.status_frame,
            text="就绪"
        )
    
    def _setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 工具栏布局
        self.toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        self.create_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        self.search_entry.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 列表布局
        self.tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 状态栏布局
        self.status_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        self.status_label.pack(side=tk.LEFT)
    
    def _bind_events(self):
        """绑定事件"""
        self.tree.bind('<Double-1>', self._on_double_click)
        self.tree.bind('<Button-3>', self._on_right_click)
        self.tree.bind('<<TreeviewSelect>>', self._on_selection_change)
    
    def _create_mock_manager(self):
        """创建模拟的策略管理器（用于测试）"""
        class MockStrategyManager:
            def list_strategies(self):
                return [
                    {
                        'id': 'strategy_1',
                        'name': '移动平均策略',
                        'strategy_type': 'moving_average',
                        'active': True,
                        'created_at': datetime.now(),
                        'last_updated': datetime.now(),
                        'performance': {
                            'total_return': 0.156,
                            'sharpe_ratio': 1.23,
                            'max_drawdown': -0.084
                        }
                    },
                    {
                        'id': 'strategy_2',
                        'name': 'RSI均值回归',
                        'strategy_type': 'rsi_mean_reversion',
                        'active': False,
                        'created_at': datetime.now(),
                        'last_updated': datetime.now(),
                        'performance': {
                            'total_return': 0.089,
                            'sharpe_ratio': 0.87,
                            'max_drawdown': -0.125
                        }
                    }
                ]
            
            def delete_strategy(self, strategy_id: str):
                return True
            
            def activate_strategy(self, strategy_id: str):
                return True
            
            def deactivate_strategy(self, strategy_id: str):
                return True
        
        return MockStrategyManager()
    
    def refresh_strategies(self):
        """刷新策略列表"""
        try:
            # 清空现有项目
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 获取策略列表
            strategies = self.strategy_manager.list_strategies()
            
            # 添加策略到列表
            for strategy in strategies:
                self._add_strategy_to_tree(strategy)
            
            # 更新状态
            self.status_label.config(text=f"已加载 {len(strategies)} 个策略")
            
        except Exception as e:
            messagebox.showerror("错误", f"刷新策略列表失败: {str(e)}")
            self.status_label.config(text="刷新失败")
    
    def _add_strategy_to_tree(self, strategy: Dict[str, Any]):
        """添加策略到树形列表"""
        strategy_id = strategy.get('id', '')
        name = strategy.get('name', '')
        strategy_type = strategy.get('strategy_type', '')
        status = '运行中' if strategy.get('active', False) else '已停止'
        
        # 获取绩效数据
        performance = strategy.get('performance', {})
        total_return = performance.get('total_return', 0)
        sharpe_ratio = performance.get('sharpe_ratio', 0)
        
        # 格式化显示
        return_str = f"{total_return:.2%}" if total_return else "N/A"
        sharpe_str = f"{sharpe_ratio:.2f}" if sharpe_ratio else "N/A"
        
        updated_time = strategy.get('last_updated', datetime.now())
        updated_str = updated_time.strftime('%Y-%m-%d %H:%M') if updated_time else "N/A"
        
        # 插入到树形列表
        item = self.tree.insert(
            '',
            'end',
            text=strategy_id,
            values=(name, strategy_type, status, return_str, sharpe_str, updated_str),
            tags=(status.lower(),)
        )
        
        # 设置状态颜色
        if status == '运行中':
            self.tree.set_item_color(item, 'green')
        else:
            self.tree.set_item_color(item, 'gray')
    
    def _on_search_change(self, *args):
        """搜索框内容变化"""
        search_text = self.search_var.get().lower()
        
        # 如果搜索框为空，显示所有项目
        if not search_text:
            for item in self.tree.get_children():
                self.tree.item(item, tags=())
            return
        
        # 过滤显示
        for item in self.tree.get_children():
            values = self.tree.item(item, 'values')
            text = self.tree.item(item, 'text')
            
            # 检查是否匹配搜索条件
            match = any(search_text in str(value).lower() for value in values)
            match = match or search_text in text.lower()
            
            if match:
                self.tree.item(item, tags=())
            else:
                self.tree.item(item, tags=('hidden',))
        
        # 隐藏不匹配的项目
        self.tree.tag_configure('hidden', foreground='lightgray')
    
    def _on_create_strategy(self):
        """创建新策略"""
        # 打开策略创建对话框
        dialog = StrategyCreateDialog(self.parent)
        if dialog.result:
            self.refresh_strategies()
    
    def _on_double_click(self, event):
        """双击事件"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if item:
            strategy_id = self.tree.item(item, 'text')
            if self.on_strategy_edit:
                self.on_strategy_edit(strategy_id)
    
    def _on_right_click(self, event):
        """右键点击事件"""
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def _on_selection_change(self, event):
        """选择变化事件"""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            strategy_id = self.tree.item(item, 'text')
            self.selected_strategy_id = strategy_id
            
            if self.on_strategy_select:
                self.on_strategy_select(strategy_id)
    
    def _on_edit_strategy(self):
        """编辑策略"""
        if self.selected_strategy_id and self.on_strategy_edit:
            self.on_strategy_edit(self.selected_strategy_id)
    
    def _on_copy_strategy(self):
        """复制策略"""
        if self.selected_strategy_id:
            # 实现策略复制逻辑
            messagebox.showinfo("提示", f"策略 {self.selected_strategy_id} 复制功能待实现")
    
    def _on_start_strategy(self):
        """启动策略"""
        if self.selected_strategy_id:
            try:
                success = self.strategy_manager.activate_strategy(self.selected_strategy_id)
                if success:
                    messagebox.showinfo("成功", "策略已启动")
                    self.refresh_strategies()
                else:
                    messagebox.showerror("错误", "策略启动失败")
            except Exception as e:
                messagebox.showerror("错误", f"启动策略失败: {str(e)}")
    
    def _on_stop_strategy(self):
        """停止策略"""
        if self.selected_strategy_id:
            try:
                success = self.strategy_manager.deactivate_strategy(self.selected_strategy_id)
                if success:
                    messagebox.showinfo("成功", "策略已停止")
                    self.refresh_strategies()
                else:
                    messagebox.showerror("错误", "策略停止失败")
            except Exception as e:
                messagebox.showerror("错误", f"停止策略失败: {str(e)}")
    
    def _on_delete_strategy(self):
        """删除策略"""
        if self.selected_strategy_id:
            result = messagebox.askyesno(
                "确认删除",
                f"确定要删除策略 {self.selected_strategy_id} 吗？\n此操作不可撤销。"
            )
            
            if result:
                try:
                    success = self.strategy_manager.delete_strategy(self.selected_strategy_id)
                    if success:
                        messagebox.showinfo("成功", "策略已删除")
                        self.refresh_strategies()
                        if self.on_strategy_delete:
                            self.on_strategy_delete(self.selected_strategy_id)
                    else:
                        messagebox.showerror("错误", "策略删除失败")
                except Exception as e:
                    messagebox.showerror("错误", f"删除策略失败: {str(e)}")
    
    def get_selected_strategy(self) -> Optional[str]:
        """获取当前选中的策略ID"""
        return self.selected_strategy_id
    
    def select_strategy(self, strategy_id: str):
        """选中指定策略"""
        for item in self.tree.get_children():
            if self.tree.item(item, 'text') == strategy_id:
                self.tree.selection_set(item)
                self.tree.focus(item)
                break


class StrategyCreateDialog:
    """策略创建对话框"""
    
    def __init__(self, parent):
        self.parent = parent
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("创建新策略")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))
        
        self._create_widgets()
        self._setup_layout()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def _create_widgets(self):
        """创建对话框组件"""
        # 策略名称
        ttk.Label(self.dialog, text="策略名称:").grid(row=0, column=0, sticky='w', padx=10, pady=5)
        self.name_var = tk.StringVar()
        self.name_entry = ttk.Entry(self.dialog, textvariable=self.name_var, width=30)
        self.name_entry.grid(row=0, column=1, padx=10, pady=5)
        
        # 策略类型
        ttk.Label(self.dialog, text="策略类型:").grid(row=1, column=0, sticky='w', padx=10, pady=5)
        self.type_var = tk.StringVar()
        self.type_combo = ttk.Combobox(
            self.dialog,
            textvariable=self.type_var,
            values=['moving_average', 'rsi_mean_reversion', 'macd_trend', 'multi_factor'],
            state='readonly',
            width=27
        )
        self.type_combo.grid(row=1, column=1, padx=10, pady=5)
        
        # 描述
        ttk.Label(self.dialog, text="描述:").grid(row=2, column=0, sticky='nw', padx=10, pady=5)
        self.desc_text = tk.Text(self.dialog, width=30, height=5)
        self.desc_text.grid(row=2, column=1, padx=10, pady=5)
        
        # 按钮
        button_frame = ttk.Frame(self.dialog)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="创建", command=self._on_create).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self._on_cancel).pack(side=tk.LEFT, padx=5)
    
    def _setup_layout(self):
        """设置布局"""
        self.dialog.grid_columnconfigure(1, weight=1)
    
    def _on_create(self):
        """创建策略"""
        name = self.name_var.get().strip()
        strategy_type = self.type_var.get()
        description = self.desc_text.get('1.0', tk.END).strip()
        
        if not name:
            messagebox.showerror("错误", "请输入策略名称")
            return
        
        if not strategy_type:
            messagebox.showerror("错误", "请选择策略类型")
            return
        
        self.result = {
            'name': name,
            'type': strategy_type,
            'description': description,
            'parameters': {}
        }
        
        self.dialog.destroy()
    
    def _on_cancel(self):
        """取消创建"""
        self.dialog.destroy()