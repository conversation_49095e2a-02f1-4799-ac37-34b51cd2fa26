"""
策略参数优化工具

提供参数优化、网格搜索、遗传算法等优化功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, List, Optional, Tuple, Union
import threading
import time
import numpy as np
from datetime import datetime, timedelta
import json

try:
    from ...optimization.parameter_optimizer import ParameterOptimizer, OptimizationMethod
    from ...optimization.grid_search import GridSearchOptimizer
    from ...optimization.genetic_algorithm import GeneticAlgorithmOptimizer
except ImportError:
    # Fallback for testing
    pass


class ParameterOptimizerWidget:
    """参数优化工具组件"""
    
    def __init__(self, parent: tk.Widget, strategy_manager=None):
        self.parent = parent
        self.strategy_manager = strategy_manager
        
        # 当前优化的策略
        self.current_strategy_id: Optional[str] = None
        self.optimization_thread: Optional[threading.Thread] = None
        self.is_optimizing = False
        
        # 优化结果
        self.optimization_results: List[Dict[str, Any]] = []
        self.best_parameters: Optional[Dict[str, Any]] = None
        
        # 回调函数
        self.on_optimization_complete: Optional[callable] = None
        
        # 创建界面
        self._create_widgets()
        self._setup_layout()
        self._bind_events()
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        
        # 标题栏
        self.title_frame = ttk.Frame(self.main_frame)
        self.title_label = ttk.Label(
            self.title_frame,
            text="参数优化工具",
            font=('Arial', 12, 'bold')
        )
        
        # 策略选择框架
        self.strategy_frame = ttk.LabelFrame(self.main_frame, text="策略选择")
        
        ttk.Label(self.strategy_frame, text="策略:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.strategy_var = tk.StringVar()
        self.strategy_combo = ttk.Combobox(
            self.strategy_frame,
            textvariable=self.strategy_var,
            state='readonly',
            width=30
        )
        self.strategy_combo.grid(row=0, column=1, padx=5, pady=5, sticky='ew')
        self.strategy_combo.bind('<<ComboboxSelected>>', self._on_strategy_select)
        
        # 优化方法框架
        self.method_frame = ttk.LabelFrame(self.main_frame, text="优化方法")
        
        self.method_var = tk.StringVar(value="grid_search")
        
        ttk.Radiobutton(
            self.method_frame,
            text="网格搜索",
            variable=self.method_var,
            value="grid_search",
            command=self._on_method_change
        ).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        
        ttk.Radiobutton(
            self.method_frame,
            text="随机搜索",
            variable=self.method_var,
            value="random_search",
            command=self._on_method_change
        ).grid(row=0, column=1, sticky='w', padx=5, pady=5)
        
        ttk.Radiobutton(
            self.method_frame,
            text="遗传算法",
            variable=self.method_var,
            value="genetic_algorithm",
            command=self._on_method_change
        ).grid(row=0, column=2, sticky='w', padx=5, pady=5)
        
        ttk.Radiobutton(
            self.method_frame,
            text="贝叶斯优化",
            variable=self.method_var,
            value="bayesian_optimization",
            command=self._on_method_change
        ).grid(row=0, column=3, sticky='w', padx=5, pady=5)
        
        # 参数范围配置框架
        self.params_frame = ttk.LabelFrame(self.main_frame, text="参数范围配置")
        
        # 参数配置表格
        self.params_tree = ttk.Treeview(
            self.params_frame,
            columns=('current', 'min', 'max', 'step', 'type'),
            show='tree headings',
            height=8
        )
        
        self.params_tree.heading('#0', text='参数名称')
        self.params_tree.heading('current', text='当前值')
        self.params_tree.heading('min', text='最小值')
        self.params_tree.heading('max', text='最大值')
        self.params_tree.heading('step', text='步长')
        self.params_tree.heading('type', text='类型')
        
        self.params_tree.column('#0', width=120, minwidth=80)
        self.params_tree.column('current', width=80, minwidth=60)
        self.params_tree.column('min', width=80, minwidth=60)
        self.params_tree.column('max', width=80, minwidth=60)
        self.params_tree.column('step', width=80, minwidth=60)
        self.params_tree.column('type', width=80, minwidth=60)
        
        # 参数配置滚动条
        self.params_scrollbar = ttk.Scrollbar(
            self.params_frame,
            orient="vertical",
            command=self.params_tree.yview
        )
        self.params_tree.configure(yscrollcommand=self.params_scrollbar.set)
        
        # 优化设置框架
        self.settings_frame = ttk.LabelFrame(self.main_frame, text="优化设置")
        
        # 目标函数
        ttk.Label(self.settings_frame, text="目标函数:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.objective_var = tk.StringVar(value="sharpe_ratio")
        self.objective_combo = ttk.Combobox(
            self.settings_frame,
            textvariable=self.objective_var,
            values=['sharpe_ratio', 'total_return', 'calmar_ratio', 'sortino_ratio', 'max_drawdown'],
            state='readonly',
            width=15
        )
        self.objective_combo.grid(row=0, column=1, padx=5, pady=5)
        
        # 回测期间
        ttk.Label(self.settings_frame, text="开始日期:").grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.start_date_var = tk.StringVar(value=(datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d'))
        self.start_date_entry = ttk.Entry(self.settings_frame, textvariable=self.start_date_var, width=15)
        self.start_date_entry.grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(self.settings_frame, text="结束日期:").grid(row=1, column=2, sticky='w', padx=5, pady=5)
        self.end_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        self.end_date_entry = ttk.Entry(self.settings_frame, textvariable=self.end_date_var, width=15)
        self.end_date_entry.grid(row=1, column=3, padx=5, pady=5)
        
        # 优化参数
        ttk.Label(self.settings_frame, text="最大迭代次数:").grid(row=2, column=0, sticky='w', padx=5, pady=5)
        self.max_iterations_var = tk.IntVar(value=100)
        self.max_iterations_spin = ttk.Spinbox(
            self.settings_frame,
            from_=10,
            to=1000,
            textvariable=self.max_iterations_var,
            width=15
        )
        self.max_iterations_spin.grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Label(self.settings_frame, text="并行进程数:").grid(row=2, column=2, sticky='w', padx=5, pady=5)
        self.n_jobs_var = tk.IntVar(value=4)
        self.n_jobs_spin = ttk.Spinbox(
            self.settings_frame,
            from_=1,
            to=16,
            textvariable=self.n_jobs_var,
            width=15
        )
        self.n_jobs_spin.grid(row=2, column=3, padx=5, pady=5)
        
        # 控制按钮框架
        self.control_frame = ttk.Frame(self.main_frame)
        
        self.start_btn = ttk.Button(
            self.control_frame,
            text="开始优化",
            command=self._on_start_optimization
        )
        
        self.stop_btn = ttk.Button(
            self.control_frame,
            text="停止优化",
            command=self._on_stop_optimization,
            state='disabled'
        )
        
        self.reset_btn = ttk.Button(
            self.control_frame,
            text="重置参数",
            command=self._on_reset_parameters
        )
        
        # 进度框架
        self.progress_frame = ttk.LabelFrame(self.main_frame, text="优化进度")
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            variable=self.progress_var,
            maximum=100
        )
        
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(
            self.progress_frame,
            textvariable=self.status_var
        )
        
        self.iteration_var = tk.StringVar(value="迭代: 0/0")
        self.iteration_label = ttk.Label(
            self.progress_frame,
            textvariable=self.iteration_var
        )
        
        # 结果框架
        self.results_frame = ttk.LabelFrame(self.main_frame, text="优化结果")
        
        # 结果表格
        self.results_tree = ttk.Treeview(
            self.results_frame,
            columns=('objective', 'return', 'sharpe', 'drawdown', 'parameters'),
            show='tree headings',
            height=6
        )
        
        self.results_tree.heading('#0', text='排名')
        self.results_tree.heading('objective', text='目标值')
        self.results_tree.heading('return', text='收益率')
        self.results_tree.heading('sharpe', text='夏普比率')
        self.results_tree.heading('drawdown', text='最大回撤')
        self.results_tree.heading('parameters', text='参数组合')
        
        self.results_tree.column('#0', width=60, minwidth=40)
        self.results_tree.column('objective', width=80, minwidth=60)
        self.results_tree.column('return', width=80, minwidth=60)
        self.results_tree.column('sharpe', width=80, minwidth=60)
        self.results_tree.column('drawdown', width=80, minwidth=60)
        self.results_tree.column('parameters', width=200, minwidth=150)
        
        # 结果滚动条
        self.results_scrollbar = ttk.Scrollbar(
            self.results_frame,
            orient="vertical",
            command=self.results_tree.yview
        )
        self.results_tree.configure(yscrollcommand=self.results_scrollbar.set)
        
        # 结果操作按钮
        self.results_btn_frame = ttk.Frame(self.results_frame)
        
        self.apply_best_btn = ttk.Button(
            self.results_btn_frame,
            text="应用最优参数",
            command=self._on_apply_best_parameters
        )
        
        self.export_results_btn = ttk.Button(
            self.results_btn_frame,
            text="导出结果",
            command=self._on_export_results
        )
        
        self.clear_results_btn = ttk.Button(
            self.results_btn_frame,
            text="清空结果",
            command=self._on_clear_results
        )
    
    def _setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题栏
        self.title_frame.pack(fill=tk.X, padx=10, pady=5)
        self.title_label.pack(side=tk.LEFT)
        
        # 策略选择
        self.strategy_frame.pack(fill=tk.X, padx=10, pady=5)
        self.strategy_frame.grid_columnconfigure(1, weight=1)
        
        # 优化方法
        self.method_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 参数范围配置
        self.params_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        params_table_frame = ttk.Frame(self.params_frame)
        params_table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.params_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.params_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 优化设置
        self.settings_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 控制按钮
        self.control_frame.pack(fill=tk.X, padx=10, pady=5)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.reset_btn.pack(side=tk.LEFT)
        
        # 进度显示
        self.progress_frame.pack(fill=tk.X, padx=10, pady=5)
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)
        
        progress_info_frame = ttk.Frame(self.progress_frame)
        progress_info_frame.pack(fill=tk.X, padx=5, pady=5)
        self.status_label.pack(side=tk.LEFT)
        self.iteration_label.pack(side=tk.RIGHT)
        
        # 优化结果
        self.results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        results_table_frame = ttk.Frame(self.results_frame)
        results_table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.results_btn_frame.pack(fill=tk.X, padx=5, pady=5)
        self.apply_best_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.export_results_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.clear_results_btn.pack(side=tk.LEFT)
    
    def _bind_events(self):
        """绑定事件"""
        self.params_tree.bind('<Double-1>', self._on_param_double_click)
        self.results_tree.bind('<Double-1>', self._on_result_double_click)
    
    def load_strategies(self):
        """加载策略列表"""
        try:
            # 模拟策略列表
            strategies = [
                {'id': 'strategy_1', 'name': '移动平均策略'},
                {'id': 'strategy_2', 'name': 'RSI均值回归'},
                {'id': 'strategy_3', 'name': 'MACD趋势跟踪'}
            ]
            
            strategy_names = [f"{s['name']} ({s['id']})" for s in strategies]
            self.strategy_combo['values'] = strategy_names
            
            if strategy_names:
                self.strategy_combo.current(0)
                self._on_strategy_select()
                
        except Exception as e:
            messagebox.showerror("错误", f"加载策略列表失败: {str(e)}")
    
    def _on_strategy_select(self, event=None):
        """策略选择事件"""
        selection = self.strategy_combo.get()
        if selection:
            # 提取策略ID
            strategy_id = selection.split('(')[-1].rstrip(')')
            self.current_strategy_id = strategy_id
            
            # 加载策略参数
            self._load_strategy_parameters(strategy_id)
    
    def _load_strategy_parameters(self, strategy_id: str):
        """加载策略参数"""
        try:
            # 清空现有参数
            for item in self.params_tree.get_children():
                self.params_tree.delete(item)
            
            # 获取策略参数（模拟数据）
            parameters = self._get_strategy_parameters(strategy_id)
            
            # 添加参数到表格
            for param_name, param_info in parameters.items():
                self.params_tree.insert(
                    '',
                    'end',
                    text=param_name,
                    values=(
                        param_info['current'],
                        param_info['min'],
                        param_info['max'],
                        param_info.get('step', 1),
                        param_info['type']
                    )
                )
                
        except Exception as e:
            messagebox.showerror("错误", f"加载策略参数失败: {str(e)}")
    
    def _get_strategy_parameters(self, strategy_id: str) -> Dict[str, Dict[str, Any]]:
        """获取策略参数（模拟实现）"""
        mock_parameters = {
            'strategy_1': {
                'fast_period': {
                    'current': 10,
                    'min': 5,
                    'max': 50,
                    'step': 1,
                    'type': 'int'
                },
                'slow_period': {
                    'current': 30,
                    'min': 20,
                    'max': 100,
                    'step': 1,
                    'type': 'int'
                },
                'signal_threshold': {
                    'current': 0.02,
                    'min': 0.01,
                    'max': 0.1,
                    'step': 0.01,
                    'type': 'float'
                }
            },
            'strategy_2': {
                'rsi_period': {
                    'current': 14,
                    'min': 5,
                    'max': 30,
                    'step': 1,
                    'type': 'int'
                },
                'oversold_threshold': {
                    'current': 30,
                    'min': 20,
                    'max': 40,
                    'step': 1,
                    'type': 'float'
                },
                'overbought_threshold': {
                    'current': 70,
                    'min': 60,
                    'max': 80,
                    'step': 1,
                    'type': 'float'
                }
            }
        }
        
        return mock_parameters.get(strategy_id, {})
    
    def _on_method_change(self):
        """优化方法变化事件"""
        method = self.method_var.get()
        
        # 根据不同方法调整界面
        if method == "genetic_algorithm":
            self.max_iterations_var.set(50)
        elif method == "bayesian_optimization":
            self.max_iterations_var.set(30)
        else:
            self.max_iterations_var.set(100)
    
    def _on_param_double_click(self, event):
        """参数双击编辑事件"""
        item = self.params_tree.selection()[0] if self.params_tree.selection() else None
        if item:
            param_name = self.params_tree.item(item, 'text')
            current_values = self.params_tree.item(item, 'values')
            
            # 打开参数编辑对话框
            dialog = ParameterEditDialog(self.parent, param_name, current_values)
            if dialog.result:
                # 更新参数范围
                self.params_tree.item(item, values=dialog.result)
    
    def _on_start_optimization(self):
        """开始优化"""
        if self.is_optimizing:
            messagebox.showwarning("警告", "优化正在进行中")
            return
        
        if not self.current_strategy_id:
            messagebox.showerror("错误", "请先选择策略")
            return
        
        # 验证参数设置
        if not self._validate_optimization_settings():
            return
        
        # 收集优化配置
        config = self._collect_optimization_config()
        
        # 启动优化线程
        self.is_optimizing = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        
        self.optimization_thread = threading.Thread(
            target=self._run_optimization,
            args=(config,)
        )
        self.optimization_thread.start()
    
    def _on_stop_optimization(self):
        """停止优化"""
        self.is_optimizing = False
        self.status_var.set("正在停止...")
        
        # 等待线程结束
        if self.optimization_thread and self.optimization_thread.is_alive():
            self.optimization_thread.join(timeout=5)
        
        self._reset_optimization_ui()
    
    def _on_reset_parameters(self):
        """重置参数"""
        if self.current_strategy_id:
            self._load_strategy_parameters(self.current_strategy_id)
    
    def _validate_optimization_settings(self) -> bool:
        """验证优化设置"""
        try:
            # 验证日期格式
            datetime.strptime(self.start_date_var.get(), '%Y-%m-%d')
            datetime.strptime(self.end_date_var.get(), '%Y-%m-%d')
            
            # 验证参数范围
            for item in self.params_tree.get_children():
                values = self.params_tree.item(item, 'values')
                min_val, max_val = float(values[1]), float(values[2])
                if min_val >= max_val:
                    param_name = self.params_tree.item(item, 'text')
                    raise ValueError(f"参数 {param_name} 的最小值必须小于最大值")
            
            return True
            
        except ValueError as e:
            messagebox.showerror("参数错误", str(e))
            return False
        except Exception as e:
            messagebox.showerror("验证失败", f"参数验证失败: {str(e)}")
            return False
    
    def _collect_optimization_config(self) -> Dict[str, Any]:
        """收集优化配置"""
        # 收集参数范围
        parameter_ranges = {}
        for item in self.params_tree.get_children():
            param_name = self.params_tree.item(item, 'text')
            values = self.params_tree.item(item, 'values')
            
            parameter_ranges[param_name] = {
                'min': float(values[1]),
                'max': float(values[2]),
                'step': float(values[3]),
                'type': values[4]
            }
        
        return {
            'strategy_id': self.current_strategy_id,
            'method': self.method_var.get(),
            'objective': self.objective_var.get(),
            'start_date': self.start_date_var.get(),
            'end_date': self.end_date_var.get(),
            'max_iterations': self.max_iterations_var.get(),
            'n_jobs': self.n_jobs_var.get(),
            'parameter_ranges': parameter_ranges
        }
    
    def _run_optimization(self, config: Dict[str, Any]):
        """运行优化（模拟实现）"""
        try:
            method = config['method']
            max_iterations = config['max_iterations']
            
            self.status_var.set(f"正在运行{method}优化...")
            self.optimization_results.clear()
            
            # 模拟优化过程
            for i in range(max_iterations):
                if not self.is_optimizing:
                    break
                
                # 模拟参数组合生成和评估
                result = self._simulate_optimization_iteration(config, i)
                self.optimization_results.append(result)
                
                # 更新进度
                progress = (i + 1) / max_iterations * 100
                self.progress_var.set(progress)
                self.iteration_var.set(f"迭代: {i + 1}/{max_iterations}")
                
                # 更新结果显示
                self.parent.after(0, self._update_results_display)
                
                # 模拟计算时间
                time.sleep(0.1)
            
            if self.is_optimizing:
                self.status_var.set("优化完成")
                self._find_best_parameters()
            else:
                self.status_var.set("优化已停止")
            
        except Exception as e:
            self.status_var.set(f"优化失败: {str(e)}")
        finally:
            self.parent.after(0, self._reset_optimization_ui)
    
    def _simulate_optimization_iteration(self, config: Dict[str, Any], iteration: int) -> Dict[str, Any]:
        """模拟优化迭代"""
        # 生成随机参数组合
        parameters = {}
        for param_name, param_range in config['parameter_ranges'].items():
            if param_range['type'] == 'int':
                value = np.random.randint(param_range['min'], param_range['max'] + 1)
            else:
                value = np.random.uniform(param_range['min'], param_range['max'])
            parameters[param_name] = value
        
        # 模拟回测结果
        total_return = np.random.normal(0.1, 0.2)
        sharpe_ratio = np.random.normal(1.0, 0.5)
        max_drawdown = -abs(np.random.normal(0.1, 0.05))
        
        # 计算目标函数值
        objective_value = sharpe_ratio if config['objective'] == 'sharpe_ratio' else total_return
        
        return {
            'iteration': iteration,
            'parameters': parameters,
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'objective_value': objective_value
        }
    
    def _update_results_display(self):
        """更新结果显示"""
        # 清空现有结果
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # 按目标函数值排序
        sorted_results = sorted(
            self.optimization_results,
            key=lambda x: x['objective_value'],
            reverse=True
        )
        
        # 显示前20个结果
        for i, result in enumerate(sorted_results[:20]):
            params_str = ', '.join([
                f"{k}={v:.3f}" if isinstance(v, float) else f"{k}={v}"
                for k, v in result['parameters'].items()
            ])
            
            self.results_tree.insert(
                '',
                'end',
                text=str(i + 1),
                values=(
                    f"{result['objective_value']:.4f}",
                    f"{result['total_return']:.2%}",
                    f"{result['sharpe_ratio']:.3f}",
                    f"{result['max_drawdown']:.2%}",
                    params_str
                )
            )
    
    def _find_best_parameters(self):
        """找到最优参数"""
        if self.optimization_results:
            self.best_parameters = max(
                self.optimization_results,
                key=lambda x: x['objective_value']
            )
            
            best_obj = self.best_parameters['objective_value']
            self.status_var.set(f"优化完成，最优目标值: {best_obj:.4f}")
    
    def _reset_optimization_ui(self):
        """重置优化界面"""
        self.is_optimizing = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
    
    def _on_result_double_click(self, event):
        """结果双击事件"""
        item = self.results_tree.selection()[0] if self.results_tree.selection() else None
        if item:
            rank = int(self.results_tree.item(item, 'text')) - 1
            if rank < len(self.optimization_results):
                result = self.optimization_results[rank]
                self._show_result_details(result)
    
    def _show_result_details(self, result: Dict[str, Any]):
        """显示结果详情"""
        details = f"""
优化结果详情

迭代次数: {result['iteration'] + 1}
目标函数值: {result['objective_value']:.4f}
总收益率: {result['total_return']:.2%}
夏普比率: {result['sharpe_ratio']:.3f}
最大回撤: {result['max_drawdown']:.2%}

参数组合:
"""
        for param_name, param_value in result['parameters'].items():
            if isinstance(param_value, float):
                details += f"  {param_name}: {param_value:.4f}\n"
            else:
                details += f"  {param_name}: {param_value}\n"
        
        messagebox.showinfo("结果详情", details)
    
    def _on_apply_best_parameters(self):
        """应用最优参数"""
        if not self.best_parameters:
            messagebox.showwarning("警告", "没有找到最优参数")
            return
        
        result = messagebox.askyesno(
            "确认应用",
            f"确定要将最优参数应用到策略 {self.current_strategy_id} 吗？"
        )
        
        if result:
            try:
                # 应用参数（模拟实现）
                success = self._apply_parameters_to_strategy(
                    self.current_strategy_id,
                    self.best_parameters['parameters']
                )
                
                if success:
                    messagebox.showinfo("成功", "最优参数已应用到策略")
                    if self.on_optimization_complete:
                        self.on_optimization_complete(
                            self.current_strategy_id,
                            self.best_parameters['parameters']
                        )
                else:
                    messagebox.showerror("错误", "应用参数失败")
                    
            except Exception as e:
                messagebox.showerror("错误", f"应用参数失败: {str(e)}")
    
    def _apply_parameters_to_strategy(self, strategy_id: str, parameters: Dict[str, Any]) -> bool:
        """应用参数到策略（模拟实现）"""
        print(f"应用参数到策略 {strategy_id}: {parameters}")
        return True
    
    def _on_export_results(self):
        """导出结果"""
        if not self.optimization_results:
            messagebox.showwarning("警告", "没有优化结果可导出")
            return
        
        try:
            # 模拟导出功能
            filename = f"optimization_results_{self.current_strategy_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            export_data = {
                'strategy_id': self.current_strategy_id,
                'optimization_method': self.method_var.get(),
                'objective_function': self.objective_var.get(),
                'total_iterations': len(self.optimization_results),
                'best_result': self.best_parameters,
                'all_results': self.optimization_results
            }
            
            # 实际实现中应该保存到文件
            print(f"导出结果到文件: {filename}")
            print(json.dumps(export_data, indent=2, default=str))
            
            messagebox.showinfo("成功", f"结果已导出到 {filename}")
            
        except Exception as e:
            messagebox.showerror("错误", f"导出结果失败: {str(e)}")
    
    def _on_clear_results(self):
        """清空结果"""
        result = messagebox.askyesno("确认清空", "确定要清空所有优化结果吗？")
        if result:
            self.optimization_results.clear()
            self.best_parameters = None
            
            # 清空结果表格
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)
            
            # 重置进度
            self.progress_var.set(0)
            self.status_var.set("就绪")
            self.iteration_var.set("迭代: 0/0")


class ParameterEditDialog:
    """参数编辑对话框"""
    
    def __init__(self, parent, param_name: str, current_values: tuple):
        self.parent = parent
        self.param_name = param_name
        self.current_values = current_values
        self.result = None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"编辑参数: {param_name}")
        self.dialog.geometry("300x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 100,
            parent.winfo_rooty() + 100
        ))
        
        self._create_widgets()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def _create_widgets(self):
        """创建对话框组件"""
        # 当前值
        ttk.Label(self.dialog, text="当前值:").grid(row=0, column=0, sticky='w', padx=10, pady=5)
        self.current_var = tk.StringVar(value=self.current_values[0])
        ttk.Entry(self.dialog, textvariable=self.current_var, width=15, state='readonly').grid(row=0, column=1, padx=10, pady=5)
        
        # 最小值
        ttk.Label(self.dialog, text="最小值:").grid(row=1, column=0, sticky='w', padx=10, pady=5)
        self.min_var = tk.StringVar(value=self.current_values[1])
        ttk.Entry(self.dialog, textvariable=self.min_var, width=15).grid(row=1, column=1, padx=10, pady=5)
        
        # 最大值
        ttk.Label(self.dialog, text="最大值:").grid(row=2, column=0, sticky='w', padx=10, pady=5)
        self.max_var = tk.StringVar(value=self.current_values[2])
        ttk.Entry(self.dialog, textvariable=self.max_var, width=15).grid(row=2, column=1, padx=10, pady=5)
        
        # 步长
        ttk.Label(self.dialog, text="步长:").grid(row=3, column=0, sticky='w', padx=10, pady=5)
        self.step_var = tk.StringVar(value=self.current_values[3])
        ttk.Entry(self.dialog, textvariable=self.step_var, width=15).grid(row=3, column=1, padx=10, pady=5)
        
        # 类型
        ttk.Label(self.dialog, text="类型:").grid(row=4, column=0, sticky='w', padx=10, pady=5)
        self.type_var = tk.StringVar(value=self.current_values[4])
        ttk.Combobox(
            self.dialog,
            textvariable=self.type_var,
            values=['int', 'float'],
            state='readonly',
            width=12
        ).grid(row=4, column=1, padx=10, pady=5)
        
        # 按钮
        button_frame = ttk.Frame(self.dialog)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="确定", command=self._on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self._on_cancel).pack(side=tk.LEFT, padx=5)
    
    def _on_ok(self):
        """确定"""
        try:
            # 验证输入
            min_val = float(self.min_var.get())
            max_val = float(self.max_var.get())
            step_val = float(self.step_var.get())
            
            if min_val >= max_val:
                raise ValueError("最小值必须小于最大值")
            
            if step_val <= 0:
                raise ValueError("步长必须大于0")
            
            self.result = (
                self.current_var.get(),
                self.min_var.get(),
                self.max_var.get(),
                self.step_var.get(),
                self.type_var.get()
            )
            
            self.dialog.destroy()
            
        except ValueError as e:
            messagebox.showerror("输入错误", str(e))
    
    def _on_cancel(self):
        """取消"""
        self.dialog.destroy()