"""
回测监控界面组件

提供策略回测的启动、监控和结果展示功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, List, Optional, Callable
import threading
import time
import json
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
import numpy as np

try:
    from ...backtest.engine import BacktestEngine
    from ...backtest.result import BacktestResult
    from ...analysis.metrics import PerformanceMetrics
except ImportError:
    # Fallback for testing
    pass


class BacktestMonitorWidget:
    """回测监控组件"""
    
    def __init__(self, parent: tk.Widget, strategy_manager=None):
        self.parent = parent
        self.strategy_manager = strategy_manager
        
        # 回测状态
        self.current_backtest_id: Optional[str] = None
        self.backtest_thread: Optional[threading.Thread] = None
        self.is_running = False
        
        # 回测结果
        self.backtest_results: Dict[str, Any] = {}
        self.performance_data: Optional[pd.DataFrame] = None
        
        # 回调函数
        self.on_backtest_complete: Optional[Callable] = None
        
        # 创建界面
        self._create_widgets()
        self._setup_layout()
        self._bind_events()
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.parent)
        
        # 创建Notebook用于分页显示
        self.notebook = ttk.Notebook(self.main_frame)
        
        # 回测配置页面
        self.config_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.config_frame, text="回测配置")
        
        # 实时监控页面
        self.monitor_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.monitor_frame, text="实时监控")
        
        # 结果分析页面
        self.results_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.results_frame, text="结果分析")
        
        # 创建各页面内容
        self._create_config_widgets()
        self._create_monitor_widgets()
        self._create_results_widgets()
    
    def _create_config_widgets(self):
        """创建回测配置组件"""
        # 策略选择框架
        strategy_frame = ttk.LabelFrame(self.config_frame, text="策略选择")
        strategy_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(strategy_frame, text="策略:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.strategy_var = tk.StringVar()
        self.strategy_combo = ttk.Combobox(
            strategy_frame,
            textvariable=self.strategy_var,
            state='readonly',
            width=30
        )
        self.strategy_combo.grid(row=0, column=1, padx=5, pady=5, sticky='ew')
        strategy_frame.grid_columnconfigure(1, weight=1)
        
        # 回测参数框架
        params_frame = ttk.LabelFrame(self.config_frame, text="回测参数")
        params_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 时间范围
        ttk.Label(params_frame, text="开始日期:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.start_date_var = tk.StringVar(value=(datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d'))
        self.start_date_entry = ttk.Entry(params_frame, textvariable=self.start_date_var, width=15)
        self.start_date_entry.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(params_frame, text="结束日期:").grid(row=0, column=2, sticky='w', padx=5, pady=5)
        self.end_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        self.end_date_entry = ttk.Entry(params_frame, textvariable=self.end_date_var, width=15)
        self.end_date_entry.grid(row=0, column=3, padx=5, pady=5)
        
        # 初始资金
        ttk.Label(params_frame, text="初始资金:").grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.initial_capital_var = tk.DoubleVar(value=100000.0)
        self.initial_capital_spin = ttk.Spinbox(
            params_frame,
            from_=10000,
            to=10000000,
            increment=10000,
            textvariable=self.initial_capital_var,
            width=15
        )
        self.initial_capital_spin.grid(row=1, column=1, padx=5, pady=5)
        
        # 手续费率
        ttk.Label(params_frame, text="手续费率:").grid(row=1, column=2, sticky='w', padx=5, pady=5)
        self.commission_var = tk.DoubleVar(value=0.001)
        self.commission_entry = ttk.Entry(params_frame, textvariable=self.commission_var, width=15)
        self.commission_entry.grid(row=1, column=3, padx=5, pady=5)
        
        # 滑点
        ttk.Label(params_frame, text="滑点(bp):").grid(row=2, column=0, sticky='w', padx=5, pady=5)
        self.slippage_var = tk.DoubleVar(value=1.0)
        self.slippage_entry = ttk.Entry(params_frame, textvariable=self.slippage_var, width=15)
        self.slippage_entry.grid(row=2, column=1, padx=5, pady=5)
        
        # 基准指数
        ttk.Label(params_frame, text="基准指数:").grid(row=2, column=2, sticky='w', padx=5, pady=5)
        self.benchmark_var = tk.StringVar(value="SPY")
        self.benchmark_entry = ttk.Entry(params_frame, textvariable=self.benchmark_var, width=15)
        self.benchmark_entry.grid(row=2, column=3, padx=5, pady=5)
        
        # 高级设置框架
        advanced_frame = ttk.LabelFrame(self.config_frame, text="高级设置")
        advanced_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 数据频率
        ttk.Label(advanced_frame, text="数据频率:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.frequency_var = tk.StringVar(value="1d")
        self.frequency_combo = ttk.Combobox(
            advanced_frame,
            textvariable=self.frequency_var,
            values=['1m', '5m', '15m', '30m', '1h', '1d'],
            state='readonly',
            width=10
        )
        self.frequency_combo.grid(row=0, column=1, padx=5, pady=5)
        
        # 预热期
        ttk.Label(advanced_frame, text="预热期(天):").grid(row=0, column=2, sticky='w', padx=5, pady=5)
        self.warmup_var = tk.IntVar(value=100)
        self.warmup_spin = ttk.Spinbox(
            advanced_frame,
            from_=0,
            to=500,
            textvariable=self.warmup_var,
            width=10
        )
        self.warmup_spin.grid(row=0, column=3, padx=5, pady=5)
        
        # 控制按钮
        control_frame = ttk.Frame(self.config_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.start_backtest_btn = ttk.Button(
            control_frame,
            text="开始回测",
            command=self._on_start_backtest
        )
        self.start_backtest_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_backtest_btn = ttk.Button(
            control_frame,
            text="停止回测",
            command=self._on_stop_backtest,
            state='disabled'
        )
        self.stop_backtest_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.save_config_btn = ttk.Button(
            control_frame,
            text="保存配置",
            command=self._on_save_config
        )
        self.save_config_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.load_config_btn = ttk.Button(
            control_frame,
            text="加载配置",
            command=self._on_load_config
        )
        self.load_config_btn.pack(side=tk.LEFT)
    
    def _create_monitor_widgets(self):
        """创建实时监控组件"""
        # 状态信息框架
        status_frame = ttk.LabelFrame(self.monitor_frame, text="回测状态")
        status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(
            status_frame,
            textvariable=self.status_var,
            font=('Arial', 10, 'bold')
        )
        self.status_label.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            status_frame,
            variable=self.progress_var,
            maximum=100
        )
        self.progress_bar.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=5, pady=5)
        
        # 实时指标框架
        metrics_frame = ttk.LabelFrame(self.monitor_frame, text="实时指标")
        metrics_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 创建指标显示网格
        metrics_grid = ttk.Frame(metrics_frame)
        metrics_grid.pack(fill=tk.X, padx=5, pady=5)
        
        # 第一行指标
        ttk.Label(metrics_grid, text="当前日期:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.current_date_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.current_date_var).grid(row=0, column=1, sticky='w', padx=5, pady=2)
        
        ttk.Label(metrics_grid, text="总资产:").grid(row=0, column=2, sticky='w', padx=5, pady=2)
        self.total_value_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.total_value_var).grid(row=0, column=3, sticky='w', padx=5, pady=2)
        
        # 第二行指标
        ttk.Label(metrics_grid, text="总收益:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.total_return_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.total_return_var).grid(row=1, column=1, sticky='w', padx=5, pady=2)
        
        ttk.Label(metrics_grid, text="年化收益:").grid(row=1, column=2, sticky='w', padx=5, pady=2)
        self.annual_return_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.annual_return_var).grid(row=1, column=3, sticky='w', padx=5, pady=2)
        
        # 第三行指标
        ttk.Label(metrics_grid, text="夏普比率:").grid(row=2, column=0, sticky='w', padx=5, pady=2)
        self.sharpe_ratio_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.sharpe_ratio_var).grid(row=2, column=1, sticky='w', padx=5, pady=2)
        
        ttk.Label(metrics_grid, text="最大回撤:").grid(row=2, column=2, sticky='w', padx=5, pady=2)
        self.max_drawdown_var = tk.StringVar(value="N/A")
        ttk.Label(metrics_grid, textvariable=self.max_drawdown_var).grid(row=2, column=3, sticky='w', padx=5, pady=2)
        
        # 实时图表框架
        chart_frame = ttk.LabelFrame(self.monitor_frame, text="实时图表")
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建matplotlib图表
        self.fig, (self.ax1, self.ax2) = plt.subplots(2, 1, figsize=(10, 6))
        self.fig.tight_layout(pad=3.0)
        
        # 资金曲线图
        self.ax1.set_title("资金曲线")
        self.ax1.set_ylabel("资产价值")
        self.ax1.grid(True, alpha=0.3)
        
        # 回撤图
        self.ax2.set_title("回撤曲线")
        self.ax2.set_ylabel("回撤 (%)")
        self.ax2.set_xlabel("日期")
        self.ax2.grid(True, alpha=0.3)
        
        # 嵌入到tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 日志框架
        log_frame = ttk.LabelFrame(self.monitor_frame, text="回测日志")
        log_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 日志文本框
        self.log_text = tk.Text(log_frame, height=6, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def _create_results_widgets(self):
        """创建结果分析组件"""
        # 绩效指标框架
        performance_frame = ttk.LabelFrame(self.results_frame, text="绩效指标")
        performance_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 创建绩效指标表格
        self.performance_tree = ttk.Treeview(
            performance_frame,
            columns=('value',),
            show='tree headings',
            height=10
        )
        
        self.performance_tree.heading('#0', text='指标名称')
        self.performance_tree.heading('value', text='数值')
        
        self.performance_tree.column('#0', width=200, minwidth=150)
        self.performance_tree.column('value', width=150, minwidth=100)
        
        perf_scrollbar = ttk.Scrollbar(
            performance_frame,
            orient="vertical",
            command=self.performance_tree.yview
        )
        self.performance_tree.configure(yscrollcommand=perf_scrollbar.set)
        
        self.performance_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        perf_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 交易记录框架
        trades_frame = ttk.LabelFrame(self.results_frame, text="交易记录")
        trades_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 交易记录表格
        self.trades_tree = ttk.Treeview(
            trades_frame,
            columns=('date', 'symbol', 'side', 'quantity', 'price', 'value', 'pnl'),
            show='tree headings',
            height=8
        )
        
        self.trades_tree.heading('#0', text='ID')
        self.trades_tree.heading('date', text='日期')
        self.trades_tree.heading('symbol', text='标的')
        self.trades_tree.heading('side', text='方向')
        self.trades_tree.heading('quantity', text='数量')
        self.trades_tree.heading('price', text='价格')
        self.trades_tree.heading('value', text='金额')
        self.trades_tree.heading('pnl', text='盈亏')
        
        self.trades_tree.column('#0', width=50, minwidth=30)
        self.trades_tree.column('date', width=100, minwidth=80)
        self.trades_tree.column('symbol', width=80, minwidth=60)
        self.trades_tree.column('side', width=60, minwidth=40)
        self.trades_tree.column('quantity', width=80, minwidth=60)
        self.trades_tree.column('price', width=80, minwidth=60)
        self.trades_tree.column('value', width=100, minwidth=80)
        self.trades_tree.column('pnl', width=100, minwidth=80)
        
        trades_scrollbar = ttk.Scrollbar(
            trades_frame,
            orient="vertical",
            command=self.trades_tree.yview
        )
        self.trades_tree.configure(yscrollcommand=trades_scrollbar.set)
        
        self.trades_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        trades_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 结果操作按钮
        results_btn_frame = ttk.Frame(self.results_frame)
        results_btn_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.export_results_btn = ttk.Button(
            results_btn_frame,
            text="导出结果",
            command=self._on_export_results
        )
        self.export_results_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.save_report_btn = ttk.Button(
            results_btn_frame,
            text="生成报告",
            command=self._on_generate_report
        )
        self.save_report_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.compare_btn = ttk.Button(
            results_btn_frame,
            text="对比分析",
            command=self._on_compare_results
        )
        self.compare_btn.pack(side=tk.LEFT)
    
    def _setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def _bind_events(self):
        """绑定事件"""
        self.trades_tree.bind('<Double-1>', self._on_trade_double_click)
    
    def load_strategies(self):
        """加载策略列表"""
        try:
            # 模拟策略列表
            strategies = [
                {'id': 'strategy_1', 'name': '移动平均策略'},
                {'id': 'strategy_2', 'name': 'RSI均值回归'},
                {'id': 'strategy_3', 'name': 'MACD趋势跟踪'}
            ]
            
            strategy_names = [f"{s['name']} ({s['id']})" for s in strategies]
            self.strategy_combo['values'] = strategy_names
            
            if strategy_names:
                self.strategy_combo.current(0)
                
        except Exception as e:
            messagebox.showerror("错误", f"加载策略列表失败: {str(e)}")
    
    def _on_start_backtest(self):
        """开始回测"""
        if self.is_running:
            messagebox.showwarning("警告", "回测正在进行中")
            return
        
        # 验证配置
        if not self._validate_backtest_config():
            return
        
        # 收集回测配置
        config = self._collect_backtest_config()
        
        # 启动回测线程
        self.is_running = True
        self.start_backtest_btn.config(state='disabled')
        self.stop_backtest_btn.config(state='normal')
        
        # 切换到监控页面
        self.notebook.select(1)
        
        # 清空之前的结果
        self._clear_results()
        
        # 启动回测
        self.backtest_thread = threading.Thread(
            target=self._run_backtest,
            args=(config,)
        )
        self.backtest_thread.start()
    
    def _on_stop_backtest(self):
        """停止回测"""
        self.is_running = False
        self.status_var.set("正在停止...")
        
        # 等待线程结束
        if self.backtest_thread and self.backtest_thread.is_alive():
            self.backtest_thread.join(timeout=5)
        
        self._reset_backtest_ui()
    
    def _validate_backtest_config(self) -> bool:
        """验证回测配置"""
        try:
            # 验证策略选择
            if not self.strategy_var.get():
                raise ValueError("请选择策略")
            
            # 验证日期格式
            start_date = datetime.strptime(self.start_date_var.get(), '%Y-%m-%d')
            end_date = datetime.strptime(self.end_date_var.get(), '%Y-%m-%d')
            
            if start_date >= end_date:
                raise ValueError("开始日期必须早于结束日期")
            
            # 验证数值参数
            if self.initial_capital_var.get() <= 0:
                raise ValueError("初始资金必须大于0")
            
            if self.commission_var.get() < 0:
                raise ValueError("手续费率不能为负数")
            
            return True
            
        except ValueError as e:
            messagebox.showerror("配置错误", str(e))
            return False
        except Exception as e:
            messagebox.showerror("验证失败", f"配置验证失败: {str(e)}")
            return False
    
    def _collect_backtest_config(self) -> Dict[str, Any]:
        """收集回测配置"""
        strategy_selection = self.strategy_var.get()
        strategy_id = strategy_selection.split('(')[-1].rstrip(')')
        
        return {
            'strategy_id': strategy_id,
            'start_date': self.start_date_var.get(),
            'end_date': self.end_date_var.get(),
            'initial_capital': self.initial_capital_var.get(),
            'commission': self.commission_var.get(),
            'slippage': self.slippage_var.get() / 10000,  # bp to decimal
            'benchmark': self.benchmark_var.get(),
            'frequency': self.frequency_var.get(),
            'warmup_period': self.warmup_var.get()
        }
    
    def _run_backtest(self, config: Dict[str, Any]):
        """运行回测（模拟实现）"""
        try:
            self.status_var.set("正在初始化...")
            self._log_message("开始回测")
            self._log_message(f"策略: {config['strategy_id']}")
            self._log_message(f"时间范围: {config['start_date']} 到 {config['end_date']}")
            
            # 模拟回测过程
            start_date = datetime.strptime(config['start_date'], '%Y-%m-%d')
            end_date = datetime.strptime(config['end_date'], '%Y-%m-%d')
            total_days = (end_date - start_date).days
            
            # 初始化数据
            dates = pd.date_range(start_date, end_date, freq='D')
            portfolio_values = []
            drawdowns = []
            trades = []
            
            current_value = config['initial_capital']
            peak_value = current_value
            
            for i, date in enumerate(dates):
                if not self.is_running:
                    break
                
                # 模拟每日收益
                daily_return = np.random.normal(0.0005, 0.02)  # 日收益率
                current_value *= (1 + daily_return)
                
                # 计算回撤
                if current_value > peak_value:
                    peak_value = current_value
                drawdown = (current_value - peak_value) / peak_value * 100
                
                portfolio_values.append(current_value)
                drawdowns.append(drawdown)
                
                # 模拟交易
                if i % 10 == 0 and np.random.random() > 0.7:  # 随机生成交易
                    trade = {
                        'id': len(trades) + 1,
                        'date': date.strftime('%Y-%m-%d'),
                        'symbol': 'AAPL',
                        'side': 'BUY' if np.random.random() > 0.5 else 'SELL',
                        'quantity': np.random.randint(100, 1000),
                        'price': np.random.uniform(100, 200),
                        'pnl': np.random.normal(0, 1000)
                    }
                    trade['value'] = trade['quantity'] * trade['price']
                    trades.append(trade)
                
                # 更新进度和显示
                progress = (i + 1) / len(dates) * 100
                self.progress_var.set(progress)
                
                # 更新实时指标
                total_return = (current_value - config['initial_capital']) / config['initial_capital']
                annual_return = total_return * 365 / (i + 1) if i > 0 else 0
                
                self.current_date_var.set(date.strftime('%Y-%m-%d'))
                self.total_value_var.set(f"${current_value:,.2f}")
                self.total_return_var.set(f"{total_return:.2%}")
                self.annual_return_var.set(f"{annual_return:.2%}")
                self.max_drawdown_var.set(f"{min(drawdowns):.2%}")
                
                # 更新图表
                if i % 20 == 0:  # 每20天更新一次图表
                    self._update_charts(dates[:i+1], portfolio_values[:i+1], drawdowns[:i+1])
                
                # 模拟计算时间
                time.sleep(0.01)
            
            if self.is_running:
                # 保存结果
                self.backtest_results = {
                    'config': config,
                    'portfolio_values': portfolio_values,
                    'drawdowns': drawdowns,
                    'trades': trades,
                    'dates': [d.strftime('%Y-%m-%d') for d in dates]
                }
                
                # 计算最终指标
                self._calculate_final_metrics()
                
                # 更新结果显示
                self.parent.after(0, self._update_results_display)
                
                self.status_var.set("回测完成")
                self._log_message("回测完成")
                
                if self.on_backtest_complete:
                    self.on_backtest_complete(self.backtest_results)
            else:
                self.status_var.set("回测已停止")
                self._log_message("回测已停止")
                
        except Exception as e:
            self.status_var.set(f"回测失败: {str(e)}")
            self._log_message(f"回测失败: {str(e)}")
        finally:
            self.parent.after(0, self._reset_backtest_ui)
    
    def _update_charts(self, dates: List, values: List[float], drawdowns: List[float]):
        """更新图表"""
        try:
            # 清空图表
            self.ax1.clear()
            self.ax2.clear()
            
            # 绘制资金曲线
            self.ax1.plot(dates, values, 'b-', linewidth=1)
            self.ax1.set_title("资金曲线")
            self.ax1.set_ylabel("资产价值")
            self.ax1.grid(True, alpha=0.3)
            self.ax1.tick_params(axis='x', rotation=45)
            
            # 绘制回撤曲线
            self.ax2.fill_between(dates, drawdowns, 0, color='red', alpha=0.3)
            self.ax2.plot(dates, drawdowns, 'r-', linewidth=1)
            self.ax2.set_title("回撤曲线")
            self.ax2.set_ylabel("回撤 (%)")
            self.ax2.set_xlabel("日期")
            self.ax2.grid(True, alpha=0.3)
            self.ax2.tick_params(axis='x', rotation=45)
            
            # 刷新图表
            self.fig.tight_layout()
            self.canvas.draw()
            
        except Exception as e:
            print(f"更新图表失败: {str(e)}")
    
    def _log_message(self, message: str):
        """添加日志消息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
    
    def _clear_results(self):
        """清空结果"""
        # 清空图表
        self.ax1.clear()
        self.ax2.clear()
        self.canvas.draw()
        
        # 清空日志
        self.log_text.delete('1.0', tk.END)
        
        # 重置指标
        self.current_date_var.set("N/A")
        self.total_value_var.set("N/A")
        self.total_return_var.set("N/A")
        self.annual_return_var.set("N/A")
        self.sharpe_ratio_var.set("N/A")
        self.max_drawdown_var.set("N/A")
        
        # 重置进度
        self.progress_var.set(0)
    
    def _calculate_final_metrics(self):
        """计算最终绩效指标"""
        if not self.backtest_results:
            return
        
        values = self.backtest_results['portfolio_values']
        initial_capital = self.backtest_results['config']['initial_capital']
        
        # 计算收益率序列
        returns = pd.Series(values).pct_change().dropna()
        
        # 计算各项指标
        total_return = (values[-1] - initial_capital) / initial_capital
        annual_return = total_return * 365 / len(values)
        volatility = returns.std() * np.sqrt(252)
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        max_drawdown = min(self.backtest_results['drawdowns']) / 100
        
        # 更新显示
        self.sharpe_ratio_var.set(f"{sharpe_ratio:.3f}")
        
        # 保存指标到结果中
        self.backtest_results['metrics'] = {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': len(self.backtest_results['trades']),
            'win_rate': 0.6,  # 模拟胜率
            'profit_factor': 1.5  # 模拟盈亏比
        }
    
    def _update_results_display(self):
        """更新结果显示"""
        if not self.backtest_results:
            return
        
        # 更新绩效指标表格
        for item in self.performance_tree.get_children():
            self.performance_tree.delete(item)
        
        metrics = self.backtest_results.get('metrics', {})
        metric_items = [
            ('总收益率', f"{metrics.get('total_return', 0):.2%}"),
            ('年化收益率', f"{metrics.get('annual_return', 0):.2%}"),
            ('年化波动率', f"{metrics.get('volatility', 0):.2%}"),
            ('夏普比率', f"{metrics.get('sharpe_ratio', 0):.3f}"),
            ('最大回撤', f"{metrics.get('max_drawdown', 0):.2%}"),
            ('总交易次数', f"{metrics.get('total_trades', 0)}"),
            ('胜率', f"{metrics.get('win_rate', 0):.2%}"),
            ('盈亏比', f"{metrics.get('profit_factor', 0):.2f}")
        ]
        
        for name, value in metric_items:
            self.performance_tree.insert('', 'end', text=name, values=(value,))
        
        # 更新交易记录表格
        for item in self.trades_tree.get_children():
            self.trades_tree.delete(item)
        
        trades = self.backtest_results.get('trades', [])
        for trade in trades:
            self.trades_tree.insert(
                '', 'end',
                text=str(trade['id']),
                values=(
                    trade['date'],
                    trade['symbol'],
                    trade['side'],
                    f"{trade['quantity']:,}",
                    f"${trade['price']:.2f}",
                    f"${trade['value']:,.2f}",
                    f"${trade['pnl']:,.2f}"
                )
            )
        
        # 切换到结果页面
        self.notebook.select(2)
    
    def _reset_backtest_ui(self):
        """重置回测界面"""
        self.is_running = False
        self.start_backtest_btn.config(state='normal')
        self.stop_backtest_btn.config(state='disabled')
    
    def _on_save_config(self):
        """保存配置"""
        try:
            config = self._collect_backtest_config()
            
            # 模拟保存配置
            filename = f"backtest_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            print(f"保存配置到文件: {filename}")
            print(json.dumps(config, indent=2, ensure_ascii=False))
            
            messagebox.showinfo("成功", f"配置已保存到 {filename}")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
    
    def _on_load_config(self):
        """加载配置"""
        # 模拟加载配置
        messagebox.showinfo("提示", "加载配置功能待实现")
    
    def _on_export_results(self):
        """导出结果"""
        if not self.backtest_results:
            messagebox.showwarning("警告", "没有回测结果可导出")
            return
        
        try:
            filename = f"backtest_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            # 模拟导出功能
            print(f"导出结果到文件: {filename}")
            print(json.dumps(self.backtest_results, indent=2, default=str, ensure_ascii=False))
            
            messagebox.showinfo("成功", f"结果已导出到 {filename}")
            
        except Exception as e:
            messagebox.showerror("错误", f"导出结果失败: {str(e)}")
    
    def _on_generate_report(self):
        """生成报告"""
        if not self.backtest_results:
            messagebox.showwarning("警告", "没有回测结果可生成报告")
            return
        
        messagebox.showinfo("提示", "生成报告功能待实现")
    
    def _on_compare_results(self):
        """对比分析"""
        messagebox.showinfo("提示", "对比分析功能待实现")
    
    def _on_trade_double_click(self, event):
        """交易记录双击事件"""
        item = self.trades_tree.selection()[0] if self.trades_tree.selection() else None
        if item:
            trade_id = self.trades_tree.item(item, 'text')
            values = self.trades_tree.item(item, 'values')
            
            # 显示交易详情
            details = f"""
交易详情

交易ID: {trade_id}
日期: {values[0]}
标的: {values[1]}
方向: {values[2]}
数量: {values[3]}
价格: {values[4]}
金额: {values[5]}
盈亏: {values[6]}
"""
            messagebox.showinfo("交易详情", details)