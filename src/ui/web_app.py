"""
量化交易系统Web界面
基于Flask的Web版本GUI
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List
import pandas as pd
import plotly.graph_objs as go
import plotly.utils

# 导入系统组件
from ..data.data_manager import DataManager
from ..strategies.strategy_manager import StrategyManager
from ..backtest.backtest_engine import BacktestEngine

logger = logging.getLogger(__name__)

def create_web_app():
    """创建Flask Web应用"""
    app = Flask(__name__, 
                template_folder='templates',
                static_folder='static')
    
    # 配置
    app.config['SECRET_KEY'] = 'quantitative-trading-system-2024'
    app.config['JSON_AS_ASCII'] = False
    
    # 初始化系统组件
    data_manager = DataManager()
    strategy_manager = StrategyManager()
    backtest_engine = BacktestEngine(initial_capital=100000, commission=0.001)
    
    @app.route('/')
    def index():
        """主页"""
        return render_template('index.html')
    
    @app.route('/dashboard')
    def dashboard():
        """仪表板页面"""
        return render_template('dashboard.html')
    
    @app.route('/charts')
    def charts():
        """图表分析页面"""
        return render_template('charts.html')
    
    @app.route('/strategies')
    def strategies():
        """策略管理页面"""
        return render_template('strategies.html')
    
    @app.route('/data-sources')
    def data_sources():
        """数据源管理页面"""
        return render_template('data_sources.html')
    
    @app.route('/analysis')
    def analysis():
        """分析报告页面"""
        return render_template('analysis.html')
    
    @app.route('/monitoring')
    def monitoring():
        """实时监控页面"""
        return render_template('monitoring.html')
    
    # API端点
    @app.route('/api/chart/data')
    def get_chart_data():
        """获取图表数据"""
        symbol = request.args.get('symbol', 'AAPL')
        days = int(request.args.get('days', 30))
        
        try:
            # 生成模拟数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 这里应该从data_manager获取真实数据
            # data = data_manager.get_data(symbol, start_date, end_date)
            
            # 模拟K线数据
            dates = pd.date_range(start=start_date, end=end_date, freq='D')
            prices = []
            base_price = 150.0
            
            for i, date in enumerate(dates):
                # 简单的随机游走模拟
                change = (hash(str(date)) % 200 - 100) / 1000
                base_price *= (1 + change)
                
                open_price = base_price
                high_price = base_price * (1 + abs(change) * 2)
                low_price = base_price * (1 - abs(change) * 2)
                close_price = base_price
                volume = 1000000 + (hash(str(date)) % 500000)
                
                prices.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': volume
                })
            
            return jsonify({
                'symbol': symbol,
                'data': prices,
                'success': True
            })
            
        except Exception as e:
            logger.error(f"获取图表数据失败: {str(e)}")
            return jsonify({
                'error': str(e),
                'success': False
            }), 500
    
    @app.route('/api/strategies/list')
    def get_strategies():
        """获取策略列表"""
        try:
            # 这里应该从strategy_manager获取真实策略
            strategies = [
                {
                    'id': 1,
                    'name': '移动平均策略',
                    'description': '基于移动平均线交叉的交易策略',
                    'status': 'active',
                    'created_at': '2024-01-15',
                    'performance': {
                        'total_return': 15.6,
                        'sharpe_ratio': 1.23,
                        'max_drawdown': -8.4
                    }
                },
                {
                    'id': 2,
                    'name': 'RSI策略',
                    'description': '基于相对强弱指数的交易策略',
                    'status': 'inactive',
                    'created_at': '2024-01-20',
                    'performance': {
                        'total_return': 8.9,
                        'sharpe_ratio': 0.87,
                        'max_drawdown': -12.1
                    }
                },
                {
                    'id': 3,
                    'name': 'MACD策略',
                    'description': '基于MACD指标的交易策略',
                    'status': 'testing',
                    'created_at': '2024-01-25',
                    'performance': {
                        'total_return': 12.3,
                        'sharpe_ratio': 1.05,
                        'max_drawdown': -6.7
                    }
                }
            ]
            
            return jsonify({
                'strategies': strategies,
                'success': True
            })
            
        except Exception as e:
            logger.error(f"获取策略列表失败: {str(e)}")
            return jsonify({
                'error': str(e),
                'success': False
            }), 500
    
    @app.route('/api/backtest/run', methods=['POST'])
    def run_backtest():
        """运行回测"""
        try:
            data = request.get_json()
            strategy_id = data.get('strategy_id')
            start_date = data.get('start_date')
            end_date = data.get('end_date')
            
            # 模拟回测结果
            result = {
                'strategy_id': strategy_id,
                'start_date': start_date,
                'end_date': end_date,
                'metrics': {
                    'total_return': 15.6,
                    'annual_return': 12.3,
                    'sharpe_ratio': 1.23,
                    'max_drawdown': -8.4,
                    'win_rate': 58.3,
                    'profit_factor': 1.45
                },
                'trades': 156,
                'success': True
            }
            
            return jsonify(result)
            
        except Exception as e:
            logger.error(f"运行回测失败: {str(e)}")
            return jsonify({
                'error': str(e),
                'success': False
            }), 500
    
    @app.route('/api/data-sources/status')
    def get_data_sources_status():
        """获取数据源状态"""
        try:
            sources = [
                {
                    'name': 'Yahoo Finance',
                    'type': 'US Stock',
                    'status': 'connected',
                    'last_sync': '2024-01-30 10:30:00',
                    'symbols_count': 500
                },
                {
                    'name': 'Tushare',
                    'type': 'A Stock',
                    'status': 'connected',
                    'last_sync': '2024-01-30 09:15:00',
                    'symbols_count': 4000
                },
                {
                    'name': 'Binance',
                    'type': 'Crypto',
                    'status': 'disconnected',
                    'last_sync': '2024-01-29 18:45:00',
                    'symbols_count': 200
                }
            ]
            
            return jsonify({
                'sources': sources,
                'success': True
            })
            
        except Exception as e:
            logger.error(f"获取数据源状态失败: {str(e)}")
            return jsonify({
                'error': str(e),
                'success': False
            }), 500
    
    @app.route('/api/monitoring/status')
    def get_monitoring_status():
        """获取系统监控状态"""
        try:
            status = {
                'system': {
                    'cpu_usage': 45.2,
                    'memory_usage': 62.8,
                    'disk_usage': 78.5,
                    'uptime': '2 days, 14:32:15'
                },
                'trading': {
                    'active_strategies': 3,
                    'total_positions': 12,
                    'daily_pnl': 2456.78,
                    'total_pnl': 15678.90
                },
                'data': {
                    'last_update': '2024-01-30 10:35:00',
                    'data_quality': 98.5,
                    'missing_data': 0.02
                }
            }
            
            return jsonify({
                'status': status,
                'success': True
            })
            
        except Exception as e:
            logger.error(f"获取监控状态失败: {str(e)}")
            return jsonify({
                'error': str(e),
                'success': False
            }), 500
    
    return app


def main():
    """启动Web应用"""
    app = create_web_app()
    
    print("=" * 60)
    print("量化交易系统 Web 界面")
    print("Quantitative Trading System Web Interface")
    print("=" * 60)
    print("访问地址: http://localhost:5000")
    print("按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=False
    )


if __name__ == "__main__":
    main()