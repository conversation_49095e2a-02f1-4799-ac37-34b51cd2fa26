"""
Indicator panel component for technical indicator display and management.
"""

import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import logging

from ...indicators.engine import IndicatorEngine
from .chart_config import ChartConfig
from .chart_utils import ChartDataProcessor


logger = logging.getLogger(__name__)


class IndicatorPanel:
    """
    Panel component for displaying and managing technical indicators.
    Supports multiple indicator types and overlay configurations.
    """
    
    def __init__(self, 
                 config: Optional[ChartConfig] = None,
                 indicator_engine: Optional[IndicatorEngine] = None):
        """
        Initialize indicator panel.
        
        Args:
            config: Chart configuration
            indicator_engine: Indicator calculation engine
        """
        self.config = config or ChartConfig()
        self.indicator_engine = indicator_engine or IndicatorEngine()
        self.processor = ChartDataProcessor(self.config)
        
        # Available indicators configuration
        self.available_indicators = {
            'moving_averages': {
                'SMA': {'periods': [5, 10, 20, 50, 200]},
                'EMA': {'periods': [5, 10, 20, 50, 200]},
                'WMA': {'periods': [5, 10, 20, 50]},
                'VWMA': {'periods': [20, 50]}
            },
            'momentum': {
                'RSI': {'period': 14, 'overbought': 70, 'oversold': 30},
                'MACD': {'fast': 12, 'slow': 26, 'signal': 9},
                'Stochastic': {'k_period': 14, 'd_period': 3},
                'Williams_R': {'period': 14},
                'CCI': {'period': 20}
            },
            'volatility': {
                'Bollinger_Bands': {'period': 20, 'std_dev': 2},
                'ATR': {'period': 14},
                'Keltner_Channels': {'period': 20, 'multiplier': 2}
            },
            'volume': {
                'Volume_SMA': {'period': 20},
                'OBV': {},
                'Volume_Profile': {'bins': 50},
                'VWAP': {}
            },
            'trend': {
                'ADX': {'period': 14},
                'Parabolic_SAR': {'acceleration': 0.02, 'maximum': 0.2},
                'Ichimoku': {'tenkan': 9, 'kijun': 26, 'senkou_b': 52}
            }
        }
        
        # Active indicators
        self.active_indicators: Dict[str, Dict[str, Any]] = {}
        self.indicator_data: Dict[str, pd.DataFrame] = {}
        
        logger.info("IndicatorPanel initialized")
    
    def get_available_indicators(self) -> Dict[str, Dict[str, Any]]:
        """
        Get list of available indicators with their default parameters.
        
        Returns:
            Dictionary of available indicators by category
        """
        return self.available_indicators.copy()
    
    def add_indicator(self, 
                     indicator_name: str, 
                     category: str,
                     market_data: pd.DataFrame,
                     **params) -> bool:
        """
        Add indicator to the panel.
        
        Args:
            indicator_name: Name of the indicator
            category: Indicator category
            market_data: Market data for calculation
            **params: Indicator parameters
            
        Returns:
            True if indicator added successfully
        """
        try:
            if market_data is None or market_data.empty:
                logger.error("No market data provided for indicator calculation")
                return False
            
            # Get default parameters and merge with provided params
            default_params = self.available_indicators.get(category, {}).get(indicator_name, {})
            final_params = {**default_params, **params}
            
            # Calculate indicator
            result = self.indicator_engine.calculate(
                indicator_name=indicator_name.lower(),
                data=market_data,
                **final_params
            )
            
            # Extract data from result
            if hasattr(result, 'data'):
                if isinstance(result.data, pd.DataFrame):
                    indicator_data = result.data
                elif isinstance(result.data, pd.Series):
                    # Convert series to dataframe with timestamp
                    indicator_data = pd.DataFrame({
                        'timestamp': market_data['timestamp'],
                        indicator_name: result.data
                    })
                else:
                    indicator_data = None
            else:
                indicator_data = result
            
            if indicator_data is not None and not indicator_data.empty:
                indicator_key = f"{indicator_name}_{hash(str(final_params))}"
                
                self.active_indicators[indicator_key] = {
                    'name': indicator_name,
                    'category': category,
                    'params': final_params,
                    'display_name': self._generate_display_name(indicator_name, final_params),
                    'color': self._get_indicator_color(indicator_name, indicator_key),
                    'overlay': self._is_overlay_indicator(indicator_name),
                    'panel_height': self._get_panel_height(indicator_name)
                }
                
                self.indicator_data[indicator_key] = indicator_data
                
                logger.info(f"Added indicator: {indicator_key}")
                return True
            else:
                logger.warning(f"Failed to calculate indicator: {indicator_name}")
                return False
                
        except Exception as e:
            logger.error(f"Error adding indicator {indicator_name}: {e}")
            return False
    
    def remove_indicator(self, indicator_key: str) -> bool:
        """
        Remove indicator from the panel.
        
        Args:
            indicator_key: Key of the indicator to remove
            
        Returns:
            True if indicator removed successfully
        """
        try:
            if indicator_key in self.active_indicators:
                del self.active_indicators[indicator_key]
            
            if indicator_key in self.indicator_data:
                del self.indicator_data[indicator_key]
            
            logger.info(f"Removed indicator: {indicator_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing indicator {indicator_key}: {e}")
            return False
    
    def update_indicator_params(self, 
                               indicator_key: str, 
                               market_data: pd.DataFrame,
                               **new_params) -> bool:
        """
        Update indicator parameters and recalculate.
        
        Args:
            indicator_key: Key of the indicator to update
            market_data: Market data for recalculation
            **new_params: New parameters
            
        Returns:
            True if indicator updated successfully
        """
        try:
            if indicator_key not in self.active_indicators:
                logger.error(f"Indicator {indicator_key} not found")
                return False
            
            indicator_info = self.active_indicators[indicator_key]
            
            # Update parameters
            updated_params = {**indicator_info['params'], **new_params}
            
            # Recalculate indicator
            result = self.indicator_engine.calculate(
                indicator_name=indicator_info['name'].lower(),
                data=market_data,
                **updated_params
            )
            
            # Extract data from result
            if hasattr(result, 'data'):
                if isinstance(result.data, pd.DataFrame):
                    indicator_data = result.data
                elif isinstance(result.data, pd.Series):
                    # Convert series to dataframe with timestamp
                    indicator_data = pd.DataFrame({
                        'timestamp': market_data['timestamp'],
                        indicator_info['name']: result.data
                    })
                else:
                    indicator_data = None
            else:
                indicator_data = result
            
            if indicator_data is not None and not indicator_data.empty:
                # Update stored information
                self.active_indicators[indicator_key]['params'] = updated_params
                self.active_indicators[indicator_key]['display_name'] = self._generate_display_name(
                    indicator_info['name'], updated_params
                )
                self.indicator_data[indicator_key] = indicator_data
                
                logger.info(f"Updated indicator: {indicator_key}")
                return True
            else:
                logger.warning(f"Failed to recalculate indicator: {indicator_key}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating indicator {indicator_key}: {e}")
            return False
    
    def get_indicator_data(self, indicator_key: str) -> Optional[pd.DataFrame]:
        """
        Get data for specific indicator.
        
        Args:
            indicator_key: Key of the indicator
            
        Returns:
            DataFrame containing indicator data
        """
        return self.indicator_data.get(indicator_key)
    
    def get_all_indicator_data(self) -> Dict[str, pd.DataFrame]:
        """
        Get data for all active indicators.
        
        Returns:
            Dictionary mapping indicator keys to their data
        """
        return self.indicator_data.copy()
    
    def get_overlay_indicators(self) -> Dict[str, Dict[str, Any]]:
        """
        Get indicators that should be overlaid on the main chart.
        
        Returns:
            Dictionary of overlay indicators
        """
        return {
            key: info for key, info in self.active_indicators.items()
            if info.get('overlay', False)
        }
    
    def get_panel_indicators(self) -> Dict[str, Dict[str, Any]]:
        """
        Get indicators that should be displayed in separate panels.
        
        Returns:
            Dictionary of panel indicators
        """
        return {
            key: info for key, info in self.active_indicators.items()
            if not info.get('overlay', False)
        }
    
    def get_processed_indicator_data(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get processed indicator data for chart rendering.
        
        Returns:
            Dictionary of processed indicator data
        """
        processed_data = {}
        
        for indicator_key, indicator_data in self.indicator_data.items():
            if indicator_key in self.active_indicators:
                indicator_name = self.active_indicators[indicator_key]['name']
                processed_data[indicator_key] = self.processor.process_indicator_data(
                    indicator_data, indicator_name
                )
        
        return processed_data
    
    def get_panel_layout(self) -> List[Dict[str, Any]]:
        """
        Get layout configuration for indicator panels.
        
        Returns:
            List of panel configurations
        """
        panels = []
        panel_indicators = self.get_panel_indicators()
        
        # Group indicators by panel type
        panel_groups = {}
        for key, info in panel_indicators.items():
            panel_type = info['category']
            if panel_type not in panel_groups:
                panel_groups[panel_type] = []
            panel_groups[panel_type].append((key, info))
        
        # Create panel configurations
        for panel_type, indicators in panel_groups.items():
            panel_config = {
                'type': panel_type,
                'height': max(info['panel_height'] for _, info in indicators),
                'indicators': [
                    {
                        'key': key,
                        'name': info['display_name'],
                        'color': info['color'],
                        'data_key': key
                    }
                    for key, info in indicators
                ]
            }
            panels.append(panel_config)
        
        return panels
    
    def clear_all_indicators(self) -> None:
        """Clear all indicators from the panel."""
        self.active_indicators.clear()
        self.indicator_data.clear()
        logger.info("All indicators cleared from panel")
    
    def get_indicator_statistics(self, indicator_key: str) -> Optional[Dict[str, Any]]:
        """
        Get statistics for specific indicator.
        
        Args:
            indicator_key: Key of the indicator
            
        Returns:
            Dictionary containing indicator statistics
        """
        if indicator_key not in self.indicator_data:
            return None
        
        data = self.indicator_data[indicator_key]
        info = self.active_indicators[indicator_key]
        
        # Calculate basic statistics
        stats = {
            'name': info['display_name'],
            'category': info['category'],
            'params': info['params'],
            'data_points': len(data),
            'last_update': data['timestamp'].max() if 'timestamp' in data.columns else None
        }
        
        # Add value-specific statistics
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if col != 'timestamp':
                col_data = data[col].dropna()
                if not col_data.empty:
                    stats[f'{col}_current'] = float(col_data.iloc[-1])
                    stats[f'{col}_min'] = float(col_data.min())
                    stats[f'{col}_max'] = float(col_data.max())
                    stats[f'{col}_mean'] = float(col_data.mean())
                    stats[f'{col}_std'] = float(col_data.std())
        
        return stats
    
    def _generate_display_name(self, indicator_name: str, params: Dict[str, Any]) -> str:
        """Generate display name for indicator."""
        if not params:
            return indicator_name
        
        # Common parameter mappings
        param_mappings = {
            'period': 'period',
            'periods': 'periods',
            'fast': 'fast',
            'slow': 'slow',
            'signal': 'signal',
            'std_dev': 'std',
            'multiplier': 'mult'
        }
        
        param_parts = []
        for key, value in params.items():
            if key in param_mappings and value is not None:
                if isinstance(value, list):
                    param_parts.append(f"{','.join(map(str, value))}")
                else:
                    param_parts.append(str(value))
        
        if param_parts:
            return f"{indicator_name}({','.join(param_parts)})"
        else:
            return indicator_name
    
    def _get_indicator_color(self, indicator_name: str, indicator_key: str) -> str:
        """Get color for indicator."""
        # Use theme colors if available
        if indicator_name in self.config.theme.ma_colors:
            return self.config.theme.ma_colors[indicator_name]
        
        # Default color rotation
        colors = [
            "#ffff00", "#ff8800", "#ff0088", "#8800ff", "#0088ff",
            "#00ff88", "#ff4444", "#44ff44", "#4444ff", "#ff44ff"
        ]
        
        # Use hash of indicator key to get consistent color
        color_index = hash(indicator_key) % len(colors)
        return colors[color_index]
    
    def _is_overlay_indicator(self, indicator_name: str) -> bool:
        """Check if indicator should be overlaid on main chart."""
        overlay_indicators = {
            'SMA', 'EMA', 'WMA', 'VWMA', 'Bollinger_Bands', 
            'Keltner_Channels', 'Parabolic_SAR', 'Ichimoku'
        }
        return indicator_name in overlay_indicators
    
    def _get_panel_height(self, indicator_name: str) -> int:
        """Get recommended panel height for indicator."""
        height_mapping = {
            'RSI': 80,
            'MACD': 100,
            'Stochastic': 80,
            'Williams_R': 80,
            'CCI': 80,
            'ATR': 60,
            'Volume_SMA': 80,
            'OBV': 80,
            'Volume_Profile': 120,
            'ADX': 80
        }
        
        return height_mapping.get(indicator_name, self.config.theme.indicator_height)