"""
Chart configuration and theme management.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, Tuple
from enum import Enum


class ChartType(Enum):
    """Chart type enumeration."""
    CANDLESTICK = "candlestick"
    LINE = "line"
    AREA = "area"
    VOLUME = "volume"
    INDICATOR = "indicator"


class TimeInterval(Enum):
    """Time interval enumeration."""
    MINUTE_1 = "1m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    MINUTE_30 = "30m"
    HOUR_1 = "1h"
    HOUR_4 = "4h"
    DAY_1 = "1d"
    WEEK_1 = "1w"
    MONTH_1 = "1M"


@dataclass
class ChartTheme:
    """Chart theme configuration."""
    background_color: str = "#1e1e1e"
    grid_color: str = "#333333"
    text_color: str = "#ffffff"
    
    # Candlestick colors
    up_color: str = "#00ff88"
    down_color: str = "#ff4444"
    up_border_color: str = "#00cc66"
    down_border_color: str = "#cc3333"
    
    # Volume colors
    volume_up_color: str = "#00ff8844"
    volume_down_color: str = "#ff444444"
    
    # Indicator colors
    ma_colors: Dict[str, str] = field(default_factory=lambda: {
        "MA5": "#ffff00",
        "MA10": "#ff8800",
        "MA20": "#ff0088",
        "MA50": "#8800ff",
        "MA200": "#0088ff"
    })
    
    # Signal colors
    buy_signal_color: str = "#00ff00"
    sell_signal_color: str = "#ff0000"
    signal_size: int = 8
    
    # Chart dimensions
    chart_height: int = 400
    volume_height: int = 100
    indicator_height: int = 80


@dataclass
class ChartConfig:
    """Main chart configuration."""
    theme: ChartTheme = field(default_factory=ChartTheme)
    chart_type: ChartType = ChartType.CANDLESTICK
    time_interval: TimeInterval = TimeInterval.DAY_1
    
    # Display options
    show_volume: bool = True
    show_grid: bool = True
    show_crosshair: bool = True
    show_price_line: bool = True
    show_time_scale: bool = True
    show_price_scale: bool = True
    
    # Interaction options
    enable_zoom: bool = True
    enable_pan: bool = True
    enable_selection: bool = True
    
    # Data options
    max_visible_candles: int = 200
    preload_candles: int = 50
    
    # Animation options
    enable_animations: bool = True
    animation_duration: int = 300
    
    # Precision
    price_precision: int = 2
    volume_precision: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            'theme': {
                'background_color': self.theme.background_color,
                'grid_color': self.theme.grid_color,
                'text_color': self.theme.text_color,
                'up_color': self.theme.up_color,
                'down_color': self.theme.down_color,
                'up_border_color': self.theme.up_border_color,
                'down_border_color': self.theme.down_border_color,
                'volume_up_color': self.theme.volume_up_color,
                'volume_down_color': self.theme.volume_down_color,
                'ma_colors': self.theme.ma_colors,
                'buy_signal_color': self.theme.buy_signal_color,
                'sell_signal_color': self.theme.sell_signal_color,
                'signal_size': self.theme.signal_size,
                'chart_height': self.theme.chart_height,
                'volume_height': self.theme.volume_height,
                'indicator_height': self.theme.indicator_height
            },
            'chart_type': self.chart_type.value,
            'time_interval': self.time_interval.value,
            'show_volume': self.show_volume,
            'show_grid': self.show_grid,
            'show_crosshair': self.show_crosshair,
            'show_price_line': self.show_price_line,
            'show_time_scale': self.show_time_scale,
            'show_price_scale': self.show_price_scale,
            'enable_zoom': self.enable_zoom,
            'enable_pan': self.enable_pan,
            'enable_selection': self.enable_selection,
            'max_visible_candles': self.max_visible_candles,
            'preload_candles': self.preload_candles,
            'enable_animations': self.enable_animations,
            'animation_duration': self.animation_duration,
            'price_precision': self.price_precision,
            'volume_precision': self.volume_precision
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChartConfig':
        """Create config from dictionary."""
        theme_data = data.get('theme', {})
        theme = ChartTheme(
            background_color=theme_data.get('background_color', "#1e1e1e"),
            grid_color=theme_data.get('grid_color', "#333333"),
            text_color=theme_data.get('text_color', "#ffffff"),
            up_color=theme_data.get('up_color', "#00ff88"),
            down_color=theme_data.get('down_color', "#ff4444"),
            up_border_color=theme_data.get('up_border_color', "#00cc66"),
            down_border_color=theme_data.get('down_border_color', "#cc3333"),
            volume_up_color=theme_data.get('volume_up_color', "#00ff8844"),
            volume_down_color=theme_data.get('volume_down_color', "#ff444444"),
            ma_colors=theme_data.get('ma_colors', {
                "MA5": "#ffff00",
                "MA10": "#ff8800", 
                "MA20": "#ff0088",
                "MA50": "#8800ff",
                "MA200": "#0088ff"
            }),
            buy_signal_color=theme_data.get('buy_signal_color', "#00ff00"),
            sell_signal_color=theme_data.get('sell_signal_color', "#ff0000"),
            signal_size=theme_data.get('signal_size', 8),
            chart_height=theme_data.get('chart_height', 400),
            volume_height=theme_data.get('volume_height', 100),
            indicator_height=theme_data.get('indicator_height', 80)
        )
        
        return cls(
            theme=theme,
            chart_type=ChartType(data.get('chart_type', ChartType.CANDLESTICK.value)),
            time_interval=TimeInterval(data.get('time_interval', TimeInterval.DAY_1.value)),
            show_volume=data.get('show_volume', True),
            show_grid=data.get('show_grid', True),
            show_crosshair=data.get('show_crosshair', True),
            show_price_line=data.get('show_price_line', True),
            show_time_scale=data.get('show_time_scale', True),
            show_price_scale=data.get('show_price_scale', True),
            enable_zoom=data.get('enable_zoom', True),
            enable_pan=data.get('enable_pan', True),
            enable_selection=data.get('enable_selection', True),
            max_visible_candles=data.get('max_visible_candles', 200),
            preload_candles=data.get('preload_candles', 50),
            enable_animations=data.get('enable_animations', True),
            animation_duration=data.get('animation_duration', 300),
            price_precision=data.get('price_precision', 2),
            volume_precision=data.get('volume_precision', 0)
        )


# Predefined themes
DARK_THEME = ChartTheme()

LIGHT_THEME = ChartTheme(
    background_color="#ffffff",
    grid_color="#e0e0e0",
    text_color="#000000",
    up_color="#00aa00",
    down_color="#aa0000",
    up_border_color="#008800",
    down_border_color="#880000",
    volume_up_color="#00aa0044",
    volume_down_color="#aa000044"
)