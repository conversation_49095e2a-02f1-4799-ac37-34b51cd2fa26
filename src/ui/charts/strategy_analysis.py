"""
Strategy analysis panel for displaying trading signals and performance metrics.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging

from ...models.trading import Trade, Signal, OrderSide, SignalAction
from ...strategies.base import BaseStrategy
from ...analysis.metrics import PerformanceMetrics
from .chart_config import ChartConfig
from .chart_utils import ChartDataProcessor


logger = logging.getLogger(__name__)


class StrategyAnalysisPanel:
    """
    Panel component for strategy analysis, signal visualization, and performance tracking.
    """
    
    def __init__(self, 
                 config: Optional[ChartConfig] = None,
                 performance_metrics: Optional[PerformanceMetrics] = None):
        """
        Initialize strategy analysis panel.
        
        Args:
            config: Chart configuration
            performance_metrics: Performance metrics calculator
        """
        self.config = config or ChartConfig()
        self.performance_metrics = performance_metrics or PerformanceMetrics()
        self.processor = ChartDataProcessor(self.config)
        
        # Strategy data
        self.strategies: Dict[str, BaseStrategy] = {}
        self.signals: Dict[str, List[Signal]] = {}
        self.trades: Dict[str, List[Trade]] = {}
        self.positions: Dict[str, List[Dict[str, Any]]] = {}
        
        # Analysis results
        self.performance_data: Dict[str, Dict[str, Any]] = {}
        self.signal_statistics: Dict[str, Dict[str, Any]] = {}
        
        logger.info("StrategyAnalysisPanel initialized")
    
    def add_strategy(self, strategy_id: str, strategy: BaseStrategy) -> None:
        """
        Add strategy for analysis.
        
        Args:
            strategy_id: Unique strategy identifier
            strategy: Strategy instance
        """
        self.strategies[strategy_id] = strategy
        
        # Initialize data containers
        if strategy_id not in self.signals:
            self.signals[strategy_id] = []
        if strategy_id not in self.trades:
            self.trades[strategy_id] = []
        if strategy_id not in self.positions:
            self.positions[strategy_id] = []
        
        logger.info(f"Added strategy: {strategy_id}")
    
    def remove_strategy(self, strategy_id: str) -> bool:
        """
        Remove strategy from analysis.
        
        Args:
            strategy_id: Strategy identifier to remove
            
        Returns:
            True if strategy removed successfully
        """
        try:
            if strategy_id in self.strategies:
                del self.strategies[strategy_id]
            if strategy_id in self.signals:
                del self.signals[strategy_id]
            if strategy_id in self.trades:
                del self.trades[strategy_id]
            if strategy_id in self.positions:
                del self.positions[strategy_id]
            if strategy_id in self.performance_data:
                del self.performance_data[strategy_id]
            if strategy_id in self.signal_statistics:
                del self.signal_statistics[strategy_id]
            
            logger.info(f"Removed strategy: {strategy_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing strategy {strategy_id}: {e}")
            return False
    
    def add_signals(self, strategy_id: str, signals: List[Signal]) -> None:
        """
        Add signals for strategy analysis.
        
        Args:
            strategy_id: Strategy identifier
            signals: List of signals to add
        """
        if strategy_id not in self.signals:
            self.signals[strategy_id] = []
        
        self.signals[strategy_id].extend(signals)
        self._update_signal_statistics(strategy_id)
        
        logger.info(f"Added {len(signals)} signals for strategy {strategy_id}")
    
    def add_trades(self, strategy_id: str, trades: List[Trade]) -> None:
        """
        Add trades for strategy analysis.
        
        Args:
            strategy_id: Strategy identifier
            trades: List of trades to add
        """
        if strategy_id not in self.trades:
            self.trades[strategy_id] = []
        
        self.trades[strategy_id].extend(trades)
        self._update_performance_data(strategy_id)
        
        logger.info(f"Added {len(trades)} trades for strategy {strategy_id}")
    
    def add_positions(self, strategy_id: str, positions: List[Dict[str, Any]]) -> None:
        """
        Add positions for strategy analysis.
        
        Args:
            strategy_id: Strategy identifier
            positions: List of positions to add
        """
        if strategy_id not in self.positions:
            self.positions[strategy_id] = []
        
        self.positions[strategy_id].extend(positions)
        
        logger.info(f"Added {len(positions)} positions for strategy {strategy_id}")
    
    def get_strategy_signals(self, strategy_id: str) -> List[Signal]:
        """
        Get signals for specific strategy.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            List of signals for the strategy
        """
        return self.signals.get(strategy_id, [])
    
    def get_strategy_trades(self, strategy_id: str) -> List[Trade]:
        """
        Get trades for specific strategy.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            List of trades for the strategy
        """
        return self.trades.get(strategy_id, [])
    
    def get_strategy_positions(self, strategy_id: str) -> List[Dict[str, Any]]:
        """
        Get positions for specific strategy.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            List of positions for the strategy
        """
        return self.positions.get(strategy_id, [])
    
    def get_processed_signals(self, strategy_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get processed signals for chart rendering.
        
        Args:
            strategy_id: Specific strategy ID, or None for all strategies
            
        Returns:
            Dictionary of processed signal data
        """
        processed_signals = {}
        
        strategies_to_process = [strategy_id] if strategy_id else list(self.signals.keys())
        
        for sid in strategies_to_process:
            if sid in self.signals:
                signals = self.signals[sid]
                processed_signals[sid] = self.processor.process_signals(signals)
        
        return processed_signals
    
    def get_processed_trades(self, strategy_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get processed trades for chart rendering.
        
        Args:
            strategy_id: Specific strategy ID, or None for all strategies
            
        Returns:
            Dictionary of processed trade data
        """
        processed_trades = {}
        
        strategies_to_process = [strategy_id] if strategy_id else list(self.trades.keys())
        
        for sid in strategies_to_process:
            if sid in self.trades:
                trades = self.trades[sid]
                processed_trades[sid] = self.processor.process_trade_signals(trades)
        
        return processed_trades
    
    def get_performance_summary(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """
        Get performance summary for strategy.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            Dictionary containing performance metrics
        """
        return self.performance_data.get(strategy_id)
    
    def get_signal_statistics(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """
        Get signal statistics for strategy.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            Dictionary containing signal statistics
        """
        return self.signal_statistics.get(strategy_id)
    
    def get_strategy_comparison(self) -> Dict[str, Any]:
        """
        Get comparison data for all strategies.
        
        Returns:
            Dictionary containing strategy comparison metrics
        """
        comparison_data = {
            'strategies': [],
            'metrics': {
                'total_return': {},
                'sharpe_ratio': {},
                'max_drawdown': {},
                'win_rate': {},
                'total_trades': {},
                'avg_trade_duration': {}
            }
        }
        
        for strategy_id in self.strategies.keys():
            comparison_data['strategies'].append(strategy_id)
            
            perf_data = self.performance_data.get(strategy_id, {})
            signal_stats = self.signal_statistics.get(strategy_id, {})
            
            comparison_data['metrics']['total_return'][strategy_id] = perf_data.get('total_return', 0)
            comparison_data['metrics']['sharpe_ratio'][strategy_id] = perf_data.get('sharpe_ratio', 0)
            comparison_data['metrics']['max_drawdown'][strategy_id] = perf_data.get('max_drawdown', 0)
            comparison_data['metrics']['win_rate'][strategy_id] = perf_data.get('win_rate', 0)
            comparison_data['metrics']['total_trades'][strategy_id] = signal_stats.get('total_signals', 0)
            comparison_data['metrics']['avg_trade_duration'][strategy_id] = perf_data.get('avg_trade_duration', 0)
        
        return comparison_data
    
    def get_equity_curve(self, strategy_id: str) -> Optional[List[Dict[str, Any]]]:
        """
        Get equity curve data for strategy.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            List of equity curve points
        """
        if strategy_id not in self.trades:
            return None
        
        trades = self.trades[strategy_id]
        if not trades:
            return None
        
        # Calculate cumulative P&L
        equity_curve = []
        cumulative_pnl = 0
        
        for trade in sorted(trades, key=lambda t: t.timestamp):
            if hasattr(trade, 'pnl') and trade.pnl is not None:
                cumulative_pnl += trade.pnl
            
            equity_curve.append({
                'time': int(trade.timestamp.timestamp() * 1000),
                'value': cumulative_pnl,
                'trade_id': trade.id
            })
        
        return equity_curve
    
    def get_drawdown_curve(self, strategy_id: str) -> Optional[List[Dict[str, Any]]]:
        """
        Get drawdown curve data for strategy.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            List of drawdown curve points
        """
        equity_curve = self.get_equity_curve(strategy_id)
        if not equity_curve:
            return None
        
        drawdown_curve = []
        peak = 0
        
        for point in equity_curve:
            value = point['value']
            peak = max(peak, value)
            drawdown = (value - peak) / peak if peak != 0 else 0
            
            drawdown_curve.append({
                'time': point['time'],
                'value': drawdown * 100,  # Convert to percentage
                'peak': peak
            })
        
        return drawdown_curve
    
    def get_signal_distribution(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """
        Get signal distribution analysis.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            Dictionary containing signal distribution data
        """
        if strategy_id not in self.signals:
            return None
        
        signals = self.signals[strategy_id]
        if not signals:
            return None
        
        # Analyze signal distribution
        buy_signals = [s for s in signals if s.action == SignalAction.BUY]
        sell_signals = [s for s in signals if s.action == SignalAction.SELL]
        
        # Time distribution (by hour of day)
        hour_distribution = {}
        for signal in signals:
            hour = signal.timestamp.hour
            hour_distribution[hour] = hour_distribution.get(hour, 0) + 1
        
        # Confidence distribution
        confidence_ranges = {'0-20': 0, '20-40': 0, '40-60': 0, '60-80': 0, '80-100': 0}
        for signal in signals:
            if signal.confidence is not None:
                conf = signal.confidence * 100
                if conf <= 20:
                    confidence_ranges['0-20'] += 1
                elif conf <= 40:
                    confidence_ranges['20-40'] += 1
                elif conf <= 60:
                    confidence_ranges['40-60'] += 1
                elif conf <= 80:
                    confidence_ranges['60-80'] += 1
                else:
                    confidence_ranges['80-100'] += 1
        
        return {
            'total_signals': len(signals),
            'buy_signals': len(buy_signals),
            'sell_signals': len(sell_signals),
            'buy_sell_ratio': len(buy_signals) / len(sell_signals) if sell_signals else float('inf'),
            'hour_distribution': hour_distribution,
            'confidence_distribution': confidence_ranges,
            'avg_confidence': np.mean([s.confidence for s in signals if s.confidence is not None]) if signals else 0
        }
    
    def _update_signal_statistics(self, strategy_id: str) -> None:
        """Update signal statistics for strategy."""
        try:
            signals = self.signals.get(strategy_id, [])
            if not signals:
                return
            
            # Calculate basic statistics
            total_signals = len(signals)
            buy_signals = len([s for s in signals if s.action == SignalAction.BUY])
            sell_signals = len([s for s in signals if s.action == SignalAction.SELL])
            
            # Calculate time-based statistics
            if len(signals) > 1:
                time_diffs = []
                sorted_signals = sorted(signals, key=lambda s: s.timestamp)
                for i in range(1, len(sorted_signals)):
                    diff = (sorted_signals[i].timestamp - sorted_signals[i-1].timestamp).total_seconds()
                    time_diffs.append(diff)
                
                avg_signal_interval = np.mean(time_diffs) if time_diffs else 0
            else:
                avg_signal_interval = 0
            
            # Calculate confidence statistics
            confidences = [s.confidence for s in signals if s.confidence is not None]
            avg_confidence = np.mean(confidences) if confidences else 0
            
            self.signal_statistics[strategy_id] = {
                'total_signals': total_signals,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'buy_sell_ratio': buy_signals / sell_signals if sell_signals > 0 else float('inf'),
                'avg_signal_interval': avg_signal_interval,
                'avg_confidence': avg_confidence,
                'last_signal_time': max(s.timestamp for s in signals) if signals else None
            }
            
        except Exception as e:
            logger.error(f"Error updating signal statistics for {strategy_id}: {e}")
    
    def _update_performance_data(self, strategy_id: str) -> None:
        """Update performance data for strategy."""
        try:
            trades = self.trades.get(strategy_id, [])
            if not trades:
                return
            
            # Calculate performance metrics using the performance metrics engine
            trades_df = pd.DataFrame([
                {
                    'timestamp': trade.timestamp,
                    'symbol': trade.symbol,
                    'side': trade.side,
                    'quantity': trade.quantity,
                    'price': trade.price,
                    'pnl': getattr(trade, 'pnl', 0)
                }
                for trade in trades
            ])
            
            if not trades_df.empty:
                metrics = self.performance_metrics.calculate_metrics(trades_df)
                self.performance_data[strategy_id] = metrics
            
        except Exception as e:
            logger.error(f"Error updating performance data for {strategy_id}: {e}")
    
    def clear_strategy_data(self, strategy_id: str) -> None:
        """Clear all data for specific strategy."""
        if strategy_id in self.signals:
            self.signals[strategy_id].clear()
        if strategy_id in self.trades:
            self.trades[strategy_id].clear()
        if strategy_id in self.positions:
            self.positions[strategy_id].clear()
        if strategy_id in self.performance_data:
            del self.performance_data[strategy_id]
        if strategy_id in self.signal_statistics:
            del self.signal_statistics[strategy_id]
        
        logger.info(f"Cleared all data for strategy: {strategy_id}")
    
    def clear_all_data(self) -> None:
        """Clear all strategy data."""
        self.signals.clear()
        self.trades.clear()
        self.positions.clear()
        self.performance_data.clear()
        self.signal_statistics.clear()
        
        logger.info("Cleared all strategy analysis data")