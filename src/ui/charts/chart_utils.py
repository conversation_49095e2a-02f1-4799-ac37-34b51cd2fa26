"""
Chart utilities for data processing and rendering.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
import json
import logging

from ...models.market_data import MarketData
from ...models.trading import Trade, Signal, OrderSide, SignalAction
from .chart_config import ChartConfig, ChartType


logger = logging.getLogger(__name__)


class ChartDataProcessor:
    """Process data for chart rendering."""
    
    def __init__(self, config: ChartConfig):
        self.config = config
    
    def process_market_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Process market data for chart display."""
        if data.empty:
            return {'candlestick': [], 'volume': []}
        
        # Ensure required columns exist
        required_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Sort by timestamp
        data = data.sort_values('timestamp')
        
        # Process candlestick data
        candlestick_data = []
        volume_data = []
        
        for _, row in data.iterrows():
            timestamp = int(row['timestamp'].timestamp() * 1000)  # Convert to milliseconds
            
            candlestick_data.append({
                'time': timestamp,
                'open': round(float(row['open']), self.config.price_precision),
                'high': round(float(row['high']), self.config.price_precision),
                'low': round(float(row['low']), self.config.price_precision),
                'close': round(float(row['close']), self.config.price_precision)
            })
            
            volume_data.append({
                'time': timestamp,
                'value': round(float(row['volume']), self.config.volume_precision),
                'color': self._get_volume_color(row)
            })
        
        return {
            'candlestick': candlestick_data,
            'volume': volume_data
        }
    
    def process_indicator_data(self, data: pd.DataFrame, indicator_name: str) -> List[Dict[str, Any]]:
        """Process indicator data for chart display."""
        if data.empty:
            return []
        
        indicator_data = []
        for _, row in data.iterrows():
            timestamp = int(row['timestamp'].timestamp() * 1000)
            value = row.get(indicator_name)
            
            if pd.notna(value):
                indicator_data.append({
                    'time': timestamp,
                    'value': round(float(value), self.config.price_precision)
                })
        
        return indicator_data
    
    def process_trade_signals(self, trades: List[Trade]) -> Dict[str, List[Dict[str, Any]]]:
        """Process trade signals for chart markers."""
        buy_signals = []
        sell_signals = []
        
        for trade in trades:
            timestamp = int(trade.timestamp.timestamp() * 1000)
            signal_data = {
                'time': timestamp,
                'price': round(trade.price, self.config.price_precision),
                'quantity': trade.quantity,
                'id': trade.id,
                'strategy_id': trade.strategy_id
            }
            
            if trade.side == OrderSide.BUY:
                buy_signals.append(signal_data)
            else:
                sell_signals.append(signal_data)
        
        return {
            'buy_signals': buy_signals,
            'sell_signals': sell_signals
        }
    
    def process_signals(self, signals: List[Signal]) -> Dict[str, List[Dict[str, Any]]]:
        """Process strategy signals for chart markers."""
        buy_signals = []
        sell_signals = []
        
        for signal in signals:
            timestamp = int(signal.timestamp.timestamp() * 1000)
            signal_data = {
                'time': timestamp,
                'price': round(signal.price or 0, self.config.price_precision),
                'quantity': signal.quantity,
                'confidence': signal.confidence,
                'metadata': signal.metadata
            }
            
            if signal.action == SignalAction.BUY:
                buy_signals.append(signal_data)
            elif signal.action == SignalAction.SELL:
                sell_signals.append(signal_data)
        
        return {
            'buy_signals': buy_signals,
            'sell_signals': sell_signals
        }
    
    def _get_volume_color(self, row: pd.Series) -> str:
        """Get volume bar color based on price movement."""
        if row['close'] >= row['open']:
            return self.config.theme.volume_up_color
        else:
            return self.config.theme.volume_down_color
    
    def calculate_price_range(self, data: pd.DataFrame, padding: float = 0.05) -> Tuple[float, float]:
        """Calculate price range with padding."""
        if data.empty:
            return 0.0, 100.0
        
        min_price = min(data['low'].min(), data['high'].min())
        max_price = max(data['low'].max(), data['high'].max())
        
        price_range = max_price - min_price
        padding_amount = price_range * padding
        
        return min_price - padding_amount, max_price + padding_amount
    
    def resample_data(self, data: pd.DataFrame, target_interval: str) -> pd.DataFrame:
        """Resample data to different time intervals."""
        if data.empty:
            return data
        
        data = data.set_index('timestamp')
        
        # Define resampling rules
        agg_rules = {
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }
        
        # Resample data
        resampled = data.resample(target_interval).agg(agg_rules)
        resampled = resampled.dropna()
        resampled = resampled.reset_index()
        
        return resampled


class ChartRenderer:
    """Render chart components using various backends."""
    
    def __init__(self, config: ChartConfig):
        self.config = config
    
    def render_to_json(self, chart_data: Dict[str, Any]) -> str:
        """Render chart data to JSON format."""
        try:
            return json.dumps(chart_data, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error rendering chart to JSON: {e}")
            return "{}"
    
    def render_to_html(self, chart_data: Dict[str, Any], container_id: str = "chart") -> str:
        """Render chart data to HTML with embedded JavaScript."""
        json_data = self.render_to_json(chart_data)
        
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Trading Chart</title>
            <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
            <style>
                #{container_id} {{
                    width: 100%;
                    height: {self.config.theme.chart_height}px;
                    background-color: {self.config.theme.background_color};
                }}
            </style>
        </head>
        <body>
            <div id="{container_id}"></div>
            <script>
                const chartData = {json_data};
                const config = {self.config.to_dict()};
                
                // Initialize chart
                const chart = LightweightCharts.createChart(document.getElementById('{container_id}'), {{
                    width: document.getElementById('{container_id}').clientWidth,
                    height: {self.config.theme.chart_height},
                    layout: {{
                        backgroundColor: config.theme.background_color,
                        textColor: config.theme.text_color,
                    }},
                    grid: {{
                        vertLines: {{
                            color: config.theme.grid_color,
                            visible: config.show_grid,
                        }},
                        horzLines: {{
                            color: config.theme.grid_color,
                            visible: config.show_grid,
                        }},
                    }},
                    crosshair: {{
                        mode: config.show_crosshair ? LightweightCharts.CrosshairMode.Normal : LightweightCharts.CrosshairMode.Hidden,
                    }},
                    timeScale: {{
                        visible: config.show_time_scale,
                        timeVisible: true,
                        secondsVisible: false,
                    }},
                    priceScale: {{
                        visible: config.show_price_scale,
                        borderColor: config.theme.grid_color,
                    }},
                }});
                
                // Add candlestick series
                if (chartData.candlestick && chartData.candlestick.length > 0) {{
                    const candlestickSeries = chart.addCandlestickSeries({{
                        upColor: config.theme.up_color,
                        downColor: config.theme.down_color,
                        borderUpColor: config.theme.up_border_color,
                        borderDownColor: config.theme.down_border_color,
                        wickUpColor: config.theme.up_border_color,
                        wickDownColor: config.theme.down_border_color,
                    }});
                    candlestickSeries.setData(chartData.candlestick);
                }}
                
                // Add volume series
                if (chartData.volume && chartData.volume.length > 0 && config.show_volume) {{
                    const volumeSeries = chart.addHistogramSeries({{
                        color: config.theme.volume_up_color,
                        priceFormat: {{
                            type: 'volume',
                        }},
                        priceScaleId: 'volume',
                    }});
                    volumeSeries.setData(chartData.volume);
                    
                    chart.priceScale('volume').applyOptions({{
                        scaleMargins: {{
                            top: 0.8,
                            bottom: 0,
                        }},
                    }});
                }}
                
                // Add indicators
                if (chartData.indicators) {{
                    Object.keys(chartData.indicators).forEach(indicatorName => {{
                        const indicatorData = chartData.indicators[indicatorName];
                        const color = config.theme.ma_colors[indicatorName] || '#ffffff';
                        
                        const indicatorSeries = chart.addLineSeries({{
                            color: color,
                            lineWidth: 2,
                            title: indicatorName,
                        }});
                        indicatorSeries.setData(indicatorData);
                    }});
                }}
                
                // Add trade signals
                if (chartData.signals) {{
                    if (chartData.signals.buy_signals) {{
                        chartData.signals.buy_signals.forEach(signal => {{
                            // Add buy signal marker
                            // This would require additional marker implementation
                        }});
                    }}
                    
                    if (chartData.signals.sell_signals) {{
                        chartData.signals.sell_signals.forEach(signal => {{
                            // Add sell signal marker
                            // This would require additional marker implementation
                        }});
                    }}
                }}
                
                // Handle resize
                window.addEventListener('resize', () => {{
                    chart.applyOptions({{
                        width: document.getElementById('{container_id}').clientWidth,
                    }});
                }});
            </script>
        </body>
        </html>
        """
        
        return html_template
    
    def create_chart_config_js(self) -> str:
        """Create JavaScript configuration object."""
        return f"""
        const chartConfig = {json.dumps(self.config.to_dict(), indent=2)};
        """


def format_timestamp(timestamp: Union[datetime, pd.Timestamp, int, float]) -> int:
    """Format timestamp for chart display."""
    if isinstance(timestamp, (int, float)):
        return int(timestamp * 1000) if timestamp < 1e10 else int(timestamp)
    elif isinstance(timestamp, (datetime, pd.Timestamp)):
        return int(timestamp.timestamp() * 1000)
    else:
        raise ValueError(f"Unsupported timestamp type: {type(timestamp)}")


def calculate_technical_levels(data: pd.DataFrame, period: int = 20) -> Dict[str, float]:
    """Calculate support and resistance levels."""
    if data.empty or len(data) < period:
        return {}
    
    highs = data['high'].rolling(window=period).max()
    lows = data['low'].rolling(window=period).min()
    
    return {
        'resistance': float(highs.iloc[-1]) if not pd.isna(highs.iloc[-1]) else None,
        'support': float(lows.iloc[-1]) if not pd.isna(lows.iloc[-1]) else None,
        'pivot': float((highs.iloc[-1] + lows.iloc[-1] + data['close'].iloc[-1]) / 3) if not pd.isna(highs.iloc[-1]) and not pd.isna(lows.iloc[-1]) else None
    }


def detect_chart_patterns(data: pd.DataFrame) -> List[Dict[str, Any]]:
    """Detect basic chart patterns."""
    patterns = []
    
    if len(data) < 10:
        return patterns
    
    # Simple pattern detection (can be expanded)
    recent_data = data.tail(10)
    
    # Detect trend
    if recent_data['close'].iloc[-1] > recent_data['close'].iloc[0]:
        patterns.append({
            'type': 'uptrend',
            'confidence': 0.7,
            'start_time': recent_data.iloc[0]['timestamp'],
            'end_time': recent_data.iloc[-1]['timestamp']
        })
    elif recent_data['close'].iloc[-1] < recent_data['close'].iloc[0]:
        patterns.append({
            'type': 'downtrend',
            'confidence': 0.7,
            'start_time': recent_data.iloc[0]['timestamp'],
            'end_time': recent_data.iloc[-1]['timestamp']
        })
    
    return patterns