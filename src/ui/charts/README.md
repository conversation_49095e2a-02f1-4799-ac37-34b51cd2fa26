# 图表组件模块

本模块提供了量化交易系统的图表可视化组件，支持K线图、成交量图、技术指标叠加显示、交易信号标记等功能。

## 组件概览

### 主要组件

1. **MainChartComponent** - 主图表组件
2. **IndicatorPanel** - 技术指标面板
3. **StrategyAnalysisPanel** - 策略分析面板
4. **MonitoringPanel** - 监控面板

### 配置和工具

- **ChartConfig** - 图表配置管理
- **ChartDataProcessor** - 数据处理工具
- **ChartRenderer** - 图表渲染工具

## MainChartComponent 主图表组件

### 功能特性

- ✅ K线图和成交量图表显示
- ✅ 技术指标叠加显示
- ✅ 交易信号标记和高亮显示
- ✅ 图表交互功能（缩放、平移、十字光标）
- ✅ 多种时间周期支持
- ✅ 实时数据更新
- ✅ 自定义主题和样式

### 基本使用

```python
from src.ui.charts.main_chart import MainChartComponent
from src.ui.charts.chart_config import ChartConfig, TimeInterval
from src.data.manager import DataManager
from src.indicators.engine import IndicatorEngine

# 创建配置
config = ChartConfig()
config.time_interval = TimeInterval.HOUR_1
config.show_volume = True
config.enable_zoom = True

# 创建组件
chart = MainChartComponent(
    config=config,
    data_manager=data_manager,
    indicator_engine=indicator_engine
)

# 加载数据
await chart.load_data("AAPL", start_time, end_time)

# 添加技术指标
chart.add_indicator("SMA", period=20)
chart.add_indicator("SMA", period=50)

# 添加交易数据
chart.add_trades(trades_list)
chart.add_signals(signals_list)

# 获取图表数据
chart_data = chart.get_chart_data()

# 渲染为HTML
html_output = chart.render_to_html()
```

### 交互功能

```python
# 缩放功能
chart.zoom(2.0)  # 放大2倍
chart.zoom(0.5)  # 缩小2倍
chart.reset_zoom()  # 重置缩放

# 平移功能
chart.pan(3600)  # 向右平移1小时

# 设置十字光标
chart.set_crosshair_position(datetime.now(), 150.0)

# 设置可见时间范围
chart.set_time_range(start_time, end_time)
```

### 指标管理

```python
# 添加指标
chart.add_indicator("SMA", period=20)
chart.add_indicator("EMA", period=12)
chart.add_indicator("RSI", period=14)

# 移除指标
chart.remove_indicator("SMA")

# 切换指标可见性
chart.toggle_indicator("EMA")

# 清除所有指标
chart.clear_all_indicators()
```

## IndicatorPanel 技术指标面板

### 功能特性

- 支持多种技术指标类型
- 指标参数配置和管理
- 指标叠加和独立面板显示
- 指标统计信息

### 使用示例

```python
from src.ui.charts.indicator_panel import IndicatorPanel

# 创建指标面板
panel = IndicatorPanel(config=config, indicator_engine=engine)

# 添加指标
panel.add_indicator("RSI", "momentum", market_data, period=14)
panel.add_indicator("MACD", "momentum", market_data, fast=12, slow=26, signal=9)

# 获取指标数据
rsi_data = panel.get_indicator_data("RSI")
all_data = panel.get_all_indicator_data()

# 获取面板布局
layout = panel.get_panel_layout()
```

## StrategyAnalysisPanel 策略分析面板

### 功能特性

- 策略信号可视化
- 交易执行分析
- 策略性能对比
- 权益曲线和回撤分析

### 使用示例

```python
from src.ui.charts.strategy_analysis import StrategyAnalysisPanel

# 创建策略分析面板
panel = StrategyAnalysisPanel(config=config)

# 添加策略
panel.add_strategy("strategy1", strategy_instance)

# 添加信号和交易数据
panel.add_signals("strategy1", signals_list)
panel.add_trades("strategy1", trades_list)

# 获取分析数据
performance = panel.get_performance_summary("strategy1")
equity_curve = panel.get_equity_curve("strategy1")
comparison = panel.get_strategy_comparison()
```

## MonitoringPanel 监控面板

### 功能特性

- 实时系统监控
- 性能指标追踪
- 告警管理
- 系统事件记录

### 使用示例

```python
from src.ui.charts.monitoring_panel import MonitoringPanel

# 创建监控面板
panel = MonitoringPanel(config=config, health_monitor=health_monitor)

# 启动监控
await panel.start_monitoring()

# 获取监控数据
current_metrics = panel.get_current_metrics()
system_overview = panel.get_system_overview()
alerts = panel.get_alerts(level="WARNING")
```

## 配置选项

### ChartConfig 配置

```python
from src.ui.charts.chart_config import ChartConfig, ChartTheme, TimeInterval

# 创建配置
config = ChartConfig()

# 基本设置
config.chart_type = ChartType.CANDLESTICK
config.time_interval = TimeInterval.DAY_1
config.show_volume = True
config.show_grid = True
config.show_crosshair = True

# 交互设置
config.enable_zoom = True
config.enable_pan = True
config.enable_selection = True

# 数据设置
config.max_visible_candles = 200
config.preload_candles = 50

# 主题设置
config.theme.background_color = "#1e1e1e"
config.theme.up_color = "#00ff88"
config.theme.down_color = "#ff4444"
```

### 自定义主题

```python
from src.ui.charts.chart_config import ChartTheme

# 创建自定义主题
custom_theme = ChartTheme(
    background_color="#ffffff",
    grid_color="#e0e0e0",
    text_color="#000000",
    up_color="#00aa00",
    down_color="#aa0000"
)

config.theme = custom_theme
```

## 数据格式

### 市场数据格式

```python
market_data = pd.DataFrame({
    'timestamp': [datetime1, datetime2, ...],
    'open': [100.0, 101.0, ...],
    'high': [102.0, 103.0, ...],
    'low': [99.0, 100.0, ...],
    'close': [101.0, 102.0, ...],
    'volume': [10000, 12000, ...]
})
```

### 交易信号格式

```python
from src.models.trading import Signal, SignalAction

signal = Signal(
    timestamp=datetime.now(),
    symbol="AAPL",
    action=SignalAction.BUY,
    quantity=100,
    price=150.0,
    confidence=0.8,
    strategy_id="strategy1"
)
```

### 交易数据格式

```python
from src.models.trading import Trade, OrderSide

trade = Trade(
    id="trade1",
    timestamp=datetime.now(),
    symbol="AAPL",
    side=OrderSide.BUY,
    quantity=100,
    price=150.0,
    strategy_id="strategy1"
)
```

## 输出格式

### JSON 输出

```python
# 获取JSON格式的图表数据
json_data = chart.render_to_json()

# 数据结构
{
    "candlestick": [...],
    "volume": [...],
    "indicators": {...},
    "signals": {...},
    "trades": {...},
    "config": {...},
    "state": {...}
}
```

### HTML 输出

```python
# 生成可交互的HTML图表
html_output = chart.render_to_html("chart-container")

# 包含完整的HTML页面，可直接在浏览器中打开
```

## 性能优化

### 数据处理优化

- 使用向量化操作处理大量数据
- 实现数据缓存机制
- 支持增量数据更新
- 优化内存使用

### 渲染优化

- 支持数据分页和懒加载
- 实现视窗裁剪
- 优化动画性能
- 支持WebGL加速

## 扩展开发

### 自定义指标

```python
# 在IndicatorPanel中添加自定义指标
def custom_indicator(data, param1, param2):
    # 自定义指标计算逻辑
    return result

panel.add_custom_indicator("CustomIndicator", custom_indicator)
```

### 自定义渲染器

```python
from src.ui.charts.chart_utils import ChartRenderer

class CustomRenderer(ChartRenderer):
    def render_custom_format(self, chart_data):
        # 自定义渲染逻辑
        return custom_output
```

## 故障排除

### 常见问题

1. **指标计算失败**
   - 检查数据格式是否正确
   - 确认指标参数有效性
   - 验证数据量是否足够

2. **图表显示异常**
   - 检查配置参数
   - 验证数据时间戳格式
   - 确认主题设置

3. **性能问题**
   - 减少可见数据点数量
   - 优化指标计算频率
   - 使用数据缓存

### 调试模式

```python
import logging

# 启用调试日志
logging.getLogger('src.ui.charts').setLevel(logging.DEBUG)

# 获取详细统计信息
stats = chart.get_chart_statistics()
print(stats)
```

## 示例项目

查看 `examples/chart_demo.py` 获取完整的使用示例。

运行演示：
```bash
python examples/chart_demo.py
```

这将生成：
- `chart_data.json` - 图表数据
- `chart_demo.html` - 可交互的HTML图表