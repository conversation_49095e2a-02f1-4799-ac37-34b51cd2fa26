"""
Main chart component for K-line and volume display with technical indicators.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Union
import logging
import asyncio
from dataclasses import dataclass, field

from ...models.market_data import MarketData
from ...models.trading import Trade, Signal
from ...indicators.engine import IndicatorEngine
from ...data.manager import DataManager
from .chart_config import ChartConfig, ChartType, TimeInterval
from .chart_utils import ChartDataProcessor, ChartRenderer, format_timestamp


logger = logging.getLogger(__name__)


@dataclass
class ChartState:
    """Chart state management."""
    symbol: str = ""
    time_interval: TimeInterval = TimeInterval.DAY_1
    visible_range: tuple = field(default_factory=lambda: (None, None))
    zoom_level: float = 1.0
    pan_offset: float = 0.0
    crosshair_position: Optional[Dict[str, float]] = None
    selected_indicators: List[str] = field(default_factory=list)
    show_signals: bool = True
    show_trades: bool = True


class MainChartComponent:
    """
    Main chart component for displaying K-line charts, volume, and technical indicators.
    Supports interactive features like zoom, pan, and crosshair.
    """
    
    def __init__(self, 
                 config: Optional[ChartConfig] = None,
                 data_manager: Optional[DataManager] = None,
                 indicator_engine: Optional[IndicatorEngine] = None):
        """
        Initialize main chart component.
        
        Args:
            config: Chart configuration
            data_manager: Data manager for fetching market data
            indicator_engine: Indicator engine for technical analysis
        """
        self.config = config or ChartConfig()
        self.data_manager = data_manager
        self.indicator_engine = indicator_engine or IndicatorEngine()
        
        self.processor = ChartDataProcessor(self.config)
        self.renderer = ChartRenderer(self.config)
        
        self.state = ChartState()
        self.market_data: Optional[pd.DataFrame] = None
        self.indicators_data: Dict[str, pd.DataFrame] = {}
        self.trades: List[Trade] = []
        self.signals: List[Signal] = []
        
        # Event callbacks
        self.on_crosshair_move: Optional[Callable] = None
        self.on_zoom_change: Optional[Callable] = None
        self.on_pan_change: Optional[Callable] = None
        self.on_time_range_change: Optional[Callable] = None
        
        logger.info("MainChartComponent initialized")
    
    async def load_data(self, 
                       symbol: str, 
                       start_time: Optional[datetime] = None,
                       end_time: Optional[datetime] = None,
                       interval: Optional[TimeInterval] = None) -> bool:
        """
        Load market data for the specified symbol and time range.
        
        Args:
            symbol: Trading symbol
            start_time: Start time for data
            end_time: End time for data
            interval: Time interval
            
        Returns:
            True if data loaded successfully
        """
        try:
            if not self.data_manager:
                logger.error("Data manager not available")
                return False
            
            self.state.symbol = symbol
            if interval:
                self.state.time_interval = interval
                self.config.time_interval = interval
            
            # Set default time range if not provided
            if not end_time:
                end_time = datetime.now()
            if not start_time:
                start_time = end_time - timedelta(days=365)  # 1 year of data
            
            # Fetch market data
            self.market_data = await self.data_manager.get_market_data(
                symbol=symbol,
                start_time=start_time,
                end_time=end_time,
                interval=self.state.time_interval.value
            )
            
            if self.market_data is None or self.market_data.empty:
                logger.warning(f"No market data found for {symbol}")
                return False
            
            # Update visible range
            self.state.visible_range = (
                self.market_data['timestamp'].min(),
                self.market_data['timestamp'].max()
            )
            
            logger.info(f"Loaded {len(self.market_data)} data points for {symbol}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading data for {symbol}: {e}")
            return False
    
    def add_indicator(self, indicator_name: str, **params) -> bool:
        """
        Add technical indicator to the chart.
        
        Args:
            indicator_name: Name of the indicator
            **params: Indicator parameters
            
        Returns:
            True if indicator added successfully
        """
        try:
            if self.market_data is None or self.market_data.empty:
                logger.error("No market data available for indicator calculation")
                return False
            
            # Calculate indicator
            result = self.indicator_engine.calculate(
                indicator_name=indicator_name.lower(),
                data=self.market_data,
                **params
            )
            
            # Extract data from result
            if hasattr(result, 'data'):
                if isinstance(result.data, pd.DataFrame):
                    indicator_data = result.data
                elif isinstance(result.data, pd.Series):
                    # Convert series to dataframe with timestamp
                    indicator_data = pd.DataFrame({
                        'timestamp': self.market_data['timestamp'],
                        indicator_name: result.data
                    })
                else:
                    indicator_data = None
            else:
                indicator_data = result
            
            if indicator_data is not None and not indicator_data.empty:
                self.indicators_data[indicator_name] = indicator_data
                if indicator_name not in self.state.selected_indicators:
                    self.state.selected_indicators.append(indicator_name)
                
                logger.info(f"Added indicator: {indicator_name}")
                return True
            else:
                logger.warning(f"Failed to calculate indicator: {indicator_name}")
                return False
                
        except Exception as e:
            logger.error(f"Error adding indicator {indicator_name}: {e}")
            return False
    
    def remove_indicator(self, indicator_name: str) -> bool:
        """
        Remove indicator from the chart.
        
        Args:
            indicator_name: Name of the indicator to remove
            
        Returns:
            True if indicator removed successfully
        """
        try:
            if indicator_name in self.indicators_data:
                del self.indicators_data[indicator_name]
            
            if indicator_name in self.state.selected_indicators:
                self.state.selected_indicators.remove(indicator_name)
            
            logger.info(f"Removed indicator: {indicator_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing indicator {indicator_name}: {e}")
            return False
    
    def add_trades(self, trades: List[Trade]) -> None:
        """
        Add trade data to the chart.
        
        Args:
            trades: List of trades to display
        """
        self.trades = trades
        logger.info(f"Added {len(trades)} trades to chart")
    
    def add_signals(self, signals: List[Signal]) -> None:
        """
        Add signal data to the chart.
        
        Args:
            signals: List of signals to display
        """
        self.signals = signals
        logger.info(f"Added {len(signals)} signals to chart")
    
    def set_time_range(self, start_time: datetime, end_time: datetime) -> None:
        """
        Set visible time range for the chart.
        
        Args:
            start_time: Start time
            end_time: End time
        """
        self.state.visible_range = (start_time, end_time)
        
        if self.on_time_range_change:
            self.on_time_range_change(start_time, end_time)
        
        logger.debug(f"Time range set: {start_time} to {end_time}")
    
    def zoom(self, factor: float, center_time: Optional[datetime] = None) -> None:
        """
        Zoom the chart by the specified factor.
        
        Args:
            factor: Zoom factor (>1 to zoom in, <1 to zoom out)
            center_time: Center point for zoom
        """
        self.state.zoom_level *= factor
        
        if self.market_data is not None and not self.market_data.empty:
            # Calculate new visible range based on zoom
            if center_time is None:
                center_time = self.market_data['timestamp'].iloc[len(self.market_data) // 2]
            
            # Implement zoom logic here
            # This is a simplified implementation
            total_duration = (self.state.visible_range[1] - self.state.visible_range[0])
            new_duration = total_duration / factor
            
            half_duration = new_duration / 2
            new_start = center_time - half_duration
            new_end = center_time + half_duration
            
            self.set_time_range(new_start, new_end)
        
        if self.on_zoom_change:
            self.on_zoom_change(self.state.zoom_level)
        
        logger.debug(f"Zoomed by factor {factor}, new zoom level: {self.state.zoom_level}")
    
    def pan(self, offset: float) -> None:
        """
        Pan the chart by the specified offset.
        
        Args:
            offset: Pan offset in time units
        """
        self.state.pan_offset += offset
        
        if self.state.visible_range[0] and self.state.visible_range[1]:
            duration = self.state.visible_range[1] - self.state.visible_range[0]
            offset_timedelta = timedelta(seconds=offset)
            
            new_start = self.state.visible_range[0] + offset_timedelta
            new_end = self.state.visible_range[1] + offset_timedelta
            
            self.set_time_range(new_start, new_end)
        
        if self.on_pan_change:
            self.on_pan_change(self.state.pan_offset)
        
        logger.debug(f"Panned by offset {offset}, new pan offset: {self.state.pan_offset}")
    
    def set_crosshair_position(self, time: datetime, price: float) -> None:
        """
        Set crosshair position.
        
        Args:
            time: Time position
            price: Price position
        """
        self.state.crosshair_position = {
            'time': time,
            'price': price
        }
        
        if self.on_crosshair_move:
            self.on_crosshair_move(time, price)
        
        logger.debug(f"Crosshair position: {time}, {price}")
    
    def get_chart_data(self) -> Dict[str, Any]:
        """
        Get processed chart data for rendering.
        
        Returns:
            Dictionary containing all chart data
        """
        chart_data = {
            'candlestick': [],
            'volume': [],
            'indicators': {},
            'signals': {},
            'trades': {},
            'config': self.config.to_dict(),
            'state': {
                'symbol': self.state.symbol,
                'time_interval': self.state.time_interval.value,
                'visible_range': [
                    format_timestamp(self.state.visible_range[0]) if self.state.visible_range[0] else None,
                    format_timestamp(self.state.visible_range[1]) if self.state.visible_range[1] else None
                ],
                'zoom_level': self.state.zoom_level,
                'pan_offset': self.state.pan_offset,
                'crosshair_position': self.state.crosshair_position,
                'selected_indicators': self.state.selected_indicators,
                'show_signals': self.state.show_signals,
                'show_trades': self.state.show_trades
            }
        }
        
        try:
            # Process market data
            if self.market_data is not None and not self.market_data.empty:
                market_data_processed = self.processor.process_market_data(self.market_data)
                chart_data['candlestick'] = market_data_processed['candlestick']
                chart_data['volume'] = market_data_processed['volume']
            
            # Process indicators
            for indicator_name, indicator_data in self.indicators_data.items():
                if indicator_name in self.state.selected_indicators:
                    chart_data['indicators'][indicator_name] = self.processor.process_indicator_data(
                        indicator_data, indicator_name
                    )
            
            # Process signals
            if self.signals and self.state.show_signals:
                chart_data['signals'] = self.processor.process_signals(self.signals)
            
            # Process trades
            if self.trades and self.state.show_trades:
                chart_data['trades'] = self.processor.process_trade_signals(self.trades)
            
        except Exception as e:
            logger.error(f"Error processing chart data: {e}")
        
        return chart_data
    
    def render_to_html(self, container_id: str = "main-chart") -> str:
        """
        Render chart to HTML format.
        
        Args:
            container_id: HTML container ID
            
        Returns:
            HTML string with embedded chart
        """
        chart_data = self.get_chart_data()
        return self.renderer.render_to_html(chart_data, container_id)
    
    def render_to_json(self) -> str:
        """
        Render chart data to JSON format.
        
        Returns:
            JSON string containing chart data
        """
        chart_data = self.get_chart_data()
        return self.renderer.render_to_json(chart_data)
    
    def update_config(self, **config_updates) -> None:
        """
        Update chart configuration.
        
        Args:
            **config_updates: Configuration updates
        """
        for key, value in config_updates.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                logger.debug(f"Updated config: {key} = {value}")
        
        # Update processor and renderer with new config
        self.processor.config = self.config
        self.renderer.config = self.config
    
    def get_visible_data(self) -> Optional[pd.DataFrame]:
        """
        Get market data within the visible time range.
        
        Returns:
            DataFrame containing visible market data
        """
        if self.market_data is None or self.market_data.empty:
            return None
        
        if not self.state.visible_range[0] or not self.state.visible_range[1]:
            return self.market_data
        
        mask = (
            (self.market_data['timestamp'] >= self.state.visible_range[0]) &
            (self.market_data['timestamp'] <= self.state.visible_range[1])
        )
        
        return self.market_data[mask]
    
    def get_price_at_time(self, timestamp: datetime) -> Optional[float]:
        """
        Get price at specific timestamp.
        
        Args:
            timestamp: Target timestamp
            
        Returns:
            Price at the timestamp or None if not found
        """
        if self.market_data is None or self.market_data.empty:
            return None
        
        # Find closest timestamp
        time_diff = abs(self.market_data['timestamp'] - timestamp)
        closest_idx = time_diff.idxmin()
        
        return float(self.market_data.loc[closest_idx, 'close'])
    
    def reset_zoom(self) -> None:
        """Reset zoom to show all data."""
        self.state.zoom_level = 1.0
        self.state.pan_offset = 0.0
        
        if self.market_data is not None and not self.market_data.empty:
            self.state.visible_range = (
                self.market_data['timestamp'].min(),
                self.market_data['timestamp'].max()
            )
        
        logger.debug("Zoom reset to default")
    
    def toggle_indicator(self, indicator_name: str) -> bool:
        """
        Toggle indicator visibility.
        
        Args:
            indicator_name: Name of the indicator
            
        Returns:
            True if indicator is now visible
        """
        if indicator_name in self.state.selected_indicators:
            self.state.selected_indicators.remove(indicator_name)
            return False
        else:
            if indicator_name in self.indicators_data:
                self.state.selected_indicators.append(indicator_name)
                return True
            else:
                logger.warning(f"Indicator {indicator_name} not found in data")
                return False
    
    def clear_all_indicators(self) -> None:
        """Clear all indicators from the chart."""
        self.indicators_data.clear()
        self.state.selected_indicators.clear()
        logger.info("All indicators cleared")
    
    def get_chart_statistics(self) -> Dict[str, Any]:
        """
        Get chart statistics and metadata.
        
        Returns:
            Dictionary containing chart statistics
        """
        stats = {
            'symbol': self.state.symbol,
            'data_points': 0,
            'time_range': None,
            'price_range': None,
            'indicators_count': len(self.indicators_data),
            'signals_count': len(self.signals),
            'trades_count': len(self.trades)
        }
        
        if self.market_data is not None and not self.market_data.empty:
            stats['data_points'] = len(self.market_data)
            stats['time_range'] = {
                'start': self.market_data['timestamp'].min(),
                'end': self.market_data['timestamp'].max()
            }
            stats['price_range'] = {
                'min': float(self.market_data['low'].min()),
                'max': float(self.market_data['high'].max()),
                'current': float(self.market_data['close'].iloc[-1])
            }
        
        return stats