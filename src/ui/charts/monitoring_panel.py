"""
Monitoring panel for real-time system status and performance tracking.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
import logging
import asyncio
from dataclasses import dataclass, field

from ...monitoring.health_monitor import HealthMonitor
from ...risk.monitoring_system import RiskMonitoringSystem
from .chart_config import ChartConfig


logger = logging.getLogger(__name__)


@dataclass
class MonitoringMetrics:
    """Container for monitoring metrics."""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, float]
    active_strategies: int
    total_positions: int
    daily_pnl: float
    risk_score: float
    alerts_count: int
    system_status: str


class MonitoringPanel:
    """
    Panel component for real-time monitoring of system performance and trading metrics.
    """
    
    def __init__(self, 
                 config: Optional[ChartConfig] = None,
                 health_monitor: Optional[HealthMonitor] = None,
                 risk_monitor: Optional[RiskMonitoringSystem] = None):
        """
        Initialize monitoring panel.
        
        Args:
            config: Chart configuration
            health_monitor: System health monitor
            risk_monitor: Risk monitoring system
        """
        self.config = config or ChartConfig()
        self.health_monitor = health_monitor
        self.risk_monitor = risk_monitor
        
        # Monitoring data storage
        self.metrics_history: List[MonitoringMetrics] = []
        self.alerts: List[Dict[str, Any]] = []
        self.system_events: List[Dict[str, Any]] = []
        
        # Configuration
        self.max_history_points = 1000
        self.update_interval = 5  # seconds
        self.alert_thresholds = {
            'cpu_usage': 80.0,
            'memory_usage': 85.0,
            'disk_usage': 90.0,
            'risk_score': 0.8,
            'daily_drawdown': -0.05
        }
        
        # Status tracking
        self.is_monitoring = False
        self.last_update: Optional[datetime] = None
        
        # Event callbacks
        self.on_alert: Optional[Callable] = None
        self.on_status_change: Optional[Callable] = None
        self.on_metrics_update: Optional[Callable] = None
        
        logger.info("MonitoringPanel initialized")
    
    async def start_monitoring(self) -> None:
        """Start real-time monitoring."""
        if self.is_monitoring:
            logger.warning("Monitoring already started")
            return
        
        self.is_monitoring = True
        logger.info("Starting real-time monitoring")
        
        # Start monitoring loop
        asyncio.create_task(self._monitoring_loop())
    
    def stop_monitoring(self) -> None:
        """Stop real-time monitoring."""
        self.is_monitoring = False
        logger.info("Stopped real-time monitoring")
    
    async def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                await self._collect_metrics()
                await asyncio.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.update_interval)
    
    async def _collect_metrics(self) -> None:
        """Collect system and trading metrics."""
        try:
            current_time = datetime.now()
            
            # Collect system metrics
            system_metrics = await self._get_system_metrics()
            
            # Collect trading metrics
            trading_metrics = await self._get_trading_metrics()
            
            # Collect risk metrics
            risk_metrics = await self._get_risk_metrics()
            
            # Create monitoring metrics object
            metrics = MonitoringMetrics(
                timestamp=current_time,
                cpu_usage=system_metrics.get('cpu_usage', 0.0),
                memory_usage=system_metrics.get('memory_usage', 0.0),
                disk_usage=system_metrics.get('disk_usage', 0.0),
                network_io=system_metrics.get('network_io', {}),
                active_strategies=trading_metrics.get('active_strategies', 0),
                total_positions=trading_metrics.get('total_positions', 0),
                daily_pnl=trading_metrics.get('daily_pnl', 0.0),
                risk_score=risk_metrics.get('risk_score', 0.0),
                alerts_count=len(self.alerts),
                system_status=self._determine_system_status(system_metrics, trading_metrics, risk_metrics)
            )
            
            # Add to history
            self.metrics_history.append(metrics)
            
            # Maintain history size
            if len(self.metrics_history) > self.max_history_points:
                self.metrics_history = self.metrics_history[-self.max_history_points:]
            
            # Check for alerts
            await self._check_alerts(metrics)
            
            # Update last update time
            self.last_update = current_time
            
            # Trigger callback
            if self.on_metrics_update:
                self.on_metrics_update(metrics)
            
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
    
    async def _get_system_metrics(self) -> Dict[str, Any]:
        """Get system performance metrics."""
        metrics = {}
        
        if self.health_monitor:
            try:
                health_status = await self.health_monitor.get_system_health()
                metrics.update(health_status)
            except Exception as e:
                logger.error(f"Error getting system health: {e}")
        
        # Default values if health monitor not available
        if not metrics:
            metrics = {
                'cpu_usage': 0.0,
                'memory_usage': 0.0,
                'disk_usage': 0.0,
                'network_io': {'bytes_sent': 0, 'bytes_recv': 0}
            }
        
        return metrics
    
    async def _get_trading_metrics(self) -> Dict[str, Any]:
        """Get trading system metrics."""
        # This would typically interface with portfolio manager and strategy coordinator
        # For now, return placeholder values
        return {
            'active_strategies': 0,
            'total_positions': 0,
            'daily_pnl': 0.0,
            'total_trades_today': 0,
            'avg_trade_duration': 0.0
        }
    
    async def _get_risk_metrics(self) -> Dict[str, Any]:
        """Get risk monitoring metrics."""
        metrics = {}
        
        if self.risk_monitor:
            try:
                risk_status = await self.risk_monitor.get_risk_summary()
                metrics.update(risk_status)
            except Exception as e:
                logger.error(f"Error getting risk metrics: {e}")
        
        # Default values if risk monitor not available
        if not metrics:
            metrics = {
                'risk_score': 0.0,
                'var_1d': 0.0,
                'max_drawdown': 0.0,
                'position_concentration': 0.0
            }
        
        return metrics
    
    def _determine_system_status(self, 
                                system_metrics: Dict[str, Any],
                                trading_metrics: Dict[str, Any],
                                risk_metrics: Dict[str, Any]) -> str:
        """Determine overall system status."""
        # Check critical thresholds
        if (system_metrics.get('cpu_usage', 0) > 90 or 
            system_metrics.get('memory_usage', 0) > 95):
            return "CRITICAL"
        
        if (system_metrics.get('cpu_usage', 0) > 80 or 
            system_metrics.get('memory_usage', 0) > 85 or
            risk_metrics.get('risk_score', 0) > 0.8):
            return "WARNING"
        
        if (system_metrics.get('cpu_usage', 0) > 60 or 
            system_metrics.get('memory_usage', 0) > 70):
            return "CAUTION"
        
        return "HEALTHY"
    
    async def _check_alerts(self, metrics: MonitoringMetrics) -> None:
        """Check for alert conditions."""
        alerts_triggered = []
        
        # CPU usage alert
        if metrics.cpu_usage > self.alert_thresholds['cpu_usage']:
            alerts_triggered.append({
                'type': 'SYSTEM',
                'level': 'WARNING',
                'message': f"High CPU usage: {metrics.cpu_usage:.1f}%",
                'timestamp': metrics.timestamp,
                'value': metrics.cpu_usage,
                'threshold': self.alert_thresholds['cpu_usage']
            })
        
        # Memory usage alert
        if metrics.memory_usage > self.alert_thresholds['memory_usage']:
            alerts_triggered.append({
                'type': 'SYSTEM',
                'level': 'WARNING',
                'message': f"High memory usage: {metrics.memory_usage:.1f}%",
                'timestamp': metrics.timestamp,
                'value': metrics.memory_usage,
                'threshold': self.alert_thresholds['memory_usage']
            })
        
        # Risk score alert
        if metrics.risk_score > self.alert_thresholds['risk_score']:
            alerts_triggered.append({
                'type': 'RISK',
                'level': 'CRITICAL',
                'message': f"High risk score: {metrics.risk_score:.2f}",
                'timestamp': metrics.timestamp,
                'value': metrics.risk_score,
                'threshold': self.alert_thresholds['risk_score']
            })
        
        # Add new alerts
        for alert in alerts_triggered:
            self.alerts.append(alert)
            
            # Trigger callback
            if self.on_alert:
                self.on_alert(alert)
        
        # Maintain alerts history (keep last 100 alerts)
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]
    
    def get_current_metrics(self) -> Optional[MonitoringMetrics]:
        """Get the most recent metrics."""
        return self.metrics_history[-1] if self.metrics_history else None
    
    def get_metrics_history(self, 
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None) -> List[MonitoringMetrics]:
        """
        Get metrics history within time range.
        
        Args:
            start_time: Start time filter
            end_time: End time filter
            
        Returns:
            List of metrics within the time range
        """
        filtered_metrics = self.metrics_history
        
        if start_time:
            filtered_metrics = [m for m in filtered_metrics if m.timestamp >= start_time]
        
        if end_time:
            filtered_metrics = [m for m in filtered_metrics if m.timestamp <= end_time]
        
        return filtered_metrics
    
    def get_chart_data(self, metric_name: str, hours: int = 24) -> List[Dict[str, Any]]:
        """
        Get chart data for specific metric.
        
        Args:
            metric_name: Name of the metric
            hours: Number of hours of history
            
        Returns:
            List of chart data points
        """
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        metrics = self.get_metrics_history(start_time, end_time)
        
        chart_data = []
        for metric in metrics:
            value = getattr(metric, metric_name, None)
            if value is not None:
                chart_data.append({
                    'time': int(metric.timestamp.timestamp() * 1000),
                    'value': float(value)
                })
        
        return chart_data
    
    def get_system_overview(self) -> Dict[str, Any]:
        """Get system overview data."""
        current_metrics = self.get_current_metrics()
        
        if not current_metrics:
            return {
                'status': 'UNKNOWN',
                'uptime': 0,
                'last_update': None,
                'active_alerts': 0
            }
        
        # Calculate uptime (simplified)
        uptime_seconds = 0
        if self.metrics_history:
            first_metric = self.metrics_history[0]
            uptime_seconds = (current_metrics.timestamp - first_metric.timestamp).total_seconds()
        
        return {
            'status': current_metrics.system_status,
            'uptime': uptime_seconds,
            'last_update': current_metrics.timestamp,
            'active_alerts': len([a for a in self.alerts if a['level'] in ['WARNING', 'CRITICAL']]),
            'cpu_usage': current_metrics.cpu_usage,
            'memory_usage': current_metrics.memory_usage,
            'disk_usage': current_metrics.disk_usage,
            'active_strategies': current_metrics.active_strategies,
            'total_positions': current_metrics.total_positions,
            'daily_pnl': current_metrics.daily_pnl,
            'risk_score': current_metrics.risk_score
        }
    
    def get_alerts(self, 
                   level: Optional[str] = None,
                   alert_type: Optional[str] = None,
                   limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get alerts with optional filtering.
        
        Args:
            level: Alert level filter
            alert_type: Alert type filter
            limit: Maximum number of alerts to return
            
        Returns:
            List of filtered alerts
        """
        filtered_alerts = self.alerts
        
        if level:
            filtered_alerts = [a for a in filtered_alerts if a['level'] == level]
        
        if alert_type:
            filtered_alerts = [a for a in filtered_alerts if a['type'] == alert_type]
        
        # Sort by timestamp (most recent first)
        filtered_alerts.sort(key=lambda a: a['timestamp'], reverse=True)
        
        return filtered_alerts[:limit]
    
    def clear_alerts(self) -> None:
        """Clear all alerts."""
        self.alerts.clear()
        logger.info("All alerts cleared")
    
    def add_system_event(self, event_type: str, message: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Add system event for tracking.
        
        Args:
            event_type: Type of event
            message: Event message
            metadata: Additional event metadata
        """
        event = {
            'timestamp': datetime.now(),
            'type': event_type,
            'message': message,
            'metadata': metadata or {}
        }
        
        self.system_events.append(event)
        
        # Maintain events history (keep last 200 events)
        if len(self.system_events) > 200:
            self.system_events = self.system_events[-200:]
        
        logger.info(f"System event added: {event_type} - {message}")
    
    def get_system_events(self, 
                         event_type: Optional[str] = None,
                         hours: int = 24,
                         limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get system events with optional filtering.
        
        Args:
            event_type: Event type filter
            hours: Number of hours of history
            limit: Maximum number of events to return
            
        Returns:
            List of filtered events
        """
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        filtered_events = [
            e for e in self.system_events 
            if e['timestamp'] >= start_time
        ]
        
        if event_type:
            filtered_events = [e for e in filtered_events if e['type'] == event_type]
        
        # Sort by timestamp (most recent first)
        filtered_events.sort(key=lambda e: e['timestamp'], reverse=True)
        
        return filtered_events[:limit]
    
    def update_alert_thresholds(self, **thresholds) -> None:
        """
        Update alert thresholds.
        
        Args:
            **thresholds: New threshold values
        """
        for key, value in thresholds.items():
            if key in self.alert_thresholds:
                self.alert_thresholds[key] = value
                logger.info(f"Updated alert threshold: {key} = {value}")
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get performance summary for the specified time period.
        
        Args:
            hours: Number of hours to analyze
            
        Returns:
            Dictionary containing performance summary
        """
        metrics = self.get_metrics_history(
            start_time=datetime.now() - timedelta(hours=hours)
        )
        
        if not metrics:
            return {}
        
        # Calculate averages and extremes
        cpu_values = [m.cpu_usage for m in metrics]
        memory_values = [m.memory_usage for m in metrics]
        risk_values = [m.risk_score for m in metrics]
        pnl_values = [m.daily_pnl for m in metrics]
        
        return {
            'period_hours': hours,
            'data_points': len(metrics),
            'cpu_usage': {
                'avg': np.mean(cpu_values),
                'max': np.max(cpu_values),
                'min': np.min(cpu_values)
            },
            'memory_usage': {
                'avg': np.mean(memory_values),
                'max': np.max(memory_values),
                'min': np.min(memory_values)
            },
            'risk_score': {
                'avg': np.mean(risk_values),
                'max': np.max(risk_values),
                'min': np.min(risk_values)
            },
            'daily_pnl': {
                'current': pnl_values[-1] if pnl_values else 0,
                'max': np.max(pnl_values) if pnl_values else 0,
                'min': np.min(pnl_values) if pnl_values else 0
            },
            'alerts_triggered': len([a for a in self.alerts if a['timestamp'] >= datetime.now() - timedelta(hours=hours)])
        }