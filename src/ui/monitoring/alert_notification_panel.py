"""
告警和通知显示组件
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Set
from dataclasses import dataclass, field
from enum import Enum
import json

from ...risk.monitoring_system import RiskAlert, AlertSeverity, AlertStatus

logger = logging.getLogger(__name__)


class NotificationType(Enum):
    """通知类型"""
    ALERT = "alert"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"


class NotificationPriority(Enum):
    """通知优先级"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Notification:
    """通知数据结构"""
    id: str
    timestamp: datetime
    type: NotificationType
    priority: NotificationPriority
    title: str
    message: str
    source: str
    read: bool = False
    dismissed: bool = False
    auto_dismiss_seconds: Optional[int] = None
    actions: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'timestamp': self.timestamp.isoformat(),
            'type': self.type.value,
            'priority': self.priority.value,
            'title': self.title,
            'message': self.message,
            'source': self.source,
            'read': self.read,
            'dismissed': self.dismissed,
            'auto_dismiss_seconds': self.auto_dismiss_seconds,
            'actions': self.actions,
            'metadata': self.metadata
        }


@dataclass
class AlertFilter:
    """告警过滤器"""
    severities: Optional[Set[AlertSeverity]] = None
    sources: Optional[Set[str]] = None
    symbols: Optional[Set[str]] = None
    strategy_ids: Optional[Set[str]] = None
    status: Optional[Set[AlertStatus]] = None
    time_range: Optional[tuple] = None  # (start_time, end_time)
    
    def matches(self, alert: RiskAlert) -> bool:
        """检查告警是否匹配过滤条件"""
        if self.severities and alert.severity not in self.severities:
            return False
        
        if self.sources and alert.source not in self.sources:
            return False
        
        if self.symbols and alert.symbol and alert.symbol not in self.symbols:
            return False
        
        if self.strategy_ids and alert.strategy_id and alert.strategy_id not in self.strategy_ids:
            return False
        
        if self.status and alert.status not in self.status:
            return False
        
        if self.time_range:
            start_time, end_time = self.time_range
            if start_time and alert.timestamp < start_time:
                return False
            if end_time and alert.timestamp > end_time:
                return False
        
        return True


class AlertNotificationPanel:
    """
    告警和通知显示面板
    
    提供以下功能：
    - 实时告警显示和管理
    - 通知系统
    - 告警过滤和搜索
    - 告警统计和分析
    - 告警确认和处理
    """
    
    def __init__(self, max_alerts: int = 1000, max_notifications: int = 500):
        """
        初始化告警通知面板
        
        Args:
            max_alerts: 最大告警数量
            max_notifications: 最大通知数量
        """
        self.max_alerts = max_alerts
        self.max_notifications = max_notifications
        
        # 数据存储
        self.alerts: List[RiskAlert] = []
        self.notifications: List[Notification] = []
        self.alert_filters: Dict[str, AlertFilter] = {}
        
        # 统计信息
        self.alert_stats = {
            'total': 0,
            'active': 0,
            'acknowledged': 0,
            'resolved': 0,
            'by_severity': {severity.value: 0 for severity in AlertSeverity},
            'by_source': {},
            'last_24h': 0
        }
        
        # 配置
        self.auto_acknowledge_timeout = 3600  # 1小时后自动确认
        self.sound_enabled = True
        self.desktop_notifications = True
        self.email_notifications = False
        
        # 事件回调
        self.on_new_alert: Optional[Callable] = None
        self.on_alert_acknowledged: Optional[Callable] = None
        self.on_alert_resolved: Optional[Callable] = None
        self.on_notification_created: Optional[Callable] = None
        
        # 自动处理任务
        self._cleanup_task: Optional[asyncio.Task] = None
        self._running = False
        
        logger.info("告警通知面板初始化完成")
    
    async def start(self) -> None:
        """启动告警通知面板"""
        if self._running:
            return
        
        self._running = True
        
        # 启动清理任务
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        logger.info("告警通知面板已启动")
    
    async def stop(self) -> None:
        """停止告警通知面板"""
        if not self._running:
            return
        
        self._running = False
        
        # 取消清理任务
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        logger.info("告警通知面板已停止")
    
    def add_alert(self, alert: RiskAlert) -> None:
        """添加新告警"""
        try:
            # 添加到列表
            self.alerts.append(alert)
            
            # 按时间排序（最新的在前面）
            self.alerts.sort(key=lambda x: x.timestamp, reverse=True)
            
            # 限制数量
            if len(self.alerts) > self.max_alerts:
                self.alerts = self.alerts[:self.max_alerts]
            
            # 更新统计
            self._update_alert_stats()
            
            # 创建对应的通知
            self._create_alert_notification(alert)
            
            # 触发回调
            if self.on_new_alert:
                try:
                    asyncio.create_task(self.on_new_alert(alert))
                except Exception as e:
                    logger.error(f"新告警回调错误: {e}")
            
            logger.info(f"添加新告警: {alert.title} ({alert.severity.value})")
            
        except Exception as e:
            logger.error(f"添加告警失败: {e}")
    
    def acknowledge_alert(self, alert_id: str, user_id: str, notes: Optional[str] = None) -> bool:
        """确认告警"""
        try:
            for alert in self.alerts:
                if alert.id == alert_id:
                    alert.status = AlertStatus.ACKNOWLEDGED
                    alert.acknowledged_by = user_id
                    alert.acknowledged_at = datetime.now()
                    
                    if notes:
                        alert.metadata['acknowledgment_notes'] = notes
                    
                    # 更新统计
                    self._update_alert_stats()
                    
                    # 创建确认通知
                    self.create_notification(
                        type=NotificationType.INFO,
                        priority=NotificationPriority.LOW,
                        title="告警已确认",
                        message=f"告警 '{alert.title}' 已被 {user_id} 确认",
                        source="alert_panel"
                    )
                    
                    # 触发回调
                    if self.on_alert_acknowledged:
                        try:
                            asyncio.create_task(self.on_alert_acknowledged(alert))
                        except Exception as e:
                            logger.error(f"告警确认回调错误: {e}")
                    
                    logger.info(f"告警已确认: {alert_id} by {user_id}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"确认告警失败: {e}")
            return False
    
    def resolve_alert(self, alert_id: str, user_id: str, resolution_notes: Optional[str] = None) -> bool:
        """解决告警"""
        try:
            for alert in self.alerts:
                if alert.id == alert_id:
                    alert.status = AlertStatus.RESOLVED
                    alert.resolved_at = datetime.now()
                    
                    if resolution_notes:
                        alert.metadata['resolution_notes'] = resolution_notes
                    
                    # 更新统计
                    self._update_alert_stats()
                    
                    # 创建解决通知
                    self.create_notification(
                        type=NotificationType.SUCCESS,
                        priority=NotificationPriority.LOW,
                        title="告警已解决",
                        message=f"告警 '{alert.title}' 已被 {user_id} 解决",
                        source="alert_panel"
                    )
                    
                    # 触发回调
                    if self.on_alert_resolved:
                        try:
                            asyncio.create_task(self.on_alert_resolved(alert))
                        except Exception as e:
                            logger.error(f"告警解决回调错误: {e}")
                    
                    logger.info(f"告警已解决: {alert_id} by {user_id}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"解决告警失败: {e}")
            return False
    
    def create_notification(self,
                          type: NotificationType,
                          priority: NotificationPriority,
                          title: str,
                          message: str,
                          source: str,
                          auto_dismiss_seconds: Optional[int] = None,
                          actions: Optional[List[Dict[str, Any]]] = None,
                          metadata: Optional[Dict[str, Any]] = None) -> str:
        """创建通知"""
        try:
            import uuid
            
            notification = Notification(
                id=str(uuid.uuid4()),
                timestamp=datetime.now(),
                type=type,
                priority=priority,
                title=title,
                message=message,
                source=source,
                auto_dismiss_seconds=auto_dismiss_seconds,
                actions=actions or [],
                metadata=metadata or {}
            )
            
            # 添加到列表
            self.notifications.append(notification)
            
            # 按优先级和时间排序
            self.notifications.sort(key=lambda x: (x.priority.value, x.timestamp), reverse=True)
            
            # 限制数量
            if len(self.notifications) > self.max_notifications:
                self.notifications = self.notifications[:self.max_notifications]
            
            # 触发回调
            if self.on_notification_created:
                try:
                    asyncio.create_task(self.on_notification_created(notification))
                except Exception as e:
                    logger.error(f"通知创建回调错误: {e}")
            
            logger.debug(f"创建通知: {title}")
            return notification.id
            
        except Exception as e:
            logger.error(f"创建通知失败: {e}")
            return ""
    
    def _create_alert_notification(self, alert: RiskAlert) -> None:
        """为告警创建对应的通知"""
        try:
            # 映射告警严重程度到通知类型和优先级
            severity_mapping = {
                AlertSeverity.INFO: (NotificationType.INFO, NotificationPriority.LOW),
                AlertSeverity.WARNING: (NotificationType.WARNING, NotificationPriority.MEDIUM),
                AlertSeverity.ERROR: (NotificationType.ERROR, NotificationPriority.HIGH),
                AlertSeverity.CRITICAL: (NotificationType.ERROR, NotificationPriority.CRITICAL)
            }
            
            notification_type, priority = severity_mapping.get(
                alert.severity, 
                (NotificationType.WARNING, NotificationPriority.MEDIUM)
            )
            
            # 创建操作按钮
            actions = [
                {
                    'id': 'acknowledge',
                    'label': '确认',
                    'action': 'acknowledge_alert',
                    'params': {'alert_id': alert.id}
                },
                {
                    'id': 'resolve',
                    'label': '解决',
                    'action': 'resolve_alert',
                    'params': {'alert_id': alert.id}
                }
            ]
            
            # 设置自动消失时间
            auto_dismiss = None
            if alert.severity in [AlertSeverity.INFO, AlertSeverity.WARNING]:
                auto_dismiss = 300  # 5分钟
            
            self.create_notification(
                type=notification_type,
                priority=priority,
                title=f"风险告警: {alert.title}",
                message=alert.message,
                source="risk_monitor",
                auto_dismiss_seconds=auto_dismiss,
                actions=actions,
                metadata={
                    'alert_id': alert.id,
                    'alert_severity': alert.severity.value,
                    'alert_source': alert.source
                }
            )
            
        except Exception as e:
            logger.error(f"创建告警通知失败: {e}")
    
    def get_filtered_alerts(self, filter_name: Optional[str] = None, 
                           custom_filter: Optional[AlertFilter] = None) -> List[RiskAlert]:
        """获取过滤后的告警列表"""
        try:
            alerts = self.alerts.copy()
            
            # 应用过滤器
            if filter_name and filter_name in self.alert_filters:
                filter_obj = self.alert_filters[filter_name]
                alerts = [alert for alert in alerts if filter_obj.matches(alert)]
            elif custom_filter:
                alerts = [alert for alert in alerts if custom_filter.matches(alert)]
            
            return alerts
            
        except Exception as e:
            logger.error(f"过滤告警失败: {e}")
            return []
    
    def add_alert_filter(self, name: str, filter_obj: AlertFilter) -> None:
        """添加告警过滤器"""
        self.alert_filters[name] = filter_obj
        logger.info(f"添加告警过滤器: {name}")
    
    def remove_alert_filter(self, name: str) -> bool:
        """移除告警过滤器"""
        if name in self.alert_filters:
            del self.alert_filters[name]
            logger.info(f"移除告警过滤器: {name}")
            return True
        return False
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """获取告警统计信息"""
        return self.alert_stats.copy()
    
    def get_recent_alerts(self, hours: int = 24, limit: int = 50) -> List[RiskAlert]:
        """获取最近的告警"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_alerts = [
            alert for alert in self.alerts
            if alert.timestamp >= cutoff_time
        ]
        return recent_alerts[:limit]
    
    def get_unread_notifications(self) -> List[Notification]:
        """获取未读通知"""
        return [notif for notif in self.notifications if not notif.read and not notif.dismissed]
    
    def mark_notification_read(self, notification_id: str) -> bool:
        """标记通知为已读"""
        for notif in self.notifications:
            if notif.id == notification_id:
                notif.read = True
                return True
        return False
    
    def dismiss_notification(self, notification_id: str) -> bool:
        """关闭通知"""
        for notif in self.notifications:
            if notif.id == notification_id:
                notif.dismissed = True
                return True
        return False
    
    def clear_all_notifications(self) -> None:
        """清除所有通知"""
        self.notifications.clear()
        logger.info("已清除所有通知")
    
    def _update_alert_stats(self) -> None:
        """更新告警统计信息"""
        try:
            # 重置统计
            self.alert_stats = {
                'total': len(self.alerts),
                'active': 0,
                'acknowledged': 0,
                'resolved': 0,
                'by_severity': {severity.value: 0 for severity in AlertSeverity},
                'by_source': {},
                'last_24h': 0
            }
            
            # 计算时间范围
            last_24h = datetime.now() - timedelta(hours=24)
            
            # 统计各种指标
            for alert in self.alerts:
                # 按状态统计
                if alert.status == AlertStatus.ACTIVE:
                    self.alert_stats['active'] += 1
                elif alert.status == AlertStatus.ACKNOWLEDGED:
                    self.alert_stats['acknowledged'] += 1
                elif alert.status == AlertStatus.RESOLVED:
                    self.alert_stats['resolved'] += 1
                
                # 按严重程度统计
                self.alert_stats['by_severity'][alert.severity.value] += 1
                
                # 按来源统计
                source = alert.source
                if source not in self.alert_stats['by_source']:
                    self.alert_stats['by_source'][source] = 0
                self.alert_stats['by_source'][source] += 1
                
                # 最近24小时统计
                if alert.timestamp >= last_24h:
                    self.alert_stats['last_24h'] += 1
            
        except Exception as e:
            logger.error(f"更新告警统计失败: {e}")
    
    async def _cleanup_loop(self) -> None:
        """清理循环任务"""
        while self._running:
            try:
                await self._cleanup_old_data()
                await self._auto_dismiss_notifications()
                await asyncio.sleep(300)  # 每5分钟清理一次
                
            except Exception as e:
                logger.error(f"清理任务错误: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_old_data(self) -> None:
        """清理旧数据"""
        try:
            # 清理超过7天的已解决告警
            cutoff_time = datetime.now() - timedelta(days=7)
            
            original_count = len(self.alerts)
            self.alerts = [
                alert for alert in self.alerts
                if not (alert.status == AlertStatus.RESOLVED and alert.resolved_at and alert.resolved_at < cutoff_time)
            ]
            
            cleaned_count = original_count - len(self.alerts)
            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个旧告警")
                self._update_alert_stats()
            
            # 清理超过24小时的已关闭通知
            notification_cutoff = datetime.now() - timedelta(hours=24)
            original_notif_count = len(self.notifications)
            
            self.notifications = [
                notif for notif in self.notifications
                if not (notif.dismissed and notif.timestamp < notification_cutoff)
            ]
            
            cleaned_notif_count = original_notif_count - len(self.notifications)
            if cleaned_notif_count > 0:
                logger.info(f"清理了 {cleaned_notif_count} 个旧通知")
            
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
    
    async def _auto_dismiss_notifications(self) -> None:
        """自动关闭通知"""
        try:
            current_time = datetime.now()
            dismissed_count = 0
            
            for notif in self.notifications:
                if (not notif.dismissed and 
                    notif.auto_dismiss_seconds and 
                    (current_time - notif.timestamp).total_seconds() >= notif.auto_dismiss_seconds):
                    
                    notif.dismissed = True
                    dismissed_count += 1
            
            if dismissed_count > 0:
                logger.debug(f"自动关闭了 {dismissed_count} 个通知")
            
        except Exception as e:
            logger.error(f"自动关闭通知失败: {e}")
    
    def export_alerts_to_json(self, filename: Optional[str] = None) -> str:
        """导出告警数据到JSON文件"""
        try:
            if not filename:
                filename = f"alerts_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            export_data = {
                'export_time': datetime.now().isoformat(),
                'total_alerts': len(self.alerts),
                'statistics': self.alert_stats,
                'alerts': [alert.to_dict() for alert in self.alerts]
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"告警数据已导出到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"导出告警数据失败: {e}")
            return ""
    
    def get_alert_trends(self, days: int = 7) -> Dict[str, Any]:
        """获取告警趋势分析"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            # 按天统计告警数量
            daily_counts = {}
            severity_trends = {severity.value: {} for severity in AlertSeverity}
            
            for alert in self.alerts:
                if start_time <= alert.timestamp <= end_time:
                    day_key = alert.timestamp.strftime('%Y-%m-%d')
                    
                    # 总数统计
                    if day_key not in daily_counts:
                        daily_counts[day_key] = 0
                    daily_counts[day_key] += 1
                    
                    # 按严重程度统计
                    severity = alert.severity.value
                    if day_key not in severity_trends[severity]:
                        severity_trends[severity][day_key] = 0
                    severity_trends[severity][day_key] += 1
            
            return {
                'period_days': days,
                'daily_counts': daily_counts,
                'severity_trends': severity_trends,
                'total_in_period': sum(daily_counts.values()),
                'avg_per_day': sum(daily_counts.values()) / days if days > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"获取告警趋势失败: {e}")
            return {}