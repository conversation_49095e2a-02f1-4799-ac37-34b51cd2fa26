"""
监控界面主入口 - 集成所有监控功能
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
import json

from .real_time_dashboard import RealTimeDashboard
from .alert_notification_panel import AlertNotificationPanel, NotificationType, NotificationPriority
from ...models.portfolio_manager import PortfolioManager
from ...monitoring.health_monitor import HealthMonitor
from ...risk.monitoring_system import RiskMonitoringSystem
from ..charts.monitoring_panel import MonitoringPanel

logger = logging.getLogger(__name__)


class MonitoringInterface:
    """
    监控界面主类
    
    集成实时仪表板、告警通知、系统监控等功能，
    提供统一的监控界面入口。
    """
    
    def __init__(self,
                 portfolio_manager: PortfolioManager,
                 health_monitor: Optional[HealthMonitor] = None,
                 risk_monitor: Optional[RiskMonitoringSystem] = None):
        """
        初始化监控界面
        
        Args:
            portfolio_manager: 投资组合管理器
            health_monitor: 健康监控器
            risk_monitor: 风险监控系统
        """
        self.portfolio_manager = portfolio_manager
        self.health_monitor = health_monitor
        self.risk_monitor = risk_monitor
        
        # 创建子组件
        self.monitoring_panel = MonitoringPanel(
            health_monitor=health_monitor,
            risk_monitor=risk_monitor
        )
        
        self.dashboard = RealTimeDashboard(
            portfolio_manager=portfolio_manager,
            health_monitor=health_monitor,
            risk_monitor=risk_monitor,
            monitoring_panel=self.monitoring_panel
        )
        
        self.alert_panel = AlertNotificationPanel()
        
        # 界面状态
        self.is_running = False
        self.current_view = "dashboard"  # dashboard, alerts, system, settings
        
        # 配置
        self.config = {
            'refresh_interval': 1.0,
            'auto_scroll': True,
            'sound_alerts': True,
            'desktop_notifications': True,
            'theme': 'dark',
            'layout': 'grid'  # grid, list, compact
        }
        
        # 设置组件间的回调
        self._setup_callbacks()
        
        logger.info("监控界面初始化完成")
    
    def _setup_callbacks(self) -> None:
        """设置组件间的回调"""
        # 仪表板数据更新回调
        self.dashboard.on_data_update = self._on_dashboard_update
        self.dashboard.on_alert_received = self._on_alert_received
        
        # 告警面板回调
        self.alert_panel.on_new_alert = self._on_new_alert
        self.alert_panel.on_notification_created = self._on_notification_created
        
        # 风险监控回调
        if self.risk_monitor:
            self.risk_monitor.alert_callbacks.append(self._handle_risk_alert)
    
    async def start(self) -> bool:
        """启动监控界面"""
        try:
            if self.is_running:
                logger.warning("监控界面已在运行中")
                return True
            
            # 启动各个组件
            success = True
            
            # 启动监控面板
            if self.monitoring_panel:
                await self.monitoring_panel.start_monitoring()
            
            # 启动仪表板
            dashboard_started = await self.dashboard.start()
            if not dashboard_started:
                logger.error("仪表板启动失败")
                success = False
            
            # 启动告警面板
            await self.alert_panel.start()
            
            if success:
                self.is_running = True
                
                # 创建启动通知
                self.alert_panel.create_notification(
                    type=NotificationType.SUCCESS,
                    priority=NotificationPriority.LOW,
                    title="监控系统已启动",
                    message="实时监控仪表板已成功启动",
                    source="monitoring_interface",
                    auto_dismiss_seconds=5
                )
                
                logger.info("监控界面已启动")
            
            return success
            
        except Exception as e:
            logger.error(f"启动监控界面失败: {e}")
            return False
    
    async def stop(self) -> bool:
        """停止监控界面"""
        try:
            if not self.is_running:
                return True
            
            # 停止各个组件
            await self.dashboard.stop()
            await self.alert_panel.stop()
            
            if self.monitoring_panel:
                self.monitoring_panel.stop_monitoring()
            
            self.is_running = False
            logger.info("监控界面已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止监控界面失败: {e}")
            return False
    
    def get_interface_data(self) -> Dict[str, Any]:
        """获取完整的界面数据"""
        try:
            # 获取仪表板数据
            dashboard_data = self.dashboard.get_dashboard_data()
            
            # 获取告警数据
            alert_stats = self.alert_panel.get_alert_statistics()
            recent_alerts = self.alert_panel.get_recent_alerts(hours=24, limit=20)
            unread_notifications = self.alert_panel.get_unread_notifications()
            
            # 获取系统概览
            system_overview = {}
            if self.monitoring_panel:
                system_overview = self.monitoring_panel.get_system_overview()
            
            return {
                'interface_status': {
                    'running': self.is_running,
                    'current_view': self.current_view,
                    'last_update': datetime.now().isoformat()
                },
                'dashboard': dashboard_data,
                'alerts': {
                    'statistics': alert_stats,
                    'recent_alerts': [alert.to_dict() for alert in recent_alerts],
                    'unread_notifications': [notif.to_dict() for notif in unread_notifications]
                },
                'system': system_overview,
                'config': self.config
            }
            
        except Exception as e:
            logger.error(f"获取界面数据失败: {e}")
            return {}
    
    def switch_view(self, view_name: str) -> bool:
        """切换视图"""
        valid_views = ['dashboard', 'alerts', 'system', 'settings']
        
        if view_name in valid_views:
            self.current_view = view_name
            logger.info(f"切换到视图: {view_name}")
            return True
        else:
            logger.warning(f"无效的视图名称: {view_name}")
            return False
    
    def update_config(self, config_updates: Dict[str, Any]) -> bool:
        """更新配置"""
        try:
            for key, value in config_updates.items():
                if key in self.config:
                    self.config[key] = value
                    logger.info(f"配置已更新: {key} = {value}")
            
            # 应用配置更改
            self._apply_config_changes(config_updates)
            return True
            
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return False
    
    def _apply_config_changes(self, changes: Dict[str, Any]) -> None:
        """应用配置更改"""
        try:
            # 更新刷新间隔
            if 'refresh_interval' in changes:
                self.dashboard.update_interval = changes['refresh_interval']
            
            # 更新告警设置
            if 'sound_alerts' in changes:
                self.alert_panel.sound_enabled = changes['sound_alerts']
            
            if 'desktop_notifications' in changes:
                self.alert_panel.desktop_notifications = changes['desktop_notifications']
            
        except Exception as e:
            logger.error(f"应用配置更改失败: {e}")
    
    async def _on_dashboard_update(self, data: Dict[str, Any]) -> None:
        """仪表板数据更新回调"""
        try:
            # 这里可以处理仪表板数据更新
            # 例如：检查关键指标变化，触发特定通知等
            
            # 检查风险评分变化
            risk_indicators = data.get('risk_indicators')
            if risk_indicators:
                risk_score = risk_indicators.get('risk_score', 0)
                
                if risk_score > 80:
                    self.alert_panel.create_notification(
                        type=NotificationType.WARNING,
                        priority=NotificationPriority.HIGH,
                        title="高风险警告",
                        message=f"当前风险评分: {risk_score:.1f}，请注意风险控制",
                        source="dashboard_monitor",
                        auto_dismiss_seconds=300
                    )
            
        except Exception as e:
            logger.error(f"仪表板更新回调错误: {e}")
    
    async def _on_alert_received(self, alert) -> None:
        """收到告警回调"""
        try:
            # 将风险告警添加到告警面板
            self.alert_panel.add_alert(alert)
            
        except Exception as e:
            logger.error(f"告警接收回调错误: {e}")
    
    async def _on_new_alert(self, alert) -> None:
        """新告警回调"""
        try:
            # 可以在这里添加额外的告警处理逻辑
            # 例如：发送邮件、短信通知等
            pass
            
        except Exception as e:
            logger.error(f"新告警回调错误: {e}")
    
    async def _on_notification_created(self, notification) -> None:
        """通知创建回调"""
        try:
            # 可以在这里处理通知创建后的逻辑
            # 例如：桌面通知、声音提醒等
            if self.config.get('desktop_notifications', False):
                # 这里可以集成桌面通知系统
                pass
            
        except Exception as e:
            logger.error(f"通知创建回调错误: {e}")
    
    def _handle_risk_alert(self, alert) -> None:
        """处理风险告警"""
        try:
            # 直接添加到告警面板
            self.alert_panel.add_alert(alert)
            
        except Exception as e:
            logger.error(f"处理风险告警失败: {e}")
    
    def get_dashboard_summary(self) -> Dict[str, Any]:
        """获取仪表板摘要"""
        try:
            position_summary = self.dashboard.get_position_summary()
            risk_summary = self.dashboard.get_risk_summary()
            
            return {
                'positions': position_summary,
                'risk': risk_summary,
                'alerts': {
                    'active_count': len(self.alert_panel.get_unread_notifications()),
                    'critical_count': len([
                        alert for alert in self.alert_panel.alerts
                        if alert.severity.value == 'critical' and alert.status.value == 'active'
                    ])
                }
            }
            
        except Exception as e:
            logger.error(f"获取仪表板摘要失败: {e}")
            return {}
    
    def export_monitoring_data(self, filename: Optional[str] = None) -> str:
        """导出监控数据"""
        try:
            if not filename:
                filename = f"monitoring_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            # 收集所有监控数据
            export_data = {
                'export_time': datetime.now().isoformat(),
                'interface_data': self.get_interface_data(),
                'dashboard_data': self.dashboard.get_dashboard_data(),
                'performance_data': self.dashboard.get_performance_chart_data(hours=24),
                'alert_statistics': self.alert_panel.get_alert_statistics(),
                'alert_trends': self.alert_panel.get_alert_trends(days=7)
            }
            
            # 写入文件
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"监控数据已导出到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"导出监控数据失败: {e}")
            return ""
    
    def get_system_health_summary(self) -> Dict[str, Any]:
        """获取系统健康摘要"""
        try:
            summary = {
                'overall_status': 'unknown',
                'components': {},
                'alerts': 0,
                'uptime': 0
            }
            
            # 获取系统状态
            if self.dashboard.system_status:
                summary['overall_status'] = self.dashboard.system_status.overall_status
                summary['uptime'] = self.dashboard.system_status.uptime_seconds
            
            # 获取组件状态
            summary['components'] = {
                'dashboard': 'running' if self.dashboard._running else 'stopped',
                'alert_panel': 'running' if self.alert_panel._running else 'stopped',
                'monitoring_panel': 'running' if self.monitoring_panel and self.monitoring_panel.is_monitoring else 'stopped'
            }
            
            # 获取告警数量
            summary['alerts'] = len(self.alert_panel.get_unread_notifications())
            
            return summary
            
        except Exception as e:
            logger.error(f"获取系统健康摘要失败: {e}")
            return {}


# 创建全局监控界面实例的工厂函数
def create_monitoring_interface(portfolio_manager: PortfolioManager,
                              health_monitor: Optional[HealthMonitor] = None,
                              risk_monitor: Optional[RiskMonitoringSystem] = None) -> MonitoringInterface:
    """
    创建监控界面实例
    
    Args:
        portfolio_manager: 投资组合管理器
        health_monitor: 健康监控器
        risk_monitor: 风险监控系统
        
    Returns:
        MonitoringInterface: 监控界面实例
    """
    return MonitoringInterface(
        portfolio_manager=portfolio_manager,
        health_monitor=health_monitor,
        risk_monitor=risk_monitor
    )