"""
实时监控仪表板 - 提供全面的实时监控界面
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
import pandas as pd
import numpy as np
from enum import Enum

from ...monitoring.health_monitor import HealthMonitor, HealthStatus
from ...risk.monitoring_system import RiskMonitoringSystem, RiskAlert, AlertSeverity
from ...models.portfolio_manager import PortfolioManager
from ..charts.monitoring_panel import MonitoringPanel

logger = logging.getLogger(__name__)


class DashboardStatus(Enum):
    """仪表板状态"""
    ACTIVE = "active"
    PAUSED = "paused"
    ERROR = "error"
    DISCONNECTED = "disconnected"


@dataclass
class PositionMonitorData:
    """持仓监控数据"""
    symbol: str
    quantity: float
    market_value: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    daily_pnl: float
    daily_pnl_pct: float
    weight: float
    last_price: float
    avg_price: float
    risk_contribution: float
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'quantity': self.quantity,
            'market_value': self.market_value,
            'unrealized_pnl': self.unrealized_pnl,
            'unrealized_pnl_pct': self.unrealized_pnl_pct,
            'daily_pnl': self.daily_pnl,
            'daily_pnl_pct': self.daily_pnl_pct,
            'weight': self.weight,
            'last_price': self.last_price,
            'avg_price': self.avg_price,
            'risk_contribution': self.risk_contribution
        }


@dataclass
class PnLSummary:
    """盈亏汇总数据"""
    total_pnl: float
    daily_pnl: float
    unrealized_pnl: float
    realized_pnl: float
    total_pnl_pct: float
    daily_pnl_pct: float
    best_position: Optional[str] = None
    worst_position: Optional[str] = None
    best_position_pnl: float = 0.0
    worst_position_pnl: float = 0.0


@dataclass
class RiskIndicators:
    """风险指标数据"""
    portfolio_var: float
    portfolio_cvar: float
    max_drawdown: float
    current_drawdown: float
    risk_score: float
    concentration_risk: float
    leverage_ratio: float
    cash_ratio: float
    beta: float
    sharpe_ratio: float
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'portfolio_var': self.portfolio_var,
            'portfolio_cvar': self.portfolio_cvar,
            'max_drawdown': self.max_drawdown,
            'current_drawdown': self.current_drawdown,
            'risk_score': self.risk_score,
            'concentration_risk': self.concentration_risk,
            'leverage_ratio': self.leverage_ratio,
            'cash_ratio': self.cash_ratio,
            'beta': self.beta,
            'sharpe_ratio': self.sharpe_ratio
        }


@dataclass
class SystemStatus:
    """系统状态数据"""
    overall_status: str
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, float]
    active_strategies: int
    total_positions: int
    uptime_seconds: int
    last_update: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'overall_status': self.overall_status,
            'cpu_usage': self.cpu_usage,
            'memory_usage': self.memory_usage,
            'disk_usage': self.disk_usage,
            'network_io': self.network_io,
            'active_strategies': self.active_strategies,
            'total_positions': self.total_positions,
            'uptime_seconds': self.uptime_seconds,
            'last_update': self.last_update.isoformat()
        }


class RealTimeDashboard:
    """
    实时监控仪表板主类
    
    提供全面的实时监控功能，包括：
    - 实时持仓和盈亏监控
    - 风险指标实时显示
    - 系统状态和性能监控
    - 告警和通知显示
    """
    
    def __init__(self,
                 portfolio_manager: PortfolioManager,
                 health_monitor: Optional[HealthMonitor] = None,
                 risk_monitor: Optional[RiskMonitoringSystem] = None,
                 monitoring_panel: Optional[MonitoringPanel] = None):
        """
        初始化实时监控仪表板
        
        Args:
            portfolio_manager: 投资组合管理器
            health_monitor: 健康监控器
            risk_monitor: 风险监控系统
            monitoring_panel: 监控面板
        """
        self.portfolio_manager = portfolio_manager
        self.health_monitor = health_monitor
        self.risk_monitor = risk_monitor
        self.monitoring_panel = monitoring_panel
        
        # 仪表板状态
        self.status = DashboardStatus.DISCONNECTED
        self.last_update: Optional[datetime] = None
        self.update_interval = 1.0  # 更新间隔（秒）
        
        # 数据缓存
        self.position_data: List[PositionMonitorData] = []
        self.pnl_summary: Optional[PnLSummary] = None
        self.risk_indicators: Optional[RiskIndicators] = None
        self.system_status: Optional[SystemStatus] = None
        self.active_alerts: List[RiskAlert] = []
        
        # 历史数据
        self.pnl_history: List[Dict[str, Any]] = []
        self.risk_history: List[Dict[str, Any]] = []
        self.max_history_points = 1000
        
        # 事件回调
        self.on_data_update: Optional[Callable] = None
        self.on_alert_received: Optional[Callable] = None
        self.on_status_change: Optional[Callable] = None
        
        # 异步任务
        self._update_task: Optional[asyncio.Task] = None
        self._running = False
        
        logger.info("实时监控仪表板初始化完成")
    
    async def start(self) -> bool:
        """启动实时监控仪表板"""
        try:
            if self._running:
                logger.warning("仪表板已在运行中")
                return True
            
            self._running = True
            self.status = DashboardStatus.ACTIVE
            
            # 启动数据更新任务
            self._update_task = asyncio.create_task(self._update_loop())
            
            # 设置风险监控回调
            if self.risk_monitor:
                self.risk_monitor.alert_callbacks.append(self._handle_risk_alert)
            
            logger.info("实时监控仪表板已启动")
            return True
            
        except Exception as e:
            logger.error(f"启动仪表板失败: {e}")
            self.status = DashboardStatus.ERROR
            return False
    
    async def stop(self) -> bool:
        """停止实时监控仪表板"""
        try:
            if not self._running:
                return True
            
            self._running = False
            self.status = DashboardStatus.DISCONNECTED
            
            # 取消更新任务
            if self._update_task:
                self._update_task.cancel()
                try:
                    await self._update_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("实时监控仪表板已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止仪表板失败: {e}")
            return False
    
    def pause(self) -> None:
        """暂停仪表板更新"""
        if self.status == DashboardStatus.ACTIVE:
            self.status = DashboardStatus.PAUSED
            logger.info("仪表板已暂停")
    
    def resume(self) -> None:
        """恢复仪表板更新"""
        if self.status == DashboardStatus.PAUSED:
            self.status = DashboardStatus.ACTIVE
            logger.info("仪表板已恢复")
    
    async def _update_loop(self) -> None:
        """主更新循环"""
        logger.info("仪表板更新循环已启动")
        
        while self._running:
            try:
                if self.status == DashboardStatus.ACTIVE:
                    await self._update_all_data()
                    self.last_update = datetime.now()
                    
                    # 触发数据更新回调
                    if self.on_data_update:
                        try:
                            await self.on_data_update(self.get_dashboard_data())
                        except Exception as e:
                            logger.error(f"数据更新回调错误: {e}")
                
                await asyncio.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"更新循环错误: {e}")
                self.status = DashboardStatus.ERROR
                await asyncio.sleep(5.0)  # 错误时等待更长时间
        
        logger.info("仪表板更新循环已停止")
    
    async def _update_all_data(self) -> None:
        """更新所有数据"""
        try:
            # 并行更新各种数据
            await asyncio.gather(
                self._update_position_data(),
                self._update_pnl_summary(),
                self._update_risk_indicators(),
                self._update_system_status(),
                self._update_alerts(),
                return_exceptions=True
            )
            
        except Exception as e:
            logger.error(f"更新数据失败: {e}")
    
    async def _update_position_data(self) -> None:
        """更新持仓数据"""
        try:
            portfolio = self.portfolio_manager.portfolio
            positions = []
            
            total_value = portfolio.total_value
            
            for symbol, position in portfolio.positions.items():
                # 计算各种指标
                weight = position.market_value / total_value if total_value > 0 else 0
                unrealized_pnl_pct = (position.unrealized_pnl / position.market_value * 100 
                                    if position.market_value > 0 else 0)
                
                # 计算当日盈亏（简化计算）
                daily_pnl = position.unrealized_pnl * 0.1  # 假设当日盈亏为未实现盈亏的10%
                daily_pnl_pct = daily_pnl / position.market_value * 100 if position.market_value > 0 else 0
                
                # 风险贡献度（简化计算）
                risk_contribution = weight * abs(unrealized_pnl_pct) / 100
                
                position_data = PositionMonitorData(
                    symbol=symbol,
                    quantity=position.quantity,
                    market_value=position.market_value,
                    unrealized_pnl=position.unrealized_pnl,
                    unrealized_pnl_pct=unrealized_pnl_pct,
                    daily_pnl=daily_pnl,
                    daily_pnl_pct=daily_pnl_pct,
                    weight=weight * 100,  # 转换为百分比
                    last_price=position.last_price,
                    avg_price=position.avg_price,
                    risk_contribution=risk_contribution
                )
                
                positions.append(position_data)
            
            # 按市值排序
            positions.sort(key=lambda x: x.market_value, reverse=True)
            self.position_data = positions
            
        except Exception as e:
            logger.error(f"更新持仓数据失败: {e}")
    
    async def _update_pnl_summary(self) -> None:
        """更新盈亏汇总"""
        try:
            portfolio = self.portfolio_manager.portfolio
            
            # 计算总盈亏
            total_unrealized_pnl = sum(pos.unrealized_pnl for pos in portfolio.positions.values())
            total_realized_pnl = sum(trade.pnl for trade in portfolio.trades if trade.pnl)
            total_pnl = total_unrealized_pnl + total_realized_pnl
            
            # 计算当日盈亏（简化）
            daily_pnl = total_unrealized_pnl * 0.1
            
            # 计算百分比
            initial_value = portfolio.initial_capital
            total_pnl_pct = (total_pnl / initial_value * 100) if initial_value > 0 else 0
            daily_pnl_pct = (daily_pnl / portfolio.total_value * 100) if portfolio.total_value > 0 else 0
            
            # 找出最佳和最差持仓
            best_position = None
            worst_position = None
            best_pnl = float('-inf')
            worst_pnl = float('inf')
            
            for pos_data in self.position_data:
                if pos_data.unrealized_pnl > best_pnl:
                    best_pnl = pos_data.unrealized_pnl
                    best_position = pos_data.symbol
                
                if pos_data.unrealized_pnl < worst_pnl:
                    worst_pnl = pos_data.unrealized_pnl
                    worst_position = pos_data.symbol
            
            self.pnl_summary = PnLSummary(
                total_pnl=total_pnl,
                daily_pnl=daily_pnl,
                unrealized_pnl=total_unrealized_pnl,
                realized_pnl=total_realized_pnl,
                total_pnl_pct=total_pnl_pct,
                daily_pnl_pct=daily_pnl_pct,
                best_position=best_position,
                worst_position=worst_position,
                best_position_pnl=best_pnl,
                worst_position_pnl=worst_pnl
            )
            
            # 添加到历史记录
            pnl_record = {
                'timestamp': datetime.now(),
                'total_pnl': total_pnl,
                'daily_pnl': daily_pnl,
                'total_pnl_pct': total_pnl_pct,
                'daily_pnl_pct': daily_pnl_pct
            }
            
            self.pnl_history.append(pnl_record)
            
            # 限制历史记录大小
            if len(self.pnl_history) > self.max_history_points:
                self.pnl_history = self.pnl_history[-self.max_history_points:]
            
        except Exception as e:
            logger.error(f"更新盈亏汇总失败: {e}")
    
    async def _update_risk_indicators(self) -> None:
        """更新风险指标"""
        try:
            # 计算各种风险指标
            portfolio_metrics = self.portfolio_manager.calculate_portfolio_metrics()
            concentration_metrics = self.portfolio_manager.analyze_position_concentration()
            
            # VaR和CVaR
            portfolio_var = self.portfolio_manager.calculate_var(0.05)
            portfolio_cvar = self.portfolio_manager.calculate_expected_shortfall(0.05)
            
            # 其他风险指标
            max_drawdown = portfolio_metrics.get('max_drawdown', 0.0)
            current_drawdown = portfolio_metrics.get('current_drawdown', 0.0)
            sharpe_ratio = portfolio_metrics.get('sharpe_ratio', 0.0)
            
            # 集中度风险
            concentration_risk = concentration_metrics.get('herfindahl_index', 0.0)
            
            # 杠杆比率（简化计算）
            portfolio = self.portfolio_manager.portfolio
            total_position_value = sum(abs(pos.market_value) for pos in portfolio.positions.values())
            leverage_ratio = total_position_value / portfolio.total_value if portfolio.total_value > 0 else 0
            
            # 现金比率
            cash_ratio = portfolio.cash / portfolio.total_value if portfolio.total_value > 0 else 0
            
            # 风险评分（综合指标，0-100）
            risk_score = min(100, max(0, (
                (max_drawdown * 100 * 2) +  # 最大回撤权重2
                (concentration_risk * 100 * 1.5) +  # 集中度风险权重1.5
                (max(0, leverage_ratio - 1) * 50) +  # 超过1的杠杆部分权重50
                (max(0, 10 - cash_ratio * 100) * 2)  # 现金不足10%的惩罚
            )))
            
            self.risk_indicators = RiskIndicators(
                portfolio_var=portfolio_var,
                portfolio_cvar=portfolio_cvar,
                max_drawdown=max_drawdown * 100,  # 转换为百分比
                current_drawdown=current_drawdown * 100,
                risk_score=risk_score,
                concentration_risk=concentration_risk,
                leverage_ratio=leverage_ratio,
                cash_ratio=cash_ratio * 100,  # 转换为百分比
                beta=portfolio_metrics.get('beta', 1.0),
                sharpe_ratio=sharpe_ratio
            )
            
            # 添加到历史记录
            risk_record = {
                'timestamp': datetime.now(),
                **self.risk_indicators.to_dict()
            }
            
            self.risk_history.append(risk_record)
            
            # 限制历史记录大小
            if len(self.risk_history) > self.max_history_points:
                self.risk_history = self.risk_history[-self.max_history_points:]
            
        except Exception as e:
            logger.error(f"更新风险指标失败: {e}")
    
    async def _update_system_status(self) -> None:
        """更新系统状态"""
        try:
            # 获取系统健康状态
            if self.health_monitor:
                health_data = await self.health_monitor.get_overall_health()
                system_metrics = await self.health_monitor.get_system_metrics()
                
                if system_metrics:
                    self.system_status = SystemStatus(
                        overall_status=health_data.get('status', 'unknown'),
                        cpu_usage=system_metrics.cpu_usage,
                        memory_usage=system_metrics.memory_usage,
                        disk_usage=system_metrics.disk_usage,
                        network_io={
                            'bytes_sent': system_metrics.network_io.get('bytes_sent', 0),
                            'bytes_recv': system_metrics.network_io.get('bytes_recv', 0)
                        },
                        active_strategies=len(self.position_data),  # 简化：使用持仓数量
                        total_positions=len(self.portfolio_manager.portfolio.positions),
                        uptime_seconds=system_metrics.uptime_seconds,
                        last_update=datetime.now()
                    )
            else:
                # 如果没有健康监控器，使用默认值
                self.system_status = SystemStatus(
                    overall_status='unknown',
                    cpu_usage=0.0,
                    memory_usage=0.0,
                    disk_usage=0.0,
                    network_io={'bytes_sent': 0, 'bytes_recv': 0},
                    active_strategies=0,
                    total_positions=len(self.portfolio_manager.portfolio.positions),
                    uptime_seconds=0,
                    last_update=datetime.now()
                )
            
        except Exception as e:
            logger.error(f"更新系统状态失败: {e}")
    
    async def _update_alerts(self) -> None:
        """更新告警信息"""
        try:
            if self.risk_monitor:
                # 获取活跃告警
                active_alerts = list(self.risk_monitor.active_alerts.values())
                
                # 按严重程度和时间排序
                severity_order = {
                    AlertSeverity.CRITICAL: 0,
                    AlertSeverity.ERROR: 1,
                    AlertSeverity.WARNING: 2,
                    AlertSeverity.INFO: 3
                }
                
                active_alerts.sort(key=lambda x: (
                    severity_order.get(x.severity, 4),
                    x.timestamp
                ), reverse=True)
                
                self.active_alerts = active_alerts[:50]  # 最多显示50个告警
            else:
                self.active_alerts = []
            
        except Exception as e:
            logger.error(f"更新告警信息失败: {e}")
    
    def _handle_risk_alert(self, alert: RiskAlert) -> None:
        """处理风险告警"""
        try:
            # 触发告警回调
            if self.on_alert_received:
                asyncio.create_task(self.on_alert_received(alert))
            
            logger.warning(f"收到风险告警: {alert.title} - {alert.message}")
            
        except Exception as e:
            logger.error(f"处理风险告警失败: {e}")
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取完整的仪表板数据"""
        return {
            'status': self.status.value,
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'positions': [pos.to_dict() for pos in self.position_data],
            'pnl_summary': self.pnl_summary.__dict__ if self.pnl_summary else None,
            'risk_indicators': self.risk_indicators.to_dict() if self.risk_indicators else None,
            'system_status': self.system_status.to_dict() if self.system_status else None,
            'active_alerts': [alert.to_dict() for alert in self.active_alerts],
            'alert_counts': self._get_alert_counts()
        }
    
    def _get_alert_counts(self) -> Dict[str, int]:
        """获取告警统计"""
        counts = {
            'critical': 0,
            'error': 0,
            'warning': 0,
            'info': 0,
            'total': len(self.active_alerts)
        }
        
        for alert in self.active_alerts:
            severity = alert.severity.value.lower()
            if severity in counts:
                counts[severity] += 1
        
        return counts
    
    def get_position_summary(self) -> Dict[str, Any]:
        """获取持仓汇总"""
        if not self.position_data:
            return {
                'total_positions': 0,
                'total_value': 0.0,
                'total_pnl': 0.0,
                'avg_weight': 0.0,
                'top_positions': []
            }
        
        total_value = sum(pos.market_value for pos in self.position_data)
        total_pnl = sum(pos.unrealized_pnl for pos in self.position_data)
        avg_weight = sum(pos.weight for pos in self.position_data) / len(self.position_data)
        
        # 前5大持仓
        top_positions = self.position_data[:5]
        
        return {
            'total_positions': len(self.position_data),
            'total_value': total_value,
            'total_pnl': total_pnl,
            'avg_weight': avg_weight,
            'top_positions': [pos.to_dict() for pos in top_positions]
        }
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """获取风险汇总"""
        if not self.risk_indicators:
            return {
                'overall_risk': 'unknown',
                'risk_score': 0.0,
                'key_risks': []
            }
        
        # 确定整体风险等级
        risk_score = self.risk_indicators.risk_score
        if risk_score >= 80:
            overall_risk = 'high'
        elif risk_score >= 60:
            overall_risk = 'medium'
        elif risk_score >= 40:
            overall_risk = 'low'
        else:
            overall_risk = 'minimal'
        
        # 识别关键风险
        key_risks = []
        
        if self.risk_indicators.max_drawdown > 15:
            key_risks.append('高回撤风险')
        
        if self.risk_indicators.concentration_risk > 0.3:
            key_risks.append('持仓集中度过高')
        
        if self.risk_indicators.leverage_ratio > 1.5:
            key_risks.append('杠杆过高')
        
        if self.risk_indicators.cash_ratio < 5:
            key_risks.append('现金不足')
        
        return {
            'overall_risk': overall_risk,
            'risk_score': risk_score,
            'key_risks': key_risks,
            'var_1d': self.risk_indicators.portfolio_var,
            'max_drawdown': self.risk_indicators.max_drawdown
        }
    
    def get_performance_chart_data(self, hours: int = 24) -> Dict[str, List[Dict[str, Any]]]:
        """获取性能图表数据"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 过滤历史数据
        pnl_data = [
            {
                'time': record['timestamp'].isoformat(),
                'total_pnl': record['total_pnl'],
                'daily_pnl': record['daily_pnl'],
                'total_pnl_pct': record['total_pnl_pct']
            }
            for record in self.pnl_history
            if record['timestamp'] >= cutoff_time
        ]
        
        risk_data = [
            {
                'time': record['timestamp'].isoformat(),
                'risk_score': record['risk_score'],
                'var': record['portfolio_var'],
                'drawdown': record['current_drawdown']
            }
            for record in self.risk_history
            if record['timestamp'] >= cutoff_time
        ]
        
        return {
            'pnl_data': pnl_data,
            'risk_data': risk_data
        }
    
    def export_dashboard_data(self) -> pd.DataFrame:
        """导出仪表板数据为DataFrame"""
        try:
            # 创建综合数据记录
            records = []
            
            current_time = datetime.now()
            record = {
                'timestamp': current_time,
                'total_positions': len(self.position_data),
                'total_pnl': self.pnl_summary.total_pnl if self.pnl_summary else 0,
                'daily_pnl': self.pnl_summary.daily_pnl if self.pnl_summary else 0,
                'risk_score': self.risk_indicators.risk_score if self.risk_indicators else 0,
                'max_drawdown': self.risk_indicators.max_drawdown if self.risk_indicators else 0,
                'var_1d': self.risk_indicators.portfolio_var if self.risk_indicators else 0,
                'cpu_usage': self.system_status.cpu_usage if self.system_status else 0,
                'memory_usage': self.system_status.memory_usage if self.system_status else 0,
                'active_alerts': len(self.active_alerts)
            }
            
            records.append(record)
            
            return pd.DataFrame(records)
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return pd.DataFrame()