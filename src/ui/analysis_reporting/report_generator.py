"""
Report generation and export functionality panel.

This module provides comprehensive report generation capabilities including
HTML, PDF, and JSON report formats, with customizable templates and
export options.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import logging
import json
import os
from pathlib import Path

from ...analysis.reports import PerformanceReporter, ReportConfig, ReportFormat
from ...analysis.comparison import ComparisonResult
from ...analysis.metrics import PerformanceMetrics

logger = logging.getLogger(__name__)


class ReportGeneratorPanel:
    """
    Panel component for report generation and export.
    
    This panel provides comprehensive report generation functionality including:
    - Multiple report formats (HTML, PDF, JSON)
    - Customizable report templates
    - Batch report generation
    - Export management
    - Report scheduling and automation
    """
    
    def __init__(self, config: Optional[ReportConfig] = None):
        """
        Initialize report generator panel.
        
        Args:
            config: Report configuration
        """
        self.config = config or ReportConfig()
        self.reporter = PerformanceReporter(self.config)
        
        # Report templates
        self.templates = {
            'executive_summary': self._get_executive_template(),
            'detailed_analysis': self._get_detailed_template(),
            'comparison_report': self._get_comparison_template(),
            'risk_analysis': self._get_risk_template(),
            'custom': self._get_custom_template()
        }
        
        # Generated reports storage
        self.generated_reports: Dict[str, Dict[str, Any]] = {}
        self.export_history: List[Dict[str, Any]] = []
        
        logger.info("ReportGeneratorPanel initialized")
    
    def generate_performance_report(self,
                                  strategy_id: str,
                                  portfolio_values: pd.Series,
                                  trades: Optional[List] = None,
                                  benchmark_values: Optional[pd.Series] = None,
                                  template: str = 'detailed_analysis',
                                  format: ReportFormat = ReportFormat.HTML,
                                  custom_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate performance report for a single strategy.
        
        Args:
            strategy_id: Strategy identifier
            portfolio_values: Portfolio value time series
            trades: Optional list of trades
            benchmark_values: Optional benchmark values
            template: Report template to use
            format: Output format
            custom_config: Custom configuration overrides
            
        Returns:
            Dictionary containing report data and metadata
        """
        try:
            # Apply custom configuration if provided
            if custom_config:
                self._apply_custom_config(custom_config)
            
            # Generate base report
            report_data = self.reporter.generate_report(
                portfolio_values, trades, benchmark_values, format
            )
            
            # Apply template formatting
            formatted_report = self._apply_template(report_data, template, format)
            
            # Create report metadata
            report_metadata = {
                'report_id': f"{strategy_id}_{template}_{format.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'strategy_id': strategy_id,
                'template': template,
                'format': format.value,
                'generated_at': datetime.now().isoformat(),
                'data_period': {
                    'start': portfolio_values.index[0].strftime('%Y-%m-%d'),
                    'end': portfolio_values.index[-1].strftime('%Y-%m-%d'),
                    'days': len(portfolio_values)
                },
                'includes_trades': trades is not None,
                'includes_benchmark': benchmark_values is not None,
                'file_size': len(str(formatted_report)) if isinstance(formatted_report, str) else 0
            }
            
            # Store generated report
            report_package = {
                'metadata': report_metadata,
                'data': formatted_report,
                'raw_data': report_data,
                'config': self.config.to_dict()
            }
            
            self.generated_reports[report_metadata['report_id']] = report_package
            
            logger.info(f"Generated performance report: {report_metadata['report_id']}")
            return report_package
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {'error': str(e)}
    
    def generate_comparison_report(self,
                                 comparison_result: ComparisonResult,
                                 template: str = 'comparison_report',
                                 format: ReportFormat = ReportFormat.HTML,
                                 custom_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate comparison report for multiple strategies.
        
        Args:
            comparison_result: ComparisonResult object
            template: Report template to use
            format: Output format
            custom_config: Custom configuration overrides
            
        Returns:
            Dictionary containing report data and metadata
        """
        try:
            # Apply custom configuration if provided
            if custom_config:
                self._apply_custom_config(custom_config)
            
            # Generate comparison report data
            report_data = self._generate_comparison_report_data(comparison_result)
            
            # Apply template formatting
            formatted_report = self._apply_template(report_data, template, format)
            
            # Create report metadata
            report_metadata = {
                'report_id': f"comparison_{template}_{format.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'report_type': 'comparison',
                'template': template,
                'format': format.value,
                'generated_at': datetime.now().isoformat(),
                'strategies_count': len(comparison_result.strategy_metrics),
                'strategies': list(comparison_result.strategy_metrics.keys()),
                'includes_correlations': not comparison_result.correlation_matrix.empty,
                'includes_optimization': bool(comparison_result.optimal_weights),
                'file_size': len(str(formatted_report)) if isinstance(formatted_report, str) else 0
            }
            
            # Store generated report
            report_package = {
                'metadata': report_metadata,
                'data': formatted_report,
                'raw_data': report_data,
                'config': self.config.to_dict()
            }
            
            self.generated_reports[report_metadata['report_id']] = report_package
            
            logger.info(f"Generated comparison report: {report_metadata['report_id']}")
            return report_package
            
        except Exception as e:
            logger.error(f"Error generating comparison report: {e}")
            return {'error': str(e)}
    
    def generate_batch_reports(self,
                             strategies_data: Dict[str, Dict[str, Any]],
                             templates: List[str],
                             formats: List[ReportFormat]) -> Dict[str, Any]:
        """
        Generate batch reports for multiple strategies.
        
        Args:
            strategies_data: Dictionary of strategy data
            templates: List of templates to use
            formats: List of formats to generate
            
        Returns:
            Dictionary containing batch generation results
        """
        try:
            batch_results = {
                'batch_id': f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'started_at': datetime.now().isoformat(),
                'total_reports': len(strategies_data) * len(templates) * len(formats),
                'completed_reports': 0,
                'failed_reports': 0,
                'reports': [],
                'errors': []
            }
            
            for strategy_id, strategy_data in strategies_data.items():
                for template in templates:
                    for format in formats:
                        try:
                            report = self.generate_performance_report(
                                strategy_id=strategy_id,
                                portfolio_values=strategy_data['portfolio_values'],
                                trades=strategy_data.get('trades'),
                                benchmark_values=strategy_data.get('benchmark_values'),
                                template=template,
                                format=format
                            )
                            
                            if 'error' not in report:
                                batch_results['reports'].append(report['metadata']['report_id'])
                                batch_results['completed_reports'] += 1
                            else:
                                batch_results['errors'].append({
                                    'strategy_id': strategy_id,
                                    'template': template,
                                    'format': format.value,
                                    'error': report['error']
                                })
                                batch_results['failed_reports'] += 1
                                
                        except Exception as e:
                            batch_results['errors'].append({
                                'strategy_id': strategy_id,
                                'template': template,
                                'format': format.value,
                                'error': str(e)
                            })
                            batch_results['failed_reports'] += 1
            
            batch_results['completed_at'] = datetime.now().isoformat()
            batch_results['success_rate'] = batch_results['completed_reports'] / batch_results['total_reports']
            
            logger.info(f"Batch report generation completed: {batch_results['completed_reports']}/{batch_results['total_reports']} successful")
            return batch_results
            
        except Exception as e:
            logger.error(f"Error in batch report generation: {e}")
            return {'error': str(e)}
    
    def export_report(self,
                     report_id: str,
                     export_path: str,
                     include_attachments: bool = True) -> Dict[str, Any]:
        """
        Export report to file system.
        
        Args:
            report_id: Report identifier
            export_path: Export file path
            include_attachments: Whether to include chart attachments
            
        Returns:
            Dictionary containing export results
        """
        try:
            if report_id not in self.generated_reports:
                return {'error': f'Report not found: {report_id}'}
            
            report_package = self.generated_reports[report_id]
            report_data = report_package['data']
            
            # Ensure export directory exists
            export_dir = Path(export_path).parent
            export_dir.mkdir(parents=True, exist_ok=True)
            
            # Export main report
            if isinstance(report_data, str):
                with open(export_path, 'w', encoding='utf-8') as f:
                    f.write(report_data)
            else:
                with open(export_path, 'w', encoding='utf-8') as f:
                    json.dump(report_data, f, indent=2, default=str)
            
            export_result = {
                'export_id': f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'report_id': report_id,
                'export_path': export_path,
                'exported_at': datetime.now().isoformat(),
                'file_size': os.path.getsize(export_path),
                'attachments': []
            }
            
            # Export attachments if requested
            if include_attachments and 'charts' in report_package.get('raw_data', {}):
                attachments_dir = export_dir / f"{Path(export_path).stem}_attachments"
                attachments_dir.mkdir(exist_ok=True)
                
                charts = report_package['raw_data']['charts']
                for chart_name, chart_data in charts.items():
                    attachment_path = attachments_dir / f"{chart_name}.json"
                    with open(attachment_path, 'w') as f:
                        json.dump(chart_data, f, indent=2, default=str)
                    
                    export_result['attachments'].append({
                        'name': chart_name,
                        'path': str(attachment_path),
                        'size': os.path.getsize(attachment_path)
                    })
            
            # Record export history
            self.export_history.append(export_result)
            
            logger.info(f"Report exported: {export_path}")
            return export_result
            
        except Exception as e:
            logger.error(f"Error exporting report: {e}")
            return {'error': str(e)}
    
    def get_report_templates(self) -> Dict[str, Dict[str, Any]]:
        """
        Get available report templates.
        
        Returns:
            Dictionary of available templates
        """
        return {
            name: {
                'name': name,
                'description': template.get('description', ''),
                'sections': list(template.get('sections', {}).keys()),
                'supports_formats': template.get('supports_formats', ['html', 'pdf', 'json'])
            }
            for name, template in self.templates.items()
        }
    
    def get_generated_reports(self) -> List[Dict[str, Any]]:
        """
        Get list of generated reports.
        
        Returns:
            List of report metadata
        """
        return [report['metadata'] for report in self.generated_reports.values()]
    
    def get_export_history(self) -> List[Dict[str, Any]]:
        """
        Get export history.
        
        Returns:
            List of export records
        """
        return self.export_history.copy()
    
    def delete_report(self, report_id: str) -> bool:
        """
        Delete generated report.
        
        Args:
            report_id: Report identifier
            
        Returns:
            True if deleted successfully
        """
        try:
            if report_id in self.generated_reports:
                del self.generated_reports[report_id]
                logger.info(f"Deleted report: {report_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error deleting report: {e}")
            return False
    
    def create_custom_template(self,
                             template_name: str,
                             template_config: Dict[str, Any]) -> bool:
        """
        Create custom report template.
        
        Args:
            template_name: Name for the template
            template_config: Template configuration
            
        Returns:
            True if created successfully
        """
        try:
            self.templates[template_name] = template_config
            logger.info(f"Created custom template: {template_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating custom template: {e}")
            return False
    
    def _apply_custom_config(self, custom_config: Dict[str, Any]) -> None:
        """Apply custom configuration to reporter."""
        for key, value in custom_config.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        
        # Update reporter config
        self.reporter.config = self.config
    
    def _apply_template(self,
                       report_data: Dict[str, Any],
                       template: str,
                       format: ReportFormat) -> Union[str, Dict[str, Any]]:
        """Apply template formatting to report data."""
        if template not in self.templates:
            logger.warning(f"Template not found: {template}, using default")
            template = 'detailed_analysis'
        
        template_config = self.templates[template]
        
        if format == ReportFormat.HTML:
            return self._format_html_template(report_data, template_config)
        elif format == ReportFormat.JSON:
            return self._format_json_template(report_data, template_config)
        else:
            return report_data
    
    def _format_html_template(self,
                            report_data: Dict[str, Any],
                            template_config: Dict[str, Any]) -> str:
        """Format report as HTML using template."""
        sections = template_config.get('sections', {})
        
        html_parts = [
            "<!DOCTYPE html>",
            "<html>",
            "<head>",
            f"<title>{report_data.get('metadata', {}).get('title', 'Performance Report')}</title>",
            self._get_html_styles(),
            "</head>",
            "<body>"
        ]
        
        # Header
        if 'header' in sections:
            html_parts.append(self._generate_html_header(report_data))
        
        # Executive Summary
        if 'summary' in sections and 'summary' in report_data:
            html_parts.append(self._generate_html_summary(report_data['summary']))
        
        # Performance Metrics
        if 'performance' in sections and 'performance_metrics' in report_data:
            html_parts.append(self._generate_html_performance(report_data['performance_metrics']))
        
        # Charts
        if 'charts' in sections and 'charts' in report_data:
            html_parts.append(self._generate_html_charts(report_data['charts']))
        
        # Risk Analysis
        if 'risk' in sections and 'risk_metrics' in report_data:
            html_parts.append(self._generate_html_risk(report_data['risk_metrics']))
        
        # Trade Analysis
        if 'trades' in sections and 'trade_analysis' in report_data:
            html_parts.append(self._generate_html_trades(report_data['trade_analysis']))
        
        # Footer
        html_parts.extend([
            "</body>",
            "</html>"
        ])
        
        return "\n".join(html_parts)
    
    def _format_json_template(self,
                            report_data: Dict[str, Any],
                            template_config: Dict[str, Any]) -> Dict[str, Any]:
        """Format report as JSON using template."""
        sections = template_config.get('sections', {})
        
        formatted_data = {
            'template': template_config.get('name', 'unknown'),
            'generated_at': datetime.now().isoformat()
        }
        
        # Include only sections specified in template
        for section_name in sections:
            if section_name in report_data:
                formatted_data[section_name] = report_data[section_name]
        
        return formatted_data
    
    def _generate_comparison_report_data(self, comparison_result: ComparisonResult) -> Dict[str, Any]:
        """Generate comparison report data."""
        return {
            'metadata': {
                'title': 'Strategy Comparison Report',
                'generated_at': datetime.now().isoformat(),
                'strategies_count': len(comparison_result.strategy_metrics)
            },
            'summary': {
                'strategies_compared': len(comparison_result.strategy_metrics),
                'top_strategy': comparison_result.rankings.get('overall', ['Unknown'])[0] if comparison_result.rankings else 'Unknown',
                'comparison_methods': list(comparison_result.rankings.keys()) if comparison_result.rankings else []
            },
            'strategy_metrics': {
                name: metrics.to_dict()
                for name, metrics in comparison_result.strategy_metrics.items()
            },
            'rankings': comparison_result.rankings,
            'correlation_analysis': comparison_result.correlation_matrix.to_dict() if not comparison_result.correlation_matrix.empty else {},
            'portfolio_optimization': {
                'optimal_weights': comparison_result.optimal_weights,
                'combined_metrics': comparison_result.combined_metrics.to_dict() if comparison_result.combined_metrics else None
            },
            'recommendations': comparison_result.recommendations,
            'significance_tests': comparison_result.significance_tests
        }
    
    def _get_html_styles(self) -> str:
        """Get HTML styles for reports."""
        return """
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
            .section { margin-bottom: 30px; }
            .section h2 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
            .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
            .metric-card { border: 1px solid #ddd; padding: 15px; border-radius: 5px; background: #f9f9f9; }
            .metric-name { font-weight: bold; color: #555; }
            .metric-value { font-size: 1.2em; color: #333; margin-top: 5px; }
            .positive { color: #28a745; }
            .negative { color: #dc3545; }
            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .chart-placeholder { background: #f0f0f0; padding: 20px; text-align: center; margin: 10px 0; border-radius: 5px; }
        </style>
        """
    
    def _generate_html_header(self, report_data: Dict[str, Any]) -> str:
        """Generate HTML header section."""
        metadata = report_data.get('metadata', {})
        return f"""
        <div class="header">
            <h1>{metadata.get('title', 'Performance Report')}</h1>
            <p>Generated on {metadata.get('generated_at', 'Unknown')}</p>
            {f"<p>{metadata.get('subtitle', '')}</p>" if metadata.get('subtitle') else ""}
        </div>
        """
    
    def _generate_html_summary(self, summary_data: Dict[str, Any]) -> str:
        """Generate HTML summary section."""
        period = summary_data.get('period', {})
        key_metrics = summary_data.get('key_metrics', {})
        
        html = """
        <div class="section">
            <h2>Executive Summary</h2>
            <div class="metric-grid">
        """
        
        for metric_name, value in key_metrics.items():
            formatted_name = metric_name.replace('_', ' ').title()
            html += f"""
                <div class="metric-card">
                    <div class="metric-name">{formatted_name}</div>
                    <div class="metric-value">{value}</div>
                </div>
            """
        
        html += """
            </div>
        </div>
        """
        
        return html
    
    def _generate_html_performance(self, performance_data: Dict[str, Any]) -> str:
        """Generate HTML performance section."""
        html = """
        <div class="section">
            <h2>Performance Metrics</h2>
        """
        
        for category, metrics in performance_data.items():
            if isinstance(metrics, dict):
                html += f"<h3>{category.replace('_', ' ').title()}</h3>"
                html += '<div class="metric-grid">'
                
                for metric_name, metric_info in metrics.items():
                    if isinstance(metric_info, dict) and 'formatted' in metric_info:
                        html += f"""
                            <div class="metric-card">
                                <div class="metric-name">{metric_name.replace('_', ' ').title()}</div>
                                <div class="metric-value">{metric_info['formatted']}</div>
                            </div>
                        """
                
                html += '</div>'
        
        html += "</div>"
        return html
    
    def _generate_html_charts(self, charts_data: Dict[str, Any]) -> str:
        """Generate HTML charts section."""
        html = """
        <div class="section">
            <h2>Performance Charts</h2>
        """
        
        for chart_name, chart_data in charts_data.items():
            title = chart_data.get('title', chart_name.replace('_', ' ').title())
            html += f"""
                <div class="chart-placeholder">
                    <h3>{title}</h3>
                    <p>Chart data available in JSON format</p>
                    <p>Type: {chart_data.get('type', 'Unknown')}</p>
                </div>
            """
        
        html += "</div>"
        return html
    
    def _generate_html_risk(self, risk_data: Dict[str, Any]) -> str:
        """Generate HTML risk section."""
        return f"""
        <div class="section">
            <h2>Risk Analysis</h2>
            <p>Risk metrics analysis would be displayed here.</p>
            <pre>{json.dumps(risk_data, indent=2, default=str)}</pre>
        </div>
        """
    
    def _generate_html_trades(self, trades_data: Dict[str, Any]) -> str:
        """Generate HTML trades section."""
        return f"""
        <div class="section">
            <h2>Trade Analysis</h2>
            <p>Trade analysis would be displayed here.</p>
            <pre>{json.dumps(trades_data, indent=2, default=str)}</pre>
        </div>
        """
    
    def _get_executive_template(self) -> Dict[str, Any]:
        """Get executive summary template configuration."""
        return {
            'name': 'executive_summary',
            'description': 'High-level executive summary report',
            'sections': {
                'header': True,
                'summary': True,
                'key_metrics': True
            },
            'supports_formats': ['html', 'pdf', 'json']
        }
    
    def _get_detailed_template(self) -> Dict[str, Any]:
        """Get detailed analysis template configuration."""
        return {
            'name': 'detailed_analysis',
            'description': 'Comprehensive detailed analysis report',
            'sections': {
                'header': True,
                'summary': True,
                'performance': True,
                'risk': True,
                'charts': True,
                'trades': True
            },
            'supports_formats': ['html', 'pdf', 'json']
        }
    
    def _get_comparison_template(self) -> Dict[str, Any]:
        """Get comparison template configuration."""
        return {
            'name': 'comparison_report',
            'description': 'Multi-strategy comparison report',
            'sections': {
                'header': True,
                'summary': True,
                'comparison_table': True,
                'rankings': True,
                'correlations': True,
                'optimization': True,
                'recommendations': True
            },
            'supports_formats': ['html', 'pdf', 'json']
        }
    
    def _get_risk_template(self) -> Dict[str, Any]:
        """Get risk analysis template configuration."""
        return {
            'name': 'risk_analysis',
            'description': 'Focused risk analysis report',
            'sections': {
                'header': True,
                'risk_metrics': True,
                'drawdown_analysis': True,
                'var_analysis': True,
                'stress_testing': True
            },
            'supports_formats': ['html', 'pdf', 'json']
        }
    
    def _get_custom_template(self) -> Dict[str, Any]:
        """Get custom template configuration."""
        return {
            'name': 'custom',
            'description': 'Customizable report template',
            'sections': {},
            'supports_formats': ['html', 'pdf', 'json']
        }
    
    def clear_reports(self, older_than_days: Optional[int] = None) -> int:
        """
        Clear generated reports.
        
        Args:
            older_than_days: Clear reports older than specified days
            
        Returns:
            Number of reports cleared
        """
        try:
            if older_than_days is None:
                count = len(self.generated_reports)
                self.generated_reports.clear()
                self.export_history.clear()
                logger.info(f"Cleared all {count} reports")
                return count
            
            cutoff_date = datetime.now() - timedelta(days=older_than_days)
            reports_to_remove = []
            
            for report_id, report_package in self.generated_reports.items():
                generated_at = datetime.fromisoformat(report_package['metadata']['generated_at'])
                if generated_at < cutoff_date:
                    reports_to_remove.append(report_id)
            
            for report_id in reports_to_remove:
                del self.generated_reports[report_id]
            
            # Clear old export history
            self.export_history = [
                export for export in self.export_history
                if datetime.fromisoformat(export['exported_at']) >= cutoff_date
            ]
            
            logger.info(f"Cleared {len(reports_to_remove)} reports older than {older_than_days} days")
            return len(reports_to_remove)
            
        except Exception as e:
            logger.error(f"Error clearing reports: {e}")
            return 0