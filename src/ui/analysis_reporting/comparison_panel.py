"""
Strategy comparison and ranking interface panel.

This module provides comprehensive strategy comparison functionality including
performance comparison tables, ranking systems, correlation analysis, and
portfolio optimization recommendations.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime
import logging

from ...analysis.comparison import ComparisonR<PERSON>ult, ComparisonMethod, RankingMethod
from ...analysis.metrics import PerformanceMetrics

logger = logging.getLogger(__name__)


class StrategyComparisonPanel:
    """
    Panel component for strategy comparison and ranking.
    
    This panel provides comprehensive comparison functionality including:
    - Performance comparison tables
    - Strategy rankings
    - Correlation analysis
    - Portfolio optimization recommendations
    - Statistical significance testing
    """
    
    def __init__(self):
        """Initialize strategy comparison panel."""
        self.current_comparison: Optional[ComparisonResult] = None
        self.comparison_history: List[ComparisonResult] = []
        
        # Display configuration
        self.display_config = {
            'decimal_places': 3,
            'percentage_format': '.2%',
            'show_rankings': True,
            'show_correlations': True,
            'show_recommendations': True,
            'max_strategies_display': 10
        }
        
        logger.info("StrategyComparisonPanel initialized")
    
    def update_comparison(self, comparison_result: ComparisonResult) -> None:
        """
        Update comparison result.
        
        Args:
            comparison_result: ComparisonResult object
        """
        self.current_comparison = comparison_result
        self.comparison_history.append(comparison_result)
        
        # Keep only last 10 comparisons
        if len(self.comparison_history) > 10:
            self.comparison_history = self.comparison_history[-10:]
        
        logger.info(f"Updated comparison with {len(comparison_result.strategy_metrics)} strategies")
    
    def get_comparison_table(self) -> Optional[Dict[str, Any]]:
        """
        Get formatted comparison table.
        
        Returns:
            Dictionary containing comparison table data
        """
        if not self.current_comparison:
            return None
        
        try:
            strategies = list(self.current_comparison.strategy_metrics.keys())
            
            # Define metrics to display
            metrics_config = [
                ('Total Return', 'total_return', '.2%', 'higher_better'),
                ('Annualized Return', 'annualized_return', '.2%', 'higher_better'),
                ('Sharpe Ratio', 'sharpe_ratio', '.3f', 'higher_better'),
                ('Sortino Ratio', 'sortino_ratio', '.3f', 'higher_better'),
                ('Calmar Ratio', 'calmar_ratio', '.3f', 'higher_better'),
                ('Max Drawdown', 'max_drawdown', '.2%', 'lower_better'),
                ('Volatility', 'volatility', '.2%', 'lower_better'),
                ('Win Rate', 'win_rate', '.1%', 'higher_better'),
                ('Profit Factor', 'profit_factor', '.2f', 'higher_better'),
                ('Total Trades', 'total_trades', 'd', 'neutral')
            ]
            
            table_data = {
                'headers': ['Metric'] + strategies + ['Best Strategy'],
                'rows': [],
                'metadata': {
                    'strategies_count': len(strategies),
                    'comparison_date': datetime.now().isoformat(),
                    'metrics_count': len(metrics_config)
                }
            }
            
            for metric_name, metric_attr, format_str, direction in metrics_config:
                row = [metric_name]
                values = []
                
                # Get values for all strategies
                for strategy in strategies:
                    metrics = self.current_comparison.strategy_metrics[strategy]
                    value = getattr(metrics, metric_attr)
                    values.append((strategy, value))
                    
                    # Format value
                    if format_str == 'd':
                        formatted_value = f"{value:d}"
                    elif format_str.endswith('%'):
                        formatted_value = f"{value:{format_str}}"
                    else:
                        formatted_value = f"{value:{format_str}}"
                    
                    row.append(formatted_value)
                
                # Find best strategy for this metric
                if direction == 'higher_better':
                    best_strategy, best_value = max(values, key=lambda x: x[1])
                elif direction == 'lower_better':
                    best_strategy, best_value = min(values, key=lambda x: x[1])
                else:
                    best_strategy = 'N/A'
                
                row.append(best_strategy)
                table_data['rows'].append(row)
            
            return table_data
            
        except Exception as e:
            logger.error(f"Error generating comparison table: {e}")
            return {'error': str(e)}
    
    def get_ranking_data(self) -> Optional[Dict[str, Any]]:
        """
        Get strategy ranking data.
        
        Returns:
            Dictionary containing ranking information
        """
        if not self.current_comparison or not self.current_comparison.rankings:
            return None
        
        try:
            ranking_data = {
                'overall_ranking': [],
                'metric_rankings': {},
                'ranking_summary': {}
            }
            
            # Overall ranking
            if 'overall' in self.current_comparison.rankings:
                overall_ranks = self.current_comparison.rankings['overall']
                for i, strategy in enumerate(overall_ranks, 1):
                    metrics = self.current_comparison.strategy_metrics[strategy]
                    ranking_data['overall_ranking'].append({
                        'rank': i,
                        'strategy': strategy,
                        'total_return': f"{metrics.total_return:.2%}",
                        'sharpe_ratio': f"{metrics.sharpe_ratio:.3f}",
                        'max_drawdown': f"{metrics.max_drawdown:.2%}",
                        'score': self._calculate_display_score(metrics)
                    })
            
            # Individual metric rankings
            for metric, ranked_strategies in self.current_comparison.rankings.items():
                if metric != 'overall':
                    ranking_data['metric_rankings'][metric] = [
                        {'rank': i + 1, 'strategy': strategy}
                        for i, strategy in enumerate(ranked_strategies)
                    ]
            
            # Ranking summary
            ranking_data['ranking_summary'] = self._generate_ranking_summary()
            
            return ranking_data
            
        except Exception as e:
            logger.error(f"Error generating ranking data: {e}")
            return {'error': str(e)}
    
    def get_correlation_analysis(self) -> Optional[Dict[str, Any]]:
        """
        Get correlation analysis data.
        
        Returns:
            Dictionary containing correlation analysis
        """
        if not self.current_comparison or self.current_comparison.correlation_matrix.empty:
            return None
        
        try:
            corr_matrix = self.current_comparison.correlation_matrix
            
            # Convert to list format for visualization
            correlation_data = []
            strategies = corr_matrix.index.tolist()
            
            for i, strategy1 in enumerate(strategies):
                for j, strategy2 in enumerate(strategies):
                    correlation_data.append({
                        'strategy1': strategy1,
                        'strategy2': strategy2,
                        'correlation': round(corr_matrix.iloc[i, j], 3),
                        'x': j,
                        'y': i
                    })
            
            # Find highest and lowest correlations (excluding self-correlation)
            off_diagonal = corr_matrix.values.copy()
            np.fill_diagonal(off_diagonal, np.nan)
            
            max_corr_idx = np.unravel_index(np.nanargmax(off_diagonal), off_diagonal.shape)
            min_corr_idx = np.unravel_index(np.nanargmin(off_diagonal), off_diagonal.shape)
            
            analysis_data = {
                'correlation_matrix': correlation_data,
                'strategies': strategies,
                'statistics': {
                    'avg_correlation': round(np.nanmean(off_diagonal), 3),
                    'max_correlation': {
                        'value': round(off_diagonal[max_corr_idx], 3),
                        'strategies': [strategies[max_corr_idx[0]], strategies[max_corr_idx[1]]]
                    },
                    'min_correlation': {
                        'value': round(off_diagonal[min_corr_idx], 3),
                        'strategies': [strategies[min_corr_idx[0]], strategies[min_corr_idx[1]]]
                    },
                    'diversification_score': self._calculate_diversification_score(corr_matrix)
                },
                'interpretation': self._interpret_correlations(corr_matrix)
            }
            
            return analysis_data
            
        except Exception as e:
            logger.error(f"Error generating correlation analysis: {e}")
            return {'error': str(e)}
    
    def get_portfolio_optimization(self) -> Optional[Dict[str, Any]]:
        """
        Get portfolio optimization recommendations.
        
        Returns:
            Dictionary containing optimization data
        """
        if not self.current_comparison or not self.current_comparison.optimal_weights:
            return None
        
        try:
            optimization_data = {
                'optimal_weights': [],
                'combined_metrics': None,
                'weight_analysis': {},
                'recommendations': []
            }
            
            # Format optimal weights
            total_weight = sum(self.current_comparison.optimal_weights.values())
            for strategy, weight in self.current_comparison.optimal_weights.items():
                normalized_weight = weight / total_weight if total_weight > 0 else 0
                optimization_data['optimal_weights'].append({
                    'strategy': strategy,
                    'weight': round(normalized_weight, 3),
                    'weight_pct': f"{normalized_weight:.1%}"
                })
            
            # Combined portfolio metrics
            if self.current_comparison.combined_metrics:
                combined = self.current_comparison.combined_metrics
                optimization_data['combined_metrics'] = {
                    'total_return': f"{combined.total_return:.2%}",
                    'sharpe_ratio': f"{combined.sharpe_ratio:.3f}",
                    'max_drawdown': f"{combined.max_drawdown:.2%}",
                    'volatility': f"{combined.volatility:.2%}"
                }
            
            # Weight analysis
            weights = list(self.current_comparison.optimal_weights.values())
            optimization_data['weight_analysis'] = {
                'concentration': self._calculate_concentration_ratio(weights),
                'diversification_ratio': self._calculate_diversification_ratio(weights),
                'dominant_strategy': max(self.current_comparison.optimal_weights.items(), key=lambda x: x[1])[0],
                'weight_distribution': self._analyze_weight_distribution(weights)
            }
            
            # Generate recommendations
            optimization_data['recommendations'] = self._generate_optimization_recommendations()
            
            return optimization_data
            
        except Exception as e:
            logger.error(f"Error generating portfolio optimization: {e}")
            return {'error': str(e)}
    
    def get_significance_tests(self) -> Optional[Dict[str, Any]]:
        """
        Get statistical significance test results.
        
        Returns:
            Dictionary containing significance test results
        """
        if not self.current_comparison or not self.current_comparison.significance_tests:
            return None
        
        try:
            significance_data = {
                'pairwise_tests': [],
                'summary': {
                    'total_comparisons': len(self.current_comparison.significance_tests),
                    'significant_differences': 0,
                    'confidence_level': 0.05
                }
            }
            
            for test_name, test_result in self.current_comparison.significance_tests.items():
                strategies = test_name.split('_vs_')
                
                significance_data['pairwise_tests'].append({
                    'strategy1': strategies[0],
                    'strategy2': strategies[1],
                    'mean_difference': round(test_result['mean_difference'], 4),
                    't_statistic': round(test_result['t_statistic'], 3),
                    'p_value': round(test_result['p_value'], 4),
                    'significant': test_result['significant'],
                    'interpretation': self._interpret_significance_test(test_result)
                })
                
                if test_result['significant']:
                    significance_data['summary']['significant_differences'] += 1
            
            return significance_data
            
        except Exception as e:
            logger.error(f"Error generating significance tests: {e}")
            return {'error': str(e)}
    
    def get_recommendations_panel(self) -> Optional[Dict[str, Any]]:
        """
        Get recommendations panel data.
        
        Returns:
            Dictionary containing recommendations
        """
        if not self.current_comparison or not self.current_comparison.recommendations:
            return None
        
        try:
            recommendations_data = {
                'recommendations': [],
                'summary': {
                    'total_recommendations': len(self.current_comparison.recommendations),
                    'categories': {}
                }
            }
            
            for rec in self.current_comparison.recommendations:
                formatted_rec = {
                    'type': rec['type'].replace('_', ' ').title(),
                    'description': rec.get('reason', 'No description available'),
                    'action': rec.get('action', 'No action specified'),
                    'priority': self._determine_recommendation_priority(rec),
                    'details': self._format_recommendation_details(rec)
                }
                
                recommendations_data['recommendations'].append(formatted_rec)
                
                # Count by category
                rec_type = rec['type']
                if rec_type not in recommendations_data['summary']['categories']:
                    recommendations_data['summary']['categories'][rec_type] = 0
                recommendations_data['summary']['categories'][rec_type] += 1
            
            return recommendations_data
            
        except Exception as e:
            logger.error(f"Error generating recommendations panel: {e}")
            return {'error': str(e)}
    
    def get_comparison_summary(self) -> Optional[Dict[str, Any]]:
        """
        Get comparison summary for dashboard display.
        
        Returns:
            Dictionary containing comparison summary
        """
        if not self.current_comparison:
            return None
        
        try:
            summary = {
                'strategies_compared': len(self.current_comparison.strategy_metrics),
                'top_strategy': None,
                'key_insights': [],
                'diversification_benefit': None,
                'risk_analysis': {}
            }
            
            # Top strategy
            if 'overall' in self.current_comparison.rankings:
                summary['top_strategy'] = {
                    'name': self.current_comparison.rankings['overall'][0],
                    'metrics': self._get_strategy_key_metrics(self.current_comparison.rankings['overall'][0])
                }
            
            # Key insights
            summary['key_insights'] = self._generate_key_insights()
            
            # Diversification benefit
            if not self.current_comparison.correlation_matrix.empty:
                avg_corr = self._calculate_average_correlation()
                summary['diversification_benefit'] = {
                    'score': self._calculate_diversification_score(self.current_comparison.correlation_matrix),
                    'avg_correlation': round(avg_corr, 3),
                    'benefit_level': 'High' if avg_corr < 0.5 else 'Medium' if avg_corr < 0.7 else 'Low'
                }
            
            # Risk analysis
            summary['risk_analysis'] = self._analyze_portfolio_risk()
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating comparison summary: {e}")
            return {'error': str(e)}
    
    def _calculate_display_score(self, metrics: PerformanceMetrics) -> float:
        """Calculate a display score for ranking."""
        # Simple weighted score
        score = (
            metrics.sharpe_ratio * 0.4 +
            metrics.total_return * 0.3 +
            (1 + metrics.max_drawdown) * 0.2 +  # Invert drawdown
            metrics.win_rate * 0.1
        )
        return round(score, 3)
    
    def _generate_ranking_summary(self) -> Dict[str, Any]:
        """Generate ranking summary statistics."""
        if not self.current_comparison.rankings:
            return {}
        
        summary = {
            'ranking_methods': list(self.current_comparison.rankings.keys()),
            'consistency_analysis': {},
            'top_performers': {}
        }
        
        # Analyze ranking consistency
        if len(self.current_comparison.rankings) > 1:
            strategies = list(self.current_comparison.strategy_metrics.keys())
            consistency_scores = {}
            
            for strategy in strategies:
                ranks = []
                for ranking in self.current_comparison.rankings.values():
                    if strategy in ranking:
                        ranks.append(ranking.index(strategy) + 1)
                
                if ranks:
                    consistency_scores[strategy] = {
                        'avg_rank': round(np.mean(ranks), 1),
                        'rank_std': round(np.std(ranks), 1),
                        'best_rank': min(ranks),
                        'worst_rank': max(ranks)
                    }
            
            summary['consistency_analysis'] = consistency_scores
        
        return summary
    
    def _calculate_diversification_score(self, corr_matrix: pd.DataFrame) -> float:
        """Calculate diversification score based on correlation matrix."""
        # Remove diagonal elements
        off_diagonal = corr_matrix.values.copy()
        np.fill_diagonal(off_diagonal, np.nan)
        
        # Lower average correlation = higher diversification
        avg_corr = np.nanmean(np.abs(off_diagonal))
        diversification_score = max(0, 1 - avg_corr)
        
        return round(diversification_score, 3)
    
    def _interpret_correlations(self, corr_matrix: pd.DataFrame) -> List[str]:
        """Interpret correlation matrix."""
        interpretations = []
        
        # Remove diagonal elements
        off_diagonal = corr_matrix.values.copy()
        np.fill_diagonal(off_diagonal, np.nan)
        
        avg_corr = np.nanmean(np.abs(off_diagonal))
        
        if avg_corr < 0.3:
            interpretations.append("Low correlation suggests good diversification potential")
        elif avg_corr < 0.7:
            interpretations.append("Moderate correlation indicates some diversification benefits")
        else:
            interpretations.append("High correlation suggests limited diversification benefits")
        
        # Check for negative correlations
        negative_corrs = np.sum(off_diagonal < -0.1)
        if negative_corrs > 0:
            interpretations.append(f"Found {negative_corrs} negative correlations - excellent for hedging")
        
        return interpretations
    
    def _calculate_concentration_ratio(self, weights: List[float]) -> float:
        """Calculate concentration ratio (Herfindahl index)."""
        total_weight = sum(weights)
        if total_weight == 0:
            return 0
        
        normalized_weights = [w / total_weight for w in weights]
        concentration = sum(w ** 2 for w in normalized_weights)
        
        return round(concentration, 3)
    
    def _calculate_diversification_ratio(self, weights: List[float]) -> float:
        """Calculate diversification ratio."""
        n = len(weights)
        if n <= 1:
            return 0
        
        concentration = self._calculate_concentration_ratio(weights)
        diversification = (1 - concentration) / (1 - 1/n)
        
        return round(diversification, 3)
    
    def _analyze_weight_distribution(self, weights: List[float]) -> Dict[str, Any]:
        """Analyze weight distribution."""
        if not weights:
            return {}
        
        total_weight = sum(weights)
        normalized_weights = [w / total_weight for w in weights] if total_weight > 0 else weights
        
        return {
            'max_weight': round(max(normalized_weights), 3),
            'min_weight': round(min(normalized_weights), 3),
            'weight_std': round(np.std(normalized_weights), 3),
            'equal_weight_deviation': round(np.std(normalized_weights) / (1/len(weights)), 3)
        }
    
    def _generate_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Generate portfolio optimization recommendations."""
        recommendations = []
        
        if not self.current_comparison.optimal_weights:
            return recommendations
        
        weights = list(self.current_comparison.optimal_weights.values())
        concentration = self._calculate_concentration_ratio(weights)
        
        if concentration > 0.5:
            recommendations.append({
                'type': 'concentration_warning',
                'message': 'Portfolio is highly concentrated in few strategies',
                'action': 'Consider more balanced allocation'
            })
        
        # Check for very small weights
        small_weights = [w for w in weights if w < 0.05]
        if small_weights:
            recommendations.append({
                'type': 'small_weights',
                'message': f'{len(small_weights)} strategies have very small allocations',
                'action': 'Consider removing strategies with minimal impact'
            })
        
        return recommendations
    
    def _interpret_significance_test(self, test_result: Dict[str, Any]) -> str:
        """Interpret significance test result."""
        if test_result['significant']:
            if test_result['mean_difference'] > 0:
                return "First strategy significantly outperforms second"
            else:
                return "Second strategy significantly outperforms first"
        else:
            return "No statistically significant difference in performance"
    
    def _determine_recommendation_priority(self, recommendation: Dict[str, Any]) -> str:
        """Determine recommendation priority."""
        rec_type = recommendation['type']
        
        if rec_type in ['high_risk_warning', 'concentration_warning']:
            return 'High'
        elif rec_type in ['diversification_benefit', 'consistent_performance']:
            return 'Medium'
        else:
            return 'Low'
    
    def _format_recommendation_details(self, recommendation: Dict[str, Any]) -> Dict[str, Any]:
        """Format recommendation details."""
        details = {}
        
        if 'value' in recommendation:
            details['value'] = recommendation['value']
        
        if 'strategies' in recommendation:
            details['strategies'] = recommendation['strategies']
        
        if 'strategy' in recommendation:
            details['strategy'] = recommendation['strategy']
        
        return details
    
    def _get_strategy_key_metrics(self, strategy_name: str) -> Dict[str, Any]:
        """Get key metrics for a strategy."""
        if strategy_name not in self.current_comparison.strategy_metrics:
            return {}
        
        metrics = self.current_comparison.strategy_metrics[strategy_name]
        
        return {
            'total_return': f"{metrics.total_return:.2%}",
            'sharpe_ratio': f"{metrics.sharpe_ratio:.3f}",
            'max_drawdown': f"{metrics.max_drawdown:.2%}",
            'win_rate': f"{metrics.win_rate:.1%}" if metrics.total_trades > 0 else "N/A"
        }
    
    def _generate_key_insights(self) -> List[str]:
        """Generate key insights from comparison."""
        insights = []
        
        if not self.current_comparison.strategy_metrics:
            return insights
        
        # Performance spread analysis
        returns = [m.total_return for m in self.current_comparison.strategy_metrics.values()]
        return_spread = max(returns) - min(returns)
        
        if return_spread > 0.2:  # 20% spread
            insights.append(f"Large performance spread of {return_spread:.1%} between strategies")
        
        # Risk analysis
        drawdowns = [abs(m.max_drawdown) for m in self.current_comparison.strategy_metrics.values()]
        avg_drawdown = np.mean(drawdowns)
        
        if avg_drawdown > 0.15:  # 15% average drawdown
            insights.append("Strategies show elevated risk levels")
        
        # Sharpe ratio analysis
        sharpe_ratios = [m.sharpe_ratio for m in self.current_comparison.strategy_metrics.values()]
        good_sharpe_count = sum(1 for s in sharpe_ratios if s > 1.0)
        
        if good_sharpe_count > 0:
            insights.append(f"{good_sharpe_count} strategies show good risk-adjusted returns")
        
        return insights
    
    def _calculate_average_correlation(self) -> float:
        """Calculate average correlation between strategies."""
        if self.current_comparison.correlation_matrix.empty:
            return 0
        
        corr_matrix = self.current_comparison.correlation_matrix
        off_diagonal = corr_matrix.values.copy()
        np.fill_diagonal(off_diagonal, np.nan)
        
        return np.nanmean(np.abs(off_diagonal))
    
    def _analyze_portfolio_risk(self) -> Dict[str, Any]:
        """Analyze portfolio risk characteristics."""
        if not self.current_comparison.strategy_metrics:
            return {}
        
        volatilities = [m.volatility for m in self.current_comparison.strategy_metrics.values()]
        drawdowns = [abs(m.max_drawdown) for m in self.current_comparison.strategy_metrics.values()]
        
        return {
            'avg_volatility': round(np.mean(volatilities), 3),
            'max_volatility': round(max(volatilities), 3),
            'avg_drawdown': round(np.mean(drawdowns), 3),
            'max_drawdown': round(max(drawdowns), 3),
            'risk_level': 'High' if np.mean(volatilities) > 0.25 else 'Medium' if np.mean(volatilities) > 0.15 else 'Low'
        }
    
    def clear_comparison_data(self) -> None:
        """Clear comparison data."""
        self.current_comparison = None
        logger.info("Comparison data cleared")
    
    def get_comparison_history(self) -> List[Dict[str, Any]]:
        """
        Get comparison history summary.
        
        Returns:
            List of comparison summaries
        """
        history = []
        
        for i, comparison in enumerate(self.comparison_history):
            summary = {
                'index': i,
                'timestamp': datetime.now().isoformat(),  # In real implementation, store actual timestamp
                'strategies_count': len(comparison.strategy_metrics),
                'top_strategy': comparison.rankings.get('overall', ['Unknown'])[0] if comparison.rankings else 'Unknown'
            }
            history.append(summary)
        
        return history