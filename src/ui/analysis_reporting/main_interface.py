"""
Main analysis and reporting interface.

This module provides the main interface for analysis and reporting functionality,
integrating performance metrics display, chart visualization, strategy comparison,
and report generation capabilities.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
import logging
import json

from ...analysis.metrics import PerformanceMetrics, MetricsCalculator
from ...analysis.reports import PerformanceReporter, ReportConfig, ReportFormat
from ...analysis.comparison import StrategyComparator, StrategyData, ComparisonResult
from ...backtest.result import BacktestResultRepository
from ...models.trading import Trade
from ..charts.chart_config import ChartConfig

from .performance_panel import PerformanceMetricsPanel
from .chart_panel import EquityDrawdownChartPanel
from .comparison_panel import StrategyComparisonPanel
from .report_generator import ReportGeneratorPanel

logger = logging.getLogger(__name__)


class AnalysisReportingInterface:
    """
    Main interface for analysis and reporting functionality.
    
    This class coordinates all analysis and reporting components including:
    - Performance metrics display
    - Chart visualization
    - Strategy comparison
    - Report generation and export
    """
    
    def __init__(self, 
                 chart_config: Optional[ChartConfig] = None,
                 report_config: Optional[ReportConfig] = None):
        """
        Initialize analysis and reporting interface.
        
        Args:
            chart_config: Configuration for chart components
            report_config: Configuration for report generation
        """
        self.chart_config = chart_config or ChartConfig()
        self.report_config = report_config or ReportConfig()
        
        # Initialize components
        self.performance_panel = PerformanceMetricsPanel()
        self.chart_panel = EquityDrawdownChartPanel(self.chart_config)
        self.comparison_panel = StrategyComparisonPanel()
        self.report_generator = ReportGeneratorPanel(self.report_config)
        
        # Initialize analysis engines
        self.metrics_calculator = MetricsCalculator()
        self.performance_reporter = PerformanceReporter(self.report_config)
        self.strategy_comparator = StrategyComparator()
        
        # Data repositories
        self.backtest_repository = BacktestResultRepository()
        
        # Current data
        self.current_strategies: Dict[str, StrategyData] = {}
        self.current_comparison: Optional[ComparisonResult] = None
        self.current_reports: Dict[str, Any] = {}
        
        logger.info("AnalysisReportingInterface initialized")
    
    def load_backtest_results(self, 
                            backtest_ids: List[str],
                            user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Load backtest results for analysis.
        
        Args:
            backtest_ids: List of backtest result IDs
            user_id: Optional user ID for filtering
            
        Returns:
            Dictionary containing loaded results
        """
        try:
            loaded_results = {}
            
            for backtest_id in backtest_ids:
                # Get backtest result
                result = self.backtest_repository.get_backtest_result(backtest_id, user_id)
                if not result:
                    logger.warning(f"Backtest result not found: {backtest_id}")
                    continue
                
                # Get portfolio history
                portfolio_history = self.backtest_repository.get_portfolio_history(backtest_id)
                if not portfolio_history:
                    logger.warning(f"No portfolio history found for: {backtest_id}")
                    continue
                
                # Convert to pandas Series
                portfolio_df = pd.DataFrame(portfolio_history)
                portfolio_df['timestamp'] = pd.to_datetime(portfolio_df['timestamp'])
                portfolio_df.set_index('timestamp', inplace=True)
                portfolio_values = portfolio_df['total_value']
                
                # Get trades
                trades_data = self.backtest_repository.get_backtest_trades(backtest_id)
                trades = []
                for trade_data in trades_data:
                    trade = Trade(
                        id=trade_data['id'],
                        symbol=trade_data['symbol'],
                        side=trade_data['side'],
                        quantity=trade_data['quantity'],
                        price=trade_data['price'],
                        timestamp=pd.to_datetime(trade_data['timestamp']),
                        commission=trade_data.get('commission', 0),
                        strategy_id=backtest_id
                    )
                    # Add P&L if available
                    if 'pnl' in trade_data:
                        trade.pnl = trade_data['pnl']
                    trades.append(trade)
                
                # Create strategy data
                strategy_data = StrategyData(
                    name=result.get('strategy_name', f"Strategy_{backtest_id[:8]}"),
                    portfolio_values=portfolio_values,
                    trades=trades,
                    metadata={
                        'backtest_id': backtest_id,
                        'start_date': result.get('start_date'),
                        'end_date': result.get('end_date'),
                        'initial_capital': result.get('initial_capital'),
                        'parameters': result.get('parameters', {})
                    }
                )
                
                self.current_strategies[backtest_id] = strategy_data
                loaded_results[backtest_id] = {
                    'strategy_data': strategy_data,
                    'result_info': result
                }
            
            logger.info(f"Loaded {len(loaded_results)} backtest results")
            return loaded_results
            
        except Exception as e:
            logger.error(f"Error loading backtest results: {e}")
            raise
    
    def add_strategy_data(self, 
                         strategy_id: str,
                         portfolio_values: pd.Series,
                         trades: Optional[List[Trade]] = None,
                         benchmark_values: Optional[pd.Series] = None,
                         metadata: Optional[Dict] = None) -> None:
        """
        Add strategy data for analysis.
        
        Args:
            strategy_id: Unique strategy identifier
            portfolio_values: Portfolio value time series
            trades: Optional list of trades
            benchmark_values: Optional benchmark values
            metadata: Optional metadata dictionary
        """
        try:
            strategy_data = StrategyData(
                name=strategy_id,
                portfolio_values=portfolio_values,
                trades=trades,
                benchmark_values=benchmark_values,
                metadata=metadata or {}
            )
            
            self.current_strategies[strategy_id] = strategy_data
            logger.info(f"Added strategy data: {strategy_id}")
            
        except Exception as e:
            logger.error(f"Error adding strategy data: {e}")
            raise
    
    def calculate_performance_metrics(self, 
                                    strategy_id: str) -> Optional[PerformanceMetrics]:
        """
        Calculate performance metrics for a strategy.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            PerformanceMetrics object or None if not found
        """
        try:
            if strategy_id not in self.current_strategies:
                logger.warning(f"Strategy not found: {strategy_id}")
                return None
            
            strategy_data = self.current_strategies[strategy_id]
            
            metrics = self.metrics_calculator.calculate_metrics(
                strategy_data.portfolio_values,
                strategy_data.trades,
                strategy_data.benchmark_values
            )
            
            # Update performance panel
            self.performance_panel.update_metrics(strategy_id, metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return None
    
    def generate_performance_charts(self, 
                                  strategy_id: str) -> Optional[Dict[str, Any]]:
        """
        Generate performance charts for a strategy.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            Dictionary containing chart data
        """
        try:
            if strategy_id not in self.current_strategies:
                logger.warning(f"Strategy not found: {strategy_id}")
                return None
            
            strategy_data = self.current_strategies[strategy_id]
            
            # Generate equity curve and drawdown charts
            chart_data = self.chart_panel.generate_charts(
                strategy_data.portfolio_values,
                strategy_data.benchmark_values,
                strategy_data.trades
            )
            
            return chart_data
            
        except Exception as e:
            logger.error(f"Error generating performance charts: {e}")
            return None
    
    def compare_strategies(self, 
                          strategy_ids: Optional[List[str]] = None) -> Optional[ComparisonResult]:
        """
        Compare multiple strategies.
        
        Args:
            strategy_ids: List of strategy IDs to compare, or None for all
            
        Returns:
            ComparisonResult object
        """
        try:
            if strategy_ids is None:
                strategy_ids = list(self.current_strategies.keys())
            
            if len(strategy_ids) < 2:
                logger.warning("Need at least 2 strategies for comparison")
                return None
            
            # Get strategy data for comparison
            strategies_to_compare = []
            for strategy_id in strategy_ids:
                if strategy_id in self.current_strategies:
                    strategies_to_compare.append(self.current_strategies[strategy_id])
                else:
                    logger.warning(f"Strategy not found for comparison: {strategy_id}")
            
            if len(strategies_to_compare) < 2:
                logger.warning("Insufficient valid strategies for comparison")
                return None
            
            # Perform comparison
            comparison_result = self.strategy_comparator.compare_strategies(strategies_to_compare)
            
            # Update comparison panel
            self.comparison_panel.update_comparison(comparison_result)
            
            # Store current comparison
            self.current_comparison = comparison_result
            
            return comparison_result
            
        except Exception as e:
            logger.error(f"Error comparing strategies: {e}")
            return None
    
    def generate_performance_report(self, 
                                  strategy_id: str,
                                  format: ReportFormat = ReportFormat.DICT,
                                  include_charts: bool = True) -> Optional[Union[Dict, str]]:
        """
        Generate performance report for a strategy.
        
        Args:
            strategy_id: Strategy identifier
            format: Report format
            include_charts: Whether to include charts
            
        Returns:
            Report in specified format
        """
        try:
            if strategy_id not in self.current_strategies:
                logger.warning(f"Strategy not found: {strategy_id}")
                return None
            
            strategy_data = self.current_strategies[strategy_id]
            
            # Update report config
            self.report_config.include_charts = include_charts
            self.performance_reporter.config = self.report_config
            
            # Generate report
            report = self.performance_reporter.generate_report(
                strategy_data.portfolio_values,
                strategy_data.trades,
                strategy_data.benchmark_values,
                format
            )
            
            # Store report
            report_key = f"{strategy_id}_{format.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.current_reports[report_key] = report
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return None
    
    def generate_comparison_report(self, 
                                 strategy_ids: Optional[List[str]] = None,
                                 format: ReportFormat = ReportFormat.DICT) -> Optional[Union[Dict, str]]:
        """
        Generate comparison report for multiple strategies.
        
        Args:
            strategy_ids: List of strategy IDs to compare
            format: Report format
            
        Returns:
            Comparison report in specified format
        """
        try:
            # Ensure we have a comparison result
            if self.current_comparison is None:
                comparison_result = self.compare_strategies(strategy_ids)
                if comparison_result is None:
                    return None
            else:
                comparison_result = self.current_comparison
            
            # Generate comparison report
            report = self.strategy_comparator.generate_comparison_report(
                comparison_result,
                include_details=True
            )
            
            # Format report if needed
            if format == ReportFormat.JSON:
                report = json.dumps(report, indent=2, default=str)
            elif format == ReportFormat.HTML:
                report = self._format_comparison_as_html(report)
            
            # Store report
            report_key = f"comparison_{format.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.current_reports[report_key] = report
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating comparison report: {e}")
            return None
    
    def export_report(self, 
                     report_key: str,
                     file_path: str) -> bool:
        """
        Export report to file.
        
        Args:
            report_key: Key of the report to export
            file_path: File path for export
            
        Returns:
            True if export successful
        """
        try:
            if report_key not in self.current_reports:
                logger.warning(f"Report not found: {report_key}")
                return False
            
            report = self.current_reports[report_key]
            
            # Determine format from file extension
            if file_path.endswith('.json'):
                if isinstance(report, dict):
                    with open(file_path, 'w') as f:
                        json.dump(report, f, indent=2, default=str)
                else:
                    with open(file_path, 'w') as f:
                        f.write(str(report))
            elif file_path.endswith('.html'):
                with open(file_path, 'w') as f:
                    if isinstance(report, str):
                        f.write(report)
                    else:
                        f.write(self._format_as_html(report))
            else:
                # Default to text format
                with open(file_path, 'w') as f:
                    if isinstance(report, dict):
                        f.write(json.dumps(report, indent=2, default=str))
                    else:
                        f.write(str(report))
            
            logger.info(f"Report exported to: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting report: {e}")
            return False
    
    def get_available_strategies(self) -> List[str]:
        """
        Get list of available strategy IDs.
        
        Returns:
            List of strategy identifiers
        """
        return list(self.current_strategies.keys())
    
    def get_available_reports(self) -> List[str]:
        """
        Get list of available report keys.
        
        Returns:
            List of report keys
        """
        return list(self.current_reports.keys())
    
    def clear_strategy_data(self, strategy_id: Optional[str] = None) -> None:
        """
        Clear strategy data.
        
        Args:
            strategy_id: Specific strategy ID to clear, or None for all
        """
        try:
            if strategy_id is None:
                self.current_strategies.clear()
                self.current_comparison = None
                logger.info("Cleared all strategy data")
            elif strategy_id in self.current_strategies:
                del self.current_strategies[strategy_id]
                # Clear comparison if it involved this strategy
                if self.current_comparison is not None:
                    if strategy_id in [s.name for s in self.current_comparison.strategy_metrics.keys()]:
                        self.current_comparison = None
                logger.info(f"Cleared strategy data: {strategy_id}")
            
        except Exception as e:
            logger.error(f"Error clearing strategy data: {e}")
    
    def clear_reports(self, report_key: Optional[str] = None) -> None:
        """
        Clear generated reports.
        
        Args:
            report_key: Specific report key to clear, or None for all
        """
        try:
            if report_key is None:
                self.current_reports.clear()
                logger.info("Cleared all reports")
            elif report_key in self.current_reports:
                del self.current_reports[report_key]
                logger.info(f"Cleared report: {report_key}")
            
        except Exception as e:
            logger.error(f"Error clearing reports: {e}")
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """
        Get dashboard data for UI display.
        
        Returns:
            Dictionary containing dashboard data
        """
        try:
            dashboard_data = {
                'strategies': {},
                'comparison': None,
                'summary': {
                    'total_strategies': len(self.current_strategies),
                    'total_reports': len(self.current_reports),
                    'last_updated': datetime.now().isoformat()
                }
            }
            
            # Add strategy summaries
            for strategy_id, strategy_data in self.current_strategies.items():
                metrics = self.calculate_performance_metrics(strategy_id)
                if metrics:
                    dashboard_data['strategies'][strategy_id] = {
                        'name': strategy_data.name,
                        'total_return': round(metrics.total_return * 100, 2),
                        'sharpe_ratio': round(metrics.sharpe_ratio, 3),
                        'max_drawdown': round(metrics.max_drawdown * 100, 2),
                        'total_trades': metrics.total_trades,
                        'win_rate': round(metrics.win_rate * 100, 1) if metrics.total_trades > 0 else None,
                        'start_date': metrics.start_date.strftime('%Y-%m-%d') if metrics.start_date else None,
                        'end_date': metrics.end_date.strftime('%Y-%m-%d') if metrics.end_date else None
                    }
            
            # Add comparison data if available
            if self.current_comparison:
                dashboard_data['comparison'] = {
                    'strategies_compared': len(self.current_comparison.strategy_metrics),
                    'top_strategy': self.current_comparison.rankings.get('overall', ['Unknown'])[0] if self.current_comparison.rankings else 'Unknown',
                    'correlation_available': not self.current_comparison.correlation_matrix.empty,
                    'recommendations_count': len(self.current_comparison.recommendations)
                }
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"Error getting dashboard data: {e}")
            return {'error': str(e)}
    
    def _format_comparison_as_html(self, report: Dict) -> str:
        """Format comparison report as HTML."""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Strategy Comparison Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .section {{ margin-bottom: 30px; }}
                .metric {{ display: inline-block; margin: 10px; padding: 15px; border: 1px solid #ddd; }}
                table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                th {{ background-color: #f2f2f2; }}
                .recommendation {{ background-color: #f9f9f9; padding: 10px; margin: 5px 0; border-left: 4px solid #007bff; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Strategy Comparison Report</h1>
                <p>Generated on {report.get('summary', {}).get('comparison_date', 'Unknown')}</p>
            </div>
            
            <div class="section">
                <h2>Summary</h2>
                <p>Strategies Compared: {report.get('summary', {}).get('strategies_compared', 0)}</p>
                <p>Top Strategy: {report.get('summary', {}).get('top_strategy', 'Unknown')}</p>
            </div>
            
            <div class="section">
                <h2>Key Metrics Comparison</h2>
                <table>
                    <tr>
                        <th>Strategy</th>
                        <th>Total Return (%)</th>
                        <th>Sharpe Ratio</th>
                        <th>Max Drawdown (%)</th>
                        <th>Volatility (%)</th>
                    </tr>
        """
        
        # Add strategy rows
        for strategy, metrics in report.get('key_metrics', {}).items():
            html += f"""
                    <tr>
                        <td style="text-align: left;">{strategy}</td>
                        <td>{metrics.get('total_return_pct', 'N/A')}</td>
                        <td>{metrics.get('sharpe_ratio', 'N/A')}</td>
                        <td>{metrics.get('max_drawdown_pct', 'N/A')}</td>
                        <td>{metrics.get('volatility_pct', 'N/A')}</td>
                    </tr>
            """
        
        html += """
                </table>
            </div>
        """
        
        # Add recommendations if available
        recommendations = report.get('detailed_analysis', {}).get('recommendations', [])
        if recommendations:
            html += """
            <div class="section">
                <h2>Recommendations</h2>
            """
            for rec in recommendations:
                html += f"""
                <div class="recommendation">
                    <strong>{rec.get('type', 'Unknown').replace('_', ' ').title()}</strong><br>
                    {rec.get('reason', 'No reason provided')}<br>
                    <em>Action: {rec.get('action', 'No action specified')}</em>
                </div>
                """
            html += "</div>"
        
        html += """
        </body>
        </html>
        """
        
        return html
    
    def _format_as_html(self, data: Any) -> str:
        """Generic HTML formatter for data."""
        if isinstance(data, dict):
            return f"<pre>{json.dumps(data, indent=2, default=str)}</pre>"
        else:
            return f"<pre>{str(data)}</pre>"