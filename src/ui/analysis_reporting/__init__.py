"""
Analysis and reporting interface module.

This module provides comprehensive analysis and reporting interfaces including:
- Performance metrics display panels
- Equity curve and drawdown analysis charts
- Strategy comparison and ranking interfaces
- Report generation and export functionality
"""

from .main_interface import AnalysisReportingInterface
from .performance_panel import PerformanceMetricsPanel
from .chart_panel import EquityDrawdownChartPanel
from .comparison_panel import StrategyComparisonPanel
from .report_generator import ReportGeneratorPanel

__all__ = [
    'AnalysisReportingInterface',
    'PerformanceMetricsPanel',
    'EquityDrawdownChartPanel', 
    'StrategyComparisonPanel',
    'ReportGeneratorPanel'
]