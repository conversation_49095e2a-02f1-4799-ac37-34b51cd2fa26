# Analysis and Reporting Interface

This module provides comprehensive analysis and reporting functionality for the quantitative trading system, including performance metrics display, chart visualization, strategy comparison, and report generation capabilities.

## Overview

The Analysis and Reporting Interface consists of several key components:

- **Main Interface**: Coordinates all analysis and reporting functionality
- **Performance Panel**: Displays comprehensive performance metrics
- **Chart Panel**: Generates equity curves, drawdown analysis, and other visualizations
- **Comparison Panel**: Provides multi-strategy comparison and ranking
- **Report Generator**: Creates and exports reports in multiple formats

## Components

### 1. AnalysisReportingInterface (`main_interface.py`)

The main orchestrator that coordinates all analysis and reporting functionality.

**Key Features:**
- Strategy data management
- Performance metrics calculation
- Chart generation coordination
- Strategy comparison
- Report generation and export
- Dashboard data aggregation

**Usage Example:**
```python
from src.ui.analysis_reporting.main_interface import AnalysisReportingInterface
from src.analysis.reports import ReportFormat

# Initialize interface
interface = AnalysisReportingInterface()

# Add strategy data
interface.add_strategy_data(
    strategy_id='my_strategy',
    portfolio_values=portfolio_series,
    trades=trade_list,
    benchmark_values=benchmark_series
)

# Calculate performance metrics
metrics = interface.calculate_performance_metrics('my_strategy')

# Generate charts
charts = interface.generate_performance_charts('my_strategy')

# Compare strategies
comparison = interface.compare_strategies(['strategy1', 'strategy2'])

# Generate reports
report = interface.generate_performance_report('my_strategy', ReportFormat.HTML)
```

### 2. PerformanceMetricsPanel (`performance_panel.py`)

Displays comprehensive performance metrics with formatting and interpretation.

**Key Features:**
- Formatted metric display with interpretations
- Performance gauge data for visualizations
- Metrics comparison tables
- Key metrics summaries for dashboards

**Metrics Categories:**
- Basic Returns (total return, CAGR, daily statistics)
- Risk-Adjusted Metrics (Sharpe, Sortino, Calmar ratios)
- Drawdown Analysis (max drawdown, recovery factors)
- Trade Statistics (win rate, profit factor, expectancy)
- Risk Metrics (volatility, VaR, skewness, kurtosis)
- Benchmark Comparison (alpha, beta, correlation)

### 3. EquityDrawdownChartPanel (`chart_panel.py`)

Generates various performance charts and visualizations.

**Chart Types:**
- **Equity Curve**: Normalized portfolio performance over time
- **Drawdown Chart**: Peak-to-trough decline analysis
- **Returns Distribution**: Histogram of daily returns with normal overlay
- **Rolling Metrics**: Rolling Sharpe ratio, volatility, and drawdown
- **Monthly Returns Heatmap**: Calendar-based performance visualization
- **Underwater Chart**: Time spent in drawdown vs. at new highs
- **Trade Analysis**: P&L distribution, frequency, win/loss analysis

**Usage Example:**
```python
from src.ui.analysis_reporting.chart_panel import EquityDrawdownChartPanel

chart_panel = EquityDrawdownChartPanel()
charts = chart_panel.generate_charts(portfolio_values, benchmark_values, trades)

# Access specific charts
equity_curve = charts['equity_curve']
drawdown_chart = charts['drawdown']
```

### 4. StrategyComparisonPanel (`comparison_panel.py`)

Provides comprehensive strategy comparison and ranking functionality.

**Key Features:**
- Performance comparison tables
- Multi-criteria strategy rankings
- Correlation analysis and diversification scoring
- Portfolio optimization recommendations
- Statistical significance testing
- Actionable recommendations

**Comparison Methods:**
- Sharpe Ratio
- Total Return
- Maximum Drawdown
- Win Rate
- Profit Factor
- Sortino Ratio
- Calmar Ratio

**Ranking Methods:**
- Weighted Score (combines multiple metrics)
- Risk-Adjusted Return
- Consistency Analysis
- Diversification Benefit

### 5. ReportGeneratorPanel (`report_generator.py`)

Creates and exports comprehensive reports in multiple formats.

**Report Templates:**
- **Executive Summary**: High-level overview for management
- **Detailed Analysis**: Comprehensive technical analysis
- **Comparison Report**: Multi-strategy comparison
- **Risk Analysis**: Focused risk assessment
- **Custom**: User-defined templates

**Export Formats:**
- HTML (with embedded charts and styling)
- JSON (structured data for APIs)
- PDF (formatted documents)

**Usage Example:**
```python
from src.ui.analysis_reporting.report_generator import ReportGeneratorPanel
from src.analysis.reports import ReportFormat

generator = ReportGeneratorPanel()

# Generate performance report
report = generator.generate_performance_report(
    strategy_id='my_strategy',
    portfolio_values=portfolio_values,
    template='detailed_analysis',
    format=ReportFormat.HTML
)

# Export report
export_result = generator.export_report(
    report['metadata']['report_id'],
    'performance_report.html'
)
```

## Implementation Details

### Performance Metrics Calculation

The system calculates comprehensive performance metrics including:

- **Return Metrics**: Total return, annualized return, CAGR
- **Risk-Adjusted Metrics**: Sharpe, Sortino, Calmar, Information ratios
- **Drawdown Analysis**: Maximum drawdown, recovery periods, underwater time
- **Trade Statistics**: Win rate, profit factor, expectancy, Kelly criterion
- **Risk Measures**: Volatility, VaR, CVaR, skewness, kurtosis
- **Benchmark Comparison**: Alpha, beta, tracking error, correlation

### Chart Generation

Charts are generated with configurable themes and formats:

- **Data Processing**: Handles missing data, alignment, and normalization
- **Visualization**: Creates chart data structures for frontend rendering
- **Statistics**: Includes relevant statistics and interpretations
- **Interactivity**: Supports zoom, pan, and selection features

### Strategy Comparison

Multi-strategy comparison includes:

- **Performance Matrix**: Side-by-side metric comparison
- **Ranking Systems**: Multiple ranking methodologies
- **Correlation Analysis**: Diversification benefit assessment
- **Portfolio Optimization**: Optimal weight calculation
- **Statistical Testing**: Significance testing for performance differences

### Report Generation

Flexible report generation system:

- **Template System**: Customizable report templates
- **Multiple Formats**: HTML, JSON, PDF export options
- **Batch Processing**: Generate multiple reports simultaneously
- **Export Management**: File system integration and history tracking

## Configuration

### Chart Configuration

Charts use the existing `ChartConfig` system:

```python
from src.ui.charts.chart_config import ChartConfig, ChartTheme

config = ChartConfig(
    theme=ChartTheme(
        chart_height=400,
        background_color="#1e1e1e",
        grid_color="#333333"
    ),
    show_grid=True,
    enable_zoom=True
)
```

### Report Configuration

Reports can be customized using `ReportConfig`:

```python
from src.analysis.reports import ReportConfig

config = ReportConfig(
    title="Custom Performance Report",
    include_charts=True,
    include_monthly_returns=True,
    chart_width=800,
    chart_height=400
)
```

## Integration

### Database Integration

The system integrates with the backtest result repository:

```python
# Load backtest results for analysis
backtest_ids = ['backtest_1', 'backtest_2']
results = interface.load_backtest_results(backtest_ids, user_id='user123')
```

### API Integration

Can be integrated with REST APIs for web interfaces:

```python
# Get dashboard data for API response
dashboard_data = interface.get_dashboard_data()
return JSONResponse(dashboard_data)
```

## Testing

The implementation includes comprehensive testing:

```bash
# Run the demo script
python examples/analysis_reporting_demo.py

# Test individual components
python -c "from src.ui.analysis_reporting import *; print('All modules imported successfully')"
```

## Requirements Fulfilled

This implementation fulfills the following requirements from task 10.4:

✅ **实现绩效指标展示面板** - PerformanceMetricsPanel provides comprehensive metrics display
✅ **创建资金曲线和回撤分析图表** - EquityDrawdownChartPanel generates equity curves and drawdown analysis
✅ **编写策略对比和排名界面** - StrategyComparisonPanel provides multi-strategy comparison and ranking
✅ **实现报告生成和导出功能** - ReportGeneratorPanel creates and exports reports in multiple formats

## Future Enhancements

Potential improvements for future versions:

1. **Real-time Updates**: WebSocket integration for live performance monitoring
2. **Advanced Visualizations**: 3D charts, interactive dashboards
3. **Machine Learning**: Automated pattern recognition in performance data
4. **Custom Metrics**: User-defined performance metrics
5. **Collaboration**: Shared reports and collaborative analysis
6. **Mobile Support**: Responsive design for mobile devices
7. **Integration**: Connect with external portfolio management systems

## Dependencies

- pandas: Data manipulation and analysis
- numpy: Numerical computations
- datetime: Date and time handling
- logging: System logging
- json: JSON data handling
- dataclasses: Data structure definitions

## License

This module is part of the quantitative trading system and follows the same licensing terms.