"""
Performance metrics display panel.

This module provides a comprehensive panel for displaying performance metrics
including returns, risk-adjusted metrics, drawdown analysis, and trade statistics.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging

from ...analysis.metrics import PerformanceMetrics, MetricsCalculator
from ...models.trading import Trade

logger = logging.getLogger(__name__)


class PerformanceMetricsPanel:
    """
    Panel component for displaying comprehensive performance metrics.
    
    This panel organizes and displays performance metrics in a structured format
    suitable for web interfaces, including formatting, categorization, and
    interactive features.
    """
    
    def __init__(self):
        """Initialize performance metrics panel."""
        self.current_metrics: Dict[str, PerformanceMetrics] = {}
        self.display_config = {
            'decimal_places': 4,
            'percentage_format': '.2%',
            'currency_format': ',.2f',
            'show_tooltips': True,
            'group_metrics': True
        }
        
        logger.info("PerformanceMetricsPanel initialized")
    
    def update_metrics(self, strategy_id: str, metrics: PerformanceMetrics) -> None:
        """
        Update metrics for a strategy.
        
        Args:
            strategy_id: Strategy identifier
            metrics: PerformanceMetrics object
        """
        self.current_metrics[strategy_id] = metrics
        logger.info(f"Updated metrics for strategy: {strategy_id}")
    
    def get_formatted_metrics(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """
        Get formatted metrics for display.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            Dictionary with formatted metrics grouped by category
        """
        if strategy_id not in self.current_metrics:
            return None
        
        metrics = self.current_metrics[strategy_id]
        
        return {
            'basic_returns': self._format_basic_returns(metrics),
            'risk_adjusted': self._format_risk_adjusted_metrics(metrics),
            'drawdown_analysis': self._format_drawdown_metrics(metrics),
            'trade_statistics': self._format_trade_statistics(metrics),
            'risk_metrics': self._format_risk_metrics(metrics),
            'benchmark_comparison': self._format_benchmark_metrics(metrics),
            'additional_metrics': self._format_additional_metrics(metrics),
            'time_period': self._format_time_period(metrics)
        }
    
    def get_metrics_summary(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """
        Get key metrics summary for dashboard display.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            Dictionary with key metrics
        """
        if strategy_id not in self.current_metrics:
            return None
        
        metrics = self.current_metrics[strategy_id]
        
        return {
            'total_return': {
                'value': metrics.total_return,
                'formatted': f"{metrics.total_return:.2%}",
                'color': 'green' if metrics.total_return > 0 else 'red'
            },
            'sharpe_ratio': {
                'value': metrics.sharpe_ratio,
                'formatted': f"{metrics.sharpe_ratio:.3f}",
                'color': 'green' if metrics.sharpe_ratio > 1.0 else 'orange' if metrics.sharpe_ratio > 0.5 else 'red'
            },
            'max_drawdown': {
                'value': metrics.max_drawdown,
                'formatted': f"{metrics.max_drawdown:.2%}",
                'color': 'green' if metrics.max_drawdown > -0.1 else 'orange' if metrics.max_drawdown > -0.2 else 'red'
            },
            'volatility': {
                'value': metrics.volatility,
                'formatted': f"{metrics.volatility:.2%}",
                'color': 'green' if metrics.volatility < 0.15 else 'orange' if metrics.volatility < 0.25 else 'red'
            },
            'win_rate': {
                'value': metrics.win_rate,
                'formatted': f"{metrics.win_rate:.1%}" if metrics.total_trades > 0 else "N/A",
                'color': 'green' if metrics.win_rate > 0.6 else 'orange' if metrics.win_rate > 0.4 else 'red'
            }
        }
    
    def get_metrics_comparison_table(self, strategy_ids: List[str]) -> Optional[Dict[str, Any]]:
        """
        Get metrics comparison table for multiple strategies.
        
        Args:
            strategy_ids: List of strategy identifiers
            
        Returns:
            Dictionary with comparison table data
        """
        if not strategy_ids or not all(sid in self.current_metrics for sid in strategy_ids):
            return None
        
        comparison_data = {
            'headers': ['Metric'] + strategy_ids,
            'rows': []
        }
        
        # Define metrics to compare
        metrics_to_compare = [
            ('Total Return', 'total_return', '.2%'),
            ('Annualized Return', 'annualized_return', '.2%'),
            ('Sharpe Ratio', 'sharpe_ratio', '.3f'),
            ('Sortino Ratio', 'sortino_ratio', '.3f'),
            ('Calmar Ratio', 'calmar_ratio', '.3f'),
            ('Max Drawdown', 'max_drawdown', '.2%'),
            ('Volatility', 'volatility', '.2%'),
            ('Win Rate', 'win_rate', '.1%'),
            ('Profit Factor', 'profit_factor', '.2f'),
            ('Total Trades', 'total_trades', 'd')
        ]
        
        for metric_name, metric_attr, format_str in metrics_to_compare:
            row = [metric_name]
            
            for strategy_id in strategy_ids:
                metrics = self.current_metrics[strategy_id]
                value = getattr(metrics, metric_attr)
                
                if format_str == 'd':
                    formatted_value = f"{value:d}"
                elif format_str.endswith('%'):
                    formatted_value = f"{value:{format_str}}"
                else:
                    formatted_value = f"{value:{format_str}}"
                
                row.append(formatted_value)
            
            comparison_data['rows'].append(row)
        
        return comparison_data
    
    def get_performance_gauge_data(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """
        Get data for performance gauge visualization.
        
        Args:
            strategy_id: Strategy identifier
            
        Returns:
            Dictionary with gauge data
        """
        if strategy_id not in self.current_metrics:
            return None
        
        metrics = self.current_metrics[strategy_id]
        
        return {
            'sharpe_gauge': {
                'value': metrics.sharpe_ratio,
                'min': -2.0,
                'max': 3.0,
                'thresholds': [
                    {'value': 0, 'color': 'red', 'label': 'Poor'},
                    {'value': 0.5, 'color': 'orange', 'label': 'Fair'},
                    {'value': 1.0, 'color': 'yellow', 'label': 'Good'},
                    {'value': 1.5, 'color': 'lightgreen', 'label': 'Very Good'},
                    {'value': 2.0, 'color': 'green', 'label': 'Excellent'}
                ]
            },
            'return_gauge': {
                'value': metrics.annualized_return,
                'min': -0.5,
                'max': 0.5,
                'thresholds': [
                    {'value': -0.1, 'color': 'red', 'label': 'Loss'},
                    {'value': 0, 'color': 'orange', 'label': 'Break Even'},
                    {'value': 0.1, 'color': 'yellow', 'label': 'Positive'},
                    {'value': 0.2, 'color': 'lightgreen', 'label': 'Good'},
                    {'value': 0.3, 'color': 'green', 'label': 'Excellent'}
                ]
            },
            'drawdown_gauge': {
                'value': abs(metrics.max_drawdown),
                'min': 0,
                'max': 0.5,
                'thresholds': [
                    {'value': 0.05, 'color': 'green', 'label': 'Low Risk'},
                    {'value': 0.1, 'color': 'lightgreen', 'label': 'Moderate'},
                    {'value': 0.2, 'color': 'yellow', 'label': 'Medium'},
                    {'value': 0.3, 'color': 'orange', 'label': 'High'},
                    {'value': 0.4, 'color': 'red', 'label': 'Very High'}
                ]
            }
        }
    
    def _format_basic_returns(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Format basic return metrics."""
        return {
            'total_return': {
                'value': metrics.total_return,
                'formatted': f"{metrics.total_return:.2%}",
                'description': "Total return over the entire period"
            },
            'annualized_return': {
                'value': metrics.annualized_return,
                'formatted': f"{metrics.annualized_return:.2%}",
                'description': "Annualized return (CAGR)"
            },
            'cagr': {
                'value': metrics.cagr,
                'formatted': f"{metrics.cagr:.2%}",
                'description': "Compound Annual Growth Rate"
            },
            'daily_return_mean': {
                'value': metrics.daily_return_mean,
                'formatted': f"{metrics.daily_return_mean:.4%}",
                'description': "Average daily return"
            },
            'daily_return_std': {
                'value': metrics.daily_return_std,
                'formatted': f"{metrics.daily_return_std:.4%}",
                'description': "Daily return standard deviation"
            }
        }
    
    def _format_risk_adjusted_metrics(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Format risk-adjusted metrics."""
        return {
            'sharpe_ratio': {
                'value': metrics.sharpe_ratio,
                'formatted': f"{metrics.sharpe_ratio:.3f}",
                'description': "Risk-adjusted return (Sharpe Ratio)",
                'interpretation': self._interpret_sharpe_ratio(metrics.sharpe_ratio)
            },
            'sortino_ratio': {
                'value': metrics.sortino_ratio,
                'formatted': f"{metrics.sortino_ratio:.3f}",
                'description': "Downside risk-adjusted return (Sortino Ratio)",
                'interpretation': self._interpret_sortino_ratio(metrics.sortino_ratio)
            },
            'calmar_ratio': {
                'value': metrics.calmar_ratio,
                'formatted': f"{metrics.calmar_ratio:.3f}",
                'description': "Return to maximum drawdown ratio",
                'interpretation': self._interpret_calmar_ratio(metrics.calmar_ratio)
            },
            'information_ratio': {
                'value': metrics.information_ratio,
                'formatted': f"{metrics.information_ratio:.3f}",
                'description': "Active return to tracking error ratio"
            },
            'treynor_ratio': {
                'value': metrics.treynor_ratio,
                'formatted': f"{metrics.treynor_ratio:.3f}",
                'description': "Return per unit of systematic risk"
            }
        }
    
    def _format_drawdown_metrics(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Format drawdown metrics."""
        return {
            'max_drawdown': {
                'value': metrics.max_drawdown,
                'formatted': f"{metrics.max_drawdown:.2%}",
                'description': "Maximum peak-to-trough decline",
                'interpretation': self._interpret_drawdown(metrics.max_drawdown)
            },
            'max_drawdown_duration': {
                'value': metrics.max_drawdown_duration,
                'formatted': f"{metrics.max_drawdown_duration} days",
                'description': "Longest drawdown period"
            },
            'avg_drawdown': {
                'value': metrics.avg_drawdown,
                'formatted': f"{metrics.avg_drawdown:.2%}",
                'description': "Average drawdown magnitude"
            },
            'avg_drawdown_duration': {
                'value': metrics.avg_drawdown_duration,
                'formatted': f"{metrics.avg_drawdown_duration} days",
                'description': "Average drawdown duration"
            },
            'recovery_factor': {
                'value': metrics.recovery_factor,
                'formatted': f"{metrics.recovery_factor:.2f}",
                'description': "Total return to max drawdown ratio"
            }
        }
    
    def _format_trade_statistics(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Format trade statistics."""
        if metrics.total_trades == 0:
            return {
                'no_trades': {
                    'value': 0,
                    'formatted': "No trades available",
                    'description': "Trade statistics not available"
                }
            }
        
        return {
            'total_trades': {
                'value': metrics.total_trades,
                'formatted': f"{metrics.total_trades:,}",
                'description': "Total number of trades"
            },
            'winning_trades': {
                'value': metrics.winning_trades,
                'formatted': f"{metrics.winning_trades:,}",
                'description': "Number of winning trades"
            },
            'losing_trades': {
                'value': metrics.losing_trades,
                'formatted': f"{metrics.losing_trades:,}",
                'description': "Number of losing trades"
            },
            'win_rate': {
                'value': metrics.win_rate,
                'formatted': f"{metrics.win_rate:.1%}",
                'description': "Percentage of winning trades",
                'interpretation': self._interpret_win_rate(metrics.win_rate)
            },
            'avg_win': {
                'value': metrics.avg_win,
                'formatted': f"{metrics.avg_win:.2f}",
                'description': "Average winning trade amount"
            },
            'avg_loss': {
                'value': metrics.avg_loss,
                'formatted': f"{metrics.avg_loss:.2f}",
                'description': "Average losing trade amount"
            },
            'profit_factor': {
                'value': metrics.profit_factor,
                'formatted': f"{metrics.profit_factor:.2f}",
                'description': "Ratio of gross profit to gross loss",
                'interpretation': self._interpret_profit_factor(metrics.profit_factor)
            },
            'expectancy': {
                'value': metrics.expectancy,
                'formatted': f"{metrics.expectancy:.2f}",
                'description': "Expected value per trade"
            }
        }
    
    def _format_risk_metrics(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Format risk metrics."""
        return {
            'volatility': {
                'value': metrics.volatility,
                'formatted': f"{metrics.volatility:.2%}",
                'description': "Annualized volatility",
                'interpretation': self._interpret_volatility(metrics.volatility)
            },
            'downside_volatility': {
                'value': metrics.downside_volatility,
                'formatted': f"{metrics.downside_volatility:.2%}",
                'description': "Volatility of negative returns"
            },
            'var_95': {
                'value': metrics.var_95,
                'formatted': f"{metrics.var_95:.2%}",
                'description': "Value at Risk (95% confidence)"
            },
            'cvar_95': {
                'value': metrics.cvar_95,
                'formatted': f"{metrics.cvar_95:.2%}",
                'description': "Conditional VaR (95% confidence)"
            },
            'skewness': {
                'value': metrics.skewness,
                'formatted': f"{metrics.skewness:.3f}",
                'description': "Return distribution skewness",
                'interpretation': self._interpret_skewness(metrics.skewness)
            },
            'kurtosis': {
                'value': metrics.kurtosis,
                'formatted': f"{metrics.kurtosis:.3f}",
                'description': "Return distribution kurtosis",
                'interpretation': self._interpret_kurtosis(metrics.kurtosis)
            }
        }
    
    def _format_benchmark_metrics(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Format benchmark comparison metrics."""
        return {
            'alpha': {
                'value': metrics.alpha,
                'formatted': f"{metrics.alpha:.2%}",
                'description': "Excess return vs benchmark"
            },
            'beta': {
                'value': metrics.beta,
                'formatted': f"{metrics.beta:.3f}",
                'description': "Sensitivity to market movements",
                'interpretation': self._interpret_beta(metrics.beta)
            },
            'correlation': {
                'value': metrics.correlation,
                'formatted': f"{metrics.correlation:.3f}",
                'description': "Correlation with benchmark"
            },
            'tracking_error': {
                'value': metrics.tracking_error,
                'formatted': f"{metrics.tracking_error:.2%}",
                'description': "Standard deviation of excess returns"
            }
        }
    
    def _format_additional_metrics(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Format additional metrics."""
        return {
            'kelly_criterion': {
                'value': metrics.kelly_criterion,
                'formatted': f"{metrics.kelly_criterion:.1%}",
                'description': "Optimal position size (Kelly Criterion)"
            },
            'gain_to_pain_ratio': {
                'value': metrics.gain_to_pain_ratio,
                'formatted': f"{metrics.gain_to_pain_ratio:.2f}",
                'description': "Ratio of positive to negative returns"
            },
            'sterling_ratio': {
                'value': metrics.sterling_ratio,
                'formatted': f"{metrics.sterling_ratio:.2f}",
                'description': "Return to average drawdown ratio"
            },
            'burke_ratio': {
                'value': metrics.burke_ratio,
                'formatted': f"{metrics.burke_ratio:.2f}",
                'description': "Return to squared drawdown ratio"
            }
        }
    
    def _format_time_period(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Format time period information."""
        return {
            'start_date': {
                'value': metrics.start_date,
                'formatted': metrics.start_date.strftime('%Y-%m-%d') if metrics.start_date else 'N/A',
                'description': "Analysis start date"
            },
            'end_date': {
                'value': metrics.end_date,
                'formatted': metrics.end_date.strftime('%Y-%m-%d') if metrics.end_date else 'N/A',
                'description': "Analysis end date"
            },
            'period_length': {
                'value': metrics.period_length,
                'formatted': f"{metrics.period_length} days",
                'description': "Analysis period length"
            }
        }
    
    def _interpret_sharpe_ratio(self, sharpe: float) -> str:
        """Interpret Sharpe ratio value."""
        if sharpe > 2.0:
            return "Excellent risk-adjusted performance"
        elif sharpe > 1.5:
            return "Very good risk-adjusted performance"
        elif sharpe > 1.0:
            return "Good risk-adjusted performance"
        elif sharpe > 0.5:
            return "Fair risk-adjusted performance"
        elif sharpe > 0:
            return "Poor risk-adjusted performance"
        else:
            return "Negative risk-adjusted performance"
    
    def _interpret_sortino_ratio(self, sortino: float) -> str:
        """Interpret Sortino ratio value."""
        if sortino > 2.0:
            return "Excellent downside risk management"
        elif sortino > 1.5:
            return "Very good downside risk management"
        elif sortino > 1.0:
            return "Good downside risk management"
        elif sortino > 0.5:
            return "Fair downside risk management"
        else:
            return "Poor downside risk management"
    
    def _interpret_calmar_ratio(self, calmar: float) -> str:
        """Interpret Calmar ratio value."""
        if calmar > 1.0:
            return "Good return relative to maximum drawdown"
        elif calmar > 0.5:
            return "Fair return relative to maximum drawdown"
        else:
            return "Poor return relative to maximum drawdown"
    
    def _interpret_drawdown(self, drawdown: float) -> str:
        """Interpret maximum drawdown value."""
        abs_drawdown = abs(drawdown)
        if abs_drawdown < 0.05:
            return "Very low drawdown - excellent risk control"
        elif abs_drawdown < 0.1:
            return "Low drawdown - good risk control"
        elif abs_drawdown < 0.2:
            return "Moderate drawdown - acceptable risk"
        elif abs_drawdown < 0.3:
            return "High drawdown - elevated risk"
        else:
            return "Very high drawdown - significant risk"
    
    def _interpret_win_rate(self, win_rate: float) -> str:
        """Interpret win rate value."""
        if win_rate > 0.7:
            return "Very high win rate"
        elif win_rate > 0.6:
            return "High win rate"
        elif win_rate > 0.5:
            return "Above average win rate"
        elif win_rate > 0.4:
            return "Below average win rate"
        else:
            return "Low win rate"
    
    def _interpret_profit_factor(self, profit_factor: float) -> str:
        """Interpret profit factor value."""
        if profit_factor > 2.0:
            return "Excellent profit factor"
        elif profit_factor > 1.5:
            return "Good profit factor"
        elif profit_factor > 1.0:
            return "Profitable strategy"
        else:
            return "Unprofitable strategy"
    
    def _interpret_volatility(self, volatility: float) -> str:
        """Interpret volatility value."""
        if volatility < 0.1:
            return "Low volatility"
        elif volatility < 0.2:
            return "Moderate volatility"
        elif volatility < 0.3:
            return "High volatility"
        else:
            return "Very high volatility"
    
    def _interpret_skewness(self, skewness: float) -> str:
        """Interpret skewness value."""
        if skewness > 0.5:
            return "Positive skew - more upside potential"
        elif skewness > -0.5:
            return "Approximately symmetric distribution"
        else:
            return "Negative skew - more downside risk"
    
    def _interpret_kurtosis(self, kurtosis: float) -> str:
        """Interpret kurtosis value."""
        if kurtosis > 3:
            return "High kurtosis - fat tails, extreme events likely"
        elif kurtosis < 3:
            return "Low kurtosis - thin tails, fewer extreme events"
        else:
            return "Normal kurtosis - similar to normal distribution"
    
    def _interpret_beta(self, beta: float) -> str:
        """Interpret beta value."""
        if beta > 1.2:
            return "High beta - more volatile than market"
        elif beta > 0.8:
            return "Moderate beta - similar to market volatility"
        elif beta > 0:
            return "Low beta - less volatile than market"
        else:
            return "Negative beta - inverse market correlation"
    
    def clear_metrics(self, strategy_id: Optional[str] = None) -> None:
        """
        Clear metrics data.
        
        Args:
            strategy_id: Specific strategy ID to clear, or None for all
        """
        if strategy_id is None:
            self.current_metrics.clear()
            logger.info("Cleared all metrics")
        elif strategy_id in self.current_metrics:
            del self.current_metrics[strategy_id]
            logger.info(f"Cleared metrics for strategy: {strategy_id}")
    
    def get_available_strategies(self) -> List[str]:
        """
        Get list of strategies with available metrics.
        
        Returns:
            List of strategy identifiers
        """
        return list(self.current_metrics.keys())