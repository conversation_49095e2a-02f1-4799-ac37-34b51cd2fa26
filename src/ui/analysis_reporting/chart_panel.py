"""
Chart panel for equity curves and drawdown analysis.

This module provides chart generation and data preparation for equity curves,
drawdown analysis, returns distribution, and other performance visualizations.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
import logging

from ...models.trading import Trade
from ..charts.chart_config import ChartConfig
from ..charts.chart_utils import ChartDataProcessor

logger = logging.getLogger(__name__)


class EquityDrawdownChartPanel:
    """
    Panel component for equity curve and drawdown chart generation.
    
    This panel generates various performance charts including equity curves,
    drawdown analysis, returns distribution, rolling metrics, and comparative
    visualizations.
    """
    
    def __init__(self, config: Optional[ChartConfig] = None):
        """
        Initialize chart panel.
        
        Args:
            config: Chart configuration
        """
        self.config = config or ChartConfig()
        self.processor = ChartDataProcessor(self.config)
        
        # Chart data cache
        self.chart_cache: Dict[str, Dict[str, Any]] = {}
        
        logger.info("EquityDrawdownChartPanel initialized")
    
    def generate_charts(self, 
                       portfolio_values: pd.Series,
                       benchmark_values: Optional[pd.Series] = None,
                       trades: Optional[List[Trade]] = None) -> Dict[str, Any]:
        """
        Generate all performance charts for a strategy.
        
        Args:
            portfolio_values: Portfolio value time series
            benchmark_values: Optional benchmark values
            trades: Optional list of trades
            
        Returns:
            Dictionary containing all chart data
        """
        try:
            charts = {}
            
            # Generate equity curve
            charts['equity_curve'] = self.generate_equity_curve(
                portfolio_values, benchmark_values
            )
            
            # Generate drawdown chart
            charts['drawdown'] = self.generate_drawdown_chart(portfolio_values)
            
            # Generate returns distribution
            returns = portfolio_values.pct_change().dropna()
            charts['returns_distribution'] = self.generate_returns_distribution(returns)
            
            # Generate rolling metrics if sufficient data
            if len(portfolio_values) > 252:  # At least 1 year of data
                charts['rolling_metrics'] = self.generate_rolling_metrics_chart(portfolio_values)
            
            # Generate monthly returns heatmap
            charts['monthly_returns'] = self.generate_monthly_returns_heatmap(portfolio_values)
            
            # Generate trade analysis charts if trades available
            if trades:
                charts['trade_analysis'] = self.generate_trade_analysis_charts(trades)
            
            # Generate underwater chart (alternative drawdown view)
            charts['underwater'] = self.generate_underwater_chart(portfolio_values)
            
            return charts
            
        except Exception as e:
            logger.error(f"Error generating charts: {e}")
            return {'error': str(e)}
    
    def generate_equity_curve(self, 
                            portfolio_values: pd.Series,
                            benchmark_values: Optional[pd.Series] = None) -> Dict[str, Any]:
        """
        Generate equity curve chart data.
        
        Args:
            portfolio_values: Portfolio value time series
            benchmark_values: Optional benchmark values
            
        Returns:
            Chart data dictionary
        """
        try:
            # Normalize values to start at 100
            normalized_portfolio = (portfolio_values / portfolio_values.iloc[0]) * 100
            
            chart_data = {
                'type': 'line_chart',
                'title': 'Portfolio Equity Curve',
                'subtitle': 'Normalized to 100 at start',
                'data': {
                    'dates': portfolio_values.index.strftime('%Y-%m-%d').tolist(),
                    'portfolio': normalized_portfolio.round(2).tolist(),
                },
                'config': {
                    'width': 800,
                    'height': self.config.theme.chart_height,
                    'colors': ['#2E86AB'],
                    'y_axis_title': 'Normalized Value',
                    'x_axis_title': 'Date',
                    'show_grid': self.config.show_grid,
                    'show_legend': True
                },
                'statistics': {
                    'start_value': 100.0,
                    'end_value': round(normalized_portfolio.iloc[-1], 2),
                    'total_return': round((normalized_portfolio.iloc[-1] / 100 - 1) * 100, 2),
                    'peak_value': round(normalized_portfolio.max(), 2),
                    'trough_value': round(normalized_portfolio.min(), 2)
                }
            }
            
            # Add benchmark if provided
            if benchmark_values is not None:
                # Align benchmark with portfolio
                common_index = portfolio_values.index.intersection(benchmark_values.index)
                if len(common_index) > 1:
                    aligned_benchmark = benchmark_values.loc[common_index]
                    normalized_benchmark = (aligned_benchmark / aligned_benchmark.iloc[0]) * 100
                    
                    chart_data['data']['benchmark'] = normalized_benchmark.round(2).tolist()
                    chart_data['data']['benchmark_dates'] = common_index.strftime('%Y-%m-%d').tolist()
                    chart_data['config']['colors'] = ['#2E86AB', '#A23B72']
                    chart_data['config']['series_names'] = ['Portfolio', 'Benchmark']
                    
                    # Add benchmark statistics
                    chart_data['statistics']['benchmark_end_value'] = round(normalized_benchmark.iloc[-1], 2)
                    chart_data['statistics']['benchmark_total_return'] = round((normalized_benchmark.iloc[-1] / 100 - 1) * 100, 2)
                    chart_data['statistics']['excess_return'] = round(
                        chart_data['statistics']['total_return'] - chart_data['statistics']['benchmark_total_return'], 2
                    )
            
            return chart_data
            
        except Exception as e:
            logger.error(f"Error generating equity curve: {e}")
            return {'error': str(e)}
    
    def generate_drawdown_chart(self, portfolio_values: pd.Series) -> Dict[str, Any]:
        """
        Generate drawdown chart data.
        
        Args:
            portfolio_values: Portfolio value time series
            
        Returns:
            Chart data dictionary
        """
        try:
            # Calculate drawdown
            running_max = portfolio_values.expanding().max()
            drawdown = (portfolio_values - running_max) / running_max * 100
            
            chart_data = {
                'type': 'area_chart',
                'title': 'Drawdown Analysis',
                'subtitle': 'Peak-to-trough decline (%)',
                'data': {
                    'dates': portfolio_values.index.strftime('%Y-%m-%d').tolist(),
                    'drawdown': drawdown.round(2).tolist(),
                    'running_max': ((running_max / portfolio_values.iloc[0]) * 100).round(2).tolist()
                },
                'config': {
                    'width': 800,
                    'height': self.config.theme.chart_height,
                    'colors': ['#FF6B6B', '#4ECDC4'],
                    'fill_negative': True,
                    'y_axis_title': 'Drawdown (%)',
                    'x_axis_title': 'Date',
                    'show_grid': self.config.show_grid,
                    'show_zero_line': True
                },
                'statistics': {
                    'max_drawdown': round(drawdown.min(), 2),
                    'max_drawdown_date': drawdown.idxmin().strftime('%Y-%m-%d'),
                    'avg_drawdown': round(drawdown[drawdown < 0].mean(), 2) if (drawdown < 0).any() else 0,
                    'current_drawdown': round(drawdown.iloc[-1], 2),
                    'drawdown_periods': self._calculate_drawdown_periods(drawdown)
                }
            }
            
            return chart_data
            
        except Exception as e:
            logger.error(f"Error generating drawdown chart: {e}")
            return {'error': str(e)}
    
    def generate_returns_distribution(self, returns: pd.Series) -> Dict[str, Any]:
        """
        Generate returns distribution chart data.
        
        Args:
            returns: Returns time series
            
        Returns:
            Chart data dictionary
        """
        try:
            # Convert to percentage
            returns_pct = returns * 100
            
            # Calculate histogram
            hist, bin_edges = np.histogram(returns_pct, bins=50, density=True)
            bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
            
            # Calculate statistics
            mean_return = returns_pct.mean()
            std_return = returns_pct.std()
            
            # Generate normal distribution for comparison
            x_normal = np.linspace(returns_pct.min(), returns_pct.max(), 100)
            y_normal = (1 / (std_return * np.sqrt(2 * np.pi))) * np.exp(-0.5 * ((x_normal - mean_return) / std_return) ** 2)
            
            chart_data = {
                'type': 'histogram',
                'title': 'Returns Distribution',
                'subtitle': 'Daily returns frequency distribution',
                'data': {
                    'bins': bin_centers.round(3).tolist(),
                    'density': hist.round(6).tolist(),
                    'normal_x': x_normal.round(3).tolist(),
                    'normal_y': y_normal.round(6).tolist()
                },
                'config': {
                    'width': 800,
                    'height': self.config.theme.chart_height,
                    'colors': ['#74B9FF', '#FD79A8'],
                    'y_axis_title': 'Density',
                    'x_axis_title': 'Daily Return (%)',
                    'show_grid': self.config.show_grid,
                    'show_normal_overlay': True
                },
                'statistics': {
                    'mean': round(mean_return, 3),
                    'std': round(std_return, 3),
                    'skewness': round(returns_pct.skew(), 3),
                    'kurtosis': round(returns_pct.kurtosis(), 3),
                    'min_return': round(returns_pct.min(), 3),
                    'max_return': round(returns_pct.max(), 3),
                    'percentile_5': round(returns_pct.quantile(0.05), 3),
                    'percentile_95': round(returns_pct.quantile(0.95), 3)
                }
            }
            
            return chart_data
            
        except Exception as e:
            logger.error(f"Error generating returns distribution: {e}")
            return {'error': str(e)}
    
    def generate_rolling_metrics_chart(self, 
                                     portfolio_values: pd.Series,
                                     window: int = 252) -> Dict[str, Any]:
        """
        Generate rolling metrics chart data.
        
        Args:
            portfolio_values: Portfolio value time series
            window: Rolling window size in days
            
        Returns:
            Chart data dictionary
        """
        try:
            returns = portfolio_values.pct_change().dropna()
            
            # Calculate rolling metrics
            rolling_sharpe = self._calculate_rolling_sharpe(returns, window)
            rolling_volatility = returns.rolling(window).std() * np.sqrt(252) * 100
            rolling_max_dd = self._calculate_rolling_max_drawdown(portfolio_values, window)
            
            # Align all series to the same index
            common_index = rolling_sharpe.dropna().index.intersection(
                rolling_volatility.dropna().index
            ).intersection(rolling_max_dd.dropna().index)
            
            if len(common_index) == 0:
                return {'error': 'Insufficient data for rolling metrics'}
            
            chart_data = {
                'type': 'multi_line_chart',
                'title': f'Rolling Performance Metrics ({window} days)',
                'subtitle': 'Rolling window analysis of key metrics',
                'data': {
                    'dates': common_index.strftime('%Y-%m-%d').tolist(),
                    'sharpe_ratio': rolling_sharpe.loc[common_index].round(3).tolist(),
                    'volatility': rolling_volatility.loc[common_index].round(2).tolist(),
                    'max_drawdown': (rolling_max_dd.loc[common_index] * 100).round(2).tolist()
                },
                'config': {
                    'width': 800,
                    'height': self.config.theme.chart_height,
                    'colors': ['#00B894', '#FDCB6E', '#E17055'],
                    'y_axis_title': 'Metric Value',
                    'x_axis_title': 'Date',
                    'show_grid': self.config.show_grid,
                    'show_legend': True,
                    'series_names': ['Sharpe Ratio', 'Volatility (%)', 'Max Drawdown (%)'],
                    'dual_y_axis': True
                },
                'statistics': {
                    'avg_sharpe': round(rolling_sharpe.mean(), 3),
                    'avg_volatility': round(rolling_volatility.mean(), 2),
                    'avg_max_drawdown': round((rolling_max_dd * 100).mean(), 2),
                    'sharpe_trend': self._calculate_trend(rolling_sharpe),
                    'volatility_trend': self._calculate_trend(rolling_volatility),
                    'drawdown_trend': self._calculate_trend(rolling_max_dd)
                }
            }
            
            return chart_data
            
        except Exception as e:
            logger.error(f"Error generating rolling metrics chart: {e}")
            return {'error': str(e)}
    
    def generate_monthly_returns_heatmap(self, portfolio_values: pd.Series) -> Dict[str, Any]:
        """
        Generate monthly returns heatmap data.
        
        Args:
            portfolio_values: Portfolio value time series
            
        Returns:
            Chart data dictionary
        """
        try:
            returns = portfolio_values.pct_change().dropna()
            
            # Group by year and month
            monthly_returns = returns.groupby([
                returns.index.year,
                returns.index.month
            ]).apply(lambda x: (1 + x).prod() - 1)
            
            # Create pivot table
            monthly_table = monthly_returns.unstack(level=1, fill_value=0) * 100
            
            # Ensure all months are present
            for month in range(1, 13):
                if month not in monthly_table.columns:
                    monthly_table[month] = 0
            
            monthly_table = monthly_table.sort_index(axis=1)
            
            # Month names
            month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                          'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            
            # Prepare heatmap data
            heatmap_data = []
            for year in monthly_table.index:
                for month_idx, month_name in enumerate(month_names, 1):
                    value = monthly_table.loc[year, month_idx] if month_idx in monthly_table.columns else 0
                    heatmap_data.append({
                        'year': int(year),
                        'month': month_name,
                        'value': round(value, 2)
                    })
            
            # Calculate annual returns
            annual_returns = []
            for year in monthly_table.index:
                year_data = portfolio_values[portfolio_values.index.year == year]
                if len(year_data) > 1:
                    annual_return = (year_data.iloc[-1] / year_data.iloc[0] - 1) * 100
                    annual_returns.append({'year': int(year), 'return': round(annual_return, 2)})
            
            chart_data = {
                'type': 'heatmap',
                'title': 'Monthly Returns Heatmap',
                'subtitle': 'Monthly performance by year (%)',
                'data': {
                    'heatmap': heatmap_data,
                    'annual_returns': annual_returns
                },
                'config': {
                    'width': 800,
                    'height': self.config.theme.chart_height,
                    'color_scale': ['#FF6B6B', '#FFFFFF', '#4ECDC4'],
                    'show_values': True,
                    'cell_size': 30
                },
                'statistics': {
                    'best_month': {
                        'value': round(monthly_table.max().max(), 2),
                        'date': self._find_best_worst_month(monthly_table, 'max')
                    },
                    'worst_month': {
                        'value': round(monthly_table.min().min(), 2),
                        'date': self._find_best_worst_month(monthly_table, 'min')
                    },
                    'positive_months': int((monthly_table > 0).sum().sum()),
                    'negative_months': int((monthly_table < 0).sum().sum()),
                    'avg_monthly_return': round(monthly_table.mean().mean(), 2)
                }
            }
            
            return chart_data
            
        except Exception as e:
            logger.error(f"Error generating monthly returns heatmap: {e}")
            return {'error': str(e)}
    
    def generate_trade_analysis_charts(self, trades: List[Trade]) -> Dict[str, Any]:
        """
        Generate trade analysis charts.
        
        Args:
            trades: List of trades
            
        Returns:
            Dictionary containing trade analysis charts
        """
        try:
            if not trades:
                return {'error': 'No trades available'}
            
            # Extract trade data
            trade_data = []
            cumulative_pnl = 0
            
            for trade in sorted(trades, key=lambda t: t.timestamp):
                pnl = getattr(trade, 'pnl', 0)
                cumulative_pnl += pnl
                
                trade_data.append({
                    'timestamp': trade.timestamp,
                    'pnl': pnl,
                    'cumulative_pnl': cumulative_pnl,
                    'symbol': trade.symbol,
                    'side': trade.side,
                    'quantity': trade.quantity,
                    'price': trade.price
                })
            
            trade_df = pd.DataFrame(trade_data)
            
            charts = {}
            
            # P&L distribution
            charts['pnl_distribution'] = self._generate_pnl_distribution(trade_df)
            
            # Cumulative P&L
            charts['cumulative_pnl'] = self._generate_cumulative_pnl(trade_df)
            
            # Trade frequency by time
            charts['trade_frequency'] = self._generate_trade_frequency(trade_df)
            
            # Win/Loss analysis
            charts['win_loss_analysis'] = self._generate_win_loss_analysis(trade_df)
            
            return charts
            
        except Exception as e:
            logger.error(f"Error generating trade analysis charts: {e}")
            return {'error': str(e)}
    
    def generate_underwater_chart(self, portfolio_values: pd.Series) -> Dict[str, Any]:
        """
        Generate underwater chart (alternative drawdown visualization).
        
        Args:
            portfolio_values: Portfolio value time series
            
        Returns:
            Chart data dictionary
        """
        try:
            # Calculate drawdown
            running_max = portfolio_values.expanding().max()
            drawdown = (portfolio_values - running_max) / running_max
            
            # Find periods above/below water
            above_water = drawdown >= 0
            
            chart_data = {
                'type': 'underwater_chart',
                'title': 'Underwater Chart',
                'subtitle': 'Time spent in drawdown vs. at new highs',
                'data': {
                    'dates': portfolio_values.index.strftime('%Y-%m-%d').tolist(),
                    'drawdown': (drawdown * 100).round(2).tolist(),
                    'above_water': above_water.tolist()
                },
                'config': {
                    'width': 800,
                    'height': self.config.theme.chart_height,
                    'colors': ['#FF6B6B', '#4ECDC4'],
                    'y_axis_title': 'Drawdown (%)',
                    'x_axis_title': 'Date',
                    'show_grid': self.config.show_grid,
                    'fill_areas': True
                },
                'statistics': {
                    'time_underwater': round((~above_water).sum() / len(above_water) * 100, 1),
                    'time_at_highs': round(above_water.sum() / len(above_water) * 100, 1),
                    'longest_underwater_period': self._calculate_longest_underwater_period(above_water),
                    'recovery_periods': self._calculate_recovery_periods(drawdown)
                }
            }
            
            return chart_data
            
        except Exception as e:
            logger.error(f"Error generating underwater chart: {e}")
            return {'error': str(e)}
    
    def _calculate_drawdown_periods(self, drawdown: pd.Series) -> List[Dict[str, Any]]:
        """Calculate drawdown periods."""
        periods = []
        in_drawdown = False
        start_date = None
        
        for date, dd in drawdown.items():
            if dd < 0 and not in_drawdown:
                in_drawdown = True
                start_date = date
            elif dd >= 0 and in_drawdown:
                in_drawdown = False
                if start_date:
                    periods.append({
                        'start': start_date.strftime('%Y-%m-%d'),
                        'end': date.strftime('%Y-%m-%d'),
                        'duration': (date - start_date).days,
                        'max_drawdown': round(drawdown.loc[start_date:date].min(), 2)
                    })
        
        # Handle case where we end in drawdown
        if in_drawdown and start_date:
            periods.append({
                'start': start_date.strftime('%Y-%m-%d'),
                'end': drawdown.index[-1].strftime('%Y-%m-%d'),
                'duration': (drawdown.index[-1] - start_date).days,
                'max_drawdown': round(drawdown.loc[start_date:].min(), 2)
            })
        
        return periods
    
    def _calculate_rolling_sharpe(self, returns: pd.Series, window: int) -> pd.Series:
        """Calculate rolling Sharpe ratio."""
        risk_free_rate = 0.02 / 252  # Daily risk-free rate
        excess_returns = returns - risk_free_rate
        rolling_sharpe = (excess_returns.rolling(window).mean() / 
                         returns.rolling(window).std() * np.sqrt(252))
        return rolling_sharpe.dropna()
    
    def _calculate_rolling_max_drawdown(self, portfolio_values: pd.Series, window: int) -> pd.Series:
        """Calculate rolling maximum drawdown."""
        rolling_max_dd = []
        
        for i in range(len(portfolio_values)):
            start_idx = max(0, i - window + 1)
            window_values = portfolio_values.iloc[start_idx:i+1]
            
            if len(window_values) > 1:
                window_running_max = window_values.expanding().max()
                window_drawdown = (window_values - window_running_max) / window_running_max
                rolling_max_dd.append(window_drawdown.min())
            else:
                rolling_max_dd.append(0)
        
        return pd.Series(rolling_max_dd, index=portfolio_values.index)
    
    def _calculate_trend(self, series: pd.Series) -> str:
        """Calculate trend direction for a time series."""
        if len(series) < 2:
            return "insufficient_data"
        
        # Use linear regression to determine trend
        x = np.arange(len(series))
        slope = np.polyfit(x, series.dropna(), 1)[0]
        
        if slope > 0.001:
            return "increasing"
        elif slope < -0.001:
            return "decreasing"
        else:
            return "stable"
    
    def _find_best_worst_month(self, monthly_table: pd.DataFrame, operation: str) -> str:
        """Find best or worst month in the heatmap."""
        if operation == 'max':
            max_val = monthly_table.max().max()
            year, month = monthly_table.stack().idxmax()
        else:
            max_val = monthly_table.min().min()
            year, month = monthly_table.stack().idxmin()
        
        month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        
        return f"{month_names[month-1]} {year}"
    
    def _generate_pnl_distribution(self, trade_df: pd.DataFrame) -> Dict[str, Any]:
        """Generate P&L distribution chart."""
        pnl_values = trade_df['pnl']
        
        hist, bin_edges = np.histogram(pnl_values, bins=30, density=True)
        bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
        
        return {
            'type': 'histogram',
            'title': 'Trade P&L Distribution',
            'data': {
                'bins': bin_centers.round(2).tolist(),
                'density': hist.round(6).tolist()
            },
            'statistics': {
                'mean_pnl': round(pnl_values.mean(), 2),
                'std_pnl': round(pnl_values.std(), 2),
                'best_trade': round(pnl_values.max(), 2),
                'worst_trade': round(pnl_values.min(), 2)
            }
        }
    
    def _generate_cumulative_pnl(self, trade_df: pd.DataFrame) -> Dict[str, Any]:
        """Generate cumulative P&L chart."""
        return {
            'type': 'line_chart',
            'title': 'Cumulative P&L',
            'data': {
                'dates': trade_df['timestamp'].dt.strftime('%Y-%m-%d').tolist(),
                'cumulative_pnl': trade_df['cumulative_pnl'].round(2).tolist()
            }
        }
    
    def _generate_trade_frequency(self, trade_df: pd.DataFrame) -> Dict[str, Any]:
        """Generate trade frequency chart."""
        # Group by hour of day
        trade_df['hour'] = trade_df['timestamp'].dt.hour
        hourly_counts = trade_df.groupby('hour').size()
        
        return {
            'type': 'bar_chart',
            'title': 'Trade Frequency by Hour',
            'data': {
                'hours': hourly_counts.index.tolist(),
                'counts': hourly_counts.values.tolist()
            }
        }
    
    def _generate_win_loss_analysis(self, trade_df: pd.DataFrame) -> Dict[str, Any]:
        """Generate win/loss analysis chart."""
        winning_trades = trade_df[trade_df['pnl'] > 0]
        losing_trades = trade_df[trade_df['pnl'] < 0]
        
        return {
            'type': 'pie_chart',
            'title': 'Win/Loss Distribution',
            'data': {
                'labels': ['Winning Trades', 'Losing Trades'],
                'values': [len(winning_trades), len(losing_trades)]
            },
            'statistics': {
                'win_rate': round(len(winning_trades) / len(trade_df) * 100, 1),
                'avg_win': round(winning_trades['pnl'].mean(), 2) if len(winning_trades) > 0 else 0,
                'avg_loss': round(losing_trades['pnl'].mean(), 2) if len(losing_trades) > 0 else 0
            }
        }
    
    def _calculate_longest_underwater_period(self, above_water: pd.Series) -> int:
        """Calculate longest period underwater."""
        max_period = 0
        current_period = 0
        
        for is_above in above_water:
            if not is_above:
                current_period += 1
                max_period = max(max_period, current_period)
            else:
                current_period = 0
        
        return max_period
    
    def _calculate_recovery_periods(self, drawdown: pd.Series) -> List[int]:
        """Calculate recovery periods from drawdowns."""
        recovery_periods = []
        in_drawdown = False
        drawdown_start = None
        
        for i, dd in enumerate(drawdown):
            if dd < 0 and not in_drawdown:
                in_drawdown = True
                drawdown_start = i
            elif dd >= 0 and in_drawdown:
                in_drawdown = False
                if drawdown_start is not None:
                    recovery_periods.append(i - drawdown_start)
        
        return recovery_periods
    
    def clear_cache(self) -> None:
        """Clear chart data cache."""
        self.chart_cache.clear()
        logger.info("Chart cache cleared")