{% extends "base.html" %}

{% block title %}量化交易系统 - 首页{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 欢迎横幅 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body text-center py-5">
                    <h1 class="display-4 mb-3">
                        <i class="fas fa-chart-line me-3"></i>
                        量化交易系统
                    </h1>
                    <p class="lead mb-4">专业的量化交易策略开发和回测平台</p>
                    <div class="row">
                        <div class="col-md-3">
                            <h3>多市场支持</h3>
                            <p>支持股票、期货、加密货币等多个市场</p>
                        </div>
                        <div class="col-md-3">
                            <h3>策略回测</h3>
                            <p>高性能的历史数据回测引擎</p>
                        </div>
                        <div class="col-md-3">
                            <h3>风险管理</h3>
                            <p>完善的风险控制和资金管理</p>
                        </div>
                        <div class="col-md-3">
                            <h3>实时监控</h3>
                            <p>实时的策略监控和绩效分析</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 快速导航 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">
                <i class="fas fa-rocket me-2"></i>
                快速开始
            </h2>
        </div>
    </div>
    
    <div class="row">
        <!-- 图表分析 -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-chart-line fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">图表分析</h5>
                    <p class="card-text">查看K线图表，分析技术指标，识别交易机会</p>
                    <a href="/charts" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        开始分析
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 策略管理 -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-bullseye fa-3x text-success"></i>
                    </div>
                    <h5 class="card-title">策略管理</h5>
                    <p class="card-text">创建、配置和管理交易策略，运行回测分析</p>
                    <a href="/strategies" class="btn btn-success">
                        <i class="fas fa-arrow-right me-2"></i>
                        管理策略
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 数据管理 -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-database fa-3x text-info"></i>
                    </div>
                    <h5 class="card-title">数据管理</h5>
                    <p class="card-text">管理数据源，同步市场数据，监控数据质量</p>
                    <a href="/data-sources" class="btn btn-info">
                        <i class="fas fa-arrow-right me-2"></i>
                        管理数据
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- 分析报告 -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-chart-bar fa-3x text-warning"></i>
                    </div>
                    <h5 class="card-title">分析报告</h5>
                    <p class="card-text">生成详细的策略绩效报告和风险分析</p>
                    <a href="/analysis" class="btn btn-warning">
                        <i class="fas fa-arrow-right me-2"></i>
                        查看报告
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 实时监控 -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-desktop fa-3x text-danger"></i>
                    </div>
                    <h5 class="card-title">实时监控</h5>
                    <p class="card-text">监控系统状态，跟踪策略表现和风险指标</p>
                    <a href="/monitoring" class="btn btn-danger">
                        <i class="fas fa-arrow-right me-2"></i>
                        实时监控
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统状态概览 -->
    <div class="row mt-4">
        <div class="col-12">
            <h2 class="mb-3">
                <i class="fas fa-info-circle me-2"></i>
                系统状态
            </h2>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-3">
            <div class="card metric-card bg-light">
                <div class="metric-value text-success" id="active-strategies">3</div>
                <div class="metric-label">活跃策略</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card bg-light">
                <div class="metric-value text-primary" id="data-sources">4</div>
                <div class="metric-label">数据源</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card bg-light">
                <div class="metric-value text-info" id="total-symbols">4,500</div>
                <div class="metric-label">交易品种</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card bg-light">
                <div class="metric-value text-warning" id="system-uptime">99.8%</div>
                <div class="metric-label">系统可用性</div>
            </div>
        </div>
    </div>
    
    <!-- 最近活动 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        最近活动
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-play-circle text-success me-2"></i>
                                移动平均策略回测完成
                            </div>
                            <small class="text-muted">2分钟前</small>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-sync text-primary me-2"></i>
                                数据同步完成 - Yahoo Finance
                            </div>
                            <small class="text-muted">15分钟前</small>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-plus-circle text-info me-2"></i>
                                新增RSI策略配置
                            </div>
                            <small class="text-muted">1小时前</small>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-chart-line text-warning me-2"></i>
                                生成月度绩效报告
                            </div>
                            <small class="text-muted">3小时前</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 加载系统状态数据
    loadSystemStatus();
    
    // 每30秒更新一次状态
    setInterval(loadSystemStatus, 30000);
});

function loadSystemStatus() {
    $.get('/api/monitoring/status')
        .done(function(data) {
            if (data.success) {
                // 更新状态指标
                $('#active-strategies').text(data.status.trading.active_strategies);
                // 其他状态更新...
            }
        })
        .fail(function() {
            console.log('获取系统状态失败');
        });
}
</script>
{% endblock %}