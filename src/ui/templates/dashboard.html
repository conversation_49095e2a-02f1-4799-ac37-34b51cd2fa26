{% extends "base.html" %}

{% block title %}仪表板 - 量化交易系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3">
                <i class="fas fa-tachometer-alt me-2"></i>
                系统仪表板
            </h1>
            <p class="text-muted">实时监控系统状态和策略表现</p>
        </div>
    </div>
    
    <!-- 关键指标卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card metric-card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="metric-value" id="total-pnl">¥15,678</div>
                            <div class="metric-label">总盈亏</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card metric-card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="metric-value" id="daily-pnl">¥2,456</div>
                            <div class="metric-label">今日盈亏</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card metric-card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="metric-value" id="active-positions">12</div>
                            <div class="metric-label">持仓数量</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-briefcase fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card metric-card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="metric-value" id="win-rate">68.5%</div>
                            <div class="metric-label">胜率</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-percentage fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 图表区域 -->
    <div class="row mb-4">
        <!-- 资金曲线图 -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-area me-2"></i>
                        资金曲线
                    </h5>
                </div>
                <div class="card-body">
                    <div id="equity-curve-chart" class="chart-container"></div>
                </div>
            </div>
        </div>
        
        <!-- 策略表现 -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bullseye me-2"></i>
                        策略表现
                    </h5>
                </div>
                <div class="card-body">
                    <div id="strategy-performance" class="loading">
                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 持仓和交易 -->
    <div class="row mb-4">
        <!-- 当前持仓 -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-briefcase me-2"></i>
                        当前持仓
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>品种</th>
                                    <th>数量</th>
                                    <th>成本价</th>
                                    <th>现价</th>
                                    <th>盈亏</th>
                                </tr>
                            </thead>
                            <tbody id="positions-table">
                                <tr>
                                    <td colspan="5" class="text-center">
                                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 最近交易 -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>
                        最近交易
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>品种</th>
                                    <th>方向</th>
                                    <th>价格</th>
                                    <th>数量</th>
                                </tr>
                            </thead>
                            <tbody id="trades-table">
                                <tr>
                                    <td colspan="5" class="text-center">
                                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统状态 -->
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-server me-2"></i>
                        系统状态
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>CPU使用率</span>
                            <span id="cpu-usage">45%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-primary" id="cpu-progress" style="width: 45%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>内存使用率</span>
                            <span id="memory-usage">62%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-info" id="memory-progress" style="width: 62%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>磁盘使用率</span>
                            <span id="disk-usage">78%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-warning" id="disk-progress" style="width: 78%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-database me-2"></i>
                        数据源状态
                    </h5>
                </div>
                <div class="card-body">
                    <div id="data-sources-status" class="loading">
                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        系统告警
                    </h5>
                </div>
                <div class="card-body">
                    <div id="system-alerts">
                        <div class="alert alert-success alert-sm mb-2">
                            <i class="fas fa-check-circle me-2"></i>
                            所有系统正常运行
                        </div>
                        <div class="alert alert-warning alert-sm mb-2">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            磁盘使用率较高
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 初始化页面数据
    loadDashboardData();
    
    // 定时刷新数据
    setInterval(loadDashboardData, 30000); // 30秒刷新一次
    
    // 初始化资金曲线图
    initEquityCurveChart();
});

function loadDashboardData() {
    // 加载监控状态
    $.get('/api/monitoring/status')
        .done(function(data) {
            if (data.success) {
                updateSystemMetrics(data.status);
                updateDataSourcesStatus();
            }
        })
        .fail(function() {
            console.log('获取监控数据失败');
        });
    
    // 加载策略表现
    loadStrategyPerformance();
    
    // 加载持仓和交易数据
    loadPositionsAndTrades();
}

function updateSystemMetrics(status) {
    // 更新关键指标
    $('#total-pnl').text('¥' + formatNumber(status.trading.total_pnl));
    $('#daily-pnl').text('¥' + formatNumber(status.trading.daily_pnl));
    $('#active-positions').text(status.trading.total_positions);
    
    // 更新系统资源使用率
    $('#cpu-usage').text(formatPercent(status.system.cpu_usage));
    $('#cpu-progress').css('width', status.system.cpu_usage + '%');
    
    $('#memory-usage').text(formatPercent(status.system.memory_usage));
    $('#memory-progress').css('width', status.system.memory_usage + '%');
    
    $('#disk-usage').text(formatPercent(status.system.disk_usage));
    $('#disk-progress').css('width', status.system.disk_usage + '%');
}

function loadStrategyPerformance() {
    $.get('/api/strategies/list')
        .done(function(data) {
            if (data.success) {
                let html = '';
                data.strategies.forEach(function(strategy) {
                    let statusClass = strategy.status === 'active' ? 'success' : 
                                    strategy.status === 'testing' ? 'warning' : 'secondary';
                    
                    html += `
                        <div class="mb-3 p-2 border rounded">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${strategy.name}</strong>
                                    <span class="badge bg-${statusClass} ms-2">${strategy.status}</span>
                                </div>
                                <div class="text-end">
                                    <div class="text-success">${formatPercent(strategy.performance.total_return)}</div>
                                    <small class="text-muted">收益率</small>
                                </div>
                            </div>
                        </div>
                    `;
                });
                $('#strategy-performance').html(html);
            }
        })
        .fail(function() {
            $('#strategy-performance').html('<div class="alert alert-danger">加载策略数据失败</div>');
        });
}

function updateDataSourcesStatus() {
    $.get('/api/data-sources/status')
        .done(function(data) {
            if (data.success) {
                let html = '';
                data.sources.forEach(function(source) {
                    let statusClass = source.status === 'connected' ? 'success' : 'danger';
                    let statusIcon = source.status === 'connected' ? 'check-circle' : 'times-circle';
                    
                    html += `
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <i class="fas fa-${statusIcon} text-${statusClass} me-2"></i>
                                ${source.name}
                            </div>
                            <small class="text-muted">${source.symbols_count} 品种</small>
                        </div>
                    `;
                });
                $('#data-sources-status').html(html);
            }
        })
        .fail(function() {
            $('#data-sources-status').html('<div class="alert alert-danger">加载数据源状态失败</div>');
        });
}

function loadPositionsAndTrades() {
    // 模拟持仓数据
    let positionsHtml = `
        <tr>
            <td>AAPL</td>
            <td>100</td>
            <td>$150.00</td>
            <td>$155.50</td>
            <td class="text-success">+$550</td>
        </tr>
        <tr>
            <td>TSLA</td>
            <td>50</td>
            <td>$800.00</td>
            <td>$795.00</td>
            <td class="text-danger">-$250</td>
        </tr>
        <tr>
            <td>MSFT</td>
            <td>75</td>
            <td>$300.00</td>
            <td>$310.00</td>
            <td class="text-success">+$750</td>
        </tr>
    `;
    $('#positions-table').html(positionsHtml);
    
    // 模拟交易数据
    let tradesHtml = `
        <tr>
            <td>10:30</td>
            <td>AAPL</td>
            <td><span class="badge bg-success">买入</span></td>
            <td>$155.50</td>
            <td>100</td>
        </tr>
        <tr>
            <td>09:45</td>
            <td>TSLA</td>
            <td><span class="badge bg-danger">卖出</span></td>
            <td>$795.00</td>
            <td>25</td>
        </tr>
        <tr>
            <td>09:15</td>
            <td>MSFT</td>
            <td><span class="badge bg-success">买入</span></td>
            <td>$310.00</td>
            <td>50</td>
        </tr>
    `;
    $('#trades-table').html(tradesHtml);
}

function initEquityCurveChart() {
    // 生成模拟资金曲线数据
    let dates = [];
    let values = [];
    let baseValue = 100000;
    
    for (let i = 30; i >= 0; i--) {
        let date = new Date();
        date.setDate(date.getDate() - i);
        dates.push(date.toISOString().split('T')[0]);
        
        // 模拟资金变化
        baseValue += (Math.random() - 0.5) * 2000;
        values.push(baseValue);
    }
    
    let trace = {
        x: dates,
        y: values,
        type: 'scatter',
        mode: 'lines',
        name: '资金曲线',
        line: {
            color: '#0078d4',
            width: 2
        }
    };
    
    let layout = {
        title: '',
        xaxis: {
            title: '日期'
        },
        yaxis: {
            title: '资金 (¥)'
        },
        margin: {
            l: 50,
            r: 50,
            t: 20,
            b: 50
        }
    };
    
    Plotly.newPlot('equity-curve-chart', [trace], layout, {responsive: true});
}
</script>
{% endblock %}