"""
策略协调机制

这个模块提供策略协调功能，包括：
- 多策略信号聚合和优先级管理
- 资金分配和仓位协调逻辑
- 策略间通信和状态同步
- 策略组合风险控制
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import threading
import queue
from abc import ABC, abstractmethod
import logging

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.strategies.signals import Signal, SignalType
from src.strategies.base import BaseStrategy
from src.models.portfolio import Portfolio
from src.models.trading import Trade, Order, OrderSide
from src.risk.manager import RiskManager
from src.strategies.multi_strategy_manager import StrategyPortfolioManager


class SignalPriority(Enum):
    """信号优先级"""
    EMERGENCY = 1    # 紧急（止损等）
    HIGH = 2         # 高优先级
    MEDIUM = 3       # 中等优先级  
    LOW = 4          # 低优先级


class AggregationMethod(Enum):
    """信号聚合方法"""
    WEIGHTED_AVERAGE = "weighted_average"      # 加权平均
    MAJORITY_VOTE = "majority_vote"           # 多数投票
    CONFIDENCE_BASED = "confidence_based"     # 基于置信度
    PRIORITY_BASED = "priority_based"         # 基于优先级
    RISK_ADJUSTED = "risk_adjusted"           # 风险调整


class StrategyState(Enum):
    """策略状态"""
    INACTIVE = "inactive"        # 非活跃
    ACTIVE = "active"           # 活跃
    SUSPENDED = "suspended"     # 暂停
    ERROR = "error"             # 错误状态
    MAINTENANCE = "maintenance" # 维护状态


@dataclass
class PrioritySignal:
    """带优先级的信号"""
    signal: Signal
    priority: SignalPriority
    weight: float = 1.0
    timestamp: datetime = field(default_factory=datetime.now)
    strategy_id: str = ""


@dataclass
class AggregatedSignal:
    """聚合信号"""
    symbol: str
    signal_type: SignalType
    aggregate_confidence: float
    component_signals: List[PrioritySignal]
    aggregation_method: AggregationMethod
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PositionAllocation:
    """仓位分配"""
    strategy_id: str
    symbol: str
    target_quantity: float
    current_quantity: float = 0.0
    allocated_capital: float = 0.0
    max_position_size: float = None
    risk_limit: float = None


@dataclass
class StrategyStatusUpdate:
    """策略状态更新"""
    strategy_id: str
    state: StrategyState
    timestamp: datetime
    message: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)


class SignalAggregator:
    """信号聚合器"""
    
    def __init__(self, method: AggregationMethod = AggregationMethod.CONFIDENCE_BASED):
        self.method = method
        self.logger = logging.getLogger(f"{__name__}.SignalAggregator")
    
    def aggregate_signals(self, signals: List[PrioritySignal]) -> Dict[str, AggregatedSignal]:
        """
        聚合信号
        
        Args:
            signals: 优先级信号列表
            
        Returns:
            按资产分组的聚合信号字典
        """
        # 按资产分组信号
        signals_by_asset = {}
        for signal in signals:
            asset = signal.signal.symbol
            if asset not in signals_by_asset:
                signals_by_asset[asset] = []
            signals_by_asset[asset].append(signal)
        
        # 对每个资产进行信号聚合
        aggregated_signals = {}
        for asset, asset_signals in signals_by_asset.items():
            aggregated = self._aggregate_asset_signals(asset_signals)
            if aggregated:
                aggregated_signals[asset] = aggregated
        
        return aggregated_signals
    
    def _aggregate_asset_signals(self, signals: List[PrioritySignal]) -> Optional[AggregatedSignal]:
        """聚合单个资产的信号"""
        if not signals:
            return None
        
        if self.method == AggregationMethod.PRIORITY_BASED:
            return self._aggregate_by_priority(signals)
        elif self.method == AggregationMethod.CONFIDENCE_BASED:
            return self._aggregate_by_confidence(signals)
        elif self.method == AggregationMethod.WEIGHTED_AVERAGE:
            return self._aggregate_by_weighted_average(signals)
        elif self.method == AggregationMethod.MAJORITY_VOTE:
            return self._aggregate_by_majority_vote(signals)
        else:
            return self._aggregate_by_confidence(signals)  # 默认方法
    
    def _aggregate_by_priority(self, signals: List[PrioritySignal]) -> AggregatedSignal:
        """基于优先级聚合"""
        # 选择最高优先级的信号
        highest_priority_signal = min(signals, key=lambda s: s.priority.value)
        
        return AggregatedSignal(
            symbol=highest_priority_signal.signal.symbol,
            signal_type=highest_priority_signal.signal.signal_type,
            aggregate_confidence=highest_priority_signal.signal.confidence,
            component_signals=signals,
            aggregation_method=self.method,
            timestamp=datetime.now()
        )
    
    def _aggregate_by_confidence(self, signals: List[PrioritySignal]) -> AggregatedSignal:
        """基于置信度聚合"""
        # 选择置信度最高的信号
        highest_confidence_signal = max(signals, key=lambda s: s.signal.confidence)
        
        return AggregatedSignal(
            symbol=highest_confidence_signal.signal.symbol,
            signal_type=highest_confidence_signal.signal.signal_type,
            aggregate_confidence=highest_confidence_signal.signal.confidence,
            component_signals=signals,
            aggregation_method=self.method,
            timestamp=datetime.now()
        )
    
    def _aggregate_by_weighted_average(self, signals: List[PrioritySignal]) -> Optional[AggregatedSignal]:
        """基于加权平均聚合"""
        if not signals:
            return None
        
        # 按信号类型分组
        buy_signals = [s for s in signals if s.signal.signal_type == SignalType.BUY]
        sell_signals = [s for s in signals if s.signal.signal_type == SignalType.SELL]
        
        # 计算加权置信度
        buy_weighted_conf = sum(s.signal.confidence * s.weight for s in buy_signals)
        sell_weighted_conf = sum(s.signal.confidence * s.weight for s in sell_signals)
        
        buy_total_weight = sum(s.weight for s in buy_signals)
        sell_total_weight = sum(s.weight for s in sell_signals)
        
        # 标准化
        if buy_total_weight > 0:
            buy_weighted_conf /= buy_total_weight
        if sell_total_weight > 0:
            sell_weighted_conf /= sell_total_weight
        
        # 确定最终信号
        if buy_weighted_conf > sell_weighted_conf:
            signal_type = SignalType.BUY
            confidence = buy_weighted_conf
        elif sell_weighted_conf > buy_weighted_conf:
            signal_type = SignalType.SELL
            confidence = sell_weighted_conf
        else:
            signal_type = SignalType.HOLD
            confidence = 0.5
        
        return AggregatedSignal(
            symbol=signals[0].signal.symbol,
            signal_type=signal_type,
            aggregate_confidence=confidence,
            component_signals=signals,
            aggregation_method=self.method,
            timestamp=datetime.now()
        )
    
    def _aggregate_by_majority_vote(self, signals: List[PrioritySignal]) -> AggregatedSignal:
        """基于多数投票聚合"""
        # 统计各类信号数量
        signal_counts = {}
        for signal in signals:
            signal_type = signal.signal.signal_type
            if signal_type not in signal_counts:
                signal_counts[signal_type] = 0
            signal_counts[signal_type] += 1
        
        # 找到多数信号
        majority_signal_type = max(signal_counts, key=signal_counts.get)
        majority_signals = [s for s in signals if s.signal.signal_type == majority_signal_type]
        
        # 计算平均置信度
        avg_confidence = sum(s.signal.confidence for s in majority_signals) / len(majority_signals)
        
        return AggregatedSignal(
            symbol=signals[0].signal.symbol,
            signal_type=majority_signal_type,
            aggregate_confidence=avg_confidence,
            component_signals=signals,
            aggregation_method=self.method,
            timestamp=datetime.now()
        )


class CapitalAllocator:
    """资金分配器"""
    
    def __init__(self, total_capital: float, risk_manager: Optional[RiskManager] = None):
        self.total_capital = total_capital
        self.risk_manager = risk_manager
        self.allocated_capital = 0.0
        self.allocations: Dict[str, PositionAllocation] = {}
        self.logger = logging.getLogger(f"{__name__}.CapitalAllocator")
    
    def allocate_capital(
        self,
        signals: Dict[str, AggregatedSignal],
        strategy_weights: Dict[str, float],
        current_positions: Dict[str, Dict[str, float]] = None
    ) -> Dict[str, List[PositionAllocation]]:
        """
        分配资金
        
        Args:
            signals: 聚合信号字典
            strategy_weights: 策略权重
            current_positions: 当前持仓情况
            
        Returns:
            策略分配字典
        """
        allocations = {}
        current_positions = current_positions or {}
        
        for symbol, signal in signals.items():
            symbol_allocations = self._allocate_for_symbol(
                signal, strategy_weights, current_positions.get(symbol, {})
            )
            
            for allocation in symbol_allocations:
                strategy_id = allocation.strategy_id
                if strategy_id not in allocations:
                    allocations[strategy_id] = []
                allocations[strategy_id].append(allocation)
        
        return allocations
    
    def _allocate_for_symbol(
        self,
        signal: AggregatedSignal,
        strategy_weights: Dict[str, float],
        current_positions: Dict[str, float]
    ) -> List[PositionAllocation]:
        """为单个资产分配资金"""
        allocations = []
        
        # 计算总的目标资金
        total_signal_capital = self.total_capital * 0.1  # 每个信号最多使用10%的总资金
        
        # 按策略分配资金
        for priority_signal in signal.component_signals:
            strategy_id = priority_signal.strategy_id
            strategy_weight = strategy_weights.get(strategy_id, 0.0)
            
            if strategy_weight <= 0:
                continue
            
            # 计算分配资金
            allocated_capital = total_signal_capital * strategy_weight * signal.aggregate_confidence
            
            # 风险检查
            if self.risk_manager:
                max_position_value = self.risk_manager.calculate_max_position_size(
                    symbol=signal.symbol,
                    total_capital=self.total_capital
                )
                allocated_capital = min(allocated_capital, max_position_value)
            
            # 计算目标数量（简化：假设每股价格为100）
            estimated_price = 100.0  # 实际中应该从市场数据获取
            target_quantity = allocated_capital / estimated_price
            
            allocation = PositionAllocation(
                strategy_id=strategy_id,
                symbol=signal.symbol,
                target_quantity=target_quantity,
                current_quantity=current_positions.get(strategy_id, 0.0),
                allocated_capital=allocated_capital
            )
            
            allocations.append(allocation)
        
        return allocations
    
    def get_available_capital(self) -> float:
        """获取可用资金"""
        return self.total_capital - self.allocated_capital
    
    def update_allocation(self, allocation: PositionAllocation):
        """更新分配"""
        key = f"{allocation.strategy_id}_{allocation.symbol}"
        old_allocation = self.allocations.get(key)
        
        if old_allocation:
            self.allocated_capital -= old_allocation.allocated_capital
        
        self.allocations[key] = allocation
        self.allocated_capital += allocation.allocated_capital


class StrategyCoordinator:
    """策略协调器"""
    
    def __init__(
        self,
        portfolio_manager: StrategyPortfolioManager,
        risk_manager: Optional[RiskManager] = None,
        aggregation_method: AggregationMethod = AggregationMethod.CONFIDENCE_BASED
    ):
        self.portfolio_manager = portfolio_manager
        self.risk_manager = risk_manager
        self.signal_aggregator = SignalAggregator(aggregation_method)
        self.capital_allocator = CapitalAllocator(
            portfolio_manager.initial_capital, risk_manager
        )
        
        # 策略状态管理
        self.strategy_states: Dict[str, StrategyState] = {}
        self.strategy_communication_queue = queue.Queue()
        
        # 同步机制
        self._lock = threading.Lock()
        
        # 日志
        self.logger = logging.getLogger(f"{__name__}.StrategyCoordinator")
        
        # 回调函数
        self.signal_callbacks: List[Callable] = []
        self.state_callbacks: List[Callable] = []
    
    def coordinate_strategies(self, market_data: Dict[str, Any] = None) -> Dict[str, List[Order]]:
        """
        协调策略执行
        
        Args:
            market_data: 市场数据
            
        Returns:
            策略订单字典
        """
        with self._lock:
            try:
                # 1. 收集各策略信号
                all_signals = self._collect_signals(market_data)
                
                # 2. 聚合信号
                aggregated_signals = self.signal_aggregator.aggregate_signals(all_signals)
                
                # 3. 分配资金
                allocations = self._allocate_capital(aggregated_signals)
                
                # 4. 生成订单
                orders = self._generate_orders(allocations, aggregated_signals)
                
                # 5. 风险控制
                validated_orders = self._apply_risk_control(orders)
                
                # 6. 通知回调
                self._notify_signal_callbacks(aggregated_signals)
                
                return validated_orders
                
            except Exception as e:
                self.logger.error(f"策略协调错误: {e}")
                return {}
    
    def _collect_signals(self, market_data: Dict[str, Any] = None) -> List[PrioritySignal]:
        """收集各策略信号"""
        all_signals = []
        
        for strategy_id, strategy in self.portfolio_manager.strategies.items():
            try:
                # 检查策略状态
                state = self.strategy_states.get(strategy_id, StrategyState.ACTIVE)
                if state != StrategyState.ACTIVE:
                    continue
                
                # 获取策略权重
                allocation = self.portfolio_manager.strategy_allocations.get(strategy_id)
                if not allocation or allocation.weight <= 0:
                    continue
                
                # 生成信号（这里模拟）
                signals = []  # strategy.on_data(market_data) if market_data else []
                
                for signal in signals:
                    priority_signal = PrioritySignal(
                        signal=signal,
                        priority=self._determine_signal_priority(signal),
                        weight=allocation.weight,
                        strategy_id=strategy_id
                    )
                    all_signals.append(priority_signal)
                    
            except Exception as e:
                self.logger.error(f"收集策略 {strategy_id} 信号时出错: {e}")
                self._update_strategy_state(strategy_id, StrategyState.ERROR, str(e))
        
        return all_signals
    
    def _determine_signal_priority(self, signal: Signal) -> SignalPriority:
        """确定信号优先级"""
        # 基于信号类型和置信度确定优先级
        if signal.signal_type in [SignalType.STOP_LOSS]:
            return SignalPriority.EMERGENCY
        elif signal.confidence > 0.8:
            return SignalPriority.HIGH
        elif signal.confidence > 0.6:
            return SignalPriority.MEDIUM
        else:
            return SignalPriority.LOW
    
    def _allocate_capital(self, signals: Dict[str, AggregatedSignal]) -> Dict[str, List[PositionAllocation]]:
        """分配资金"""
        strategy_weights = {
            sid: alloc.weight 
            for sid, alloc in self.portfolio_manager.strategy_allocations.items()
        }
        
        return self.capital_allocator.allocate_capital(signals, strategy_weights)
    
    def _generate_orders(
        self,
        allocations: Dict[str, List[PositionAllocation]],
        signals: Dict[str, AggregatedSignal]
    ) -> Dict[str, List[Order]]:
        """生成订单"""
        orders = {}
        
        for strategy_id, strategy_allocations in allocations.items():
            strategy_orders = []
            
            for allocation in strategy_allocations:
                signal = signals.get(allocation.symbol)
                if not signal:
                    continue
                
                # 计算订单数量
                quantity_diff = allocation.target_quantity - allocation.current_quantity
                
                if abs(quantity_diff) < 1:  # 忽略小于1股的调整
                    continue
                
                # 确定订单方向
                order_side = OrderSide.BUY if quantity_diff > 0 else OrderSide.SELL
                
                # 创建订单（这里简化，实际中需要从具体的交易模型创建）
                order = {
                    'symbol': allocation.symbol,
                    'side': order_side,
                    'quantity': abs(quantity_diff),
                    'strategy_id': strategy_id,
                    'signal_confidence': signal.aggregate_confidence,
                    'timestamp': datetime.now()
                }
                
                strategy_orders.append(order)
            
            if strategy_orders:
                orders[strategy_id] = strategy_orders
        
        return orders
    
    def _apply_risk_control(self, orders: Dict[str, List[Order]]) -> Dict[str, List[Order]]:
        """应用风险控制"""
        if not self.risk_manager:
            return orders
        
        validated_orders = {}
        
        for strategy_id, strategy_orders in orders.items():
            validated_strategy_orders = []
            
            for order in strategy_orders:
                # 简化的风险检查
                try:
                    # 这里应该调用风险管理器的具体检查方法
                    # is_allowed = self.risk_manager.check_order(order, portfolio)
                    is_allowed = True  # 简化
                    
                    if is_allowed:
                        validated_strategy_orders.append(order)
                    else:
                        self.logger.warning(f"订单被风险控制拒绝: {order}")
                        
                except Exception as e:
                    self.logger.error(f"风险检查错误: {e}")
            
            if validated_strategy_orders:
                validated_orders[strategy_id] = validated_strategy_orders
        
        return validated_orders
    
    def update_strategy_state(self, strategy_id: str, state: StrategyState, message: str = ""):
        """更新策略状态"""
        self._update_strategy_state(strategy_id, state, message)
    
    def _update_strategy_state(self, strategy_id: str, state: StrategyState, message: str = ""):
        """内部策略状态更新"""
        old_state = self.strategy_states.get(strategy_id, StrategyState.INACTIVE)
        self.strategy_states[strategy_id] = state
        
        # 创建状态更新事件
        update = StrategyStatusUpdate(
            strategy_id=strategy_id,
            state=state,
            timestamp=datetime.now(),
            message=message,
            metadata={'old_state': old_state}
        )
        
        # 通知回调
        self._notify_state_callbacks(update)
        
        self.logger.info(f"策略 {strategy_id} 状态更新: {old_state} -> {state}, {message}")
    
    def add_signal_callback(self, callback: Callable):
        """添加信号回调"""
        self.signal_callbacks.append(callback)
    
    def add_state_callback(self, callback: Callable):
        """添加状态回调"""
        self.state_callbacks.append(callback)
    
    def _notify_signal_callbacks(self, signals: Dict[str, AggregatedSignal]):
        """通知信号回调"""
        for callback in self.signal_callbacks:
            try:
                callback(signals)
            except Exception as e:
                self.logger.error(f"信号回调错误: {e}")
    
    def _notify_state_callbacks(self, update: StrategyStatusUpdate):
        """通知状态回调"""
        for callback in self.state_callbacks:
            try:
                callback(update)
            except Exception as e:
                self.logger.error(f"状态回调错误: {e}")
    
    def get_coordination_status(self) -> Dict[str, Any]:
        """获取协调状态"""
        return {
            'total_strategies': len(self.portfolio_manager.strategies),
            'active_strategies': sum(1 for state in self.strategy_states.values() 
                                   if state == StrategyState.ACTIVE),
            'strategy_states': {sid: state.value for sid, state in self.strategy_states.items()},
            'available_capital': self.capital_allocator.get_available_capital(),
            'allocated_capital': self.capital_allocator.allocated_capital,
            'last_coordination': datetime.now().isoformat()
        }
    
    def synchronize_strategies(self) -> Dict[str, bool]:
        """同步策略状态"""
        sync_results = {}
        
        for strategy_id, strategy in self.portfolio_manager.strategies.items():
            try:
                # 检查策略是否响应
                # 这里可以实现心跳检查或状态查询
                is_responsive = True  # 简化
                
                if is_responsive:
                    if strategy_id not in self.strategy_states:
                        self.strategy_states[strategy_id] = StrategyState.ACTIVE
                    sync_results[strategy_id] = True
                else:
                    self._update_strategy_state(strategy_id, StrategyState.ERROR, "无响应")
                    sync_results[strategy_id] = False
                    
            except Exception as e:
                self.logger.error(f"同步策略 {strategy_id} 时出错: {e}")
                self._update_strategy_state(strategy_id, StrategyState.ERROR, str(e))
                sync_results[strategy_id] = False
        
        return sync_results