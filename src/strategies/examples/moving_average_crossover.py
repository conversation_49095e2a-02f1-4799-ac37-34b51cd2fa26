"""
移动平均交叉策略

基于快速移动平均线和慢速移动平均线的交叉信号进行交易
"""

from typing import Dict, Any, List, Union
from datetime import datetime
import pandas as pd

from ..base import BaseStrategy, StrategyContext
from ..signals import Signal, SignalType
from ..parameters import StrategyParameter, ParameterType, ParameterValidator
from ...models.market_data import MarketData


class MovingAverageCrossoverStrategy(BaseStrategy):
    """
    移动平均交叉策略
    
    策略逻辑：
    1. 计算快速移动平均线（如5日MA）和慢速移动平均线（如20日MA）
    2. 当快速MA上穿慢速MA时，产生买入信号
    3. 当快速MA下穿慢速MA时，产生卖出信号
    4. 可设置止损和止盈条件
    """
    
    def __init__(self, name: str = "MA_Crossover", parameters: Dict[str, Any] = None):
        super().__init__(name, parameters)
        self.last_fast_ma = None
        self.last_slow_ma = None
        self.last_signal_type = None
        
    def get_parameter_definitions(self) -> Dict[str, StrategyParameter]:
        """获取策略参数定义"""
        return {
            'fast_period': StrategyParameter(
                name='fast_period',
                param_type=ParameterType.INTEGER,
                default_value=5,
                required=True,
                min_value=1,
                max_value=50,
                description='快速移动平均周期',
                validator=ParameterValidator.time_period
            ),
            'slow_period': StrategyParameter(
                name='slow_period',
                param_type=ParameterType.INTEGER,
                default_value=20,
                required=True,
                min_value=2,
                max_value=200,
                description='慢速移动平均周期',
                validator=ParameterValidator.time_period
            ),
            'ma_type': StrategyParameter(
                name='ma_type',
                param_type=ParameterType.STRING,
                default_value='SMA',
                required=False,
                choices=['SMA', 'EMA', 'WMA'],
                description='移动平均类型'
            ),
            'min_crossover_strength': StrategyParameter(
                name='min_crossover_strength',
                param_type=ParameterType.FLOAT,
                default_value=0.001,
                required=False,
                min_value=0.0,
                max_value=0.1,
                description='最小交叉强度（避免假突破）'
            ),
            'stop_loss_pct': StrategyParameter(
                name='stop_loss_pct',
                param_type=ParameterType.FLOAT,
                default_value=0.05,
                required=False,
                min_value=0.01,
                max_value=0.2,
                description='止损百分比'
            ),
            'take_profit_pct': StrategyParameter(
                name='take_profit_pct',
                param_type=ParameterType.FLOAT,
                default_value=0.1,
                required=False,
                min_value=0.02,
                max_value=0.5,
                description='止盈百分比'
            ),
            'lookback_period': StrategyParameter(
                name='lookback_period',
                param_type=ParameterType.INTEGER,
                default_value=100,
                required=False,
                min_value=50,
                max_value=500,
                description='历史数据回看周期'
            )
        }
    
    def initialize(self, context: StrategyContext) -> None:
        """策略初始化"""
        self.context = context
        
        # 验证参数
        fast_period = self.get_parameter('fast_period')
        slow_period = self.get_parameter('slow_period')
        
        if fast_period >= slow_period:
            raise ValueError(f"快速周期({fast_period})必须小于慢速周期({slow_period})")
        
        self.is_initialized = True
        self.logger.info(f"移动平均交叉策略初始化完成: 快速周期={fast_period}, 慢速周期={slow_period}")
    
    def on_data(self, data: Union[MarketData, Dict[str, MarketData]]) -> List[Signal]:
        """处理市场数据并生成交易信号"""
        signals = []
        
        # 处理单个标的数据
        if isinstance(data, MarketData):
            signal = self._process_single_symbol(data.symbol, data)
            if signal:
                signals.append(signal)
        
        # 处理多个标的数据
        elif isinstance(data, dict):
            for symbol, market_data in data.items():
                signal = self._process_single_symbol(symbol, market_data)
                if signal:
                    signals.append(signal)
        
        return signals
    
    def _process_single_symbol(self, symbol: str, current_data: MarketData) -> Signal:
        """处理单个标的的数据"""
        try:
            # 获取历史数据
            lookback_period = self.get_parameter('lookback_period')
            historical_data = self.context.get_historical_data(
                symbol=symbol,
                lookback=lookback_period,
                end_date=current_data.timestamp
            )
            
            if len(historical_data) < self.get_parameter('slow_period'):
                self.logger.warning(f"历史数据不足，无法计算移动平均: {symbol}")
                return None
            
            # 计算移动平均线
            fast_ma, slow_ma = self._calculate_moving_averages(historical_data)
            
            if fast_ma is None or slow_ma is None:
                return None
            
            # 检测交叉信号
            signal = self._detect_crossover_signal(
                symbol=symbol,
                current_price=current_data.close,
                current_time=current_data.timestamp,
                fast_ma=fast_ma,
                slow_ma=slow_ma
            )
            
            # 更新状态
            self.last_fast_ma = fast_ma
            self.last_slow_ma = slow_ma
            
            return signal
            
        except Exception as e:
            self.logger.error(f"处理数据时发生错误 {symbol}: {e}")
            return None
    
    def _calculate_moving_averages(self, data: pd.DataFrame) -> tuple:
        """计算移动平均线"""
        fast_period = self.get_parameter('fast_period')
        slow_period = self.get_parameter('slow_period')
        ma_type = self.get_parameter('ma_type')
        
        try:
            if ma_type == 'SMA':
                fast_ma = self.context.get_indicator('SMA', data, period=fast_period)
                slow_ma = self.context.get_indicator('SMA', data, period=slow_period)
            elif ma_type == 'EMA':
                fast_ma = self.context.get_indicator('EMA', data, period=fast_period)
                slow_ma = self.context.get_indicator('EMA', data, period=slow_period)
            elif ma_type == 'WMA':
                fast_ma = self.context.get_indicator('WMA', data, period=fast_period)
                slow_ma = self.context.get_indicator('WMA', data, period=slow_period)
            else:
                raise ValueError(f"不支持的移动平均类型: {ma_type}")
            
            if len(fast_ma) == 0 or len(slow_ma) == 0:
                return None, None
            
            return fast_ma.iloc[-1], slow_ma.iloc[-1]
            
        except Exception as e:
            self.logger.error(f"计算移动平均线失败: {e}")
            return None, None
    
    def _detect_crossover_signal(self, symbol: str, current_price: float, 
                               current_time: datetime, fast_ma: float, slow_ma: float) -> Signal:
        """检测交叉信号"""
        min_strength = self.get_parameter('min_crossover_strength')
        
        # 需要有历史MA数据才能检测交叉
        if self.last_fast_ma is None or self.last_slow_ma is None:
            return None
        
        # 计算交叉强度
        current_diff = (fast_ma - slow_ma) / slow_ma
        last_diff = (self.last_fast_ma - self.last_slow_ma) / self.last_slow_ma
        
        signal_type = None
        confidence = 0.5
        
        # 检测金叉（快线上穿慢线）
        if last_diff <= 0 and current_diff > min_strength:
            signal_type = SignalType.BUY
            confidence = min(0.9, 0.5 + abs(current_diff) * 10)  # 交叉强度越大，置信度越高
            
        # 检测死叉（快线下穿慢线）
        elif last_diff >= 0 and current_diff < -min_strength:
            signal_type = SignalType.SELL
            confidence = min(0.9, 0.5 + abs(current_diff) * 10)
        
        # 没有有效交叉信号
        if signal_type is None:
            return None
        
        # 避免重复信号
        if signal_type == self.last_signal_type:
            return None
        
        # 计算止损止盈价格
        stop_loss_pct = self.get_parameter('stop_loss_pct')
        take_profit_pct = self.get_parameter('take_profit_pct')
        
        if signal_type == SignalType.BUY:
            stop_loss = current_price * (1 - stop_loss_pct)
            take_profit = current_price * (1 + take_profit_pct)
        else:  # SELL
            stop_loss = current_price * (1 + stop_loss_pct)
            take_profit = current_price * (1 - take_profit_pct)
        
        # 创建信号
        signal = Signal(
            symbol=symbol,
            signal_type=signal_type,
            timestamp=current_time,
            strategy_name=self.name,
            confidence=confidence,
            price=current_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            metadata={
                'fast_ma': fast_ma,
                'slow_ma': slow_ma,
                'crossover_strength': abs(current_diff),
                'ma_type': self.get_parameter('ma_type'),
                'fast_period': self.get_parameter('fast_period'),
                'slow_period': self.get_parameter('slow_period')
            }
        )
        
        # 更新最后信号类型
        self.last_signal_type = signal_type
        
        self.logger.info(f"生成交叉信号: {symbol} {signal_type.value} @ {current_price:.2f}, "
                        f"置信度: {confidence:.2f}, 快线: {fast_ma:.2f}, 慢线: {slow_ma:.2f}")
        
        return signal
    
    def on_order_fill(self, fill_info: Dict[str, Any]) -> None:
        """订单成交后的处理"""
        super().on_order_fill(fill_info)
        
        symbol = fill_info.get('symbol')
        side = fill_info.get('side')
        price = fill_info.get('price')
        quantity = fill_info.get('quantity')
        
        self.logger.info(f"订单成交: {symbol} {side} {quantity}@{price}")
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.name,
            'type': 'Trend Following',
            'description': '基于移动平均线交叉的趋势跟踪策略',
            'parameters': self.parameters,
            'last_fast_ma': self.last_fast_ma,
            'last_slow_ma': self.last_slow_ma,
            'last_signal_type': self.last_signal_type.value if self.last_signal_type else None
        }