"""
多因子组合策略

结合多个技术指标的综合交易策略
"""

from typing import Dict, Any, List, Union
from datetime import datetime
import pandas as pd
import numpy as np

from ..base import BaseStrategy, StrategyContext
from ..signals import Signal, SignalType
from ..parameters import StrategyParameter, ParameterType, ParameterValidator
from ...models.market_data import MarketData


class MultiFactorStrategy(BaseStrategy):
    """
    多因子组合策略
    
    策略逻辑：
    1. 计算多个技术指标（MA、RSI、MACD、布林带等）
    2. 为每个指标分配权重
    3. 计算综合信号强度
    4. 当综合信号强度超过阈值时产生交易信号
    """
    
    def __init__(self, name: str = "MultiFactorStrategy", parameters: Dict[str, Any] = None):
        super().__init__(name, parameters)
        self.last_indicators = {}
        self.signal_history = []
        
    def get_parameter_definitions(self) -> Dict[str, StrategyParameter]:
        """获取策略参数定义"""
        return {
            # 移动平均参数
            'ma_fast_period': StrategyParameter(
                name='ma_fast_period',
                param_type=ParameterType.INTEGER,
                default_value=10,
                required=True,
                min_value=5,
                max_value=30,
                description='快速移动平均周期'
            ),
            'ma_slow_period': StrategyParameter(
                name='ma_slow_period',
                param_type=ParameterType.INTEGER,
                default_value=30,
                required=True,
                min_value=15,
                max_value=100,
                description='慢速移动平均周期'
            ),
            'ma_weight': StrategyParameter(
                name='ma_weight',
                param_type=ParameterType.FLOAT,
                default_value=0.3,
                required=True,
                min_value=0.0,
                max_value=1.0,
                description='移动平均指标权重'
            ),
            
            # RSI参数
            'rsi_period': StrategyParameter(
                name='rsi_period',
                param_type=ParameterType.INTEGER,
                default_value=14,
                required=True,
                min_value=5,
                max_value=30,
                description='RSI计算周期'
            ),
            'rsi_oversold': StrategyParameter(
                name='rsi_oversold',
                param_type=ParameterType.FLOAT,
                default_value=30.0,
                required=True,
                min_value=10.0,
                max_value=40.0,
                description='RSI超卖阈值'
            ),
            'rsi_overbought': StrategyParameter(
                name='rsi_overbought',
                param_type=ParameterType.FLOAT,
                default_value=70.0,
                required=True,
                min_value=60.0,
                max_value=90.0,
                description='RSI超买阈值'
            ),
            'rsi_weight': StrategyParameter(
                name='rsi_weight',
                param_type=ParameterType.FLOAT,
                default_value=0.25,
                required=True,
                min_value=0.0,
                max_value=1.0,
                description='RSI指标权重'
            ),
            
            # MACD参数
            'macd_fast': StrategyParameter(
                name='macd_fast',
                param_type=ParameterType.INTEGER,
                default_value=12,
                required=True,
                min_value=5,
                max_value=20,
                description='MACD快线周期'
            ),
            'macd_slow': StrategyParameter(
                name='macd_slow',
                param_type=ParameterType.INTEGER,
                default_value=26,
                required=True,
                min_value=15,
                max_value=40,
                description='MACD慢线周期'
            ),
            'macd_signal': StrategyParameter(
                name='macd_signal',
                param_type=ParameterType.INTEGER,
                default_value=9,
                required=True,
                min_value=5,
                max_value=15,
                description='MACD信号线周期'
            ),
            'macd_weight': StrategyParameter(
                name='macd_weight',
                param_type=ParameterType.FLOAT,
                default_value=0.25,
                required=True,
                min_value=0.0,
                max_value=1.0,
                description='MACD指标权重'
            ),
            
            # 布林带参数
            'bb_period': StrategyParameter(
                name='bb_period',
                param_type=ParameterType.INTEGER,
                default_value=20,
                required=True,
                min_value=10,
                max_value=50,
                description='布林带周期'
            ),
            'bb_std': StrategyParameter(
                name='bb_std',
                param_type=ParameterType.FLOAT,
                default_value=2.0,
                required=True,
                min_value=1.0,
                max_value=3.0,
                description='布林带标准差倍数'
            ),
            'bb_weight': StrategyParameter(
                name='bb_weight',
                param_type=ParameterType.FLOAT,
                default_value=0.2,
                required=True,
                min_value=0.0,
                max_value=1.0,
                description='布林带指标权重'
            ),
            
            # 信号阈值
            'buy_threshold': StrategyParameter(
                name='buy_threshold',
                param_type=ParameterType.FLOAT,
                default_value=0.6,
                required=True,
                min_value=0.3,
                max_value=0.9,
                description='买入信号阈值'
            ),
            'sell_threshold': StrategyParameter(
                name='sell_threshold',
                param_type=ParameterType.FLOAT,
                default_value=-0.6,
                required=True,
                min_value=-0.9,
                max_value=-0.3,
                description='卖出信号阈值'
            ),
            
            # 风险管理
            'stop_loss_pct': StrategyParameter(
                name='stop_loss_pct',
                param_type=ParameterType.FLOAT,
                default_value=0.05,
                required=False,
                min_value=0.01,
                max_value=0.15,
                description='止损百分比'
            ),
            'take_profit_pct': StrategyParameter(
                name='take_profit_pct',
                param_type=ParameterType.FLOAT,
                default_value=0.1,
                required=False,
                min_value=0.02,
                max_value=0.3,
                description='止盈百分比'
            ),
            'lookback_period': StrategyParameter(
                name='lookback_period',
                param_type=ParameterType.INTEGER,
                default_value=150,
                required=False,
                min_value=100,
                max_value=500,
                description='历史数据回看周期'
            )
        }
    
    def initialize(self, context: StrategyContext) -> None:
        """策略初始化"""
        self.context = context
        
        # 验证权重总和
        total_weight = (self.get_parameter('ma_weight') + 
                       self.get_parameter('rsi_weight') + 
                       self.get_parameter('macd_weight') + 
                       self.get_parameter('bb_weight'))
        
        if abs(total_weight - 1.0) > 0.01:
            self.logger.warning(f"指标权重总和不等于1.0: {total_weight:.3f}")
        
        # 验证阈值
        buy_threshold = self.get_parameter('buy_threshold')
        sell_threshold = self.get_parameter('sell_threshold')
        
        if buy_threshold <= abs(sell_threshold):
            self.logger.warning(f"买入阈值({buy_threshold})应该大于卖出阈值绝对值({abs(sell_threshold)})")
        
        self.is_initialized = True
        self.logger.info(f"多因子策略初始化完成，权重分配: "
                        f"MA={self.get_parameter('ma_weight'):.2f}, "
                        f"RSI={self.get_parameter('rsi_weight'):.2f}, "
                        f"MACD={self.get_parameter('macd_weight'):.2f}, "
                        f"BB={self.get_parameter('bb_weight'):.2f}")
    
    def on_data(self, data: Union[MarketData, Dict[str, MarketData]]) -> List[Signal]:
        """处理市场数据并生成交易信号"""
        signals = []
        
        # 处理单个标的数据
        if isinstance(data, MarketData):
            signal = self._process_single_symbol(data.symbol, data)
            if signal:
                signals.append(signal)
        
        # 处理多个标的数据
        elif isinstance(data, dict):
            for symbol, market_data in data.items():
                signal = self._process_single_symbol(symbol, market_data)
                if signal:
                    signals.append(signal)
        
        return signals
    
    def _process_single_symbol(self, symbol: str, current_data: MarketData) -> Signal:
        """处理单个标的的数据"""
        try:
            # 获取历史数据
            lookback_period = self.get_parameter('lookback_period')
            historical_data = self.context.get_historical_data(
                symbol=symbol,
                lookback=lookback_period,
                end_date=current_data.timestamp
            )
            
            if len(historical_data) < max(self.get_parameter('ma_slow_period'),
                                        self.get_parameter('bb_period')) + 20:
                self.logger.warning(f"历史数据不足，无法计算指标: {symbol}")
                return None
            
            # 计算所有指标
            indicators = self._calculate_all_indicators(historical_data)
            
            if not indicators:
                return None
            
            # 计算综合信号强度
            signal_strength = self._calculate_signal_strength(indicators)
            
            # 生成交易信号
            signal = self._generate_multi_factor_signal(
                symbol=symbol,
                current_price=current_data.close,
                current_time=current_data.timestamp,
                signal_strength=signal_strength,
                indicators=indicators
            )
            
            # 更新状态
            self.last_indicators[symbol] = indicators
            
            return signal
            
        except Exception as e:
            self.logger.error(f"处理多因子数据时发生错误 {symbol}: {e}")
            return None
    
    def _calculate_all_indicators(self, data: pd.DataFrame) -> Dict[str, Any]:
        """计算所有技术指标"""
        indicators = {}
        
        try:
            # 移动平均
            ma_fast = self.context.get_indicator('SMA', data, period=self.get_parameter('ma_fast_period'))
            ma_slow = self.context.get_indicator('SMA', data, period=self.get_parameter('ma_slow_period'))
            
            if len(ma_fast) > 0 and len(ma_slow) > 0:
                indicators['ma_fast'] = ma_fast.iloc[-1]
                indicators['ma_slow'] = ma_slow.iloc[-1]
                indicators['ma_signal'] = 1 if ma_fast.iloc[-1] > ma_slow.iloc[-1] else -1
            
            # RSI
            rsi = self.context.get_indicator('RSI', data, period=self.get_parameter('rsi_period'))
            if len(rsi) > 0:
                rsi_value = rsi.iloc[-1]
                indicators['rsi'] = rsi_value
                
                # RSI信号：超卖买入，超买卖出
                if rsi_value <= self.get_parameter('rsi_oversold'):
                    indicators['rsi_signal'] = 1  # 买入
                elif rsi_value >= self.get_parameter('rsi_overbought'):
                    indicators['rsi_signal'] = -1  # 卖出
                else:
                    indicators['rsi_signal'] = 0  # 中性
            
            # MACD
            macd_result = self.context.get_indicator(
                'MACD', data,
                fast_period=self.get_parameter('macd_fast'),
                slow_period=self.get_parameter('macd_slow'),
                signal_period=self.get_parameter('macd_signal')
            )
            
            if len(macd_result) > 0:
                if isinstance(macd_result, pd.DataFrame):
                    macd_line = macd_result['MACD'].iloc[-1]
                    signal_line = macd_result['Signal'].iloc[-1]
                else:
                    macd_line = macd_result.iloc[-1]
                    signal_line = 0  # 简化处理
                
                indicators['macd'] = macd_line
                indicators['macd_signal_line'] = signal_line
                indicators['macd_signal'] = 1 if macd_line > signal_line else -1
            
            # 布林带
            bb_result = self.context.get_indicator(
                'BOLLINGER_BANDS', data,
                period=self.get_parameter('bb_period'),
                std_dev=self.get_parameter('bb_std')
            )
            
            if len(bb_result) > 0:
                current_price = data['close'].iloc[-1]
                
                if isinstance(bb_result, pd.DataFrame):
                    upper_band = bb_result['Upper'].iloc[-1]
                    lower_band = bb_result['Lower'].iloc[-1]
                    middle_band = bb_result['Middle'].iloc[-1]
                else:
                    # 简化处理
                    middle_band = bb_result.iloc[-1]
                    std_dev = data['close'].rolling(self.get_parameter('bb_period')).std().iloc[-1]
                    upper_band = middle_band + self.get_parameter('bb_std') * std_dev
                    lower_band = middle_band - self.get_parameter('bb_std') * std_dev
                
                indicators['bb_upper'] = upper_band
                indicators['bb_lower'] = lower_band
                indicators['bb_middle'] = middle_band
                
                # 布林带信号：价格接近下轨买入，接近上轨卖出
                bb_position = (current_price - lower_band) / (upper_band - lower_band)
                if bb_position <= 0.2:
                    indicators['bb_signal'] = 1  # 买入
                elif bb_position >= 0.8:
                    indicators['bb_signal'] = -1  # 卖出
                else:
                    indicators['bb_signal'] = 0  # 中性
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"计算指标失败: {e}")
            return {}
    
    def _calculate_signal_strength(self, indicators: Dict[str, Any]) -> float:
        """计算综合信号强度"""
        signal_strength = 0.0
        
        # 移动平均信号
        if 'ma_signal' in indicators:
            ma_strength = indicators['ma_signal'] * self.get_parameter('ma_weight')
            signal_strength += ma_strength
        
        # RSI信号
        if 'rsi_signal' in indicators:
            rsi_strength = indicators['rsi_signal'] * self.get_parameter('rsi_weight')
            signal_strength += rsi_strength
        
        # MACD信号
        if 'macd_signal' in indicators:
            macd_strength = indicators['macd_signal'] * self.get_parameter('macd_weight')
            signal_strength += macd_strength
        
        # 布林带信号
        if 'bb_signal' in indicators:
            bb_strength = indicators['bb_signal'] * self.get_parameter('bb_weight')
            signal_strength += bb_strength
        
        return signal_strength
    
    def _generate_multi_factor_signal(self, symbol: str, current_price: float,
                                    current_time: datetime, signal_strength: float,
                                    indicators: Dict[str, Any]) -> Signal:
        """基于多因子分析生成交易信号"""
        buy_threshold = self.get_parameter('buy_threshold')
        sell_threshold = self.get_parameter('sell_threshold')
        
        signal_type = None
        confidence = 0.5
        
        # 判断信号类型
        if signal_strength >= buy_threshold:
            signal_type = SignalType.BUY
            # 信号强度越高，置信度越高
            confidence = min(0.95, 0.6 + (signal_strength - buy_threshold) / (1.0 - buy_threshold) * 0.35)
            
        elif signal_strength <= sell_threshold:
            signal_type = SignalType.SELL
            # 信号强度越低（越负），置信度越高
            confidence = min(0.95, 0.6 + (sell_threshold - signal_strength) / (1.0 + sell_threshold) * 0.35)
        
        # 没有有效信号
        if signal_type is None:
            return None
        
        # 计算止损止盈价格
        stop_loss_pct = self.get_parameter('stop_loss_pct')
        take_profit_pct = self.get_parameter('take_profit_pct')
        
        if signal_type == SignalType.BUY:
            stop_loss = current_price * (1 - stop_loss_pct)
            take_profit = current_price * (1 + take_profit_pct)
        else:  # SELL
            stop_loss = current_price * (1 + stop_loss_pct)
            take_profit = current_price * (1 - take_profit_pct)
        
        # 创建信号
        signal = Signal(
            symbol=symbol,
            signal_type=signal_type,
            timestamp=current_time,
            strategy_name=self.name,
            confidence=confidence,
            price=current_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            metadata={
                'signal_strength': signal_strength,
                'indicators': indicators,
                'weights': {
                    'ma_weight': self.get_parameter('ma_weight'),
                    'rsi_weight': self.get_parameter('rsi_weight'),
                    'macd_weight': self.get_parameter('macd_weight'),
                    'bb_weight': self.get_parameter('bb_weight')
                },
                'thresholds': {
                    'buy_threshold': buy_threshold,
                    'sell_threshold': sell_threshold
                }
            }
        )
        
        self.logger.info(f"生成多因子信号: {symbol} {signal_type.value} @ {current_price:.2f}, "
                        f"信号强度: {signal_strength:.3f}, 置信度: {confidence:.2f}")
        
        return signal
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.name,
            'type': 'Multi-Factor',
            'description': '基于多个技术指标的综合交易策略',
            'parameters': self.parameters,
            'last_indicators': self.last_indicators,
            'factor_weights': {
                'ma_weight': self.get_parameter('ma_weight'),
                'rsi_weight': self.get_parameter('rsi_weight'),
                'macd_weight': self.get_parameter('macd_weight'),
                'bb_weight': self.get_parameter('bb_weight')
            },
            'signal_thresholds': {
                'buy_threshold': self.get_parameter('buy_threshold'),
                'sell_threshold': self.get_parameter('sell_threshold')
            }
        }