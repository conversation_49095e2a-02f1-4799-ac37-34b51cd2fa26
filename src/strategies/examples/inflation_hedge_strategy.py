"""
通胀对冲策略

基于通胀预期和实际通胀数据调整投资组合以对冲通胀风险
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import logging

try:
    from ..economic_base import EconomicStrategy, EconomicContext
    from ..signals import Signal
    from ..parameters import StrategyParameter, ParameterType, ParameterValidator
    from ...models.market_data import MarketData
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from strategies.economic_base import EconomicStrategy, EconomicContext
    from strategies.signals import Signal
    from strategies.parameters import StrategyParameter, ParameterType, ParameterValidator
    from models.market_data import MarketData


class InflationHedgeStrategy(EconomicStrategy):
    """
    通胀对冲策略
    
    根据通胀环境动态调整投资组合，重点配置通胀对冲资产
    """
    
    def __init__(self, name: str = "Inflation Hedge Strategy", 
                 parameters: Optional[Dict[str, Any]] = None):
        super().__init__(name, parameters)
        
        # 通胀对冲资产配置
        self.hedge_assets = {
            'commodities': {
                'symbols': ['GLD', 'SLV', 'DJP'],  # 黄金、白银、商品ETF
                'weight_range': (0.1, 0.4),
                'inflation_sensitivity': 0.8
            },
            'real_estate': {
                'symbols': ['VNQ', 'SCHH'],  # 房地产ETF
                'weight_range': (0.05, 0.25),
                'inflation_sensitivity': 0.6
            },
            'tips': {
                'symbols': ['SCHP', 'VTIP'],  # 通胀保护债券
                'weight_range': (0.1, 0.3),
                'inflation_sensitivity': 0.9
            },
            'energy': {
                'symbols': ['XLE', 'VDE'],  # 能源股ETF
                'weight_range': (0.0, 0.2),
                'inflation_sensitivity': 0.7
            },
            'materials': {
                'symbols': ['XLB', 'VAW'],  # 材料股ETF
                'weight_range': (0.0, 0.15),
                'inflation_sensitivity': 0.5
            }
        }
        
        # 通胀环境分类
        self.inflation_regimes = {
            'DEFLATION': {'threshold': 0, 'description': '通缩环境'},
            'LOW_INFLATION': {'threshold': 2, 'description': '低通胀环境'},
            'MODERATE_INFLATION': {'threshold': 4, 'description': '温和通胀环境'},
            'HIGH_INFLATION': {'threshold': 6, 'description': '高通胀环境'},
            'HYPERINFLATION': {'threshold': 10, 'description': '恶性通胀环境'}
        }
        
        self.logger = logging.getLogger(f"{__name__}.InflationHedgeStrategy")
    
    def initialize(self, context: EconomicContext) -> None:
        """
        初始化通胀对冲策略
        
        Args:
            context: 经济策略上下文
        """
        super().initialize(context)
        self.logger.info(f"通胀对冲策略 {self.name} 初始化完成")
    
    def get_economic_indicators(self) -> List[str]:
        """获取通胀分析所需的经济指标"""
        return [
            # 通胀指标
            'CPIAUCSL',     # 消费者价格指数
            'CPILFESL',     # 核心CPI（不含食品和能源）
            'PCEPI',        # PCE价格指数
            'PCEPILFE',     # 核心PCE价格指数
            
            # 通胀预期指标
            'T5YIE',        # 5年通胀预期
            'T10YIE',       # 10年通胀预期
            'T5YIFR',       # 5年5年远期通胀预期
            
            # 货币政策指标
            'FEDFUNDS',     # 联邦基金利率
            'DGS10',        # 10年期国债收益率
            'DGS5',         # 5年期国债收益率
            
            # 货币供应量
            'M1SL',         # M1货币供应量
            'M2SL',         # M2货币供应量
            'BOGMBASE',     # 货币基础
            
            # 商品价格
            'DCOILWTICO',   # 原油价格
            'GOLDAMGBD228NLBM',  # 黄金价格
            'DHHNGSP',      # 天然气价格
            
            # 工资和收入
            'AHETPI',       # 平均时薪
            'MEHOINUSA672N', # 家庭收入中位数
            
            # 房价
            'CSUSHPISA',    # Case-Shiller房价指数
            
            # 美元指数
            'DTWEXBGS'      # 美元贸易加权指数
        ]
    
    def analyze_economic_conditions(self, economic_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        分析通胀环境和趋势
        """
        analysis = {
            'current_inflation_rate': 0.0,
            'core_inflation_rate': 0.0,
            'inflation_regime': 'LOW_INFLATION',
            'inflation_trend': 'STABLE',
            'inflation_expectations': {},
            'monetary_policy_stance': 'NEUTRAL',
            'commodity_pressure': 'NEUTRAL',
            'wage_pressure': 'NEUTRAL',
            'currency_impact': 'NEUTRAL',
            'hedge_recommendations': {},
            'risk_level': 'MEDIUM'
        }
        
        try:
            # 1. 当前通胀率分析
            analysis['current_inflation_rate'] = self._calculate_current_inflation(economic_data)
            analysis['core_inflation_rate'] = self._calculate_core_inflation(economic_data)
            
            # 2. 通胀环境分类
            analysis['inflation_regime'] = self._classify_inflation_regime(analysis['current_inflation_rate'])
            
            # 3. 通胀趋势分析
            analysis['inflation_trend'] = self._analyze_inflation_trend(economic_data)
            
            # 4. 通胀预期分析
            analysis['inflation_expectations'] = self._analyze_inflation_expectations(economic_data)
            
            # 5. 货币政策立场
            analysis['monetary_policy_stance'] = self._analyze_monetary_policy(economic_data)
            
            # 6. 商品价格压力
            analysis['commodity_pressure'] = self._analyze_commodity_pressure(economic_data)
            
            # 7. 工资压力分析
            analysis['wage_pressure'] = self._analyze_wage_pressure(economic_data)
            
            # 8. 货币影响分析
            analysis['currency_impact'] = self._analyze_currency_impact(economic_data)
            
            # 9. 对冲建议
            analysis['hedge_recommendations'] = self._generate_hedge_recommendations(analysis)
            
            # 10. 风险等级评估
            analysis['risk_level'] = self._assess_inflation_risk(analysis)
            
        except Exception as e:
            self.logger.error(f"通胀环境分析失败: {e}")
        
        return analysis
    
    def _calculate_current_inflation(self, economic_data: Dict[str, pd.DataFrame]) -> float:
        """计算当前通胀率"""
        try:
            cpi_data = economic_data.get('CPIAUCSL')
            if cpi_data is None or cpi_data.empty or len(cpi_data) < 12:
                return 0.0
            
            current_cpi = cpi_data['value'].iloc[-1]
            year_ago_cpi = cpi_data['value'].iloc[-12]
            
            inflation_rate = (current_cpi - year_ago_cpi) / year_ago_cpi * 100
            return round(inflation_rate, 2)
            
        except Exception as e:
            self.logger.error(f"当前通胀率计算失败: {e}")
            return 0.0
    
    def _calculate_core_inflation(self, economic_data: Dict[str, pd.DataFrame]) -> float:
        """计算核心通胀率"""
        try:
            core_cpi_data = economic_data.get('CPILFESL')
            if core_cpi_data is None or core_cpi_data.empty or len(core_cpi_data) < 12:
                return 0.0
            
            current_core_cpi = core_cpi_data['value'].iloc[-1]
            year_ago_core_cpi = core_cpi_data['value'].iloc[-12]
            
            core_inflation_rate = (current_core_cpi - year_ago_core_cpi) / year_ago_core_cpi * 100
            return round(core_inflation_rate, 2)
            
        except Exception as e:
            self.logger.error(f"核心通胀率计算失败: {e}")
            return 0.0
    
    def _classify_inflation_regime(self, inflation_rate: float) -> str:
        """分类通胀环境"""
        for regime, config in self.inflation_regimes.items():
            if inflation_rate <= config['threshold']:
                return regime
        return 'HYPERINFLATION'
    
    def _analyze_inflation_trend(self, economic_data: Dict[str, pd.DataFrame]) -> str:
        """分析通胀趋势"""
        try:
            cpi_trend = self.economic_context.get_economic_trend('CPIAUCSL', 90)
            core_cpi_trend = self.economic_context.get_economic_trend('CPILFESL', 90)
            
            # 综合判断趋势
            if cpi_trend == 'RISING' and core_cpi_trend == 'RISING':
                return 'ACCELERATING'
            elif cpi_trend == 'FALLING' and core_cpi_trend == 'FALLING':
                return 'DECELERATING'
            elif cpi_trend == 'RISING' or core_cpi_trend == 'RISING':
                return 'MIXED_UP'
            elif cpi_trend == 'FALLING' or core_cpi_trend == 'FALLING':
                return 'MIXED_DOWN'
            else:
                return 'STABLE'
                
        except Exception as e:
            self.logger.error(f"通胀趋势分析失败: {e}")
            return 'STABLE'
    
    def _analyze_inflation_expectations(self, economic_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """分析通胀预期"""
        expectations = {}
        
        try:
            # 5年通胀预期
            if 'T5YIE' in economic_data and not economic_data['T5YIE'].empty:
                expectations['5_year'] = {
                    'current': economic_data['T5YIE']['value'].iloc[-1],
                    'trend': self.economic_context.get_economic_trend('T5YIE', 90)
                }
            
            # 10年通胀预期
            if 'T10YIE' in economic_data and not economic_data['T10YIE'].empty:
                expectations['10_year'] = {
                    'current': economic_data['T10YIE']['value'].iloc[-1],
                    'trend': self.economic_context.get_economic_trend('T10YIE', 90)
                }
            
            # 5年5年远期通胀预期
            if 'T5YIFR' in economic_data and not economic_data['T5YIFR'].empty:
                expectations['5y5y_forward'] = {
                    'current': economic_data['T5YIFR']['value'].iloc[-1],
                    'trend': self.economic_context.get_economic_trend('T5YIFR', 90)
                }
            
        except Exception as e:
            self.logger.error(f"通胀预期分析失败: {e}")
        
        return expectations
    
    def _analyze_monetary_policy(self, economic_data: Dict[str, pd.DataFrame]) -> str:
        """分析货币政策立场"""
        try:
            fed_funds_trend = self.economic_context.get_economic_trend('FEDFUNDS', 180)
            m2_trend = self.economic_context.get_economic_trend('M2SL', 180)
            
            # 综合判断货币政策立场
            if fed_funds_trend == 'RISING':
                return 'TIGHTENING'
            elif fed_funds_trend == 'FALLING':
                return 'EASING'
            elif m2_trend == 'RISING':
                return 'ACCOMMODATIVE'
            else:
                return 'NEUTRAL'
                
        except Exception as e:
            self.logger.error(f"货币政策分析失败: {e}")
            return 'NEUTRAL'
    
    def _analyze_commodity_pressure(self, economic_data: Dict[str, pd.DataFrame]) -> str:
        """分析商品价格压力"""
        try:
            pressure_score = 0
            indicators = 0
            
            # 原油价格趋势
            if 'DCOILWTICO' in economic_data:
                oil_trend = self.economic_context.get_economic_trend('DCOILWTICO', 90)
                if oil_trend == 'RISING':
                    pressure_score += 1
                elif oil_trend == 'FALLING':
                    pressure_score -= 1
                indicators += 1
            
            # 黄金价格趋势
            if 'GOLDAMGBD228NLBM' in economic_data:
                gold_trend = self.economic_context.get_economic_trend('GOLDAMGBD228NLBM', 90)
                if gold_trend == 'RISING':
                    pressure_score += 1
                elif gold_trend == 'FALLING':
                    pressure_score -= 1
                indicators += 1
            
            if indicators == 0:
                return 'NEUTRAL'
            
            avg_pressure = pressure_score / indicators
            
            if avg_pressure > 0.5:
                return 'HIGH'
            elif avg_pressure < -0.5:
                return 'LOW'
            else:
                return 'NEUTRAL'
                
        except Exception as e:
            self.logger.error(f"商品压力分析失败: {e}")
            return 'NEUTRAL'
    
    def _analyze_wage_pressure(self, economic_data: Dict[str, pd.DataFrame]) -> str:
        """分析工资压力"""
        try:
            if 'AHETPI' in economic_data and not economic_data['AHETPI'].empty:
                wage_trend = self.economic_context.get_economic_trend('AHETPI', 180)
                
                if wage_trend == 'RISING':
                    return 'HIGH'
                elif wage_trend == 'FALLING':
                    return 'LOW'
                else:
                    return 'NEUTRAL'
            
            return 'NEUTRAL'
            
        except Exception as e:
            self.logger.error(f"工资压力分析失败: {e}")
            return 'NEUTRAL'
    
    def _analyze_currency_impact(self, economic_data: Dict[str, pd.DataFrame]) -> str:
        """分析货币影响"""
        try:
            if 'DTWEXBGS' in economic_data and not economic_data['DTWEXBGS'].empty:
                dollar_trend = self.economic_context.get_economic_trend('DTWEXBGS', 90)
                
                # 美元走强通常抑制通胀，美元走弱推高通胀
                if dollar_trend == 'RISING':
                    return 'DEFLATIONARY'
                elif dollar_trend == 'FALLING':
                    return 'INFLATIONARY'
                else:
                    return 'NEUTRAL'
            
            return 'NEUTRAL'
            
        except Exception as e:
            self.logger.error(f"货币影响分析失败: {e}")
            return 'NEUTRAL'
    
    def _generate_hedge_recommendations(self, analysis: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """生成通胀对冲建议"""
        recommendations = {}
        
        try:
            inflation_regime = analysis.get('inflation_regime', 'LOW_INFLATION')
            inflation_trend = analysis.get('inflation_trend', 'STABLE')
            
            for asset_class, config in self.hedge_assets.items():
                # 基础权重
                min_weight, max_weight = config['weight_range']
                base_weight = (min_weight + max_weight) / 2
                
                # 根据通胀环境调整权重
                if inflation_regime in ['HIGH_INFLATION', 'HYPERINFLATION']:
                    weight_multiplier = 1.5
                elif inflation_regime == 'MODERATE_INFLATION':
                    weight_multiplier = 1.2
                elif inflation_regime == 'LOW_INFLATION':
                    weight_multiplier = 0.8
                else:  # DEFLATION
                    weight_multiplier = 0.5
                
                # 根据趋势调整
                if inflation_trend == 'ACCELERATING':
                    weight_multiplier *= 1.3
                elif inflation_trend == 'DECELERATING':
                    weight_multiplier *= 0.7
                
                # 计算推荐权重
                recommended_weight = min(max_weight, max(min_weight, base_weight * weight_multiplier))
                
                recommendations[asset_class] = {
                    'recommended_weight': recommended_weight,
                    'symbols': config['symbols'],
                    'rationale': self._get_asset_rationale(asset_class, inflation_regime, inflation_trend),
                    'sensitivity': config['inflation_sensitivity']
                }
            
        except Exception as e:
            self.logger.error(f"对冲建议生成失败: {e}")
        
        return recommendations
    
    def _get_asset_rationale(self, asset_class: str, regime: str, trend: str) -> str:
        """获取资产配置理由"""
        rationales = {
            'commodities': f"商品在{regime}环境下通常表现良好，趋势为{trend}",
            'real_estate': f"房地产在通胀环境下具有保值功能，当前{regime}",
            'tips': f"通胀保护债券直接对冲通胀风险，适合{regime}环境",
            'energy': f"能源股在通胀上升期表现较好，趋势{trend}",
            'materials': f"材料股受益于商品价格上涨，当前环境{regime}"
        }
        return rationales.get(asset_class, "通胀对冲资产")
    
    def _assess_inflation_risk(self, analysis: Dict[str, Any]) -> str:
        """评估通胀风险等级"""
        try:
            risk_score = 0
            
            # 当前通胀率风险
            inflation_rate = analysis.get('current_inflation_rate', 0)
            if inflation_rate > 4:
                risk_score += 2
            elif inflation_rate > 2:
                risk_score += 1
            
            # 趋势风险
            trend = analysis.get('inflation_trend', 'STABLE')
            if trend == 'ACCELERATING':
                risk_score += 2
            elif trend in ['MIXED_UP', 'RISING']:
                risk_score += 1
            
            # 预期风险
            expectations = analysis.get('inflation_expectations', {})
            if expectations:
                avg_expectation = np.mean([
                    exp.get('current', 0) for exp in expectations.values()
                    if isinstance(exp, dict) and 'current' in exp
                ])
                if avg_expectation > 3:
                    risk_score += 1
            
            # 商品压力风险
            if analysis.get('commodity_pressure') == 'HIGH':
                risk_score += 1
            
            # 工资压力风险
            if analysis.get('wage_pressure') == 'HIGH':
                risk_score += 1
            
            # 风险等级判断
            if risk_score >= 5:
                return 'VERY_HIGH'
            elif risk_score >= 3:
                return 'HIGH'
            elif risk_score >= 1:
                return 'MEDIUM'
            else:
                return 'LOW'
                
        except Exception as e:
            self.logger.error(f"通胀风险评估失败: {e}")
            return 'MEDIUM'
    
    def on_data(self, data: Union[MarketData, Dict[str, MarketData]]) -> List[Signal]:
        """
        基于通胀分析生成对冲信号
        """
        try:
            # 获取经济数据
            economic_data = self.get_economic_data_for_analysis()
            if not economic_data:
                return []
            
            # 分析通胀环境
            inflation_analysis = self.analyze_economic_conditions(economic_data)
            
            # 生成对冲信号
            signals = self._generate_hedge_signals(data, inflation_analysis)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"通胀对冲策略数据处理失败: {e}")
            return []
    
    def _generate_hedge_signals(self, market_data: Union[MarketData, Dict[str, MarketData]],
                              inflation_analysis: Dict[str, Any]) -> List[Signal]:
        """生成通胀对冲信号"""
        signals = []
        
        try:
            hedge_recommendations = inflation_analysis.get('hedge_recommendations', {})
            risk_level = inflation_analysis.get('risk_level', 'MEDIUM')
            
            # 只有在风险等级足够高时才生成信号
            min_risk_levels = ['MEDIUM', 'HIGH', 'VERY_HIGH']
            if risk_level not in min_risk_levels:
                return signals
            
            # 为每个对冲资产类别生成信号
            for asset_class, recommendation in hedge_recommendations.items():
                symbols = recommendation.get('symbols', [])
                target_weight = recommendation.get('recommended_weight', 0)
                
                for symbol in symbols:
                    # 获取市场数据
                    if isinstance(market_data, dict):
                        symbol_data = market_data.get(symbol)
                    else:
                        symbol_data = market_data if market_data.symbol == symbol else None
                    
                    if symbol_data:
                        signal = self._create_hedge_signal(
                            symbol, symbol_data, asset_class, target_weight, 
                            inflation_analysis
                        )
                        if signal:
                            signals.append(signal)
            
        except Exception as e:
            self.logger.error(f"对冲信号生成失败: {e}")
        
        return signals
    
    def _create_hedge_signal(self, symbol: str, market_data: MarketData,
                           asset_class: str, target_weight: float,
                           inflation_analysis: Dict[str, Any]) -> Optional[Signal]:
        """创建通胀对冲信号"""
        try:
            # 计算目标仓位
            portfolio_value = self.get_parameter('portfolio_value', 100000)
            target_position_value = portfolio_value * target_weight
            target_quantity = int(target_position_value / market_data.close)
            
            # 获取当前持仓
            current_position = self.context.get_position(symbol) if self.context else 0
            position_diff = target_quantity - current_position
            
            # 只有在仓位差异足够大时才生成信号
            min_trade_size = self.get_parameter('min_trade_size', 10)
            if abs(position_diff) < min_trade_size:
                return None
            
            from ..signals import SignalType
            
            signal_type = SignalType.BUY if position_diff > 0 else SignalType.SELL
            quantity = abs(position_diff)
            
            return Signal(
                symbol=symbol,
                signal_type=signal_type,
                quantity=quantity,
                price=market_data.close,
                timestamp=market_data.timestamp,
                confidence=0.8,  # 通胀对冲信号通常置信度较高
                strategy_name=self.name,
                metadata={
                    'asset_class': asset_class,
                    'target_weight': target_weight,
                    'inflation_regime': inflation_analysis.get('inflation_regime'),
                    'inflation_rate': inflation_analysis.get('current_inflation_rate'),
                    'risk_level': inflation_analysis.get('risk_level'),
                    'signal_category': 'INFLATION_HEDGE'
                }
            )
            
        except Exception as e:
            self.logger.error(f"对冲信号创建失败 {symbol}: {e}")
            return None
    
    def get_parameter_definitions(self) -> Dict[str, StrategyParameter]:
        """获取策略参数定义"""
        base_params = super().get_parameter_definitions()
        
        hedge_params = {
            'portfolio_value': StrategyParameter(
                name='portfolio_value',
                param_type=ParameterType.FLOAT,
                default_value=100000.0,
                description='投资组合总价值',
                min_value=1000.0,
                validator=ParameterValidator.positive_number
            ),
            'min_trade_size': StrategyParameter(
                name='min_trade_size',
                param_type=ParameterType.INTEGER,
                default_value=10,
                description='最小交易数量',
                min_value=1,
                validator=ParameterValidator.positive_number
            ),
            'rebalance_threshold': StrategyParameter(
                name='rebalance_threshold',
                param_type=ParameterType.FLOAT,
                default_value=0.05,
                description='再平衡阈值',
                min_value=0.01,
                max_value=0.2
            ),
            'inflation_sensitivity': StrategyParameter(
                name='inflation_sensitivity',
                param_type=ParameterType.FLOAT,
                default_value=1.0,
                description='通胀敏感度系数',
                min_value=0.1,
                max_value=2.0
            )
        }
        
        base_params.update(hedge_params)
        return base_params