"""
示例策略实现

包含各种常见的交易策略实现，用于演示策略框架的使用
"""

from .moving_average_crossover import MovingAverageCrossoverStrategy
from .rsi_mean_reversion import RSIMeanReversionStrategy
from .macd_trend_following import MACDTrendFollowingStrategy
from .multi_factor_strategy import MultiFactorStrategy

__all__ = [
    'MovingAverageCrossoverStrategy',
    'RSIMeanReversionStrategy', 
    'MACDTrendFollowingStrategy',
    'MultiFactorStrategy'
]