"""
MACD趋势跟踪策略

基于MACD指标的趋势跟踪交易策略
"""

from typing import Dict, Any, List, Union
from datetime import datetime
import pandas as pd

from ..base import BaseStrategy, StrategyContext
from ..signals import Signal, SignalType
from ..parameters import StrategyParameter, ParameterType, ParameterValidator
from ...models.market_data import MarketData


class MACDTrendFollowingStrategy(BaseStrategy):
    """
    MACD趋势跟踪策略
    
    策略逻辑：
    1. 计算MACD指标（MACD线、信号线、柱状图）
    2. 当MACD线上穿信号线时，产生买入信号
    3. 当MACD线下穿信号线时，产生卖出信号
    4. 结合MACD柱状图和零轴位置进行信号过滤
    """
    
    def __init__(self, name: str = "MACD_TrendFollowing", parameters: Dict[str, Any] = None):
        super().__init__(name, parameters)
        self.last_macd = None
        self.last_signal_line = None
        self.last_histogram = None
        self.last_signal_type = None
        
    def get_parameter_definitions(self) -> Dict[str, StrategyParameter]:
        """获取策略参数定义"""
        return {
            'fast_period': StrategyParameter(
                name='fast_period',
                param_type=ParameterType.INTEGER,
                default_value=12,
                required=True,
                min_value=5,
                max_value=30,
                description='MACD快速EMA周期',
                validator=ParameterValidator.time_period
            ),
            'slow_period': StrategyParameter(
                name='slow_period',
                param_type=ParameterType.INTEGER,
                default_value=26,
                required=True,
                min_value=10,
                max_value=50,
                description='MACD慢速EMA周期',
                validator=ParameterValidator.time_period
            ),
            'signal_period': StrategyParameter(
                name='signal_period',
                param_type=ParameterType.INTEGER,
                default_value=9,
                required=True,
                min_value=3,
                max_value=20,
                description='MACD信号线EMA周期',
                validator=ParameterValidator.time_period
            ),
            'min_crossover_strength': StrategyParameter(
                name='min_crossover_strength',
                param_type=ParameterType.FLOAT,
                default_value=0.001,
                required=False,
                min_value=0.0,
                max_value=0.01,
                description='最小交叉强度'
            ),
            'require_zero_line_filter': StrategyParameter(
                name='require_zero_line_filter',
                param_type=ParameterType.BOOLEAN,
                default_value=True,
                required=False,
                description='是否启用零轴过滤（只在MACD>0时买入，MACD<0时卖出）'
            ),
            'require_histogram_confirmation': StrategyParameter(
                name='require_histogram_confirmation',
                param_type=ParameterType.BOOLEAN,
                default_value=True,
                required=False,
                description='是否需要柱状图确认（柱状图方向与信号一致）'
            ),
            'min_histogram_change': StrategyParameter(
                name='min_histogram_change',
                param_type=ParameterType.FLOAT,
                default_value=0.0005,
                required=False,
                min_value=0.0,
                max_value=0.005,
                description='最小柱状图变化幅度'
            ),
            'stop_loss_pct': StrategyParameter(
                name='stop_loss_pct',
                param_type=ParameterType.FLOAT,
                default_value=0.04,
                required=False,
                min_value=0.01,
                max_value=0.15,
                description='止损百分比'
            ),
            'take_profit_pct': StrategyParameter(
                name='take_profit_pct',
                param_type=ParameterType.FLOAT,
                default_value=0.08,
                required=False,
                min_value=0.02,
                max_value=0.3,
                description='止盈百分比'
            ),
            'lookback_period': StrategyParameter(
                name='lookback_period',
                param_type=ParameterType.INTEGER,
                default_value=100,
                required=False,
                min_value=60,
                max_value=300,
                description='历史数据回看周期'
            )
        }
    
    def initialize(self, context: StrategyContext) -> None:
        """策略初始化"""
        self.context = context
        
        # 验证参数
        fast_period = self.get_parameter('fast_period')
        slow_period = self.get_parameter('slow_period')
        signal_period = self.get_parameter('signal_period')
        
        if fast_period >= slow_period:
            raise ValueError(f"快速周期({fast_period})必须小于慢速周期({slow_period})")
        
        self.is_initialized = True
        self.logger.info(f"MACD趋势跟踪策略初始化完成: "
                        f"快速={fast_period}, 慢速={slow_period}, 信号={signal_period}")
    
    def on_data(self, data: Union[MarketData, Dict[str, MarketData]]) -> List[Signal]:
        """处理市场数据并生成交易信号"""
        signals = []
        
        # 处理单个标的数据
        if isinstance(data, MarketData):
            signal = self._process_single_symbol(data.symbol, data)
            if signal:
                signals.append(signal)
        
        # 处理多个标的数据
        elif isinstance(data, dict):
            for symbol, market_data in data.items():
                signal = self._process_single_symbol(symbol, market_data)
                if signal:
                    signals.append(signal)
        
        return signals
    
    def _process_single_symbol(self, symbol: str, current_data: MarketData) -> Signal:
        """处理单个标的的数据"""
        try:
            # 获取历史数据
            lookback_period = self.get_parameter('lookback_period')
            historical_data = self.context.get_historical_data(
                symbol=symbol,
                lookback=lookback_period,
                end_date=current_data.timestamp
            )
            
            slow_period = self.get_parameter('slow_period')
            signal_period = self.get_parameter('signal_period')
            min_required = slow_period + signal_period + 10
            
            if len(historical_data) < min_required:
                self.logger.warning(f"历史数据不足，无法计算MACD: {symbol}")
                return None
            
            # 计算MACD指标
            macd_data = self._calculate_macd(historical_data)
            
            if macd_data is None:
                return None
            
            macd, signal_line, histogram = macd_data
            
            # 生成交易信号
            signal = self._generate_macd_signal(
                symbol=symbol,
                current_price=current_data.close,
                current_time=current_data.timestamp,
                macd=macd,
                signal_line=signal_line,
                histogram=histogram
            )
            
            # 更新状态
            self.last_macd = macd
            self.last_signal_line = signal_line
            self.last_histogram = histogram
            
            return signal
            
        except Exception as e:
            self.logger.error(f"处理MACD数据时发生错误 {symbol}: {e}")
            return None
    
    def _calculate_macd(self, data: pd.DataFrame) -> tuple:
        """计算MACD指标"""
        try:
            fast_period = self.get_parameter('fast_period')
            slow_period = self.get_parameter('slow_period')
            signal_period = self.get_parameter('signal_period')
            
            # 计算MACD
            macd_result = self.context.get_indicator(
                'MACD', data, 
                fast_period=fast_period,
                slow_period=slow_period,
                signal_period=signal_period
            )
            
            if len(macd_result) == 0:
                return None
            
            # MACD指标通常返回三个序列：MACD线、信号线、柱状图
            if isinstance(macd_result, pd.DataFrame):
                macd = macd_result['MACD'].iloc[-1]
                signal_line = macd_result['Signal'].iloc[-1]
                histogram = macd_result['Histogram'].iloc[-1]
            else:
                # 如果指标引擎返回的是单个序列，需要手动计算
                # 这里假设返回的是MACD线
                macd = macd_result.iloc[-1]
                # 简化处理，实际应该计算信号线和柱状图
                signal_line = 0  # 占位符
                histogram = macd  # 占位符
            
            return macd, signal_line, histogram
            
        except Exception as e:
            self.logger.error(f"计算MACD失败: {e}")
            return None
    
    def _generate_macd_signal(self, symbol: str, current_price: float, 
                            current_time: datetime, macd: float, 
                            signal_line: float, histogram: float) -> Signal:
        """基于MACD生成交易信号"""
        min_strength = self.get_parameter('min_crossover_strength')
        zero_line_filter = self.get_parameter('require_zero_line_filter')
        histogram_confirmation = self.get_parameter('require_histogram_confirmation')
        min_histogram_change = self.get_parameter('min_histogram_change')
        
        # 需要有历史MACD数据才能检测交叉
        if self.last_macd is None or self.last_signal_line is None:
            return None
        
        signal_type = None
        confidence = 0.5
        
        # 计算交叉强度
        current_diff = macd - signal_line
        last_diff = self.last_macd - self.last_signal_line
        crossover_strength = abs(current_diff - last_diff)
        
        # 检测金叉（MACD线上穿信号线）
        if last_diff <= 0 and current_diff > min_strength:
            signal_type = SignalType.BUY
            confidence = min(0.9, 0.6 + crossover_strength * 100)
            
        # 检测死叉（MACD线下穿信号线）
        elif last_diff >= 0 and current_diff < -min_strength:
            signal_type = SignalType.SELL
            confidence = min(0.9, 0.6 + crossover_strength * 100)
        
        # 没有交叉信号
        if signal_type is None:
            return None
        
        # 零轴过滤
        if zero_line_filter:
            if signal_type == SignalType.BUY and macd <= 0:
                self.logger.debug(f"买入信号被零轴过滤: MACD={macd:.4f} <= 0")
                return None
            elif signal_type == SignalType.SELL and macd >= 0:
                self.logger.debug(f"卖出信号被零轴过滤: MACD={macd:.4f} >= 0")
                return None
        
        # 柱状图确认
        if histogram_confirmation and self.last_histogram is not None:
            histogram_change = histogram - self.last_histogram
            
            if signal_type == SignalType.BUY:
                # 买入信号需要柱状图向上
                if histogram_change < min_histogram_change:
                    self.logger.debug(f"买入信号缺乏柱状图确认: 变化={histogram_change:.4f}")
                    return None
            elif signal_type == SignalType.SELL:
                # 卖出信号需要柱状图向下
                if histogram_change > -min_histogram_change:
                    self.logger.debug(f"卖出信号缺乏柱状图确认: 变化={histogram_change:.4f}")
                    return None
        
        # 避免重复信号
        if signal_type == self.last_signal_type:
            return None
        
        # 根据MACD强度调整置信度
        macd_strength = abs(macd) / current_price  # 相对强度
        confidence = min(0.95, confidence + macd_strength * 10)
        
        # 计算止损止盈价格
        stop_loss_pct = self.get_parameter('stop_loss_pct')
        take_profit_pct = self.get_parameter('take_profit_pct')
        
        if signal_type == SignalType.BUY:
            stop_loss = current_price * (1 - stop_loss_pct)
            take_profit = current_price * (1 + take_profit_pct)
        else:  # SELL
            stop_loss = current_price * (1 + stop_loss_pct)
            take_profit = current_price * (1 - take_profit_pct)
        
        # 创建信号
        signal = Signal(
            symbol=symbol,
            signal_type=signal_type,
            timestamp=current_time,
            strategy_name=self.name,
            confidence=confidence,
            price=current_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            metadata={
                'macd': macd,
                'signal_line': signal_line,
                'histogram': histogram,
                'crossover_strength': crossover_strength,
                'macd_above_zero': macd > 0,
                'histogram_direction': 'up' if histogram > self.last_histogram else 'down',
                'fast_period': self.get_parameter('fast_period'),
                'slow_period': self.get_parameter('slow_period'),
                'signal_period': self.get_parameter('signal_period')
            }
        )
        
        # 更新最后信号类型
        self.last_signal_type = signal_type
        
        self.logger.info(f"生成MACD信号: {symbol} {signal_type.value} @ {current_price:.2f}, "
                        f"MACD: {macd:.4f}, 信号线: {signal_line:.4f}, 置信度: {confidence:.2f}")
        
        return signal
    
    def on_order_fill(self, fill_info: Dict[str, Any]) -> None:
        """订单成交后的处理"""
        super().on_order_fill(fill_info)
        
        symbol = fill_info.get('symbol')
        side = fill_info.get('side')
        price = fill_info.get('price')
        
        self.logger.info(f"MACD策略订单成交: {symbol} {side} @ {price}")
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.name,
            'type': 'Trend Following',
            'description': '基于MACD指标的趋势跟踪策略',
            'parameters': self.parameters,
            'last_macd': self.last_macd,
            'last_signal_line': self.last_signal_line,
            'last_histogram': self.last_histogram,
            'last_signal_type': self.last_signal_type.value if self.last_signal_type else None,
            'filters': {
                'zero_line_filter': self.get_parameter('require_zero_line_filter'),
                'histogram_confirmation': self.get_parameter('require_histogram_confirmation')
            }
        }