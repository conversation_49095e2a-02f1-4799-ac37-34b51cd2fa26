"""
RSI均值回归策略

基于RSI指标的超买超卖信号进行均值回归交易
"""

from typing import Dict, Any, List, Union
from datetime import datetime
import pandas as pd

from ..base import BaseStrategy, StrategyContext
from ..signals import Signal, SignalType
from ..parameters import StrategyParameter, ParameterType, ParameterValidator
from ...models.market_data import MarketData


class RSIMeanReversionStrategy(BaseStrategy):
    """
    RSI均值回归策略
    
    策略逻辑：
    1. 计算RSI指标
    2. 当RSI < 超卖阈值（如30）时，产生买入信号
    3. 当RSI > 超买阈值（如70）时，产生卖出信号
    4. 可设置RSI回归到中性区域时的平仓信号
    """
    
    def __init__(self, name: str = "RSI_MeanReversion", parameters: Dict[str, Any] = None):
        super().__init__(name, parameters)
        self.last_rsi = None
        self.last_position = None  # 'long', 'short', None
        
    def get_parameter_definitions(self) -> Dict[str, StrategyParameter]:
        """获取策略参数定义"""
        return {
            'rsi_period': StrategyParameter(
                name='rsi_period',
                param_type=ParameterType.INTEGER,
                default_value=14,
                required=True,
                min_value=2,
                max_value=50,
                description='RSI计算周期',
                validator=ParameterValidator.time_period
            ),
            'oversold_threshold': StrategyParameter(
                name='oversold_threshold',
                param_type=ParameterType.FLOAT,
                default_value=30.0,
                required=True,
                min_value=10.0,
                max_value=40.0,
                description='超卖阈值'
            ),
            'overbought_threshold': StrategyParameter(
                name='overbought_threshold',
                param_type=ParameterType.FLOAT,
                default_value=70.0,
                required=True,
                min_value=60.0,
                max_value=90.0,
                description='超买阈值'
            ),
            'neutral_exit_lower': StrategyParameter(
                name='neutral_exit_lower',
                param_type=ParameterType.FLOAT,
                default_value=45.0,
                required=False,
                min_value=35.0,
                max_value=55.0,
                description='中性区域下限（多头平仓）'
            ),
            'neutral_exit_upper': StrategyParameter(
                name='neutral_exit_upper',
                param_type=ParameterType.FLOAT,
                default_value=55.0,
                required=False,
                min_value=45.0,
                max_value=65.0,
                description='中性区域上限（空头平仓）'
            ),
            'min_rsi_change': StrategyParameter(
                name='min_rsi_change',
                param_type=ParameterType.FLOAT,
                default_value=2.0,
                required=False,
                min_value=0.5,
                max_value=10.0,
                description='最小RSI变化幅度（避免噪音）'
            ),
            'stop_loss_pct': StrategyParameter(
                name='stop_loss_pct',
                param_type=ParameterType.FLOAT,
                default_value=0.03,
                required=False,
                min_value=0.01,
                max_value=0.1,
                description='止损百分比'
            ),
            'take_profit_pct': StrategyParameter(
                name='take_profit_pct',
                param_type=ParameterType.FLOAT,
                default_value=0.06,
                required=False,
                min_value=0.02,
                max_value=0.2,
                description='止盈百分比'
            ),
            'lookback_period': StrategyParameter(
                name='lookback_period',
                param_type=ParameterType.INTEGER,
                default_value=100,
                required=False,
                min_value=50,
                max_value=300,
                description='历史数据回看周期'
            )
        }
    
    def initialize(self, context: StrategyContext) -> None:
        """策略初始化"""
        self.context = context
        
        # 验证参数
        oversold = self.get_parameter('oversold_threshold')
        overbought = self.get_parameter('overbought_threshold')
        neutral_lower = self.get_parameter('neutral_exit_lower')
        neutral_upper = self.get_parameter('neutral_exit_upper')
        
        if oversold >= overbought:
            raise ValueError(f"超卖阈值({oversold})必须小于超买阈值({overbought})")
        
        if neutral_lower >= neutral_upper:
            raise ValueError(f"中性区域下限({neutral_lower})必须小于上限({neutral_upper})")
        
        if oversold >= neutral_lower or neutral_upper >= overbought:
            raise ValueError("中性区域必须在超买超卖阈值之间")
        
        self.is_initialized = True
        self.logger.info(f"RSI均值回归策略初始化完成: RSI周期={self.get_parameter('rsi_period')}, "
                        f"超卖={oversold}, 超买={overbought}")
    
    def on_data(self, data: Union[MarketData, Dict[str, MarketData]]) -> List[Signal]:
        """处理市场数据并生成交易信号"""
        signals = []
        
        # 处理单个标的数据
        if isinstance(data, MarketData):
            signal = self._process_single_symbol(data.symbol, data)
            if signal:
                signals.append(signal)
        
        # 处理多个标的数据
        elif isinstance(data, dict):
            for symbol, market_data in data.items():
                signal = self._process_single_symbol(symbol, market_data)
                if signal:
                    signals.append(signal)
        
        return signals
    
    def _process_single_symbol(self, symbol: str, current_data: MarketData) -> Signal:
        """处理单个标的的数据"""
        try:
            # 获取历史数据
            lookback_period = self.get_parameter('lookback_period')
            historical_data = self.context.get_historical_data(
                symbol=symbol,
                lookback=lookback_period,
                end_date=current_data.timestamp
            )
            
            rsi_period = self.get_parameter('rsi_period')
            if len(historical_data) < rsi_period + 10:  # 需要额外数据计算RSI
                self.logger.warning(f"历史数据不足，无法计算RSI: {symbol}")
                return None
            
            # 计算RSI
            current_rsi = self._calculate_rsi(historical_data)
            
            if current_rsi is None:
                return None
            
            # 生成交易信号
            signal = self._generate_rsi_signal(
                symbol=symbol,
                current_price=current_data.close,
                current_time=current_data.timestamp,
                current_rsi=current_rsi
            )
            
            # 更新状态
            self.last_rsi = current_rsi
            
            return signal
            
        except Exception as e:
            self.logger.error(f"处理RSI数据时发生错误 {symbol}: {e}")
            return None
    
    def _calculate_rsi(self, data: pd.DataFrame) -> float:
        """计算RSI指标"""
        try:
            rsi_period = self.get_parameter('rsi_period')
            rsi_series = self.context.get_indicator('RSI', data, period=rsi_period)
            
            if len(rsi_series) == 0:
                return None
            
            return rsi_series.iloc[-1]
            
        except Exception as e:
            self.logger.error(f"计算RSI失败: {e}")
            return None
    
    def _generate_rsi_signal(self, symbol: str, current_price: float, 
                           current_time: datetime, current_rsi: float) -> Signal:
        """基于RSI生成交易信号"""
        oversold = self.get_parameter('oversold_threshold')
        overbought = self.get_parameter('overbought_threshold')
        neutral_lower = self.get_parameter('neutral_exit_lower')
        neutral_upper = self.get_parameter('neutral_exit_upper')
        min_change = self.get_parameter('min_rsi_change')
        
        signal_type = None
        confidence = 0.5
        
        # 检查RSI变化幅度
        if self.last_rsi is not None:
            rsi_change = abs(current_rsi - self.last_rsi)
            if rsi_change < min_change:
                return None  # RSI变化太小，可能是噪音
        
        # 获取当前持仓状态
        current_position = self.context.get_position(symbol)
        position_side = 'long' if current_position > 0 else ('short' if current_position < 0 else None)
        
        # 入场信号
        if position_side is None:
            # 超卖买入信号
            if current_rsi <= oversold:
                signal_type = SignalType.BUY
                # RSI越低，置信度越高
                confidence = min(0.9, 0.6 + (oversold - current_rsi) / oversold * 0.3)
                
            # 超买卖出信号
            elif current_rsi >= overbought:
                signal_type = SignalType.SELL
                # RSI越高，置信度越高
                confidence = min(0.9, 0.6 + (current_rsi - overbought) / (100 - overbought) * 0.3)
        
        # 出场信号
        else:
            # 多头持仓，RSI回归到中性区域上方时平仓
            if position_side == 'long' and current_rsi >= neutral_lower:
                signal_type = SignalType.CLOSE
                confidence = 0.7
                
            # 空头持仓，RSI回归到中性区域下方时平仓
            elif position_side == 'short' and current_rsi <= neutral_upper:
                signal_type = SignalType.CLOSE
                confidence = 0.7
        
        # 没有有效信号
        if signal_type is None:
            return None
        
        # 计算止损止盈价格
        stop_loss_pct = self.get_parameter('stop_loss_pct')
        take_profit_pct = self.get_parameter('take_profit_pct')
        
        stop_loss = None
        take_profit = None
        
        if signal_type == SignalType.BUY:
            stop_loss = current_price * (1 - stop_loss_pct)
            take_profit = current_price * (1 + take_profit_pct)
        elif signal_type == SignalType.SELL:
            stop_loss = current_price * (1 + stop_loss_pct)
            take_profit = current_price * (1 - take_profit_pct)
        
        # 创建信号
        signal = Signal(
            symbol=symbol,
            signal_type=signal_type,
            timestamp=current_time,
            strategy_name=self.name,
            confidence=confidence,
            price=current_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            metadata={
                'rsi': current_rsi,
                'rsi_period': self.get_parameter('rsi_period'),
                'oversold_threshold': oversold,
                'overbought_threshold': overbought,
                'position_side': position_side,
                'signal_reason': self._get_signal_reason(signal_type, current_rsi, position_side)
            }
        )
        
        self.logger.info(f"生成RSI信号: {symbol} {signal_type.value} @ {current_price:.2f}, "
                        f"RSI: {current_rsi:.1f}, 置信度: {confidence:.2f}")
        
        return signal
    
    def _get_signal_reason(self, signal_type: SignalType, rsi: float, position_side: str) -> str:
        """获取信号原因描述"""
        if signal_type == SignalType.BUY:
            return f"RSI超卖({rsi:.1f} <= {self.get_parameter('oversold_threshold')})"
        elif signal_type == SignalType.SELL:
            return f"RSI超买({rsi:.1f} >= {self.get_parameter('overbought_threshold')})"
        elif signal_type == SignalType.CLOSE:
            if position_side == 'long':
                return f"多头平仓，RSI回归中性({rsi:.1f} >= {self.get_parameter('neutral_exit_lower')})"
            else:
                return f"空头平仓，RSI回归中性({rsi:.1f} <= {self.get_parameter('neutral_exit_upper')})"
        return "未知原因"
    
    def on_order_fill(self, fill_info: Dict[str, Any]) -> None:
        """订单成交后的处理"""
        super().on_order_fill(fill_info)
        
        symbol = fill_info.get('symbol')
        side = fill_info.get('side')
        
        # 更新持仓状态
        if side in ['BUY', 'LONG']:
            self.last_position = 'long'
        elif side in ['SELL', 'SHORT']:
            self.last_position = 'short'
        elif side == 'CLOSE':
            self.last_position = None
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.name,
            'type': 'Mean Reversion',
            'description': '基于RSI指标的均值回归策略',
            'parameters': self.parameters,
            'last_rsi': self.last_rsi,
            'last_position': self.last_position,
            'current_thresholds': {
                'oversold': self.get_parameter('oversold_threshold'),
                'overbought': self.get_parameter('overbought_threshold'),
                'neutral_lower': self.get_parameter('neutral_exit_lower'),
                'neutral_upper': self.get_parameter('neutral_exit_upper')
            }
        }