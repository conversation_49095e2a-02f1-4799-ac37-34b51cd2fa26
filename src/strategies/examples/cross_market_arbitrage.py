"""
Cross-market arbitrage strategy implementation.

This strategy identifies and exploits price differences for the same or similar
assets across different markets and exchanges.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
import pandas as pd
import numpy as np

try:
    from ..multi_market import BaseMultiMarketStrategy, MultiMarketContext
    from ..signals import Signal
    from ..parameters import StrategyParameter
    from ...models.market_data import UnifiedMarketData
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from strategies.multi_market import BaseMultiMarketStrategy, MultiMarketContext
    from strategies.signals import Signal
    from strategies.parameters import StrategyParameter
    from models.market_data import UnifiedMarketData


class CrossMarketArbitrageStrategy(BaseMultiMarketStrategy):
    """
    Cross-market arbitrage strategy that identifies price differences
    between the same asset on different exchanges or markets.
    """
    
    def __init__(self, name: str = "CrossMarketArbitrage", parameters: Optional[Dict[str, Any]] = None):
        # Define supported markets for arbitrage
        supported_markets = ['US', 'CN', 'CRYPTO']
        super().__init__(name, parameters, supported_markets)
        
        # Strategy state
        self.price_history: Dict[str, List[Tuple[datetime, float]]] = {}
        self.arbitrage_opportunities: List[Dict[str, Any]] = []
        self.active_positions: Dict[str, Dict[str, Any]] = {}
        
        # Performance tracking
        self.total_opportunities = 0
        self.successful_trades = 0
        self.total_profit = 0.0
    
    def get_parameter_definitions(self) -> Dict[str, StrategyParameter]:
        """Define strategy parameters."""
        from ..parameters import ParameterType
        
        return {
            'min_profit_threshold': StrategyParameter(
                name='min_profit_threshold',
                description='Minimum profit threshold for arbitrage (percentage)',
                param_type=ParameterType.FLOAT,
                default_value=0.005,  # 0.5%
                min_value=0.001,
                max_value=0.1,
                required=True
            ),
            'max_position_size': StrategyParameter(
                name='max_position_size',
                description='Maximum position size per arbitrage trade',
                param_type=ParameterType.FLOAT,
                default_value=10000.0,
                min_value=100.0,
                max_value=100000.0,
                required=True
            ),
            'price_staleness_threshold': StrategyParameter(
                name='price_staleness_threshold',
                description='Maximum age of price data in seconds',
                param_type=ParameterType.INTEGER,
                default_value=30,
                min_value=5,
                max_value=300,
                required=True
            ),
            'correlation_threshold': StrategyParameter(
                name='correlation_threshold',
                description='Minimum correlation between assets for arbitrage',
                param_type=ParameterType.FLOAT,
                default_value=0.95,
                min_value=0.8,
                max_value=1.0,
                required=True
            ),
            'max_holding_time': StrategyParameter(
                name='max_holding_time',
                description='Maximum holding time for arbitrage positions (minutes)',
                param_type=ParameterType.INTEGER,
                default_value=60,
                min_value=5,
                max_value=1440,
                required=True
            ),
            'risk_limit': StrategyParameter(
                name='risk_limit',
                description='Maximum risk exposure as percentage of portfolio',
                param_type=ParameterType.FLOAT,
                default_value=0.1,  # 10%
                min_value=0.01,
                max_value=0.5,
                required=True
            )
        }
    
    def initialize(self, context: MultiMarketContext) -> None:
        """Initialize the strategy."""
        self.set_multi_market_context(context)
        self.is_initialized = True
        
        # Initialize price history for tracking
        self.price_history.clear()
        self.arbitrage_opportunities.clear()
        self.active_positions.clear()
        
        self.logger.info(f"Cross-market arbitrage strategy initialized for markets: {self.supported_markets}")
    
    def on_multi_market_data(self, market_data: Dict[str, List[UnifiedMarketData]]) -> List[Signal]:
        """Process multi-market data and identify arbitrage opportunities."""
        signals = []
        
        try:
            # Update price history
            self._update_price_history(market_data)
            
            # Find arbitrage opportunities
            opportunities = self._find_arbitrage_opportunities(market_data)
            
            # Generate signals for valid opportunities
            for opportunity in opportunities:
                signal = self._create_arbitrage_signal(opportunity)
                if signal:
                    signals.append(signal)
            
            # Check existing positions for exit signals
            exit_signals = self._check_position_exits()
            signals.extend(exit_signals)
            
            # Update performance metrics
            self._update_performance_metrics()
            
        except Exception as e:
            self.logger.error(f"Error processing multi-market data: {e}")
        
        return signals
    
    def _update_price_history(self, market_data: Dict[str, List[UnifiedMarketData]]) -> None:
        """Update price history for all symbols and markets."""
        current_time = datetime.now(timezone.utc)
        
        for market, data_list in market_data.items():
            for data in data_list:
                key = f"{data.symbol}_{market}_{data.exchange}"
                
                if key not in self.price_history:
                    self.price_history[key] = []
                
                # Add current price
                self.price_history[key].append((data.timestamp, data.close))
                
                # Keep only recent history (last 1000 points or 24 hours)
                cutoff_time = current_time - timedelta(hours=24)
                self.price_history[key] = [
                    (ts, price) for ts, price in self.price_history[key]
                    if ts > cutoff_time
                ][-1000:]  # Keep last 1000 points
    
    def _find_arbitrage_opportunities(self, market_data: Dict[str, List[UnifiedMarketData]]) -> List[Dict[str, Any]]:
        """Find arbitrage opportunities across markets."""
        opportunities = []
        min_profit = self.get_parameter('min_profit_threshold')
        staleness_threshold = self.get_parameter('price_staleness_threshold')
        correlation_threshold = self.get_parameter('correlation_threshold')
        
        current_time = datetime.now(timezone.utc)
        
        # Group data by symbol to find cross-market opportunities
        symbol_data = {}
        for market, data_list in market_data.items():
            for data in data_list:
                if data.symbol not in symbol_data:
                    symbol_data[data.symbol] = []
                symbol_data[data.symbol].append((market, data))
        
        # Look for arbitrage opportunities for each symbol
        for symbol, market_data_list in symbol_data.items():
            if len(market_data_list) < 2:
                continue  # Need at least 2 markets for arbitrage
            
            # Check all pairs of markets for this symbol
            for i in range(len(market_data_list)):
                for j in range(i + 1, len(market_data_list)):
                    market1, data1 = market_data_list[i]
                    market2, data2 = market_data_list[j]
                    
                    # Check data freshness
                    age1 = (current_time - data1.timestamp).total_seconds()
                    age2 = (current_time - data2.timestamp).total_seconds()
                    
                    if age1 > staleness_threshold or age2 > staleness_threshold:
                        continue
                    
                    # Check if markets are open
                    if not (self.context.is_market_open(market1) and self.context.is_market_open(market2)):
                        continue
                    
                    # Calculate price difference in common currency
                    try:
                        price1_usd = self.context.convert_currency(data1.close, data1.currency, 'USD')
                        price2_usd = self.context.convert_currency(data2.close, data2.currency, 'USD')
                        
                        price_diff = abs(price1_usd - price2_usd)
                        avg_price = (price1_usd + price2_usd) / 2
                        profit_percentage = price_diff / avg_price if avg_price > 0 else 0
                        
                        if profit_percentage >= min_profit:
                            # Check correlation if we have enough history
                            correlation = self._calculate_correlation(symbol, market1, market2)
                            
                            if correlation is None or correlation >= correlation_threshold:
                                # Validate the opportunity
                                if self.validate_cross_market_opportunity(
                                    symbol, market1, symbol, market2,
                                    data1.close, data2.close, min_profit
                                ):
                                    opportunity = {
                                        'symbol': symbol,
                                        'market1': market1,
                                        'market2': market2,
                                        'exchange1': data1.exchange,
                                        'exchange2': data2.exchange,
                                        'price1': data1.close,
                                        'price2': data2.close,
                                        'price1_usd': price1_usd,
                                        'price2_usd': price2_usd,
                                        'profit_percentage': profit_percentage,
                                        'correlation': correlation,
                                        'timestamp': current_time,
                                        'data1': data1,
                                        'data2': data2
                                    }
                                    opportunities.append(opportunity)
                    
                    except Exception as e:
                        self.logger.warning(f"Error calculating arbitrage for {symbol}: {e}")
                        continue
        
        # Sort opportunities by profit potential
        opportunities.sort(key=lambda x: x['profit_percentage'], reverse=True)
        
        # Store for analysis
        self.arbitrage_opportunities = opportunities[:10]  # Keep top 10
        self.total_opportunities += len(opportunities)
        
        return opportunities
    
    def _calculate_correlation(self, symbol: str, market1: str, market2: str) -> Optional[float]:
        """Calculate price correlation between the same symbol in different markets."""
        try:
            key1 = f"{symbol}_{market1}"
            key2 = f"{symbol}_{market2}"
            
            # Find matching keys in price history
            history_keys1 = [k for k in self.price_history.keys() if k.startswith(key1)]
            history_keys2 = [k for k in self.price_history.keys() if k.startswith(key2)]
            
            if not history_keys1 or not history_keys2:
                return None
            
            # Use the first matching key for each market
            history1 = self.price_history[history_keys1[0]]
            history2 = self.price_history[history_keys2[0]]
            
            if len(history1) < 10 or len(history2) < 10:
                return None  # Need at least 10 data points
            
            # Align timestamps and calculate correlation
            df1 = pd.DataFrame(history1, columns=['timestamp', 'price1'])
            df2 = pd.DataFrame(history2, columns=['timestamp', 'price2'])
            
            # Merge on timestamp with tolerance
            merged = pd.merge_asof(
                df1.sort_values('timestamp'),
                df2.sort_values('timestamp'),
                on='timestamp',
                tolerance=pd.Timedelta(minutes=5)
            ).dropna()
            
            if len(merged) < 10:
                return None
            
            correlation = merged['price1'].corr(merged['price2'])
            return correlation if not pd.isna(correlation) else None
            
        except Exception as e:
            self.logger.warning(f"Error calculating correlation for {symbol}: {e}")
            return None
    
    def _create_arbitrage_signal(self, opportunity: Dict[str, Any]) -> Optional[Signal]:
        """Create trading signal for arbitrage opportunity."""
        try:
            max_position_size = self.get_parameter('max_position_size')
            risk_limit = self.get_parameter('risk_limit')
            
            # Check risk limits
            if not self._check_risk_limits(opportunity, risk_limit):
                return None
            
            # Determine trade direction (buy low, sell high)
            if opportunity['price1_usd'] < opportunity['price2_usd']:
                # Buy in market1, sell in market2
                primary_market = opportunity['market1']
                primary_action = 'BUY'
                secondary_market = opportunity['market2']
                secondary_action = 'SELL'
                price_diff = opportunity['price2_usd'] - opportunity['price1_usd']
            else:
                # Buy in market2, sell in market1
                primary_market = opportunity['market2']
                primary_action = 'BUY'
                secondary_market = opportunity['market1']
                secondary_action = 'SELL'
                price_diff = opportunity['price1_usd'] - opportunity['price2_usd']
            
            # Calculate position size based on available capital and risk
            portfolio_value = self.context.get_portfolio().total_value if self.context.get_portfolio() else 100000
            max_risk_amount = portfolio_value * risk_limit
            
            # Position size limited by risk and maximum position size
            avg_price = (opportunity['price1_usd'] + opportunity['price2_usd']) / 2
            max_shares_by_risk = max_risk_amount / avg_price if avg_price > 0 else 0
            max_shares_by_limit = max_position_size / avg_price if avg_price > 0 else 0
            
            position_size = min(max_shares_by_risk, max_shares_by_limit)
            
            if position_size < 1:  # Minimum 1 share
                return None
            
            # Create the primary signal
            signal = self.create_cross_market_signal(
                primary_symbol=opportunity['symbol'],
                primary_market=primary_market,
                secondary_symbol=opportunity['symbol'],
                secondary_market=secondary_market,
                action=primary_action,
                quantity=position_size,
                confidence=min(opportunity['profit_percentage'] * 10, 1.0),  # Scale confidence
                metadata={
                    'arbitrage_type': 'cross_market',
                    'expected_profit': price_diff * position_size,
                    'profit_percentage': opportunity['profit_percentage'],
                    'secondary_action': secondary_action,
                    'correlation': opportunity.get('correlation'),
                    'opportunity_id': f"{opportunity['symbol']}_{opportunity['timestamp'].isoformat()}"
                }
            )
            
            # Track the position
            position_id = signal.metadata['opportunity_id']
            self.active_positions[position_id] = {
                'signal': signal,
                'opportunity': opportunity,
                'entry_time': opportunity['timestamp'],
                'status': 'pending'
            }
            
            return signal
            
        except Exception as e:
            self.logger.error(f"Error creating arbitrage signal: {e}")
            return None
    
    def _check_risk_limits(self, opportunity: Dict[str, Any], risk_limit: float) -> bool:
        """Check if the opportunity meets risk management criteria."""
        try:
            # Check if we already have too many active positions
            if len(self.active_positions) >= 5:  # Max 5 concurrent arbitrage positions
                return False
            
            # Check if we already have a position in this symbol
            symbol = opportunity['symbol']
            for pos_id, position in self.active_positions.items():
                if position['opportunity']['symbol'] == symbol:
                    return False  # One position per symbol
            
            # Check portfolio risk exposure
            portfolio = self.context.get_portfolio() if self.context else None
            if portfolio:
                current_exposure = sum(
                    abs(pos.market_value) for pos in portfolio.positions.values()
                )
                max_exposure = portfolio.total_value * risk_limit
                
                avg_price = (opportunity['price1_usd'] + opportunity['price2_usd']) / 2
                position_value = self.get_parameter('max_position_size')
                
                if current_exposure + position_value > max_exposure:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking risk limits: {e}")
            return False
    
    def _check_position_exits(self) -> List[Signal]:
        """Check existing positions for exit conditions."""
        exit_signals = []
        max_holding_time = self.get_parameter('max_holding_time')
        current_time = datetime.now(timezone.utc)
        
        positions_to_close = []
        
        for pos_id, position in self.active_positions.items():
            try:
                # Check holding time
                holding_time = (current_time - position['entry_time']).total_seconds() / 60  # minutes
                
                if holding_time > max_holding_time:
                    # Time to exit
                    exit_signal = self._create_exit_signal(position)
                    if exit_signal:
                        exit_signals.append(exit_signal)
                        positions_to_close.append(pos_id)
                
                # TODO: Add other exit conditions like profit target reached, stop loss, etc.
                
            except Exception as e:
                self.logger.error(f"Error checking position exit for {pos_id}: {e}")
        
        # Remove closed positions
        for pos_id in positions_to_close:
            del self.active_positions[pos_id]
        
        return exit_signals
    
    def _create_exit_signal(self, position: Dict[str, Any]) -> Optional[Signal]:
        """Create exit signal for an active position."""
        try:
            original_signal = position['signal']
            opportunity = position['opportunity']
            
            # Create opposite signal to close the position
            exit_action = 'SELL' if original_signal.action == 'BUY' else 'BUY'
            
            exit_signal = Signal(
                symbol=original_signal.symbol,
                action=exit_action,
                quantity=original_signal.quantity,
                timestamp=datetime.now(timezone.utc),
                confidence=0.8,  # High confidence for exit
                metadata={
                    'signal_type': 'exit',
                    'original_signal_id': original_signal.metadata.get('opportunity_id'),
                    'exit_reason': 'max_holding_time',
                    'arbitrage_type': 'cross_market_exit'
                },
                strategy_name=self.name
            )
            
            return exit_signal
            
        except Exception as e:
            self.logger.error(f"Error creating exit signal: {e}")
            return None
    
    def _update_performance_metrics(self) -> None:
        """Update strategy performance metrics."""
        # This would typically be called after trades are executed
        # For now, just update basic counters
        pass
    
    def on_order_fill(self, fill_info: Dict[str, Any]) -> None:
        """Handle order fill notifications."""
        try:
            # Update position status
            signal_id = fill_info.get('signal_id')
            if signal_id:
                for pos_id, position in self.active_positions.items():
                    if position['signal'].metadata.get('opportunity_id') == signal_id:
                        position['status'] = 'filled'
                        break
            
            # Update performance metrics
            if fill_info.get('side') == 'SELL':  # Closing position
                profit = fill_info.get('realized_pnl', 0)
                if profit > 0:
                    self.successful_trades += 1
                self.total_profit += profit
            
            super().on_order_fill(fill_info)
            
        except Exception as e:
            self.logger.error(f"Error handling order fill: {e}")
    
    def get_strategy_status(self) -> Dict[str, Any]:
        """Get detailed strategy status."""
        base_status = self.get_status()
        
        arbitrage_status = {
            'active_positions': len(self.active_positions),
            'total_opportunities_found': self.total_opportunities,
            'successful_trades': self.successful_trades,
            'total_profit': self.total_profit,
            'success_rate': (self.successful_trades / max(self.total_opportunities, 1)) * 100,
            'recent_opportunities': len(self.arbitrage_opportunities),
            'supported_markets': self.supported_markets,
            'active_markets': self.get_active_markets()
        }
        
        base_status.update(arbitrage_status)
        return base_status
    
    def get_current_opportunities(self) -> List[Dict[str, Any]]:
        """Get current arbitrage opportunities."""
        return self.arbitrage_opportunities.copy()
    
    def get_active_positions(self) -> Dict[str, Dict[str, Any]]:
        """Get active arbitrage positions."""
        return self.active_positions.copy()