"""
策略评估和选择系统

这个模块提供策略评估和选择功能，包括：
- 策略表现评估算法
- 策略替换建议系统
- 策略生命周期管理
- 策略优化和参数调整工具
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import warnings
from abc import ABC, abstractmethod

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.strategies.base import BaseStrategy
from src.strategies.parameters import StrategyParameter
from src.analysis.metrics import MetricsCalculator, PerformanceMetrics
from src.strategies.multi_strategy_manager import StrategyPortfolioManager
from src.strategies.strategy_coordinator import StrategyCoordinator, StrategyState


class EvaluationPeriod(Enum):
    """评估周期"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    ANNUALLY = "annually"


class LifecycleStage(Enum):
    """策略生命周期阶段"""
    DEVELOPMENT = "development"      # 开发阶段
    TESTING = "testing"             # 测试阶段
    PAPER_TRADING = "paper_trading" # 模拟交易
    LIVE_TRADING = "live_trading"   # 实盘交易
    MONITORING = "monitoring"       # 监控阶段
    DECLINING = "declining"         # 衰减阶段
    RETIRED = "retired"            # 退役


class ReplacementReason(Enum):
    """替换原因"""
    POOR_PERFORMANCE = "poor_performance"      # 表现不佳
    HIGH_RISK = "high_risk"                   # 风险过高
    MARKET_REGIME_CHANGE = "market_regime_change"  # 市场环境变化
    STRATEGY_DECAY = "strategy_decay"         # 策略衰减
    CORRELATION_INCREASE = "correlation_increase"  # 相关性增加
    CAPACITY_CONSTRAINTS = "capacity_constraints"  # 容量限制


@dataclass
class StrategyEvaluation:
    """策略评估结果"""
    strategy_id: str
    strategy_name: str
    evaluation_date: datetime
    evaluation_period: EvaluationPeriod
    
    # 绩效指标
    performance_metrics: PerformanceMetrics
    
    # 评估分数
    overall_score: float = 0.0
    performance_score: float = 0.0
    risk_score: float = 0.0
    consistency_score: float = 0.0
    adaptability_score: float = 0.0
    
    # 排名信息
    rank: int = 0
    percentile: float = 0.0
    
    # 风险指标
    max_drawdown: float = 0.0
    volatility: float = 0.0
    var_95: float = 0.0
    
    # 其他指标
    trade_count: int = 0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    
    # 建议
    recommendation: str = "HOLD"  # BUY, SELL, HOLD
    confidence: float = 0.0


@dataclass
class ReplacementSuggestion:
    """替换建议"""
    target_strategy_id: str
    target_strategy_name: str
    replacement_strategy_id: str = None
    replacement_strategy_name: str = None
    reason: ReplacementReason = ReplacementReason.POOR_PERFORMANCE
    urgency: str = "medium"  # low, medium, high, critical
    expected_improvement: Dict[str, float] = field(default_factory=dict)
    implementation_cost: float = 0.0
    confidence: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class ParameterOptimizationResult:
    """参数优化结果"""
    strategy_id: str
    original_parameters: Dict[str, Any]
    optimized_parameters: Dict[str, Any] 
    performance_improvement: float
    optimization_method: str
    optimization_date: datetime
    backtest_results: Dict[str, float] = field(default_factory=dict)


class StrategyEvaluator:
    """策略评估器"""
    
    def __init__(
        self,
        portfolio_manager: StrategyPortfolioManager,
        evaluation_period: EvaluationPeriod = EvaluationPeriod.MONTHLY,
        lookback_days: int = 252
    ):
        self.portfolio_manager = portfolio_manager
        self.evaluation_period = evaluation_period
        self.lookback_days = lookback_days
        self.metrics_calculator = MetricsCalculator()
        
        # 评估权重
        self.evaluation_weights = {
            'performance': 0.4,    # 绩效权重
            'risk': 0.3,          # 风险权重
            'consistency': 0.2,    # 一致性权重
            'adaptability': 0.1    # 适应性权重
        }
        
    def evaluate_strategy(
        self,
        strategy_id: str,
        end_date: datetime = None
    ) -> StrategyEvaluation:
        """
        评估单个策略
        
        Args:
            strategy_id: 策略ID
            end_date: 评估结束日期
            
        Returns:
            策略评估结果
        """
        end_date = end_date or datetime.now()
        start_date = end_date - timedelta(days=self.lookback_days)
        
        # 获取策略信息
        strategy = self.portfolio_manager.strategies.get(strategy_id)
        allocation = self.portfolio_manager.strategy_allocations.get(strategy_id)
        
        if not strategy or not allocation:
            raise ValueError(f"策略 {strategy_id} 不存在")
        
        # 获取策略历史数据（这里简化，实际中需要从数据源获取）
        performance_data = self._get_strategy_performance_data(strategy_id, start_date, end_date)
        
        # 计算绩效指标
        performance_metrics = self._calculate_performance_metrics(performance_data)
        
        # 计算各项评分
        performance_score = self._calculate_performance_score(performance_metrics)
        risk_score = self._calculate_risk_score(performance_metrics)
        consistency_score = self._calculate_consistency_score(performance_data)
        adaptability_score = self._calculate_adaptability_score(performance_data)
        
        # 计算总评分
        overall_score = (
            performance_score * self.evaluation_weights['performance'] +
            risk_score * self.evaluation_weights['risk'] +
            consistency_score * self.evaluation_weights['consistency'] +
            adaptability_score * self.evaluation_weights['adaptability']
        )
        
        # 生成建议
        recommendation, confidence = self._generate_recommendation(
            overall_score, performance_metrics
        )
        
        return StrategyEvaluation(
            strategy_id=strategy_id,
            strategy_name=allocation.strategy_name,
            evaluation_date=end_date,
            evaluation_period=self.evaluation_period,
            performance_metrics=performance_metrics,
            overall_score=overall_score,
            performance_score=performance_score,
            risk_score=risk_score,
            consistency_score=consistency_score,
            adaptability_score=adaptability_score,
            max_drawdown=performance_metrics.max_drawdown,
            volatility=performance_metrics.volatility,
            var_95=performance_metrics.var_95,
            trade_count=performance_metrics.total_trades,
            win_rate=performance_metrics.win_rate,
            profit_factor=performance_metrics.profit_factor,
            recommendation=recommendation,
            confidence=confidence
        )
    
    def evaluate_all_strategies(self, end_date: datetime = None) -> List[StrategyEvaluation]:
        """
        评估所有策略
        
        Args:
            end_date: 评估结束日期
            
        Returns:
            所有策略的评估结果列表
        """
        evaluations = []
        
        for strategy_id in self.portfolio_manager.strategies.keys():
            try:
                evaluation = self.evaluate_strategy(strategy_id, end_date)
                evaluations.append(evaluation)
            except Exception as e:
                print(f"评估策略 {strategy_id} 时出错: {e}")
        
        # 计算排名和百分位
        evaluations.sort(key=lambda x: x.overall_score, reverse=True)
        total_count = len(evaluations)
        
        for i, evaluation in enumerate(evaluations):
            evaluation.rank = i + 1
            evaluation.percentile = (total_count - i) / total_count * 100
        
        return evaluations
    
    def _get_strategy_performance_data(
        self, 
        strategy_id: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> pd.Series:
        """获取策略绩效数据（模拟）"""
        # 在实际实现中，这里应该从数据库或历史记录中获取真实数据
        # 这里我们模拟生成一些数据用于测试
        
        dates = pd.date_range(start_date, end_date, freq='D')
        np.random.seed(hash(strategy_id) % 2**32)  # 确保可重现性
        
        # 模拟收益率序列
        returns = np.random.normal(0.0005, 0.015, len(dates))  # 年化约12%收益，15%波动
        
        # 添加一些趋势和周期性
        trend = np.linspace(0, 0.001, len(dates))  # 轻微上升趋势
        seasonal = 0.0005 * np.sin(2 * np.pi * np.arange(len(dates)) / 252)  # 年度周期
        
        returns = returns + trend + seasonal
        
        # 计算累积价值
        portfolio_values = pd.Series(100000.0, index=dates)  # 起始价值
        for i in range(1, len(portfolio_values)):
            portfolio_values.iloc[i] = portfolio_values.iloc[i-1] * (1 + returns[i])
        
        return portfolio_values
    
    def _calculate_performance_metrics(self, performance_data: pd.Series) -> PerformanceMetrics:
        """计算绩效指标"""
        return self.metrics_calculator.calculate_metrics(performance_data)
    
    def _calculate_performance_score(self, metrics: PerformanceMetrics) -> float:
        """计算绩效得分 (0-100)"""
        # 基于多个绩效指标的组合评分
        sharpe_score = min(100, max(0, metrics.sharpe_ratio * 25))  # 夏普比率4.0 = 100分
        return_score = min(100, max(0, metrics.annualized_return * 500))  # 20%年化收益 = 100分
        
        # 加权平均
        score = sharpe_score * 0.6 + return_score * 0.4
        return min(100, max(0, score))
    
    def _calculate_risk_score(self, metrics: PerformanceMetrics) -> float:
        """计算风险得分 (0-100，分数越高风险越低)"""
        # 最大回撤评分（回撤越小分数越高）
        drawdown_score = min(100, max(0, (0.2 + metrics.max_drawdown) * 500))  # -20%回撤 = 0分
        
        # 波动率评分（波动率越低分数越高）
        volatility_score = min(100, max(0, (0.5 - metrics.volatility) * 200))  # 50%波动率 = 0分
        
        # VaR评分
        var_score = min(100, max(0, (0.1 + metrics.var_95) * 500))  # -10% VaR = 0分
        
        # 加权平均
        score = drawdown_score * 0.5 + volatility_score * 0.3 + var_score * 0.2
        return min(100, max(0, score))
    
    def _calculate_consistency_score(self, performance_data: pd.Series) -> float:
        """计算一致性得分 (0-100)"""
        if len(performance_data) < 30:
            return 50.0  # 数据不足，给中等分数
        
        # 计算月度收益率
        monthly_returns = performance_data.resample('M').last().pct_change().dropna()
        
        if len(monthly_returns) < 3:
            return 50.0
        
        # 正收益月份比例
        positive_months_ratio = (monthly_returns > 0).mean()
        
        # 收益率标准差（相对于平均值）
        returns_cv = monthly_returns.std() / abs(monthly_returns.mean()) if monthly_returns.mean() != 0 else 10
        consistency_score = positive_months_ratio * 50 + min(50, max(0, (2 - returns_cv) * 25))
        
        return min(100, max(0, consistency_score))
    
    def _calculate_adaptability_score(self, performance_data: pd.Series) -> float:
        """计算适应性得分 (0-100)"""
        if len(performance_data) < 60:
            return 50.0  # 数据不足
        
        # 计算滚动绩效指标的变化
        window = 60  # 60天窗口
        rolling_returns = performance_data.pct_change(window).dropna()
        
        # 适应性体现在不同市场环境下的表现稳定性
        # 这里简化为收益率的变异系数
        if len(rolling_returns) > 0 and rolling_returns.std() > 0:
            adaptability = min(100, max(0, 100 - rolling_returns.std() * 1000))
        else:
            adaptability = 50.0
        
        return adaptability
    
    def _generate_recommendation(
        self, 
        overall_score: float, 
        metrics: PerformanceMetrics
    ) -> Tuple[str, float]:
        """生成投资建议"""
        confidence = min(1.0, overall_score / 100)
        
        if overall_score >= 80:
            return "BUY", confidence
        elif overall_score >= 60:
            return "HOLD", confidence
        elif overall_score >= 40:
            return "REDUCE", confidence
        else:
            return "SELL", confidence


class StrategySelector:
    """策略选择器"""
    
    def __init__(
        self,
        evaluator: StrategyEvaluator,
        max_strategies: int = 10,
        min_score_threshold: float = 40.0
    ):
        self.evaluator = evaluator
        self.max_strategies = max_strategies
        self.min_score_threshold = min_score_threshold
    
    def select_optimal_strategies(
        self,
        candidate_strategies: List[str] = None,
        diversification_constraint: bool = True
    ) -> List[str]:
        """
        选择最优策略组合
        
        Args:
            candidate_strategies: 候选策略列表
            diversification_constraint: 是否应用分散化约束
            
        Returns:
            选择的策略ID列表
        """
        # 获取候选策略
        if candidate_strategies is None:
            candidate_strategies = list(self.evaluator.portfolio_manager.strategies.keys())
        
        # 评估所有候选策略
        evaluations = []
        for strategy_id in candidate_strategies:
            try:
                evaluation = self.evaluator.evaluate_strategy(strategy_id)
                if evaluation.overall_score >= self.min_score_threshold:
                    evaluations.append(evaluation)
            except Exception as e:
                print(f"评估策略 {strategy_id} 时出错: {e}")
        
        # 按分数排序
        evaluations.sort(key=lambda x: x.overall_score, reverse=True)
        
        # 应用分散化约束
        if diversification_constraint:
            selected_evaluations = self._apply_diversification_constraint(evaluations)
        else:
            selected_evaluations = evaluations[:self.max_strategies]
        
        return [eval.strategy_id for eval in selected_evaluations]
    
    def _apply_diversification_constraint(
        self, 
        evaluations: List[StrategyEvaluation]
    ) -> List[StrategyEvaluation]:
        """应用分散化约束"""
        selected = []
        
        for evaluation in evaluations:
            if len(selected) >= self.max_strategies:
                break
            
            # 检查与已选择策略的相关性（这里简化）
            is_diverse = True
            for selected_eval in selected:
                # 实际中应该计算策略间的相关性
                # 这里简化为策略类型检查
                if self._are_strategies_similar(evaluation, selected_eval):
                    is_diverse = False
                    break
            
            if is_diverse:
                selected.append(evaluation)
        
        return selected
    
    def _are_strategies_similar(
        self, 
        eval1: StrategyEvaluation, 
        eval2: StrategyEvaluation
    ) -> bool:
        """检查两个策略是否相似（简化实现）"""
        # 实际实现中应该基于策略特征、相关性等进行判断
        # 这里简化为名称模式匹配
        name1_parts = eval1.strategy_name.lower().split('_')
        name2_parts = eval2.strategy_name.lower().split('_')
        
        common_parts = set(name1_parts) & set(name2_parts)
        return len(common_parts) > 0


class ReplacementAdvisor:
    """替换建议器"""
    
    def __init__(self, evaluator: StrategyEvaluator):
        self.evaluator = evaluator
    
    def generate_replacement_suggestions(
        self,
        performance_threshold: float = 40.0,
        risk_threshold: float = 30.0
    ) -> List[ReplacementSuggestion]:
        """
        生成替换建议
        
        Args:
            performance_threshold: 绩效阈值
            risk_threshold: 风险阈值
            
        Returns:
            替换建议列表
        """
        suggestions = []
        evaluations = self.evaluator.evaluate_all_strategies()
        
        for evaluation in evaluations:
            suggestion = self._analyze_replacement_need(
                evaluation, performance_threshold, risk_threshold
            )
            if suggestion:
                suggestions.append(suggestion)
        
        return suggestions
    
    def _analyze_replacement_need(
        self,
        evaluation: StrategyEvaluation,
        performance_threshold: float,
        risk_threshold: float
    ) -> Optional[ReplacementSuggestion]:
        """分析是否需要替换策略"""
        reasons = []
        urgency = "low"
        
        # 检查绩效
        if evaluation.performance_score < performance_threshold:
            reasons.append(ReplacementReason.POOR_PERFORMANCE)
            urgency = "medium"
        
        # 检查风险
        if evaluation.risk_score < risk_threshold:
            reasons.append(ReplacementReason.HIGH_RISK)
            urgency = "high"
        
        # 检查回撤
        if evaluation.max_drawdown < -0.3:  # 超过30%回撤
            reasons.append(ReplacementReason.STRATEGY_DECAY)
            urgency = "critical"
        
        if not reasons:
            return None
        
        # 选择主要原因
        primary_reason = reasons[0]
        
        # 计算置信度
        confidence = 1.0 - (evaluation.overall_score / 100.0)
        
        return ReplacementSuggestion(
            target_strategy_id=evaluation.strategy_id,
            target_strategy_name=evaluation.strategy_name,
            reason=primary_reason,
            urgency=urgency,
            confidence=confidence,
            expected_improvement={
                'score_improvement': max(0, performance_threshold - evaluation.overall_score),
                'risk_reduction': max(0, risk_threshold - evaluation.risk_score)
            }
        )


class ParameterOptimizer:
    """参数优化器"""
    
    def __init__(self, evaluator: StrategyEvaluator):
        self.evaluator = evaluator
    
    def optimize_strategy_parameters(
        self,
        strategy_id: str,
        parameter_ranges: Dict[str, Tuple[float, float]],
        optimization_method: str = "grid_search",
        max_iterations: int = 100
    ) -> ParameterOptimizationResult:
        """
        优化策略参数
        
        Args:
            strategy_id: 策略ID
            parameter_ranges: 参数范围字典
            optimization_method: 优化方法
            max_iterations: 最大迭代次数
            
        Returns:
            参数优化结果
        """
        strategy = self.evaluator.portfolio_manager.strategies.get(strategy_id)
        if not strategy:
            raise ValueError(f"策略 {strategy_id} 不存在")
        
        original_parameters = strategy.parameters.copy()
        best_parameters = original_parameters.copy()
        best_score = 0.0
        
        if optimization_method == "grid_search":
            best_parameters, best_score = self._grid_search_optimization(
                strategy_id, parameter_ranges, max_iterations
            )
        elif optimization_method == "random_search":
            best_parameters, best_score = self._random_search_optimization(
                strategy_id, parameter_ranges, max_iterations
            )
        else:
            raise ValueError(f"不支持的优化方法: {optimization_method}")
        
        # 计算性能提升
        original_score = self._evaluate_parameters(strategy_id, original_parameters)
        performance_improvement = best_score - original_score
        
        return ParameterOptimizationResult(
            strategy_id=strategy_id,
            original_parameters=original_parameters,
            optimized_parameters=best_parameters,
            performance_improvement=performance_improvement,
            optimization_method=optimization_method,
            optimization_date=datetime.now(),
            backtest_results={
                'original_score': original_score,
                'optimized_score': best_score
            }
        )
    
    def _grid_search_optimization(
        self,
        strategy_id: str,
        parameter_ranges: Dict[str, Tuple[float, float]],
        max_iterations: int
    ) -> Tuple[Dict[str, Any], float]:
        """网格搜索优化"""
        best_parameters = {}
        best_score = -np.inf
        
        # 简化的网格搜索（实际实现会更复杂）
        import itertools
        
        param_names = list(parameter_ranges.keys())
        param_values = []
        
        for param_name, (min_val, max_val) in parameter_ranges.items():
            # 为每个参数创建5个测试值
            values = np.linspace(min_val, max_val, 5)
            param_values.append(values)
        
        # 生成参数组合
        param_combinations = list(itertools.product(*param_values))
        
        # 限制测试次数
        if len(param_combinations) > max_iterations:
            indices = np.random.choice(len(param_combinations), max_iterations, replace=False)
            param_combinations = [param_combinations[i] for i in indices]
        
        for params in param_combinations:
            test_parameters = dict(zip(param_names, params))
            score = self._evaluate_parameters(strategy_id, test_parameters)
            
            if score > best_score:
                best_score = score
                best_parameters = test_parameters.copy()
        
        return best_parameters, best_score
    
    def _random_search_optimization(
        self,
        strategy_id: str,
        parameter_ranges: Dict[str, Tuple[float, float]],
        max_iterations: int
    ) -> Tuple[Dict[str, Any], float]:
        """随机搜索优化"""
        best_parameters = {}
        best_score = -np.inf
        
        for _ in range(max_iterations):
            test_parameters = {}
            for param_name, (min_val, max_val) in parameter_ranges.items():
                test_parameters[param_name] = np.random.uniform(min_val, max_val)
            
            score = self._evaluate_parameters(strategy_id, test_parameters)
            
            if score > best_score:
                best_score = score
                best_parameters = test_parameters.copy()
        
        return best_parameters, best_score
    
    def _evaluate_parameters(self, strategy_id: str, parameters: Dict[str, Any]) -> float:
        """评估参数组合（模拟）"""
        # 在实际实现中，这里应该：
        # 1. 使用新参数配置策略
        # 2. 运行回测
        # 3. 计算绩效指标
        # 4. 返回综合评分
        
        # 这里我们模拟一个评分
        # 基于参数值生成一个模拟评分
        score = 50.0  # 基础分
        
        for param_name, value in parameters.items():
            # 模拟参数对性能的影响
            param_hash = hash(f"{param_name}_{value}") % 100
            score += (param_hash - 50) * 0.1  # 随机影响-5到+5分
        
        # 确保分数在合理范围内
        return max(0, min(100, score))


class LifecycleManager:
    """策略生命周期管理器"""
    
    def __init__(self, evaluator: StrategyEvaluator):
        self.evaluator = evaluator
        self.strategy_stages: Dict[str, LifecycleStage] = {}
        self.stage_history: Dict[str, List[Tuple[LifecycleStage, datetime]]] = {}
    
    def update_strategy_stage(
        self,
        strategy_id: str,
        new_stage: LifecycleStage,
        reason: str = ""
    ):
        """
        更新策略生命周期阶段
        
        Args:
            strategy_id: 策略ID
            new_stage: 新阶段
            reason: 更新原因
        """
        old_stage = self.strategy_stages.get(strategy_id)
        self.strategy_stages[strategy_id] = new_stage
        
        # 记录历史
        if strategy_id not in self.stage_history:
            self.stage_history[strategy_id] = []
        
        self.stage_history[strategy_id].append((new_stage, datetime.now()))
        
        print(f"策略 {strategy_id} 生命周期更新: {old_stage} -> {new_stage}, 原因: {reason}")
    
    def get_strategy_stage(self, strategy_id: str) -> LifecycleStage:
        """获取策略当前阶段"""
        return self.strategy_stages.get(strategy_id, LifecycleStage.DEVELOPMENT)
    
    def get_stage_duration(self, strategy_id: str) -> timedelta:
        """获取策略在当前阶段的持续时间"""
        history = self.stage_history.get(strategy_id, [])
        if not history:
            return timedelta(0)
        
        last_update = history[-1][1]
        return datetime.now() - last_update
    
    def recommend_stage_transition(self, strategy_id: str) -> Optional[LifecycleStage]:
        """推荐阶段转换"""
        current_stage = self.get_strategy_stage(strategy_id)
        stage_duration = self.get_stage_duration(strategy_id)
        
        try:
            evaluation = self.evaluator.evaluate_strategy(strategy_id)
            
            # 基于绩效和阶段持续时间推荐转换
            if current_stage == LifecycleStage.DEVELOPMENT:
                if stage_duration.days > 30:  # 开发超过30天
                    return LifecycleStage.TESTING
            
            elif current_stage == LifecycleStage.TESTING:
                if evaluation.overall_score > 70 and stage_duration.days > 14:
                    return LifecycleStage.PAPER_TRADING
                elif evaluation.overall_score < 40 and stage_duration.days > 30:
                    return LifecycleStage.DEVELOPMENT
            
            elif current_stage == LifecycleStage.PAPER_TRADING:
                if evaluation.overall_score > 80 and stage_duration.days > 30:
                    return LifecycleStage.LIVE_TRADING
                elif evaluation.overall_score < 50:
                    return LifecycleStage.TESTING
            
            elif current_stage == LifecycleStage.LIVE_TRADING:
                if evaluation.overall_score < 40:
                    return LifecycleStage.DECLINING
                elif stage_duration.days > 90:  # 3个月后进入监控阶段
                    return LifecycleStage.MONITORING
            
            elif current_stage == LifecycleStage.MONITORING:
                if evaluation.overall_score < 30:
                    return LifecycleStage.DECLINING
            
            elif current_stage == LifecycleStage.DECLINING:
                if evaluation.overall_score < 20 or stage_duration.days > 60:
                    return LifecycleStage.RETIRED
                elif evaluation.overall_score > 60:
                    return LifecycleStage.LIVE_TRADING
            
        except Exception as e:
            print(f"评估策略 {strategy_id} 时出错: {e}")
        
        return None  # 不建议转换