"""
策略框架模块

提供策略开发的基础类和接口，包括：
- BaseStrategy: 策略基础抽象类
- StrategyContext: 策略上下文管理器
- Signal: 交易信号数据结构
- StrategyParameter: 策略参数管理
"""

from .base import BaseStrategy, StrategyContext
from .signals import (Signal, SignalType, SignalManager, SignalQualityAssessor, 
                     SignalAggregator, SignalConflictResolver, SignalRepository)
from .parameters import StrategyParameter, ParameterType, ParameterValidator
from .lifecycle import StrategyLifecycleManager

__all__ = [
    'BaseStrategy',
    'StrategyContext', 
    'Signal',
    'SignalType',
    'SignalManager',
    'SignalQualityAssessor',
    'SignalAggregator',
    'SignalConflictResolver',
    'SignalRepository',
    'StrategyParameter',
    'ParameterType',
    'ParameterValidator',
    'StrategyLifecycleManager'
]