"""
策略参数管理和验证系统
"""

from enum import Enum
from dataclasses import dataclass
from typing import Any, Optional, Union, List, Dict, Callable
import json


class ParameterType(Enum):
    """参数类型枚举"""
    INTEGER = "integer"
    FLOAT = "float"
    STRING = "string"
    BOOLEAN = "boolean"
    LIST = "list"
    DICT = "dict"


@dataclass
class StrategyParameter:
    """
    策略参数定义
    """
    name: str                                    # 参数名称
    param_type: ParameterType                   # 参数类型
    default_value: Any = None                   # 默认值
    required: bool = False                      # 是否必需
    description: str = ""                       # 参数描述
    min_value: Optional[Union[int, float]] = None    # 最小值（数值类型）
    max_value: Optional[Union[int, float]] = None    # 最大值（数值类型）
    choices: Optional[List[Any]] = None         # 可选值列表
    validator: Optional[Callable[[Any], bool]] = None  # 自定义验证函数
    
    def validate(self, value: Any) -> bool:
        """
        验证参数值
        
        Args:
            value: 待验证的值
            
        Returns:
            是否验证通过
            
        Raises:
            ValueError: 验证失败时抛出异常
        """
        if value is None:
            if self.required:
                raise ValueError(f"参数 {self.name} 是必需的")
            return True
        
        # 类型验证
        if not self._validate_type(value):
            raise ValueError(f"参数 {self.name} 类型错误，期望 {self.param_type.value}")
        
        # 范围验证（数值类型）
        if self.param_type in [ParameterType.INTEGER, ParameterType.FLOAT]:
            if self.min_value is not None and value < self.min_value:
                raise ValueError(f"参数 {self.name} 值 {value} 小于最小值 {self.min_value}")
            if self.max_value is not None and value > self.max_value:
                raise ValueError(f"参数 {self.name} 值 {value} 大于最大值 {self.max_value}")
        
        # 选择值验证
        if self.choices is not None and value not in self.choices:
            raise ValueError(f"参数 {self.name} 值 {value} 不在允许的选择中: {self.choices}")
        
        # 自定义验证
        if self.validator is not None:
            if not self.validator(value):
                raise ValueError(f"参数 {self.name} 自定义验证失败")
        
        return True
    
    def _validate_type(self, value: Any) -> bool:
        """验证参数类型"""
        type_mapping = {
            ParameterType.INTEGER: int,
            ParameterType.FLOAT: (int, float),  # 整数也可以作为浮点数
            ParameterType.STRING: str,
            ParameterType.BOOLEAN: bool,
            ParameterType.LIST: list,
            ParameterType.DICT: dict
        }
        
        expected_type = type_mapping.get(self.param_type)
        if expected_type is None:
            return False
        
        return isinstance(value, expected_type)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'name': self.name,
            'type': self.param_type.value,
            'default_value': self.default_value,
            'required': self.required,
            'description': self.description,
            'min_value': self.min_value,
            'max_value': self.max_value,
            'choices': self.choices
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StrategyParameter':
        """从字典创建参数对象"""
        return cls(
            name=data['name'],
            param_type=ParameterType(data['type']),
            default_value=data.get('default_value'),
            required=data.get('required', False),
            description=data.get('description', ''),
            min_value=data.get('min_value'),
            max_value=data.get('max_value'),
            choices=data.get('choices')
        )


class ParameterValidator:
    """
    参数验证器
    
    提供常用的参数验证函数
    """
    
    @staticmethod
    def positive_number(value: Union[int, float]) -> bool:
        """验证正数"""
        return value > 0
    
    @staticmethod
    def non_negative_number(value: Union[int, float]) -> bool:
        """验证非负数"""
        return value >= 0
    
    @staticmethod
    def percentage(value: Union[int, float]) -> bool:
        """验证百分比（0-100）"""
        return 0 <= value <= 100
    
    @staticmethod
    def probability(value: Union[int, float]) -> bool:
        """验证概率（0-1）"""
        return 0 <= value <= 1
    
    @staticmethod
    def non_empty_string(value: str) -> bool:
        """验证非空字符串"""
        return isinstance(value, str) and len(value.strip()) > 0
    
    @staticmethod
    def valid_symbol(value: str) -> bool:
        """验证交易标的代码格式"""
        if not isinstance(value, str):
            return False
        # 简单的符号验证：只包含字母、数字、点和连字符
        import re
        return bool(re.match(r'^[A-Za-z0-9.-]+$', value))
    
    @staticmethod
    def time_period(value: int) -> bool:
        """验证时间周期（正整数）"""
        return isinstance(value, int) and value > 0
    
    @staticmethod
    def create_range_validator(min_val: Union[int, float], 
                             max_val: Union[int, float]) -> Callable[[Any], bool]:
        """创建范围验证器"""
        def validator(value: Union[int, float]) -> bool:
            return min_val <= value <= max_val
        return validator
    
    @staticmethod
    def create_length_validator(min_len: int, max_len: int) -> Callable[[Any], bool]:
        """创建长度验证器"""
        def validator(value: Union[str, list, dict]) -> bool:
            return min_len <= len(value) <= max_len
        return validator


class ParameterManager:
    """
    参数管理器
    
    管理策略参数的定义、验证和序列化
    """
    
    def __init__(self):
        self.parameters: Dict[str, StrategyParameter] = {}
    
    def add_parameter(self, parameter: StrategyParameter) -> None:
        """
        添加参数定义
        
        Args:
            parameter: 参数定义对象
        """
        self.parameters[parameter.name] = parameter
    
    def remove_parameter(self, name: str) -> None:
        """
        移除参数定义
        
        Args:
            name: 参数名称
        """
        if name in self.parameters:
            del self.parameters[name]
    
    def get_parameter(self, name: str) -> Optional[StrategyParameter]:
        """
        获取参数定义
        
        Args:
            name: 参数名称
            
        Returns:
            参数定义对象，如果不存在则返回None
        """
        return self.parameters.get(name)
    
    def validate_parameters(self, values: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证参数值
        
        Args:
            values: 参数值字典
            
        Returns:
            验证后的参数值字典（包含默认值）
            
        Raises:
            ValueError: 验证失败时抛出异常
        """
        validated = {}
        
        # 验证提供的参数
        for name, value in values.items():
            param_def = self.parameters.get(name)
            if param_def is None:
                raise ValueError(f"未知参数: {name}")
            
            param_def.validate(value)
            validated[name] = value
        
        # 添加缺失的默认值
        for name, param_def in self.parameters.items():
            if name not in validated:
                if param_def.required and param_def.default_value is None:
                    raise ValueError(f"必需参数 {name} 未提供")
                validated[name] = param_def.default_value
        
        return validated
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        """
        获取参数模式定义
        
        Returns:
            参数模式字典
        """
        return {name: param.to_dict() for name, param in self.parameters.items()}
    
    def load_from_json(self, json_str: str) -> None:
        """
        从JSON字符串加载参数定义
        
        Args:
            json_str: JSON字符串
        """
        data = json.loads(json_str)
        self.parameters.clear()
        
        for param_data in data:
            param = StrategyParameter.from_dict(param_data)
            self.parameters[param.name] = param
    
    def save_to_json(self) -> str:
        """
        保存参数定义为JSON字符串
        
        Returns:
            JSON字符串
        """
        param_list = [param.to_dict() for param in self.parameters.values()]
        return json.dumps(param_list, indent=2, ensure_ascii=False)