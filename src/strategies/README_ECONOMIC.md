# 宏观经济策略模块

本模块实现了基于宏观经济数据的量化交易策略，扩展了策略框架以支持经济指标分析和投资决策。

## 模块结构

```
src/strategies/
├── economic_base.py              # 经济策略基础类
├── economic_correlation.py       # 经济相关性分析工具
├── examples/
│   ├── asset_allocation_strategy.py    # 资产配置策略
│   ├── economic_cycle_strategy.py      # 经济周期策略
│   └── inflation_hedge_strategy.py     # 通胀对冲策略
└── README_ECONOMIC.md            # 本文档
```

## 核心功能

### 1. 经济数据集成 (EconomicContext)
- 支持从FRED等数据源获取宏观经济指标
- 提供经济数据缓存和趋势分析
- 自动识别经济周期阶段
- 计算经济指标间的相关性

### 2. 预置策略

#### 资产配置策略 (AssetAllocationStrategy)
根据经济周期和关键指标动态调整资产配置权重：
- 基于经济周期调整股票、债券、商品配置
- 考虑通胀环境、利率趋势、风险情绪
- 支持多资产类别动态再平衡

#### 经济周期策略 (EconomicCycleStrategy)
基于经济周期阶段调整投资策略：
- **扩张期** - 增加风险资产配置
- **峰值期** - 转向防御性资产
- **收缩期** - 减少风险敞口
- **谷底期** - 准备重新配置风险资产

#### 通胀对冲策略 (InflationHedgeStrategy)
基于通胀环境动态调整投资组合：
- 商品 (黄金、白银、商品ETF)
- 房地产投资信托基金
- 通胀保护债券 (TIPS)
- 能源和材料行业股票

### 3. 相关性分析工具

#### EconomicCorrelationAnalyzer
- 计算经济指标与市场数据的相关性矩阵
- 分析滞后相关性，识别先行指标
- 按行业分析相关性模式
- 生成综合相关性分析报告

#### EconomicEventAnalyzer
- 检测重要经济事件 (利率决议、就业报告等)
- 分析经济事件对市场的影响
- 量化事件影响的持续时间和强度

## 支持的经济指标

### GDP和增长指标
- `GDPC1` - 实际GDP
- `GDPPOT` - 潜在GDP
- `INDPRO` - 工业生产指数

### 就业指标
- `UNRATE` - 失业率
- `CIVPART` - 劳动参与率
- `PAYEMS` - 非农就业人数
- `AHETPI` - 平均时薪

### 通胀指标
- `CPIAUCSL` - 消费者价格指数
- `CPILFESL` - 核心CPI
- `PCEPI` - PCE价格指数
- `T5YIE` - 5年通胀预期

### 货币政策指标
- `FEDFUNDS` - 联邦基金利率
- `DGS10` - 10年期国债收益率
- `DGS2` - 2年期国债收益率
- `M1SL` - M1货币供应量

### 先行指标
- `HOUST` - 新屋开工
- `SP500` - 标普500指数
- `VIXCLS` - VIX恐慌指数
- `UMCSENT` - 消费者信心指数

## 使用示例

### 基本用法

```python
from src.strategies.economic_base import EconomicContext
from src.strategies.examples.asset_allocation_strategy import AssetAllocationStrategy

# 创建经济上下文
context = EconomicContext(
    economic_data_manager=your_economic_data_manager
)

# 创建资产配置策略
strategy = AssetAllocationStrategy(
    name="Dynamic Asset Allocation",
    parameters={
        'min_confidence': 0.7,
        'min_weight_change': 0.05
    }
)

# 初始化策略
strategy.initialize(context)

# 执行策略
signals = strategy.on_data(market_data)
```

### 相关性分析

```python
from src.strategies.economic_correlation import EconomicCorrelationAnalyzer

# 创建分析器
analyzer = EconomicCorrelationAnalyzer(economic_context)

# 计算相关性矩阵
correlation_matrix = analyzer.calculate_correlation_matrix(
    market_symbols=['SPY', 'TLT', 'GLD'],
    economic_indicators=['GDPC1', 'UNRATE', 'CPIAUCSL']
)

# 寻找先行指标
leading_indicators = analyzer.find_leading_indicators('SPY')
```

## 配置参数

### 通用参数
- `correlation_threshold` - 相关性阈值 (默认: 0.7)
- `trend_lookback_days` - 趋势分析回看天数 (默认: 90)
- `buy_threshold` - 买入信号阈值 (默认: 0.3)
- `sell_threshold` - 卖出信号阈值 (默认: -0.3)

### 策略特定参数
- **资产配置策略**: `min_confidence`, `min_weight_change`, `rebalance_frequency`
- **经济周期策略**: `min_cycle_confidence`, `base_position_size`, `cycle_check_frequency`
- **通胀对冲策略**: `portfolio_value`, `inflation_sensitivity`, `rebalance_threshold`

## 测试

运行测试套件：
```bash
python -m pytest tests/test_economic_strategies.py -v
```

运行示例：
```bash
python examples/economic_strategy_usage.py
```

## 扩展开发

### 创建自定义经济策略

```python
from src.strategies.economic_base import EconomicStrategy

class CustomEconomicStrategy(EconomicStrategy):
    def get_economic_indicators(self):
        return ['GDPC1', 'UNRATE', 'CPIAUCSL']
    
    def analyze_economic_conditions(self, economic_data):
        # 实现自定义的经济分析逻辑
        pass
    
    def initialize(self, context):
        super().initialize(context)
        # 策略特定的初始化逻辑
```

### 添加新的经济指标

1. 在数据适配器中添加新指标的支持
2. 更新经济上下文以处理新指标
3. 在策略中使用新指标进行分析

## 依赖项

- pandas - 数据处理
- numpy - 数值计算
- scipy - 统计分析
- fredapi - FRED数据访问 (可选)

## 注意事项

1. **数据质量**: 确保经济数据的及时性和准确性
2. **参数调优**: 根据历史回测结果调整策略参数
3. **风险管理**: 设置合理的仓位限制和止损机制
4. **监控维护**: 定期监控策略表现并调整参数

## 文档

详细文档请参考：
- [宏观经济策略系统文档](../../docs/economic_strategies.md)
- [API参考文档](../../docs/api_reference.md)

## 许可证

本模块遵循项目的整体许可证协议。