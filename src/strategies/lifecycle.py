"""
策略生命周期管理器

负责策略的初始化、运行、暂停、恢复和清理等生命周期管理
"""

from enum import Enum
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
import logging
import threading
import time

try:
    from .base import BaseStrategy, StrategyContext
    from .signals import Signal, SignalManager
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from strategies.base import BaseStrategy, StrategyContext
    from strategies.signals import Signal, SignalManager


class StrategyState(Enum):
    """策略状态枚举"""
    CREATED = "created"           # 已创建
    INITIALIZING = "initializing" # 初始化中
    INITIALIZED = "initialized"   # 已初始化
    RUNNING = "running"          # 运行中
    PAUSED = "paused"           # 已暂停
    STOPPING = "stopping"       # 停止中
    STOPPED = "stopped"         # 已停止
    ERROR = "error"             # 错误状态


class StrategyLifecycleManager:
    """
    策略生命周期管理器
    
    管理策略的完整生命周期，包括状态转换、错误处理和资源管理
    """
    
    def __init__(self):
        self.strategies: Dict[str, BaseStrategy] = {}
        self.strategy_states: Dict[str, StrategyState] = {}
        self.strategy_contexts: Dict[str, StrategyContext] = {}
        self.signal_manager = SignalManager()
        self.logger = logging.getLogger(f"{__name__}.StrategyLifecycleManager")
        
        # 事件回调
        self.state_change_callbacks: List[Callable[[str, StrategyState, StrategyState], None]] = []
        self.error_callbacks: List[Callable[[str, Exception], None]] = []
        
        # 线程安全锁
        self._lock = threading.RLock()
    
    def register_strategy(self, strategy: BaseStrategy, 
                         context: StrategyContext) -> bool:
        """
        注册策略
        
        Args:
            strategy: 策略实例
            context: 策略上下文
            
        Returns:
            是否注册成功
        """
        with self._lock:
            if strategy.name in self.strategies:
                self.logger.warning(f"策略 {strategy.name} 已存在，将被覆盖")
            
            try:
                self.strategies[strategy.name] = strategy
                self.strategy_contexts[strategy.name] = context
                self.strategy_states[strategy.name] = StrategyState.CREATED
                
                self.logger.info(f"策略 {strategy.name} 注册成功")
                return True
                
            except Exception as e:
                self.logger.error(f"注册策略 {strategy.name} 失败: {e}")
                return False
    
    def unregister_strategy(self, strategy_name: str) -> bool:
        """
        注销策略
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            是否注销成功
        """
        with self._lock:
            if strategy_name not in self.strategies:
                self.logger.warning(f"策略 {strategy_name} 不存在")
                return False
            
            try:
                # 如果策略正在运行，先停止它
                if self.strategy_states[strategy_name] == StrategyState.RUNNING:
                    self.stop_strategy(strategy_name)
                
                # 清理资源
                strategy = self.strategies[strategy_name]
                strategy.cleanup()
                
                # 移除引用
                del self.strategies[strategy_name]
                del self.strategy_contexts[strategy_name]
                del self.strategy_states[strategy_name]
                
                self.logger.info(f"策略 {strategy_name} 注销成功")
                return True
                
            except Exception as e:
                self.logger.error(f"注销策略 {strategy_name} 失败: {e}")
                return False
    
    def initialize_strategy(self, strategy_name: str) -> bool:
        """
        初始化策略
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            是否初始化成功
        """
        with self._lock:
            if strategy_name not in self.strategies:
                self.logger.error(f"策略 {strategy_name} 不存在")
                return False
            
            strategy = self.strategies[strategy_name]
            context = self.strategy_contexts[strategy_name]
            current_state = self.strategy_states[strategy_name]
            
            if current_state != StrategyState.CREATED:
                self.logger.warning(f"策略 {strategy_name} 状态不正确: {current_state}")
                return False
            
            try:
                self._change_state(strategy_name, StrategyState.INITIALIZING)
                
                # 设置上下文
                strategy.set_context(context)
                
                # 调用初始化方法
                strategy.initialize(context)
                strategy.is_initialized = True
                
                self._change_state(strategy_name, StrategyState.INITIALIZED)
                self.logger.info(f"策略 {strategy_name} 初始化成功")
                return True
                
            except Exception as e:
                self._change_state(strategy_name, StrategyState.ERROR)
                self._handle_error(strategy_name, e)
                return False
    
    def start_strategy(self, strategy_name: str) -> bool:
        """
        启动策略
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            是否启动成功
        """
        with self._lock:
            if strategy_name not in self.strategies:
                self.logger.error(f"策略 {strategy_name} 不存在")
                return False
            
            current_state = self.strategy_states[strategy_name]
            
            if current_state not in [StrategyState.INITIALIZED, StrategyState.PAUSED]:
                self.logger.warning(f"策略 {strategy_name} 状态不正确: {current_state}")
                return False
            
            try:
                strategy = self.strategies[strategy_name]
                strategy.is_active = True
                
                self._change_state(strategy_name, StrategyState.RUNNING)
                self.logger.info(f"策略 {strategy_name} 启动成功")
                return True
                
            except Exception as e:
                self._change_state(strategy_name, StrategyState.ERROR)
                self._handle_error(strategy_name, e)
                return False
    
    def pause_strategy(self, strategy_name: str) -> bool:
        """
        暂停策略
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            是否暂停成功
        """
        with self._lock:
            if strategy_name not in self.strategies:
                self.logger.error(f"策略 {strategy_name} 不存在")
                return False
            
            current_state = self.strategy_states[strategy_name]
            
            if current_state != StrategyState.RUNNING:
                self.logger.warning(f"策略 {strategy_name} 状态不正确: {current_state}")
                return False
            
            try:
                strategy = self.strategies[strategy_name]
                strategy.is_active = False
                
                self._change_state(strategy_name, StrategyState.PAUSED)
                self.logger.info(f"策略 {strategy_name} 暂停成功")
                return True
                
            except Exception as e:
                self._change_state(strategy_name, StrategyState.ERROR)
                self._handle_error(strategy_name, e)
                return False
    
    def stop_strategy(self, strategy_name: str) -> bool:
        """
        停止策略
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            是否停止成功
        """
        with self._lock:
            if strategy_name not in self.strategies:
                self.logger.error(f"策略 {strategy_name} 不存在")
                return False
            
            current_state = self.strategy_states[strategy_name]
            
            if current_state in [StrategyState.STOPPED, StrategyState.CREATED]:
                self.logger.info(f"策略 {strategy_name} 已经停止")
                return True
            
            try:
                self._change_state(strategy_name, StrategyState.STOPPING)
                
                strategy = self.strategies[strategy_name]
                strategy.is_active = False
                strategy.cleanup()
                
                self._change_state(strategy_name, StrategyState.STOPPED)
                self.logger.info(f"策略 {strategy_name} 停止成功")
                return True
                
            except Exception as e:
                self._change_state(strategy_name, StrategyState.ERROR)
                self._handle_error(strategy_name, e)
                return False
    
    def process_data(self, strategy_name: str, data: Any) -> List[Signal]:
        """
        处理数据并生成信号
        
        Args:
            strategy_name: 策略名称
            data: 市场数据
            
        Returns:
            生成的信号列表
        """
        with self._lock:
            if strategy_name not in self.strategies:
                self.logger.error(f"策略 {strategy_name} 不存在")
                return []
            
            current_state = self.strategy_states[strategy_name]
            
            if current_state != StrategyState.RUNNING:
                return []
            
            try:
                strategy = self.strategies[strategy_name]
                signals = strategy.on_data(data)
                
                # 处理生成的信号
                processed_signals = []
                for signal in signals:
                    if self.signal_manager.add_signal(signal):
                        strategy.on_signal(signal)
                        processed_signals.append(signal)
                
                return processed_signals
                
            except Exception as e:
                self._change_state(strategy_name, StrategyState.ERROR)
                self._handle_error(strategy_name, e)
                return []
    
    def get_strategy_state(self, strategy_name: str) -> Optional[StrategyState]:
        """
        获取策略状态
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            策略状态，如果策略不存在则返回None
        """
        return self.strategy_states.get(strategy_name)
    
    def get_all_strategies(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有策略的信息
        
        Returns:
            策略信息字典
        """
        with self._lock:
            result = {}
            for name, strategy in self.strategies.items():
                result[name] = {
                    'strategy': strategy,
                    'state': self.strategy_states[name],
                    'status': strategy.get_status()
                }
            return result
    
    def get_running_strategies(self) -> List[str]:
        """
        获取正在运行的策略列表
        
        Returns:
            策略名称列表
        """
        with self._lock:
            return [name for name, state in self.strategy_states.items() 
                   if state == StrategyState.RUNNING]
    
    def add_state_change_callback(self, callback: Callable[[str, StrategyState, StrategyState], None]) -> None:
        """
        添加状态变化回调
        
        Args:
            callback: 回调函数，参数为(策略名称, 旧状态, 新状态)
        """
        self.state_change_callbacks.append(callback)
    
    def add_error_callback(self, callback: Callable[[str, Exception], None]) -> None:
        """
        添加错误回调
        
        Args:
            callback: 回调函数，参数为(策略名称, 异常对象)
        """
        self.error_callbacks.append(callback)
    
    def _change_state(self, strategy_name: str, new_state: StrategyState) -> None:
        """
        改变策略状态
        
        Args:
            strategy_name: 策略名称
            new_state: 新状态
        """
        old_state = self.strategy_states.get(strategy_name)
        self.strategy_states[strategy_name] = new_state
        
        # 调用状态变化回调
        for callback in self.state_change_callbacks:
            try:
                callback(strategy_name, old_state, new_state)
            except Exception as e:
                self.logger.error(f"状态变化回调异常: {e}")
    
    def _handle_error(self, strategy_name: str, error: Exception) -> None:
        """
        处理策略错误
        
        Args:
            strategy_name: 策略名称
            error: 异常对象
        """
        self.logger.error(f"策略 {strategy_name} 发生错误: {error}")
        
        # 调用错误回调
        for callback in self.error_callbacks:
            try:
                callback(strategy_name, error)
            except Exception as e:
                self.logger.error(f"错误回调异常: {e}")
    
    def shutdown(self) -> None:
        """
        关闭生命周期管理器
        """
        with self._lock:
            self.logger.info("开始关闭策略生命周期管理器")
            
            # 停止所有运行中的策略
            running_strategies = self.get_running_strategies()
            for strategy_name in running_strategies:
                self.stop_strategy(strategy_name)
            
            # 清理所有策略
            strategy_names = list(self.strategies.keys())
            for strategy_name in strategy_names:
                self.unregister_strategy(strategy_name)
            
            self.logger.info("策略生命周期管理器关闭完成")