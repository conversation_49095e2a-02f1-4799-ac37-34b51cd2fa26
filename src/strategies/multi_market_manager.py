"""
Multi-market strategy manager for coordinating strategies across different markets.

This module provides centralized management for multi-market strategies,
including strategy coordination, resource allocation, and performance monitoring.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading

try:
    from .multi_market import BaseMultiMarketStrategy, MultiMarketContext
    from .signals import Signal
    from .market_config import MarketConfigManager
    from .multi_market_risk import MultiMarketRiskManager
    from ..models.portfolio import Portfolio
    from ..models.trading import Order
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from strategies.multi_market import BaseMultiMarketStrategy, MultiMarketContext
    from strategies.signals import Signal
    from strategies.market_config import MarketConfigManager
    from strategies.multi_market_risk import MultiMarketRiskManager
    from models.portfolio import Portfolio
    from models.trading import Order


class StrategyPriority(Enum):
    """Strategy execution priority levels."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class StrategyAllocation:
    """Resource allocation for a strategy."""
    strategy_id: str
    capital_allocation: float  # Percentage of total capital
    risk_allocation: float     # Percentage of total risk budget
    priority: StrategyPriority
    max_positions: int
    enabled: bool = True


@dataclass
class MarketAllocation:
    """Market-specific resource allocation."""
    market: str
    capital_allocation: float
    risk_allocation: float
    max_strategies: int
    enabled: bool = True


@dataclass
class StrategyPerformance:
    """Strategy performance metrics."""
    strategy_id: str
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    avg_trade_duration: float
    last_updated: datetime


class MultiMarketStrategyManager:
    """Manager for coordinating multiple multi-market strategies."""
    
    def __init__(self, market_config: Optional[MarketConfigManager] = None,
                 risk_manager: Optional[MultiMarketRiskManager] = None):
        self.market_config = market_config or MarketConfigManager()
        self.risk_manager = risk_manager or MultiMarketRiskManager()
        self.logger = logging.getLogger(f"{__name__}.MultiMarketStrategyManager")
        
        # Strategy management
        self.strategies: Dict[str, BaseMultiMarketStrategy] = {}
        self.strategy_allocations: Dict[str, StrategyAllocation] = {}
        self.market_allocations: Dict[str, MarketAllocation] = {}
        self.strategy_contexts: Dict[str, MultiMarketContext] = {}
        
        # Performance tracking
        self.strategy_performance: Dict[str, StrategyPerformance] = {}
        self.performance_history: List[Dict[str, Any]] = []
        
        # Execution coordination
        self.signal_queue: List[Tuple[str, Signal]] = []  # (strategy_id, signal)
        self.execution_lock = threading.Lock()
        self.last_execution_time: Dict[str, datetime] = {}
        
        # Market data coordination
        self.market_data_subscribers: Dict[str, List[str]] = {}  # market -> strategy_ids
        self.last_market_update: Dict[str, datetime] = {}
        
        # Initialize default allocations
        self._initialize_default_allocations()
    
    def _initialize_default_allocations(self) -> None:
        """Initialize default market allocations."""
        default_markets = ['US', 'CN', 'CRYPTO']
        
        for market in default_markets:
            self.market_allocations[market] = MarketAllocation(
                market=market,
                capital_allocation=0.33,  # Equal allocation initially
                risk_allocation=0.33,
                max_strategies=5,
                enabled=True
            )
    
    def register_strategy(self, strategy: BaseMultiMarketStrategy,
                         allocation: Optional[StrategyAllocation] = None) -> bool:
        """Register a multi-market strategy."""
        try:
            strategy_id = f"{strategy.name}_{id(strategy)}"
            
            # Set default allocation if not provided
            if allocation is None:
                allocation = StrategyAllocation(
                    strategy_id=strategy_id,
                    capital_allocation=0.1,  # 10% default
                    risk_allocation=0.1,
                    priority=StrategyPriority.MEDIUM,
                    max_positions=10
                )
            
            # Create strategy context
            context = MultiMarketContext(
                currency_converter=self.risk_manager.currency_converter,
                market_sessions=self.market_config.sessions,
                fee_models=self.market_config.fees,
                slippage_models=self.market_config.slippage_models,
                risk_manager=self.risk_manager
            )
            
            # Initialize strategy
            strategy.set_multi_market_context(context)
            strategy.initialize(context)
            
            # Register
            self.strategies[strategy_id] = strategy
            self.strategy_allocations[strategy_id] = allocation
            self.strategy_contexts[strategy_id] = context
            
            # Subscribe to market data for supported markets
            for market in strategy.supported_markets:
                if market not in self.market_data_subscribers:
                    self.market_data_subscribers[market] = []
                self.market_data_subscribers[market].append(strategy_id)
            
            self.logger.info(f"Registered strategy: {strategy.name} (ID: {strategy_id})")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to register strategy {strategy.name}: {e}")
            return False
    
    def unregister_strategy(self, strategy_id: str) -> bool:
        """Unregister a strategy."""
        try:
            if strategy_id not in self.strategies:
                return False
            
            # Clean up strategy
            strategy = self.strategies[strategy_id]
            strategy.cleanup()
            
            # Remove from data structures
            del self.strategies[strategy_id]
            del self.strategy_allocations[strategy_id]
            del self.strategy_contexts[strategy_id]
            
            if strategy_id in self.strategy_performance:
                del self.strategy_performance[strategy_id]
            
            # Remove from market subscriptions
            for market, subscribers in self.market_data_subscribers.items():
                if strategy_id in subscribers:
                    subscribers.remove(strategy_id)
            
            self.logger.info(f"Unregistered strategy: {strategy_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to unregister strategy {strategy_id}: {e}")
            return False
    
    def update_strategy_allocation(self, strategy_id: str, allocation: StrategyAllocation) -> bool:
        """Update strategy resource allocation."""
        if strategy_id not in self.strategies:
            return False
        
        self.strategy_allocations[strategy_id] = allocation
        self.logger.info(f"Updated allocation for strategy: {strategy_id}")
        return True
    
    def update_market_allocation(self, market: str, allocation: MarketAllocation) -> bool:
        """Update market resource allocation."""
        self.market_allocations[market] = allocation
        self.logger.info(f"Updated allocation for market: {market}")
        return True
    
    def process_market_data(self, market_data: Dict[str, Any]) -> List[Signal]:
        """Process market data and generate signals from all strategies."""
        all_signals = []
        
        try:
            # Group data by market
            market_grouped_data = {}
            for symbol, data in market_data.items():
                if hasattr(data, 'market'):
                    market = data.market
                    if market not in market_grouped_data:
                        market_grouped_data[market] = {}
                    market_grouped_data[market][symbol] = data
            
            # Process each market's data with subscribed strategies
            for market, data in market_grouped_data.items():
                if market not in self.market_data_subscribers:
                    continue
                
                # Update last market update time
                self.last_market_update[market] = datetime.now(timezone.utc)
                
                # Process with each subscribed strategy
                strategy_ids = self.market_data_subscribers[market]
                
                # Use thread pool for parallel processing
                with ThreadPoolExecutor(max_workers=min(len(strategy_ids), 4)) as executor:
                    future_to_strategy = {
                        executor.submit(self._process_strategy_data, strategy_id, {market: data}): strategy_id
                        for strategy_id in strategy_ids
                        if self.strategy_allocations[strategy_id].enabled
                    }
                    
                    for future in as_completed(future_to_strategy):
                        strategy_id = future_to_strategy[future]
                        try:
                            signals = future.result(timeout=5.0)  # 5 second timeout
                            if signals:
                                # Add strategy ID to signals and filter by allocation
                                filtered_signals = self._filter_signals_by_allocation(strategy_id, signals)
                                all_signals.extend([(strategy_id, signal) for signal in filtered_signals])
                        except Exception as e:
                            self.logger.error(f"Strategy {strategy_id} processing failed: {e}")
            
            # Coordinate and prioritize signals
            coordinated_signals = self._coordinate_signals(all_signals)
            
            return coordinated_signals
            
        except Exception as e:
            self.logger.error(f"Error processing market data: {e}")
            return []
    
    def _process_strategy_data(self, strategy_id: str, market_data: Dict[str, Dict[str, Any]]) -> List[Signal]:
        """Process data for a single strategy."""
        try:
            strategy = self.strategies[strategy_id]
            
            # Convert to format expected by strategy
            formatted_data = {}
            for market, data_dict in market_data.items():
                formatted_data[market] = list(data_dict.values())
            
            # Generate signals
            signals = strategy.on_multi_market_data(formatted_data)
            
            # Update last execution time
            self.last_execution_time[strategy_id] = datetime.now(timezone.utc)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Error processing data for strategy {strategy_id}: {e}")
            return []
    
    def _filter_signals_by_allocation(self, strategy_id: str, signals: List[Signal]) -> List[Signal]:
        """Filter signals based on strategy allocation limits."""
        allocation = self.strategy_allocations.get(strategy_id)
        if not allocation or not allocation.enabled:
            return []
        
        # Apply position limits
        current_positions = self._get_strategy_position_count(strategy_id)
        available_positions = max(0, allocation.max_positions - current_positions)
        
        if available_positions == 0:
            self.logger.warning(f"Strategy {strategy_id} at position limit")
            return []
        
        # Sort signals by confidence and take top ones
        sorted_signals = sorted(signals, key=lambda s: s.confidence, reverse=True)
        return sorted_signals[:available_positions]
    
    def _get_strategy_position_count(self, strategy_id: str) -> int:
        """Get current position count for a strategy."""
        # This would integrate with portfolio management system
        # For now, return a placeholder
        return 0
    
    def _coordinate_signals(self, strategy_signals: List[Tuple[str, Signal]]) -> List[Signal]:
        """Coordinate signals from multiple strategies to avoid conflicts."""
        try:
            # Group signals by symbol and action
            signal_groups = {}
            for strategy_id, signal in strategy_signals:
                key = f"{signal.symbol}_{signal.action}"
                if key not in signal_groups:
                    signal_groups[key] = []
                signal_groups[key].append((strategy_id, signal))
            
            coordinated_signals = []
            
            for key, group in signal_groups.items():
                if len(group) == 1:
                    # No conflict, add signal
                    coordinated_signals.append(group[0][1])
                else:
                    # Multiple strategies want to trade the same symbol
                    # Resolve by priority and confidence
                    resolved_signal = self._resolve_signal_conflict(group)
                    if resolved_signal:
                        coordinated_signals.append(resolved_signal)
            
            return coordinated_signals
            
        except Exception as e:
            self.logger.error(f"Error coordinating signals: {e}")
            return [signal for _, signal in strategy_signals]
    
    def _resolve_signal_conflict(self, conflicting_signals: List[Tuple[str, Signal]]) -> Optional[Signal]:
        """Resolve conflicts between signals from different strategies."""
        try:
            # Score each signal based on strategy priority and signal confidence
            scored_signals = []
            
            for strategy_id, signal in conflicting_signals:
                allocation = self.strategy_allocations.get(strategy_id)
                if not allocation:
                    continue
                
                priority_score = allocation.priority.value
                confidence_score = signal.confidence
                
                # Combined score (priority weighted more heavily)
                total_score = (priority_score * 0.7) + (confidence_score * 0.3)
                scored_signals.append((total_score, strategy_id, signal))
            
            if not scored_signals:
                return None
            
            # Return highest scoring signal
            scored_signals.sort(key=lambda x: x[0], reverse=True)
            best_score, best_strategy_id, best_signal = scored_signals[0]
            
            self.logger.info(f"Resolved signal conflict: chose {best_strategy_id} with score {best_score:.3f}")
            return best_signal
            
        except Exception as e:
            self.logger.error(f"Error resolving signal conflict: {e}")
            return conflicting_signals[0][1] if conflicting_signals else None
    
    def get_strategy_performance(self, strategy_id: str) -> Optional[StrategyPerformance]:
        """Get performance metrics for a strategy."""
        return self.strategy_performance.get(strategy_id)
    
    def get_all_strategy_performance(self) -> Dict[str, StrategyPerformance]:
        """Get performance metrics for all strategies."""
        return self.strategy_performance.copy()
    
    def update_strategy_performance(self, strategy_id: str, performance: StrategyPerformance) -> None:
        """Update performance metrics for a strategy."""
        self.strategy_performance[strategy_id] = performance
        
        # Add to history
        self.performance_history.append({
            'timestamp': datetime.now(timezone.utc),
            'strategy_id': strategy_id,
            'performance': performance
        })
        
        # Keep only recent history (last 1000 entries)
        if len(self.performance_history) > 1000:
            self.performance_history = self.performance_history[-1000:]
    
    def get_manager_status(self) -> Dict[str, Any]:
        """Get comprehensive manager status."""
        active_strategies = sum(1 for alloc in self.strategy_allocations.values() if alloc.enabled)
        total_capital_allocated = sum(alloc.capital_allocation for alloc in self.strategy_allocations.values())
        total_risk_allocated = sum(alloc.risk_allocation for alloc in self.strategy_allocations.values())
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'total_strategies': len(self.strategies),
            'active_strategies': active_strategies,
            'supported_markets': list(self.market_allocations.keys()),
            'total_capital_allocated': total_capital_allocated,
            'total_risk_allocated': total_risk_allocated,
            'market_allocations': {
                market: {
                    'capital': alloc.capital_allocation,
                    'risk': alloc.risk_allocation,
                    'enabled': alloc.enabled,
                    'subscribers': len(self.market_data_subscribers.get(market, []))
                }
                for market, alloc in self.market_allocations.items()
            },
            'strategy_allocations': {
                strategy_id: {
                    'capital': alloc.capital_allocation,
                    'risk': alloc.risk_allocation,
                    'priority': alloc.priority.name,
                    'enabled': alloc.enabled,
                    'max_positions': alloc.max_positions
                }
                for strategy_id, alloc in self.strategy_allocations.items()
            },
            'last_market_updates': {
                market: timestamp.isoformat()
                for market, timestamp in self.last_market_update.items()
            },
            'performance_summary': {
                strategy_id: {
                    'total_return': perf.total_return,
                    'sharpe_ratio': perf.sharpe_ratio,
                    'max_drawdown': perf.max_drawdown,
                    'total_trades': perf.total_trades
                }
                for strategy_id, perf in self.strategy_performance.items()
            }
        }
    
    def optimize_allocations(self, target_return: float = 0.15, 
                           max_risk: float = 0.2) -> Dict[str, float]:
        """Optimize strategy allocations based on performance and risk."""
        try:
            # Simple optimization based on Sharpe ratios
            # In practice, this would use more sophisticated portfolio optimization
            
            if not self.strategy_performance:
                return {}
            
            # Calculate weights based on Sharpe ratios
            sharpe_ratios = {}
            for strategy_id, perf in self.strategy_performance.items():
                if perf.sharpe_ratio > 0:
                    sharpe_ratios[strategy_id] = perf.sharpe_ratio
            
            if not sharpe_ratios:
                return {}
            
            # Normalize weights
            total_sharpe = sum(sharpe_ratios.values())
            optimized_weights = {
                strategy_id: sharpe / total_sharpe
                for strategy_id, sharpe in sharpe_ratios.items()
            }
            
            # Apply constraints
            max_single_allocation = 0.4  # No single strategy > 40%
            for strategy_id in optimized_weights:
                if optimized_weights[strategy_id] > max_single_allocation:
                    optimized_weights[strategy_id] = max_single_allocation
            
            # Renormalize
            total_weight = sum(optimized_weights.values())
            if total_weight > 0:
                optimized_weights = {
                    strategy_id: weight / total_weight
                    for strategy_id, weight in optimized_weights.items()
                }
            
            self.logger.info("Optimized strategy allocations based on performance")
            return optimized_weights
            
        except Exception as e:
            self.logger.error(f"Error optimizing allocations: {e}")
            return {}
    
    def rebalance_strategies(self, new_allocations: Dict[str, float]) -> bool:
        """Rebalance strategy allocations."""
        try:
            for strategy_id, new_allocation in new_allocations.items():
                if strategy_id in self.strategy_allocations:
                    self.strategy_allocations[strategy_id].capital_allocation = new_allocation
                    self.strategy_allocations[strategy_id].risk_allocation = new_allocation
            
            self.logger.info("Rebalanced strategy allocations")
            return True
            
        except Exception as e:
            self.logger.error(f"Error rebalancing strategies: {e}")
            return False
    
    def cleanup(self) -> None:
        """Clean up manager resources."""
        try:
            # Clean up all strategies
            for strategy_id in list(self.strategies.keys()):
                self.unregister_strategy(strategy_id)
            
            # Clear data structures
            self.signal_queue.clear()
            self.performance_history.clear()
            
            self.logger.info("Multi-market strategy manager cleaned up")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")