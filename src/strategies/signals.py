"""
交易信号相关类和枚举定义
"""

from enum import Enum
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid


class SignalType(Enum):
    """交易信号类型"""
    BUY = "BUY"           # 买入信号
    SELL = "SELL"         # 卖出信号
    HOLD = "HOLD"         # 持有信号
    CLOSE = "CLOSE"       # 平仓信号
    STOP_LOSS = "STOP_LOSS"     # 止损信号
    TAKE_PROFIT = "TAKE_PROFIT" # 止盈信号


@dataclass
class Signal:
    """
    交易信号数据结构
    """
    symbol: str                           # 交易标的代码
    signal_type: SignalType              # 信号类型
    timestamp: datetime                   # 信号生成时间
    strategy_name: str                   # 生成信号的策略名称
    confidence: float = 1.0              # 信号置信度 (0-1)
    quantity: Optional[float] = None     # 交易数量
    price: Optional[float] = None        # 期望价格
    stop_loss: Optional[float] = None    # 止损价格
    take_profit: Optional[float] = None  # 止盈价格
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外信息
    signal_id: str = field(default_factory=lambda: str(uuid.uuid4()))  # 信号唯一ID
    
    def __post_init__(self):
        """数据验证"""
        if not (0 <= self.confidence <= 1):
            raise ValueError("信号置信度必须在0-1之间")
        
        if self.quantity is not None and self.quantity <= 0:
            raise ValueError("交易数量必须大于0")
        
        if self.price is not None and self.price <= 0:
            raise ValueError("价格必须大于0")
    
    def validate(self) -> bool:
        """验证信号的有效性"""
        try:
            # 基本字段验证
            if not self.symbol or not isinstance(self.symbol, str):
                return False
            
            if not isinstance(self.signal_type, SignalType):
                return False
            
            if not isinstance(self.timestamp, datetime):
                return False
            
            if not self.strategy_name or not isinstance(self.strategy_name, str):
                return False
            
            # 数值验证
            if not (0 <= self.confidence <= 1):
                return False
            
            if self.quantity is not None and self.quantity <= 0:
                return False
            
            if self.price is not None and self.price <= 0:
                return False
            
            return True
        except Exception:
            return False
    
    def is_entry_signal(self) -> bool:
        """判断是否为入场信号"""
        return self.signal_type in [SignalType.BUY, SignalType.SELL]
    
    def is_exit_signal(self) -> bool:
        """判断是否为出场信号"""
        return self.signal_type in [SignalType.CLOSE, SignalType.STOP_LOSS, SignalType.TAKE_PROFIT]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'signal_id': self.signal_id,
            'symbol': self.symbol,
            'signal_type': self.signal_type.value,
            'timestamp': self.timestamp.isoformat(),
            'strategy_name': self.strategy_name,
            'confidence': self.confidence,
            'quantity': self.quantity,
            'price': self.price,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Signal':
        """从字典创建信号对象"""
        return cls(
            signal_id=data.get('signal_id', str(uuid.uuid4())),
            symbol=data['symbol'],
            signal_type=SignalType(data['signal_type']),
            timestamp=datetime.fromisoformat(data['timestamp']),
            strategy_name=data['strategy_name'],
            confidence=data.get('confidence', 1.0),
            quantity=data.get('quantity'),
            price=data.get('price'),
            stop_loss=data.get('stop_loss'),
            take_profit=data.get('take_profit'),
            metadata=data.get('metadata', {})
        )


class SignalManager:
    """
    信号管理器
    
    负责信号的生成、过滤、聚合和历史记录
    """
    
    def __init__(self):
        self.signal_history: List[Signal] = []
        self.active_signals: Dict[str, Signal] = {}  # symbol -> latest signal
        self.signal_filters: List[callable] = []
        self.quality_assessor = SignalQualityAssessor()
        
    def add_signal(self, signal: Signal) -> bool:
        """
        添加新信号
        
        Args:
            signal: 交易信号
            
        Returns:
            是否成功添加信号
        """
        # 应用过滤器
        if not self._apply_filters(signal):
            return False
        
        # 记录到历史
        self.signal_history.append(signal)
        
        # 更新活跃信号
        self.active_signals[signal.symbol] = signal
        
        return True
    
    def get_latest_signal(self, symbol: str) -> Optional[Signal]:
        """
        获取指定标的的最新信号
        
        Args:
            symbol: 交易标的代码
            
        Returns:
            最新信号，如果没有则返回None
        """
        return self.active_signals.get(symbol)
    
    def get_signals_by_strategy(self, strategy_name: str) -> List[Signal]:
        """
        获取指定策略生成的所有信号
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            信号列表
        """
        return [s for s in self.signal_history if s.strategy_name == strategy_name]
    
    def get_signals_by_timerange(self, start_time: datetime, 
                               end_time: datetime) -> List[Signal]:
        """
        获取指定时间范围内的信号
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            信号列表
        """
        return [s for s in self.signal_history 
                if start_time <= s.timestamp <= end_time]
    
    def add_filter(self, filter_func: callable) -> None:
        """
        添加信号过滤器
        
        Args:
            filter_func: 过滤函数，接受Signal参数，返回bool
        """
        self.signal_filters.append(filter_func)
    
    def remove_filter(self, filter_func: callable) -> None:
        """
        移除信号过滤器
        
        Args:
            filter_func: 要移除的过滤函数
        """
        if filter_func in self.signal_filters:
            self.signal_filters.remove(filter_func)
    
    def _apply_filters(self, signal: Signal) -> bool:
        """
        应用所有过滤器
        
        Args:
            signal: 待过滤的信号
            
        Returns:
            是否通过所有过滤器
        """
        for filter_func in self.signal_filters:
            try:
                if not filter_func(signal):
                    return False
            except Exception as e:
                # 过滤器异常时记录日志但不阻止信号
                import logging
                logging.warning(f"信号过滤器异常: {e}")
        
        return True
    
    def aggregate_signals(self, symbols: List[str], 
                         time_window: int = 300) -> Dict[str, Signal]:
        """
        聚合多个标的的信号
        
        Args:
            symbols: 标的代码列表
            time_window: 时间窗口（秒）
            
        Returns:
            聚合后的信号字典
        """
        aggregated = {}
        current_time = datetime.now()
        
        for symbol in symbols:
            # 获取时间窗口内的信号
            recent_signals = [
                s for s in self.signal_history
                if (s.symbol == symbol and 
                    (current_time - s.timestamp).total_seconds() <= time_window)
            ]
            
            if recent_signals:
                # 选择置信度最高的信号
                best_signal = max(recent_signals, key=lambda x: x.confidence)
                aggregated[symbol] = best_signal
        
        return aggregated
    
    def get_signal_statistics(self) -> Dict[str, Any]:
        """
        获取信号统计信息
        
        Returns:
            统计信息字典
        """
        if not self.signal_history:
            return {}
        
        total_signals = len(self.signal_history)
        signal_types = {}
        strategies = {}
        
        for signal in self.signal_history:
            # 统计信号类型
            signal_type = signal.signal_type.value
            signal_types[signal_type] = signal_types.get(signal_type, 0) + 1
            
            # 统计策略
            strategy = signal.strategy_name
            strategies[strategy] = strategies.get(strategy, 0) + 1
        
        return {
            'total_signals': total_signals,
            'signal_types': signal_types,
            'strategies': strategies,
            'active_signals_count': len(self.active_signals)
        }
    
    def clear_history(self) -> None:
        """清空信号历史"""
        self.signal_history.clear()
        self.active_signals.clear()

class SignalQualityAssessor:
    """
    信号质量评估器
    
    评估交易信号的质量和可靠性
    """
    
    def __init__(self):
        self.signal_performance_history: Dict[str, List[Dict[str, Any]]] = {}
        self.strategy_performance: Dict[str, Dict[str, float]] = {}
    
    def assess_signal_quality(self, signal: Signal, 
                            historical_data: Optional[List[Signal]] = None) -> Dict[str, float]:
        """
        评估信号质量
        
        Args:
            signal: 待评估的信号
            historical_data: 历史信号数据
            
        Returns:
            质量评估结果字典
        """
        quality_metrics = {}
        
        # 基础质量评估
        quality_metrics['confidence_score'] = signal.confidence
        quality_metrics['completeness_score'] = self._assess_completeness(signal)
        quality_metrics['consistency_score'] = self._assess_consistency(signal, historical_data)
        
        # 策略历史表现评估
        if signal.strategy_name in self.strategy_performance:
            perf = self.strategy_performance[signal.strategy_name]
            quality_metrics['strategy_win_rate'] = perf.get('win_rate', 0.5)
            quality_metrics['strategy_avg_return'] = perf.get('avg_return', 0.0)
            quality_metrics['strategy_sharpe_ratio'] = perf.get('sharpe_ratio', 0.0)
        else:
            quality_metrics['strategy_win_rate'] = 0.5  # 默认值
            quality_metrics['strategy_avg_return'] = 0.0
            quality_metrics['strategy_sharpe_ratio'] = 0.0
        
        # 时间相关性评估
        quality_metrics['timing_score'] = self._assess_timing(signal)
        
        # 综合质量分数
        quality_metrics['overall_quality'] = self._calculate_overall_quality(quality_metrics)
        
        return quality_metrics
    
    def _assess_completeness(self, signal: Signal) -> float:
        """评估信号完整性"""
        score = 0.0
        total_fields = 5
        
        # 检查关键字段是否完整
        if signal.quantity is not None:
            score += 0.2
        if signal.price is not None:
            score += 0.2
        if signal.stop_loss is not None:
            score += 0.2
        if signal.take_profit is not None:
            score += 0.2
        if signal.metadata:
            score += 0.2
        
        return score
    
    def _assess_consistency(self, signal: Signal, 
                          historical_data: Optional[List[Signal]]) -> float:
        """评估信号一致性"""
        if not historical_data:
            return 0.5  # 默认中等一致性
        
        # 获取同一策略的历史信号
        strategy_signals = [s for s in historical_data 
                          if s.strategy_name == signal.strategy_name]
        
        if len(strategy_signals) < 2:
            return 0.5
        
        # 计算信号类型一致性
        recent_signals = strategy_signals[-10:]  # 最近10个信号
        signal_types = [s.signal_type for s in recent_signals]
        
        # 检查是否有明显的信号模式
        buy_signals = sum(1 for st in signal_types if st == SignalType.BUY)
        sell_signals = sum(1 for st in signal_types if st == SignalType.SELL)
        
        # 避免过于频繁的买卖切换
        if len(recent_signals) > 5:
            switches = sum(1 for i in range(1, len(signal_types)) 
                         if signal_types[i] != signal_types[i-1])
            consistency_score = max(0, 1 - switches / len(signal_types))
        else:
            consistency_score = 0.5
        
        return consistency_score
    
    def _assess_timing(self, signal: Signal) -> float:
        """评估信号时机"""
        current_time = datetime.now()
        signal_time = signal.timestamp
        
        # 检查信号是否过于陈旧
        time_diff = (current_time - signal_time).total_seconds()
        
        if time_diff < 60:  # 1分钟内
            return 1.0
        elif time_diff < 300:  # 5分钟内
            return 0.8
        elif time_diff < 900:  # 15分钟内
            return 0.6
        elif time_diff < 3600:  # 1小时内
            return 0.4
        else:
            return 0.2  # 信号过于陈旧
    
    def _calculate_overall_quality(self, metrics: Dict[str, float]) -> float:
        """计算综合质量分数"""
        weights = {
            'confidence_score': 0.25,
            'completeness_score': 0.15,
            'consistency_score': 0.20,
            'strategy_win_rate': 0.20,
            'timing_score': 0.20
        }
        
        overall_score = 0.0
        for metric, weight in weights.items():
            if metric in metrics:
                overall_score += metrics[metric] * weight
        
        return min(1.0, max(0.0, overall_score))
    
    def update_strategy_performance(self, strategy_name: str, 
                                  performance_metrics: Dict[str, float]) -> None:
        """
        更新策略历史表现
        
        Args:
            strategy_name: 策略名称
            performance_metrics: 表现指标
        """
        self.strategy_performance[strategy_name] = performance_metrics
    
    def record_signal_outcome(self, signal_id: str, outcome: Dict[str, Any]) -> None:
        """
        记录信号结果
        
        Args:
            signal_id: 信号ID
            outcome: 结果信息（包含盈亏、持有时间等）
        """
        if signal_id not in self.signal_performance_history:
            self.signal_performance_history[signal_id] = []
        
        self.signal_performance_history[signal_id].append({
            'timestamp': datetime.now(),
            'outcome': outcome
        })


class SignalAggregator:
    """
    信号聚合器
    
    将多个策略的信号进行聚合和冲突解决
    """
    
    def __init__(self):
        self.aggregation_rules: List[callable] = []
        self.conflict_resolver = SignalConflictResolver()
    
    def aggregate_signals(self, signals: List[Signal], 
                         aggregation_method: str = 'weighted_average') -> List[Signal]:
        """
        聚合多个信号
        
        Args:
            signals: 信号列表
            aggregation_method: 聚合方法
            
        Returns:
            聚合后的信号列表
        """
        if not signals:
            return []
        
        # 按标的分组
        signals_by_symbol = {}
        for signal in signals:
            if signal.symbol not in signals_by_symbol:
                signals_by_symbol[signal.symbol] = []
            signals_by_symbol[signal.symbol].append(signal)
        
        aggregated_signals = []
        
        for symbol, symbol_signals in signals_by_symbol.items():
            if len(symbol_signals) == 1:
                aggregated_signals.extend(symbol_signals)
            else:
                # 处理冲突信号
                resolved_signals = self.conflict_resolver.resolve_conflicts(symbol_signals)
                
                if aggregation_method == 'weighted_average':
                    aggregated = self._weighted_average_aggregation(resolved_signals)
                elif aggregation_method == 'majority_vote':
                    aggregated = self._majority_vote_aggregation(resolved_signals)
                elif aggregation_method == 'highest_confidence':
                    aggregated = self._highest_confidence_aggregation(resolved_signals)
                else:
                    aggregated = resolved_signals
                
                aggregated_signals.extend(aggregated)
        
        return aggregated_signals
    
    def _weighted_average_aggregation(self, signals: List[Signal]) -> List[Signal]:
        """加权平均聚合"""
        if not signals:
            return []
        
        # 按信号类型分组
        buy_signals = [s for s in signals if s.signal_type == SignalType.BUY]
        sell_signals = [s for s in signals if s.signal_type == SignalType.SELL]
        
        aggregated = []
        
        if buy_signals:
            avg_confidence = sum(s.confidence for s in buy_signals) / len(buy_signals)
            if avg_confidence > 0.5:  # 只有平均置信度足够高才生成信号
                representative_signal = max(buy_signals, key=lambda x: x.confidence)
                representative_signal.confidence = avg_confidence
                aggregated.append(representative_signal)
        
        if sell_signals:
            avg_confidence = sum(s.confidence for s in sell_signals) / len(sell_signals)
            if avg_confidence > 0.5:
                representative_signal = max(sell_signals, key=lambda x: x.confidence)
                representative_signal.confidence = avg_confidence
                aggregated.append(representative_signal)
        
        return aggregated
    
    def _majority_vote_aggregation(self, signals: List[Signal]) -> List[Signal]:
        """多数投票聚合"""
        if not signals:
            return []
        
        # 统计各种信号类型的数量
        signal_counts = {}
        for signal in signals:
            signal_type = signal.signal_type
            if signal_type not in signal_counts:
                signal_counts[signal_type] = []
            signal_counts[signal_type].append(signal)
        
        # 选择数量最多的信号类型
        if not signal_counts:
            return []
        
        majority_type = max(signal_counts.keys(), key=lambda x: len(signal_counts[x]))
        majority_signals = signal_counts[majority_type]
        
        # 返回置信度最高的信号
        best_signal = max(majority_signals, key=lambda x: x.confidence)
        return [best_signal]
    
    def _highest_confidence_aggregation(self, signals: List[Signal]) -> List[Signal]:
        """最高置信度聚合"""
        if not signals:
            return []
        
        best_signal = max(signals, key=lambda x: x.confidence)
        return [best_signal]


class SignalConflictResolver:
    """
    信号冲突解决器
    
    处理相互冲突的交易信号
    """
    
    def resolve_conflicts(self, signals: List[Signal]) -> List[Signal]:
        """
        解决信号冲突
        
        Args:
            signals: 可能冲突的信号列表
            
        Returns:
            解决冲突后的信号列表
        """
        if len(signals) <= 1:
            return signals
        
        # 检查是否存在冲突
        buy_signals = [s for s in signals if s.signal_type == SignalType.BUY]
        sell_signals = [s for s in signals if s.signal_type == SignalType.SELL]
        
        if buy_signals and sell_signals:
            # 存在买卖冲突，需要解决
            return self._resolve_buy_sell_conflict(buy_signals, sell_signals)
        else:
            # 没有冲突，返回所有信号
            return signals
    
    def _resolve_buy_sell_conflict(self, buy_signals: List[Signal], 
                                 sell_signals: List[Signal]) -> List[Signal]:
        """解决买卖信号冲突"""
        # 计算买卖信号的平均置信度
        buy_avg_confidence = sum(s.confidence for s in buy_signals) / len(buy_signals)
        sell_avg_confidence = sum(s.confidence for s in sell_signals) / len(sell_signals)
        
        # 选择平均置信度更高的一方
        if buy_avg_confidence > sell_avg_confidence:
            return buy_signals
        elif sell_avg_confidence > buy_avg_confidence:
            return sell_signals
        else:
            # 置信度相等，选择数量更多的一方
            if len(buy_signals) > len(sell_signals):
                return buy_signals
            elif len(sell_signals) > len(buy_signals):
                return sell_signals
            else:
                # 数量也相等，选择最新的信号
                all_signals = buy_signals + sell_signals
                latest_signal = max(all_signals, key=lambda x: x.timestamp)
                return [latest_signal]


class SignalRepository:
    """
    信号存储库
    
    提供信号的持久化存储和查询功能
    """
    
    def __init__(self, storage_backend=None):
        self.storage_backend = storage_backend or InMemorySignalStorage()
    
    def save_signal(self, signal: Signal) -> bool:
        """
        保存信号
        
        Args:
            signal: 要保存的信号
            
        Returns:
            是否保存成功
        """
        try:
            return self.storage_backend.save(signal)
        except Exception as e:
            import logging
            logging.error(f"保存信号失败: {e}")
            return False
    
    def get_signals_by_symbol(self, symbol: str, 
                            start_time: Optional[datetime] = None,
                            end_time: Optional[datetime] = None) -> List[Signal]:
        """
        按标的查询信号
        
        Args:
            symbol: 标的代码
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            信号列表
        """
        return self.storage_backend.query_by_symbol(symbol, start_time, end_time)
    
    def get_signals_by_strategy(self, strategy_name: str,
                              start_time: Optional[datetime] = None,
                              end_time: Optional[datetime] = None) -> List[Signal]:
        """
        按策略查询信号
        
        Args:
            strategy_name: 策略名称
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            信号列表
        """
        return self.storage_backend.query_by_strategy(strategy_name, start_time, end_time)
    
    def delete_signals_before(self, cutoff_time: datetime) -> int:
        """
        删除指定时间之前的信号
        
        Args:
            cutoff_time: 截止时间
            
        Returns:
            删除的信号数量
        """
        return self.storage_backend.delete_before(cutoff_time)


class InMemorySignalStorage:
    """
    内存信号存储实现
    """
    
    def __init__(self):
        self.signals: List[Signal] = []
    
    def save(self, signal: Signal) -> bool:
        """保存信号到内存"""
        self.signals.append(signal)
        return True
    
    def query_by_symbol(self, symbol: str, 
                       start_time: Optional[datetime] = None,
                       end_time: Optional[datetime] = None) -> List[Signal]:
        """按标的查询"""
        results = [s for s in self.signals if s.symbol == symbol]
        
        if start_time:
            results = [s for s in results if s.timestamp >= start_time]
        if end_time:
            results = [s for s in results if s.timestamp <= end_time]
        
        return sorted(results, key=lambda x: x.timestamp)
    
    def query_by_strategy(self, strategy_name: str,
                         start_time: Optional[datetime] = None,
                         end_time: Optional[datetime] = None) -> List[Signal]:
        """按策略查询"""
        results = [s for s in self.signals if s.strategy_name == strategy_name]
        
        if start_time:
            results = [s for s in results if s.timestamp >= start_time]
        if end_time:
            results = [s for s in results if s.timestamp <= end_time]
        
        return sorted(results, key=lambda x: x.timestamp)
    
    def delete_before(self, cutoff_time: datetime) -> int:
        """删除指定时间之前的信号"""
        original_count = len(self.signals)
        self.signals = [s for s in self.signals if s.timestamp >= cutoff_time]
        return original_count - len(self.signals)