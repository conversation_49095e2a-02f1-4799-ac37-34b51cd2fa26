#!/usr/bin/env python3
"""
量化交易系统主入口
简化版本 - 直接使用Python模块，无需API
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.manager import DataManager
from src.strategies.multi_strategy_manager import StrategyPortfolioManager
from src.backtest.engine import BacktestEngine
from src.risk.manager import RiskManager
from src.analysis.metrics import MetricsCalculator
from src.models.portfolio import Portfolio

class TradingSystem:
    """
    量化交易系统主类
    提供统一的系统接口，替代原来的API功能
    """
    
    def __init__(self, use_sqlite=True):
        """初始化交易系统"""
        self.data_manager = DataManager(use_sqlite=use_sqlite)
        self.strategy_manager = StrategyPortfolioManager()
        self.backtest_engine = BacktestEngine(initial_capital=100000)
        self.risk_manager = RiskManager()
        self.metrics_calculator = MetricsCalculator()
        
        print("✅ 量化交易系统初始化完成")
    
    def get_market_data(self, symbol, start_date, end_date, market=None):
        """获取市场数据"""
        return self.data_manager.get_market_data(symbol, start_date, end_date, market)
    
    def add_strategy(self, strategy):
        """添加策略"""
        return self.strategy_manager.add_strategy(strategy)
    
    def run_backtest(self, strategy_id, start_date, end_date):
        """运行回测"""
        strategy = self.strategy_manager.get_strategy(strategy_id)
        if strategy:
            return self.backtest_engine.run_backtest(strategy, start_date, end_date)
        else:
            raise ValueError(f"策略 {strategy_id} 不存在")
    
    def calculate_metrics(self, portfolio_values, trades=None):
        """计算绩效指标"""
        return self.metrics_calculator.calculate_metrics(portfolio_values, trades)
    
    def check_risk(self, order, portfolio):
        """风险检查"""
        return self.risk_manager.check_order(order, portfolio)
    
    def get_system_status(self):
        """获取系统状态"""
        return {
            'data_manager': 'active',
            'strategy_manager': f'{len(self.strategy_manager.strategies)} strategies loaded',
            'backtest_engine': 'ready',
            'risk_manager': 'active',
            'database': 'connected'
        }

def main():
    """主函数 - 演示系统使用"""
    print("🚀 启动量化交易系统")
    
    # 初始化系统
    system = TradingSystem()
    
    # 显示系统状态
    status = system.get_system_status()
    print("📊 系统状态:")
    for component, status_info in status.items():
        print(f"  {component}: {status_info}")
    
    print("\n💡 使用提示:")
    print("  - 启动GUI: python src/ui/main_app.py")
    print("  - 运行示例: python examples/basic_usage.py")
    print("  - 系统验证: python tools/verify_system.py")
    
    return system

if __name__ == "__main__":
    system = main()