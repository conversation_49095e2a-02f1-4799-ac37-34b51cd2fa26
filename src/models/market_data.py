"""
Market data models for different asset classes and data sources.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Dict, Any
from .base import BaseModel


@dataclass
class MarketData(BaseModel):
    """Basic market data structure for OHLCV data."""
    
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    adj_close: Optional[float] = None
    
    def validate(self) -> bool:
        """Validate market data."""
        if not self.symbol or not isinstance(self.symbol, str):
            return False
        if not isinstance(self.timestamp, datetime):
            return False
        if any(not isinstance(x, (int, float)) or x < 0 for x in [self.open, self.high, self.low, self.close, self.volume]):
            return False
        if self.high < max(self.open, self.close) or self.low > min(self.open, self.close):
            return False
        return True


@dataclass
class UnifiedMarketData(BaseModel):
    """Unified market data structure supporting multiple markets."""
    
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    market: str  # 'US', 'CN', 'CRYPTO', 'ECONOMIC'
    exchange: str  # 'NASDAQ', 'SSE', 'BINANCE', 'FRED'
    currency: str
    adj_close: Optional[float] = None
    turnover: Optional[float] = None  # 成交额
    
    def validate(self) -> bool:
        """Validate unified market data."""
        if not all([self.symbol, self.market, self.exchange, self.currency]):
            return False
        if self.market not in ['US', 'CN', 'CRYPTO', 'ECONOMIC']:
            return False
        return super().validate()


@dataclass
class EconomicData(BaseModel):
    """Economic data structure for macroeconomic indicators."""
    
    series_id: str
    timestamp: datetime
    value: float
    market: str = 'ECONOMIC'
    source: str = 'FRED'
    unit: str = ''
    frequency: str = 'Daily'  # Daily, Weekly, Monthly, Quarterly, Annual
    seasonal_adjustment: str = 'Not Seasonally Adjusted'
    last_updated: Optional[datetime] = None
    notes: Optional[str] = None
    
    def validate(self) -> bool:
        """Validate economic data."""
        if not self.series_id or not isinstance(self.series_id, str):
            return False
        if self.frequency not in ['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Annual']:
            return False
        return True


@dataclass
class MarketInfo(BaseModel):
    """Market information and trading rules."""
    
    symbol: str
    name: str
    market: str  # 'US', 'CN', 'CRYPTO', 'ECONOMIC'
    exchange: str  # 'NASDAQ', 'SSE', 'BINANCE', 'FRED'
    currency: str
    lot_size: float
    tick_size: float
    trading_hours: Dict[str, str]
    timezone: str
    is_active: bool = True
    
    def validate(self) -> bool:
        """Validate market info."""
        if not all([self.symbol, self.name, self.market, self.exchange, self.currency, self.timezone]):
            return False
        if self.lot_size <= 0 or self.tick_size <= 0:
            return False
        return True