"""
Base model class with common functionality.
"""

from abc import ABC
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Any, Optional
import json


@dataclass
class BaseModel(ABC):
    """Base class for all data models."""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary."""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, datetime):
                result[key] = value.isoformat()
            elif isinstance(value, BaseModel):
                result[key] = value.to_dict()
            elif isinstance(value, list) and value and isinstance(value[0], BaseModel):
                result[key] = [item.to_dict() for item in value]
            else:
                result[key] = value
        return result
    
    def to_json(self) -> str:
        """Convert model to JSON string."""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create model instance from dictionary."""
        # This is a basic implementation - subclasses should override for complex types
        return cls(**data)
    
    def validate(self) -> bool:
        """Validate model data. Override in subclasses."""
        return True