"""
Trading-related data models.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum
from .base import BaseModel


class OrderSide(Enum):
    """Order side enumeration."""
    BUY = "BUY"
    SELL = "SELL"


class OrderType(Enum):
    """Order type enumeration."""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "STOP"
    STOP_LIMIT = "STOP_LIMIT"


class OrderStatus(Enum):
    """Order status enumeration."""
    PENDING = "PENDING"
    FILLED = "FILLED"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"


class SignalAction(Enum):
    """Signal action enumeration."""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"


@dataclass
class Order(BaseModel):
    """Order data structure."""
    
    id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    timestamp: Optional[datetime] = None
    status: OrderStatus = OrderStatus.PENDING
    strategy_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def validate(self) -> bool:
        """Validate order data."""
        if not self.id or not self.symbol:
            return False
        if self.quantity <= 0:
            return False
        if self.order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT] and not self.price:
            return False
        if self.order_type in [OrderType.STOP, OrderType.STOP_LIMIT] and not self.stop_price:
            return False
        return True


@dataclass
class OrderFill(BaseModel):
    """Order fill/execution data structure."""
    
    id: str
    order_id: str
    symbol: str
    side: OrderSide
    quantity: float
    price: float
    timestamp: datetime
    commission: float = 0.0
    strategy_id: Optional[str] = None
    
    def validate(self) -> bool:
        """Validate order fill data."""
        if not all([self.id, self.order_id, self.symbol]):
            return False
        if self.quantity <= 0 or self.price <= 0:
            return False
        if self.commission < 0:
            return False
        return True


@dataclass
class Trade(BaseModel):
    """Trade data structure representing a completed transaction."""
    
    id: str
    symbol: str
    side: OrderSide
    quantity: float
    price: float
    timestamp: datetime
    commission: float = 0.0
    strategy_id: Optional[str] = None
    order_id: Optional[str] = None
    pnl: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def validate(self) -> bool:
        """Validate trade data."""
        if not all([self.id, self.symbol]):
            return False
        if self.quantity <= 0 or self.price <= 0:
            return False
        if self.commission < 0:
            return False
        return True


@dataclass
class Signal(BaseModel):
    """Trading signal data structure."""
    
    symbol: str
    action: SignalAction
    quantity: float
    price: Optional[float] = None
    timestamp: Optional[datetime] = None
    confidence: float = 1.0
    strategy_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def validate(self) -> bool:
        """Validate signal data."""
        if not self.symbol:
            return False
        if self.quantity <= 0:
            return False
        if not 0 <= self.confidence <= 1:
            return False
        return True