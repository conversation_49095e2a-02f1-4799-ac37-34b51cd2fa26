"""
Portfolio and position management models.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Optional, List, Tuple
import pandas as pd
import numpy as np
from .base import BaseModel
from .trading import Trade


@dataclass
class Position(BaseModel):
    """Position data structure representing holdings in a security."""
    
    symbol: str
    quantity: float
    avg_price: float
    market_value: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    last_price: Optional[float] = None
    timestamp: Optional[datetime] = None
    
    def update_market_value(self, current_price: float) -> None:
        """Update market value and unrealized PnL based on current price."""
        self.last_price = current_price
        self.market_value = self.quantity * current_price
        self.unrealized_pnl = (current_price - self.avg_price) * self.quantity
        self.timestamp = datetime.now()
    
    def add_trade(self, trade: Trade) -> None:
        """Add a trade to this position and update average price."""
        if trade.symbol != self.symbol:
            raise ValueError(f"Trade symbol {trade.symbol} doesn't match position symbol {self.symbol}")
        
        if trade.side.value == "BUY":
            # Adding to position
            total_cost = (self.quantity * self.avg_price) + (trade.quantity * trade.price)
            self.quantity += trade.quantity
            self.avg_price = total_cost / self.quantity if self.quantity > 0 else 0
        else:
            # Reducing position
            if trade.quantity > self.quantity:
                raise ValueError("Cannot sell more than current position")
            
            # Calculate realized PnL
            realized_pnl = (trade.price - self.avg_price) * trade.quantity
            self.realized_pnl += realized_pnl
            self.quantity -= trade.quantity
            
            # If position is closed, reset avg_price
            if self.quantity == 0:
                self.avg_price = 0
    
    def validate(self) -> bool:
        """Validate position data."""
        if not self.symbol:
            return False
        if self.avg_price < 0:
            return False
        return True


@dataclass
class PortfolioSnapshot:
    """Portfolio snapshot for historical tracking."""
    
    timestamp: datetime
    total_value: float
    cash: float
    positions_value: float
    unrealized_pnl: float
    realized_pnl: float
    positions: Dict[str, Position] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, any]:
        """Convert snapshot to dictionary."""
        return {
            'timestamp': self.timestamp,
            'total_value': self.total_value,
            'cash': self.cash,
            'positions_value': self.positions_value,
            'unrealized_pnl': self.unrealized_pnl,
            'realized_pnl': self.realized_pnl,
            'positions': {symbol: {
                'symbol': pos.symbol,
                'quantity': pos.quantity,
                'avg_price': pos.avg_price,
                'market_value': pos.market_value,
                'unrealized_pnl': pos.unrealized_pnl
            } for symbol, pos in self.positions.items()}
        }


@dataclass
class CashFlow:
    """Cash flow record for tracking money movements."""
    
    timestamp: datetime
    amount: float
    flow_type: str  # 'TRADE', 'COMMISSION', 'DIVIDEND', 'DEPOSIT', 'WITHDRAWAL'
    description: str
    trade_id: Optional[str] = None
    symbol: Optional[str] = None


@dataclass
class Portfolio(BaseModel):
    """Enhanced portfolio data structure managing cash, positions, and history."""
    
    initial_capital: float
    cash: float
    positions: Dict[str, Position] = field(default_factory=dict)
    total_value: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    trades: List[Trade] = field(default_factory=list)
    timestamp: Optional[datetime] = None
    
    # Enhanced portfolio management features
    history: List[PortfolioSnapshot] = field(default_factory=list)
    cash_flows: List[CashFlow] = field(default_factory=list)
    daily_returns: List[float] = field(default_factory=list)
    
    # Performance tracking
    peak_value: float = 0.0
    current_drawdown: float = 0.0
    max_drawdown: float = 0.0
    
    def __post_init__(self):
        """Initialize portfolio after creation."""
        if self.cash == 0:
            self.cash = self.initial_capital
        self.peak_value = self.initial_capital
        self.update_total_value()
        self._record_snapshot()
    
    def update_total_value(self) -> None:
        """Update total portfolio value and PnL."""
        positions_value = sum(pos.market_value for pos in self.positions.values())
        previous_value = self.total_value
        
        self.total_value = self.cash + positions_value
        self.unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        self.realized_pnl = sum(pos.realized_pnl for pos in self.positions.values())
        self.timestamp = datetime.now()
        
        # Update drawdown tracking
        if self.total_value > self.peak_value:
            self.peak_value = self.total_value
            self.current_drawdown = 0.0
        else:
            self.current_drawdown = (self.total_value - self.peak_value) / self.peak_value
            if self.current_drawdown < self.max_drawdown:
                self.max_drawdown = self.current_drawdown
        
        # Calculate daily return if we have previous value
        if previous_value > 0 and self.total_value != previous_value:
            daily_return = (self.total_value - previous_value) / previous_value
            self.daily_returns.append(daily_return)
    
    def add_trade(self, trade: Trade) -> None:
        """Add a trade to the portfolio."""
        self.trades.append(trade)
        
        # Record cash flow for trade
        trade_amount = trade.quantity * trade.price
        if trade.side.value == "BUY":
            self.cash -= (trade_amount + trade.commission)
            self._record_cash_flow(-trade_amount, "TRADE", f"Buy {trade.quantity} {trade.symbol}", trade.id, trade.symbol)
        else:
            self.cash += (trade_amount - trade.commission)
            self._record_cash_flow(trade_amount, "TRADE", f"Sell {trade.quantity} {trade.symbol}", trade.id, trade.symbol)
        
        # Record commission as separate cash flow
        if trade.commission > 0:
            self._record_cash_flow(-trade.commission, "COMMISSION", f"Commission for {trade.symbol}", trade.id, trade.symbol)
        
        # Update or create position
        if trade.symbol in self.positions:
            self.positions[trade.symbol].add_trade(trade)
            # Remove position if quantity becomes zero
            if self.positions[trade.symbol].quantity == 0:
                del self.positions[trade.symbol]
        else:
            # Create new position for buy orders
            if trade.side.value == "BUY":
                self.positions[trade.symbol] = Position(
                    symbol=trade.symbol,
                    quantity=trade.quantity,
                    avg_price=trade.price
                )
        
        self.update_total_value()
        self._record_snapshot()
    
    def update_positions_market_value(self, market_prices: Dict[str, float]) -> None:
        """Update all positions with current market prices."""
        for symbol, position in self.positions.items():
            if symbol in market_prices:
                position.update_market_value(market_prices[symbol])
        self.update_total_value()
        self._record_snapshot()
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """Get position for a specific symbol."""
        return self.positions.get(symbol)
    
    def get_available_cash(self) -> float:
        """Get available cash for trading."""
        return self.cash
    
    def get_total_return(self) -> float:
        """Get total return percentage."""
        if self.initial_capital == 0:
            return 0.0
        return (self.total_value - self.initial_capital) / self.initial_capital
    
    def get_positions_summary(self) -> Dict[str, Dict[str, float]]:
        """Get summary of all positions."""
        summary = {}
        for symbol, position in self.positions.items():
            summary[symbol] = {
                'quantity': position.quantity,
                'avg_price': position.avg_price,
                'market_value': position.market_value,
                'unrealized_pnl': position.unrealized_pnl,
                'unrealized_pnl_pct': position.unrealized_pnl / (position.avg_price * position.quantity) if position.quantity > 0 else 0.0,
                'weight': position.market_value / self.total_value if self.total_value > 0 else 0.0
            }
        return summary
    
    def get_cash_flow_summary(self, start_date: Optional[datetime] = None, 
                             end_date: Optional[datetime] = None) -> Dict[str, float]:
        """Get cash flow summary for a period."""
        filtered_flows = self.cash_flows
        
        if start_date:
            filtered_flows = [cf for cf in filtered_flows if cf.timestamp >= start_date]
        if end_date:
            filtered_flows = [cf for cf in filtered_flows if cf.timestamp <= end_date]
        
        summary = {
            'total_inflow': sum(cf.amount for cf in filtered_flows if cf.amount > 0),
            'total_outflow': sum(cf.amount for cf in filtered_flows if cf.amount < 0),
            'net_flow': sum(cf.amount for cf in filtered_flows),
            'trade_flows': sum(cf.amount for cf in filtered_flows if cf.flow_type == 'TRADE'),
            'commission_paid': sum(abs(cf.amount) for cf in filtered_flows if cf.flow_type == 'COMMISSION'),
            'dividend_received': sum(cf.amount for cf in filtered_flows if cf.flow_type == 'DIVIDEND')
        }
        
        return summary
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Calculate and return performance metrics."""
        if not self.daily_returns:
            return {}
        
        returns_array = np.array(self.daily_returns)
        
        # Basic metrics
        total_return = self.get_total_return()
        avg_daily_return = np.mean(returns_array)
        volatility = np.std(returns_array)
        
        # Annualized metrics (assuming 252 trading days)
        annualized_return = (1 + avg_daily_return) ** 252 - 1
        annualized_volatility = volatility * np.sqrt(252)
        
        # Risk-adjusted metrics
        sharpe_ratio = annualized_return / annualized_volatility if annualized_volatility > 0 else 0.0
        
        # Downside metrics
        negative_returns = returns_array[returns_array < 0]
        downside_deviation = np.std(negative_returns) if len(negative_returns) > 0 else 0.0
        sortino_ratio = annualized_return / (downside_deviation * np.sqrt(252)) if downside_deviation > 0 else 0.0
        
        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': annualized_volatility,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'max_drawdown': self.max_drawdown,
            'current_drawdown': self.current_drawdown,
            'peak_value': self.peak_value
        }
    
    def get_history_dataframe(self) -> pd.DataFrame:
        """Get portfolio history as pandas DataFrame."""
        if not self.history:
            return pd.DataFrame()
        
        data = []
        for snapshot in self.history:
            data.append({
                'timestamp': snapshot.timestamp,
                'total_value': snapshot.total_value,
                'cash': snapshot.cash,
                'positions_value': snapshot.positions_value,
                'unrealized_pnl': snapshot.unrealized_pnl,
                'realized_pnl': snapshot.realized_pnl
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df.sort_index()
    
    def get_cash_flows_dataframe(self) -> pd.DataFrame:
        """Get cash flows as pandas DataFrame."""
        if not self.cash_flows:
            return pd.DataFrame()
        
        data = []
        for cf in self.cash_flows:
            data.append({
                'timestamp': cf.timestamp,
                'amount': cf.amount,
                'flow_type': cf.flow_type,
                'description': cf.description,
                'trade_id': cf.trade_id,
                'symbol': cf.symbol
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df.sort_index()
    
    def get_equity_curve(self) -> pd.Series:
        """Get equity curve as pandas Series."""
        df = self.get_history_dataframe()
        if df.empty:
            return pd.Series()
        return df['total_value']
    
    def get_drawdown_series(self) -> pd.Series:
        """Get drawdown series as pandas Series."""
        equity_curve = self.get_equity_curve()
        if equity_curve.empty:
            return pd.Series()
        
        # Calculate running maximum (peak)
        peak = equity_curve.expanding().max()
        
        # Calculate drawdown
        drawdown = (equity_curve - peak) / peak
        
        return drawdown
    
    def add_dividend(self, symbol: str, amount: float, timestamp: Optional[datetime] = None) -> None:
        """Add dividend payment to portfolio."""
        if timestamp is None:
            timestamp = datetime.now()
        
        self.cash += amount
        self._record_cash_flow(amount, "DIVIDEND", f"Dividend from {symbol}", symbol=symbol)
        self.update_total_value()
        self._record_snapshot()
    
    def add_deposit(self, amount: float, description: str = "Deposit", 
                   timestamp: Optional[datetime] = None) -> None:
        """Add cash deposit to portfolio."""
        if timestamp is None:
            timestamp = datetime.now()
        
        self.cash += amount
        self.initial_capital += amount  # Adjust initial capital for performance calculation
        self._record_cash_flow(amount, "DEPOSIT", description)
        self.update_total_value()
        self._record_snapshot()
    
    def add_withdrawal(self, amount: float, description: str = "Withdrawal", 
                      timestamp: Optional[datetime] = None) -> None:
        """Add cash withdrawal from portfolio."""
        if timestamp is None:
            timestamp = datetime.now()
        
        if amount > self.cash:
            raise ValueError(f"Insufficient cash for withdrawal: {amount} > {self.cash}")
        
        self.cash -= amount
        self.initial_capital -= amount  # Adjust initial capital for performance calculation
        self._record_cash_flow(-amount, "WITHDRAWAL", description)
        self.update_total_value()
        self._record_snapshot()
    
    def _record_cash_flow(self, amount: float, flow_type: str, description: str, 
                         trade_id: Optional[str] = None, symbol: Optional[str] = None) -> None:
        """Record a cash flow entry."""
        cash_flow = CashFlow(
            timestamp=datetime.now(),
            amount=amount,
            flow_type=flow_type,
            description=description,
            trade_id=trade_id,
            symbol=symbol
        )
        self.cash_flows.append(cash_flow)
    
    def _record_snapshot(self) -> None:
        """Record current portfolio state as snapshot."""
        positions_value = sum(pos.market_value for pos in self.positions.values())
        
        snapshot = PortfolioSnapshot(
            timestamp=datetime.now(),
            total_value=self.total_value,
            cash=self.cash,
            positions_value=positions_value,
            unrealized_pnl=self.unrealized_pnl,
            realized_pnl=self.realized_pnl,
            positions={symbol: Position(
                symbol=pos.symbol,
                quantity=pos.quantity,
                avg_price=pos.avg_price,
                market_value=pos.market_value,
                unrealized_pnl=pos.unrealized_pnl,
                realized_pnl=pos.realized_pnl,
                last_price=pos.last_price,
                timestamp=pos.timestamp
            ) for symbol, pos in self.positions.items()}
        )
        
        self.history.append(snapshot)
    
    def validate(self) -> bool:
        """Validate portfolio data."""
        if self.initial_capital <= 0:
            return False
        if self.cash < 0:
            return False
        return all(pos.validate() for pos in self.positions.values())