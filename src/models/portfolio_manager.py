"""
Portfolio management system providing advanced portfolio operations and analysis.
"""

from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np
from copy import deepcopy

from .portfolio import Portfolio, Position, PortfolioSnapshot, CashFlow
from .trading import Trade
from .market_data import MarketData


@dataclass
class PortfolioAllocation:
    """Target portfolio allocation configuration."""
    
    symbol: str
    target_weight: float  # Target weight as percentage (0.0 to 1.0)
    min_weight: Optional[float] = None
    max_weight: Optional[float] = None
    rebalance_threshold: float = 0.05  # Rebalance when deviation exceeds this threshold


@dataclass
class RebalanceRecommendation:
    """Rebalancing recommendation for a symbol."""
    
    symbol: str
    current_weight: float
    target_weight: float
    deviation: float
    recommended_action: str  # 'BUY', 'SELL', 'HOLD'
    recommended_quantity: float
    recommended_value: float


class PortfolioManager:
    """
    Advanced portfolio management system providing comprehensive portfolio operations,
    analysis, and optimization capabilities.
    """
    
    def __init__(self, portfolio: Portfolio):
        """
        Initialize portfolio manager.
        
        Args:
            portfolio: Portfolio instance to manage
        """
        self.portfolio = portfolio
        self.allocations: Dict[str, PortfolioAllocation] = {}
        self.rebalance_history: List[Dict[str, Any]] = []
        
    def set_target_allocation(self, allocations: List[PortfolioAllocation]) -> None:
        """
        Set target portfolio allocation.
        
        Args:
            allocations: List of target allocations
        """
        # Validate allocations
        total_weight = sum(alloc.target_weight for alloc in allocations)
        if abs(total_weight - 1.0) > 0.01:
            raise ValueError(f"Total allocation weights must sum to 1.0, got {total_weight}")
        
        self.allocations = {alloc.symbol: alloc for alloc in allocations}
    
    def get_current_allocation(self) -> Dict[str, float]:
        """
        Get current portfolio allocation weights.
        
        Returns:
            Dictionary mapping symbols to current weights
        """
        if self.portfolio.total_value <= 0:
            return {}
        
        allocation = {}
        for symbol, position in self.portfolio.positions.items():
            allocation[symbol] = position.market_value / self.portfolio.total_value
        
        # Include cash as allocation if it's a significant portion
        cash_weight = self.portfolio.cash / self.portfolio.total_value
        if cash_weight > 0.001:  # Only include if cash is more than 0.1%
            allocation['CASH'] = cash_weight
        
        return allocation
    
    def analyze_allocation_drift(self) -> Dict[str, float]:
        """
        Analyze how much current allocation has drifted from target.
        
        Returns:
            Dictionary mapping symbols to drift percentages
        """
        current_allocation = self.get_current_allocation()
        drift = {}
        
        for symbol, target_alloc in self.allocations.items():
            current_weight = current_allocation.get(symbol, 0.0)
            drift[symbol] = current_weight - target_alloc.target_weight
        
        return drift
    
    def get_rebalance_recommendations(self, market_prices: Dict[str, float]) -> List[RebalanceRecommendation]:
        """
        Get rebalancing recommendations based on target allocation.
        
        Args:
            market_prices: Current market prices for symbols
            
        Returns:
            List of rebalancing recommendations
        """
        recommendations = []
        current_allocation = self.get_current_allocation()
        drift = self.analyze_allocation_drift()
        
        for symbol, target_alloc in self.allocations.items():
            current_weight = current_allocation.get(symbol, 0.0)
            deviation = abs(drift[symbol])
            
            # Check if rebalancing is needed
            if deviation > target_alloc.rebalance_threshold:
                current_price = market_prices.get(symbol)
                if current_price is None:
                    continue
                
                # Calculate recommended action
                target_value = target_alloc.target_weight * self.portfolio.total_value
                current_value = current_weight * self.portfolio.total_value
                value_difference = target_value - current_value
                
                if value_difference > 0:
                    action = 'BUY'
                    quantity = value_difference / current_price
                else:
                    action = 'SELL'
                    quantity = abs(value_difference) / current_price
                
                recommendation = RebalanceRecommendation(
                    symbol=symbol,
                    current_weight=current_weight,
                    target_weight=target_alloc.target_weight,
                    deviation=drift[symbol],
                    recommended_action=action,
                    recommended_quantity=quantity,
                    recommended_value=abs(value_difference)
                )
                
                recommendations.append(recommendation)
        
        return recommendations
    
    def calculate_portfolio_metrics(self, benchmark_returns: Optional[pd.Series] = None) -> Dict[str, float]:
        """
        Calculate comprehensive portfolio performance metrics.
        
        Args:
            benchmark_returns: Optional benchmark returns for comparison
            
        Returns:
            Dictionary of performance metrics
        """
        metrics = self.portfolio.get_performance_metrics()
        
        if benchmark_returns is not None and len(self.portfolio.daily_returns) > 0:
            # Calculate additional metrics vs benchmark
            portfolio_returns = pd.Series(self.portfolio.daily_returns)
            
            # Align series
            min_length = min(len(portfolio_returns), len(benchmark_returns))
            portfolio_returns = portfolio_returns.tail(min_length)
            benchmark_returns = benchmark_returns.tail(min_length)
            
            # Calculate excess returns
            excess_returns = portfolio_returns - benchmark_returns
            
            # Information ratio
            if excess_returns.std() > 0:
                metrics['information_ratio'] = excess_returns.mean() / excess_returns.std() * np.sqrt(252)
            else:
                metrics['information_ratio'] = 0.0
            
            # Beta
            if benchmark_returns.var() > 0:
                metrics['beta'] = portfolio_returns.cov(benchmark_returns) / benchmark_returns.var()
            else:
                metrics['beta'] = 0.0
            
            # Alpha (annualized)
            risk_free_rate = 0.0  # Assume 0% risk-free rate
            metrics['alpha'] = (metrics['annualized_return'] - risk_free_rate) - \
                              metrics['beta'] * (benchmark_returns.mean() * 252 - risk_free_rate)
            
            # Tracking error
            metrics['tracking_error'] = excess_returns.std() * np.sqrt(252)
        
        return metrics
    
    def analyze_position_concentration(self) -> Dict[str, Any]:
        """
        Analyze portfolio concentration and diversification.
        
        Returns:
            Dictionary with concentration analysis
        """
        if not self.portfolio.positions:
            return {
                'concentration_ratio': 0.0, 
                'herfindahl_index': 0.0, 
                'effective_positions': 0,
                'number_of_positions': 0,
                'largest_position_weight': 0.0,
                'smallest_position_weight': 0.0,
                'average_position_weight': 0.0
            }
        
        weights = []
        for position in self.portfolio.positions.values():
            if self.portfolio.total_value > 0:
                weight = position.market_value / self.portfolio.total_value
                weights.append(weight)
        
        if not weights:
            return {
                'concentration_ratio': 0.0, 
                'herfindahl_index': 0.0, 
                'effective_positions': 0,
                'number_of_positions': 0,
                'largest_position_weight': 0.0,
                'smallest_position_weight': 0.0,
                'average_position_weight': 0.0
            }
        
        weights = np.array(weights)
        
        # Top 5 concentration ratio (or all positions if less than 5)
        num_top = min(5, len(weights))
        top_weights = np.sort(weights)[-num_top:]
        concentration_ratio = np.sum(top_weights)
        
        # Herfindahl-Hirschman Index
        herfindahl_index = np.sum(weights ** 2)
        
        # Effective number of positions
        effective_positions = 1 / herfindahl_index if herfindahl_index > 0 else 0
        
        return {
            'concentration_ratio': concentration_ratio,
            'herfindahl_index': herfindahl_index,
            'effective_positions': effective_positions,
            'number_of_positions': len(weights),
            'largest_position_weight': np.max(weights),
            'smallest_position_weight': np.min(weights),
            'average_position_weight': np.mean(weights)
        }
    
    def calculate_var(self, confidence_level: float = 0.05, lookback_days: int = 252) -> float:
        """
        Calculate Value at Risk (VaR) using historical simulation.
        
        Args:
            confidence_level: Confidence level (e.g., 0.05 for 95% VaR)
            lookback_days: Number of days to look back for calculation
            
        Returns:
            VaR value (positive number representing potential loss)
        """
        if len(self.portfolio.daily_returns) < lookback_days:
            lookback_days = len(self.portfolio.daily_returns)
        
        if lookback_days == 0:
            return 0.0
        
        recent_returns = self.portfolio.daily_returns[-lookback_days:]
        returns_array = np.array(recent_returns)
        
        # Calculate VaR as the percentile
        var_percentile = np.percentile(returns_array, confidence_level * 100)
        
        # Convert to dollar amount
        var_dollar = abs(var_percentile * self.portfolio.total_value)
        
        return var_dollar
    
    def calculate_expected_shortfall(self, confidence_level: float = 0.05, 
                                   lookback_days: int = 252) -> float:
        """
        Calculate Expected Shortfall (Conditional VaR).
        
        Args:
            confidence_level: Confidence level (e.g., 0.05 for 95% ES)
            lookback_days: Number of days to look back for calculation
            
        Returns:
            Expected Shortfall value
        """
        if len(self.portfolio.daily_returns) < lookback_days:
            lookback_days = len(self.portfolio.daily_returns)
        
        if lookback_days == 0:
            return 0.0
        
        recent_returns = self.portfolio.daily_returns[-lookback_days:]
        returns_array = np.array(recent_returns)
        
        # Calculate VaR threshold
        var_threshold = np.percentile(returns_array, confidence_level * 100)
        
        # Calculate expected shortfall (average of returns below VaR)
        tail_returns = returns_array[returns_array <= var_threshold]
        
        if len(tail_returns) == 0:
            return 0.0
        
        expected_shortfall = np.mean(tail_returns)
        
        # Convert to dollar amount
        es_dollar = abs(expected_shortfall * self.portfolio.total_value)
        
        return es_dollar
    
    def generate_performance_report(self, start_date: Optional[datetime] = None,
                                  end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Generate comprehensive performance report.
        
        Args:
            start_date: Start date for report period
            end_date: End date for report period
            
        Returns:
            Comprehensive performance report
        """
        # Filter history by date range if specified
        history_df = self.portfolio.get_history_dataframe()
        if not history_df.empty:
            if start_date:
                history_df = history_df[history_df.index >= start_date]
            if end_date:
                history_df = history_df[history_df.index <= end_date]
        
        # Basic portfolio information
        report = {
            'portfolio_summary': {
                'initial_capital': self.portfolio.initial_capital,
                'current_value': self.portfolio.total_value,
                'cash': self.portfolio.cash,
                'positions_value': sum(pos.market_value for pos in self.portfolio.positions.values()),
                'number_of_positions': len(self.portfolio.positions),
                'total_trades': len(self.portfolio.trades)
            },
            'performance_metrics': self.portfolio.get_performance_metrics(),
            'allocation_analysis': {
                'current_allocation': self.get_current_allocation(),
                'concentration_metrics': self.analyze_position_concentration()
            },
            'risk_metrics': {
                'var_95': self.calculate_var(0.05),
                'var_99': self.calculate_var(0.01),
                'expected_shortfall_95': self.calculate_expected_shortfall(0.05),
                'expected_shortfall_99': self.calculate_expected_shortfall(0.01)
            },
            'cash_flow_summary': self.portfolio.get_cash_flow_summary(start_date, end_date),
            'positions_summary': self.portfolio.get_positions_summary()
        }
        
        # Add target allocation analysis if configured
        if self.allocations:
            report['allocation_analysis']['target_allocation'] = {
                symbol: alloc.target_weight for symbol, alloc in self.allocations.items()
            }
            report['allocation_analysis']['allocation_drift'] = self.analyze_allocation_drift()
        
        return report
    
    def export_to_dataframe(self) -> Dict[str, pd.DataFrame]:
        """
        Export portfolio data to pandas DataFrames.
        
        Returns:
            Dictionary containing various DataFrames
        """
        dataframes = {
            'portfolio_history': self.portfolio.get_history_dataframe(),
            'cash_flows': self.portfolio.get_cash_flows_dataframe()
        }
        
        # Trades DataFrame
        if self.portfolio.trades:
            trades_data = []
            for trade in self.portfolio.trades:
                trades_data.append({
                    'timestamp': trade.timestamp,
                    'symbol': trade.symbol,
                    'side': trade.side.value,
                    'quantity': trade.quantity,
                    'price': trade.price,
                    'commission': trade.commission,
                    'strategy_id': trade.strategy_id,
                    'pnl': trade.pnl
                })
            
            trades_df = pd.DataFrame(trades_data)
            trades_df.set_index('timestamp', inplace=True)
            dataframes['trades'] = trades_df.sort_index()
        
        # Positions DataFrame
        if self.portfolio.positions:
            positions_data = []
            for symbol, position in self.portfolio.positions.items():
                positions_data.append({
                    'symbol': symbol,
                    'quantity': position.quantity,
                    'avg_price': position.avg_price,
                    'market_value': position.market_value,
                    'unrealized_pnl': position.unrealized_pnl,
                    'realized_pnl': position.realized_pnl,
                    'last_price': position.last_price,
                    'weight': position.market_value / self.portfolio.total_value if self.portfolio.total_value > 0 else 0.0
                })
            
            dataframes['positions'] = pd.DataFrame(positions_data)
        
        return dataframes
    
    def simulate_rebalancing(self, market_prices: Dict[str, float], 
                           dry_run: bool = True) -> List[Dict[str, Any]]:
        """
        Simulate portfolio rebalancing based on target allocation.
        
        Args:
            market_prices: Current market prices
            dry_run: If True, only simulate without executing trades
            
        Returns:
            List of simulated trades
        """
        recommendations = self.get_rebalance_recommendations(market_prices)
        simulated_trades = []
        
        for rec in recommendations:
            trade_info = {
                'symbol': rec.symbol,
                'action': rec.recommended_action,
                'quantity': rec.recommended_quantity,
                'estimated_value': rec.recommended_value,
                'current_weight': rec.current_weight,
                'target_weight': rec.target_weight,
                'price': market_prices.get(rec.symbol, 0.0)
            }
            simulated_trades.append(trade_info)
        
        if not dry_run:
            # Record rebalancing event
            rebalance_event = {
                'timestamp': datetime.now(),
                'total_value': self.portfolio.total_value,
                'trades': simulated_trades,
                'allocation_before': self.get_current_allocation()
            }
            self.rebalance_history.append(rebalance_event)
        
        return simulated_trades