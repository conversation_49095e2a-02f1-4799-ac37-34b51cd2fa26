"""
日志管理和错误追踪系统
"""

import os
import sys
import json
import logging
import logging.handlers
import traceback
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass, asdict
import threading
from queue import Queue, Empty

from config.settings import config


@dataclass
class LogEntry:
    """日志条目"""
    timestamp: str
    level: str
    logger: str
    message: str
    module: Optional[str] = None
    function_name: Optional[str] = None
    line_number: Optional[int] = None
    exception_info: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None
    thread_id: Optional[int] = None
    process_id: Optional[int] = None


class DatabaseLogHandler(logging.Handler):
    """数据库日志处理器"""
    
    def __init__(self, batch_size: int = 100, flush_interval: int = 30):
        super().__init__()
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.log_queue = Queue()
        self.batch_logs = []
        
        # 启动后台线程处理日志
        self.worker_thread = threading.Thread(target=self._process_logs, daemon=True)
        self.worker_thread.start()
        
        # 启动定时刷新线程
        self.flush_thread = threading.Thread(target=self._flush_periodically, daemon=True)
        self.flush_thread.start()
    
    def emit(self, record):
        """发送日志记录"""
        try:
            log_entry = self._create_log_entry(record)
            self.log_queue.put(log_entry)
        except Exception:
            self.handleError(record)
    
    def _create_log_entry(self, record) -> LogEntry:
        """创建日志条目"""
        # 获取异常信息
        exception_info = None
        if record.exc_info:
            exception_info = ''.join(traceback.format_exception(*record.exc_info))
        
        # 获取额外数据
        extra_data = {}
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'exc_info', 'exc_text', 'stack_info']:
                extra_data[key] = value
        
        return LogEntry(
            timestamp=datetime.fromtimestamp(record.created).isoformat(),
            level=record.levelname,
            logger=record.name,
            message=record.getMessage(),
            module=record.module,
            function_name=record.funcName,
            line_number=record.lineno,
            exception_info=exception_info,
            extra_data=extra_data if extra_data else None,
            thread_id=record.thread,
            process_id=record.process
        )
    
    def _process_logs(self):
        """处理日志队列"""
        while True:
            try:
                # 从队列获取日志
                log_entry = self.log_queue.get(timeout=1)
                self.batch_logs.append(log_entry)
                
                # 批量写入数据库
                if len(self.batch_logs) >= self.batch_size:
                    self._flush_to_database()
                    
            except Empty:
                continue
            except Exception as e:
                print(f"处理日志失败: {e}", file=sys.stderr)
    
    def _flush_periodically(self):
        """定期刷新日志"""
        import time
        
        while True:
            time.sleep(self.flush_interval)
            if self.batch_logs:
                self._flush_to_database()
    
    def _flush_to_database(self):
        """刷新日志到数据库"""
        if not self.batch_logs:
            return
        
        try:
            from sqlalchemy import create_engine, text
            
            engine = create_engine(config.get_database_url())
            
            with engine.connect() as conn:
                for log_entry in self.batch_logs:
                    conn.execute(text("""
                        INSERT INTO system_logs 
                        (timestamp, level, logger, message, module, function_name, 
                         line_number, exception_info, extra_data)
                        VALUES (:timestamp, :level, :logger, :message, :module, 
                                :function_name, :line_number, :exception_info, :extra_data)
                    """), {
                        'timestamp': log_entry.timestamp,
                        'level': log_entry.level,
                        'logger': log_entry.logger,
                        'message': log_entry.message,
                        'module': log_entry.module,
                        'function_name': log_entry.function_name,
                        'line_number': log_entry.line_number,
                        'exception_info': log_entry.exception_info,
                        'extra_data': json.dumps(log_entry.extra_data) if log_entry.extra_data else None
                    })
                
                conn.commit()
            
            self.batch_logs.clear()
            
        except Exception as e:
            print(f"写入数据库日志失败: {e}", file=sys.stderr)


class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record):
        """格式化日志记录为JSON"""
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'process': record.process
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = ''.join(traceback.format_exception(*record.exc_info))
        
        # 添加额外字段
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'exc_info', 'exc_text', 'stack_info']:
                log_data[key] = value
        
        return json.dumps(log_data, ensure_ascii=False)


class ErrorTracker:
    """错误追踪器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.error_counts = {}
        self.error_details = {}
    
    def track_error(self, error: Exception, context: Dict[str, Any] = None):
        """追踪错误"""
        error_type = type(error).__name__
        error_message = str(error)
        error_key = f"{error_type}:{error_message}"
        
        # 统计错误次数
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # 记录错误详情
        error_detail = {
            'type': error_type,
            'message': error_message,
            'traceback': traceback.format_exc(),
            'context': context or {},
            'timestamp': datetime.now().isoformat(),
            'count': self.error_counts[error_key]
        }
        
        self.error_details[error_key] = error_detail
        
        # 记录日志
        self.logger.error(
            f"错误追踪: {error_type} - {error_message}",
            extra={
                'error_type': error_type,
                'error_count': self.error_counts[error_key],
                'context': context
            },
            exc_info=True
        )
        
        # 如果错误频繁发生，发送告警
        if self.error_counts[error_key] >= 10:
            self._send_alert(error_detail)
    
    def _send_alert(self, error_detail: Dict[str, Any]):
        """发送告警"""
        try:
            # 这里可以集成告警系统，如邮件、Slack、钉钉等
            self.logger.critical(
                f"高频错误告警: {error_detail['type']} 已发生 {error_detail['count']} 次",
                extra={'alert': True, 'error_detail': error_detail}
            )
        except Exception as e:
            self.logger.error(f"发送告警失败: {e}")
    
    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        return {
            'total_errors': len(self.error_counts),
            'total_occurrences': sum(self.error_counts.values()),
            'top_errors': sorted(
                self.error_counts.items(),
                key=lambda x: x[1],
                reverse=True
            )[:10],
            'recent_errors': [
                detail for detail in self.error_details.values()
                if datetime.fromisoformat(detail['timestamp']) > 
                   datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            ]
        }


class LoggingManager:
    """日志管理器"""
    
    def __init__(self):
        self.error_tracker = ErrorTracker()
        self.configured = False
    
    def setup_logging(self):
        """设置日志系统"""
        if self.configured:
            return
        
        # 创建日志目录
        log_dir = Path(config.logging.file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, config.logging.level.upper()))
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 创建格式化器
        if hasattr(config.logging, 'enable_json_format') and config.logging.enable_json_format:
            formatter = JSONFormatter()
        else:
            formatter = logging.Formatter(config.logging.format)
        
        # 文件处理器（轮转）
        file_handler = logging.handlers.RotatingFileHandler(
            config.logging.file_path,
            maxBytes=config.logging.max_file_size,
            backupCount=config.logging.backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        root_logger.addHandler(file_handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(getattr(logging, config.logging.level.upper()))
        root_logger.addHandler(console_handler)
        
        # 错误文件处理器
        error_handler = logging.handlers.RotatingFileHandler(
            str(log_dir / "errors.log"),
            maxBytes=config.logging.max_file_size,
            backupCount=config.logging.backup_count,
            encoding='utf-8'
        )
        error_handler.setFormatter(formatter)
        error_handler.setLevel(logging.ERROR)
        root_logger.addHandler(error_handler)
        
        # 数据库处理器（生产环境）
        if config.is_production():
            try:
                db_handler = DatabaseLogHandler()
                db_handler.setFormatter(formatter)
                db_handler.setLevel(logging.WARNING)
                root_logger.addHandler(db_handler)
            except Exception as e:
                logging.error(f"设置数据库日志处理器失败: {e}")
        
        # Syslog处理器（生产环境）
        if (config.is_production() and 
            hasattr(config.logging, 'enable_syslog') and 
            config.logging.enable_syslog):
            try:
                syslog_handler = logging.handlers.SysLogHandler(
                    address=config.logging.syslog_address
                )
                syslog_handler.setFormatter(formatter)
                syslog_handler.setLevel(logging.WARNING)
                root_logger.addHandler(syslog_handler)
            except Exception as e:
                logging.error(f"设置Syslog处理器失败: {e}")
        
        # 设置第三方库日志级别
        self._configure_third_party_loggers()
        
        # 安装全局异常处理器
        self._install_exception_handler()
        
        self.configured = True
        logging.info(f"日志系统初始化完成 - 环境: {config.environment}")
    
    def _configure_third_party_loggers(self):
        """配置第三方库日志级别"""
        third_party_loggers = {
            'urllib3': logging.WARNING,
            'requests': logging.WARNING,
            'sqlalchemy.engine': logging.WARNING,
            'sqlalchemy.pool': logging.WARNING,
            'celery': logging.INFO,
            'uvicorn': logging.INFO,
            'fastapi': logging.INFO
        }
        
        for logger_name, level in third_party_loggers.items():
            logging.getLogger(logger_name).setLevel(level)
    
    def _install_exception_handler(self):
        """安装全局异常处理器"""
        def handle_exception(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            
            # 追踪错误
            self.error_tracker.track_error(exc_value, {
                'type': 'unhandled_exception',
                'traceback': ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            })
            
            # 记录日志
            logging.critical(
                "未处理的异常",
                exc_info=(exc_type, exc_value, exc_traceback)
            )
        
        sys.excepthook = handle_exception
    
    def get_log_stats(self) -> Dict[str, Any]:
        """获取日志统计"""
        try:
            from sqlalchemy import create_engine, text
            
            engine = create_engine(config.get_database_url())
            
            with engine.connect() as conn:
                # 获取日志级别统计
                result = conn.execute(text("""
                    SELECT level, COUNT(*) as count
                    FROM system_logs
                    WHERE timestamp >= NOW() - INTERVAL '24 hours'
                    GROUP BY level
                    ORDER BY count DESC
                """))
                
                level_stats = {row[0]: row[1] for row in result}
                
                # 获取最近错误
                result = conn.execute(text("""
                    SELECT timestamp, logger, message, exception_info
                    FROM system_logs
                    WHERE level IN ('ERROR', 'CRITICAL')
                    AND timestamp >= NOW() - INTERVAL '1 hour'
                    ORDER BY timestamp DESC
                    LIMIT 10
                """))
                
                recent_errors = [
                    {
                        'timestamp': row[0].isoformat(),
                        'logger': row[1],
                        'message': row[2],
                        'exception': row[3]
                    }
                    for row in result
                ]
                
                return {
                    'level_stats': level_stats,
                    'recent_errors': recent_errors,
                    'error_summary': self.error_tracker.get_error_summary()
                }
                
        except Exception as e:
            logging.error(f"获取日志统计失败: {e}")
            return {
                'error_summary': self.error_tracker.get_error_summary()
            }
    
    def cleanup_old_logs(self, days: int = 30):
        """清理旧日志"""
        try:
            from sqlalchemy import create_engine, text
            
            engine = create_engine(config.get_database_url())
            
            with engine.connect() as conn:
                result = conn.execute(text("""
                    DELETE FROM system_logs
                    WHERE timestamp < NOW() - INTERVAL '%s days'
                """), (days,))
                
                deleted_count = result.rowcount
                conn.commit()
                
                logging.info(f"清理了 {deleted_count} 条旧日志记录")
                return deleted_count
                
        except Exception as e:
            logging.error(f"清理旧日志失败: {e}")
            return 0


# 全局日志管理器实例
logging_manager = LoggingManager()


def setup_logging():
    """设置日志系统（便捷函数）"""
    logging_manager.setup_logging()


def track_error(error: Exception, context: Dict[str, Any] = None):
    """追踪错误（便捷函数）"""
    logging_manager.error_tracker.track_error(error, context)


def get_logger(name: str) -> logging.Logger:
    """获取日志器（便捷函数）"""
    return logging.getLogger(name)