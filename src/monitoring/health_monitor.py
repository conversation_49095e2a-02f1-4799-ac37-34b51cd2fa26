"""
系统健康监控服务
"""

import asyncio
import logging
import psutil
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import json

logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    """健康状态"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

@dataclass
class HealthCheck:
    """健康检查结果"""
    name: str
    status: HealthStatus
    message: str
    response_time_ms: Optional[float] = None
    details: Dict[str, Any] = None
    last_check: datetime = None
    error: Optional[str] = None
    
    def __post_init__(self):
        if self.last_check is None:
            self.last_check = datetime.now()
        if self.details is None:
            self.details = {}

@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, int]
    process_count: int
    load_average: float
    uptime_seconds: int

class HealthMonitor:
    """系统健康监控器"""
    
    def __init__(self):
        self.checks: Dict[str, Callable] = {}
        self.check_intervals: Dict[str, int] = {}
        self.check_results: Dict[str, HealthCheck] = {}
        self.metrics_history: List[SystemMetrics] = []
        self.max_history_size = 1000
        self.running = False
        self.tasks: List[asyncio.Task] = []
        self.alert_thresholds = {
            "cpu_usage": 80.0,
            "memory_usage": 85.0,
            "disk_usage": 90.0,
            "response_time_ms": 5000.0
        }
        self.start_time = time.time()
    
    def register_check(self, name: str, check_func: Callable, interval: int = 60):
        """注册健康检查"""
        self.checks[name] = check_func
        self.check_intervals[name] = interval
        logger.info(f"Registered health check: {name} (interval: {interval}s)")
    
    async def start(self):
        """启动健康监控"""
        if self.running:
            return
        
        self.running = True
        
        # 启动系统指标收集任务
        self.tasks.append(asyncio.create_task(self._collect_system_metrics()))
        
        # 启动各个健康检查任务
        for name, check_func in self.checks.items():
            interval = self.check_intervals[name]
            task = asyncio.create_task(self._run_health_check(name, check_func, interval))
            self.tasks.append(task)
        
        logger.info("Health monitor started")
    
    async def stop(self):
        """停止健康监控"""
        if not self.running:
            return
        
        self.running = False
        
        # 取消所有任务
        for task in self.tasks:
            task.cancel()
        
        # 等待任务完成
        await asyncio.gather(*self.tasks, return_exceptions=True)
        self.tasks.clear()
        
        logger.info("Health monitor stopped")
    
    async def get_overall_health(self) -> Dict[str, Any]:
        """获取整体健康状态"""
        if not self.check_results:
            return {
                "status": HealthStatus.UNKNOWN.value,
                "message": "No health checks available",
                "timestamp": datetime.now().isoformat()
            }
        
        # 确定整体状态
        statuses = [check.status for check in self.check_results.values()]
        
        if HealthStatus.CRITICAL in statuses:
            overall_status = HealthStatus.CRITICAL
        elif HealthStatus.WARNING in statuses:
            overall_status = HealthStatus.WARNING
        elif HealthStatus.UNKNOWN in statuses:
            overall_status = HealthStatus.WARNING
        else:
            overall_status = HealthStatus.HEALTHY
        
        # 统计各状态数量
        status_counts = {
            HealthStatus.HEALTHY.value: statuses.count(HealthStatus.HEALTHY),
            HealthStatus.WARNING.value: statuses.count(HealthStatus.WARNING),
            HealthStatus.CRITICAL.value: statuses.count(HealthStatus.CRITICAL),
            HealthStatus.UNKNOWN.value: statuses.count(HealthStatus.UNKNOWN)
        }
        
        return {
            "status": overall_status.value,
            "timestamp": datetime.now().isoformat(),
            "total_checks": len(self.check_results),
            "status_counts": status_counts,
            "checks": {name: asdict(check) for name, check in self.check_results.items()},
            "uptime_seconds": int(time.time() - self.start_time)
        }
    
    async def get_system_metrics(self) -> Optional[SystemMetrics]:
        """获取最新的系统指标"""
        if self.metrics_history:
            return self.metrics_history[-1]
        return None
    
    async def get_metrics_history(self, minutes: int = 60) -> List[SystemMetrics]:
        """获取指定时间范围内的指标历史"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [
            metric for metric in self.metrics_history
            if metric.timestamp >= cutoff_time
        ]
    
    async def run_check(self, name: str) -> Optional[HealthCheck]:
        """手动运行指定的健康检查"""
        if name not in self.checks:
            return None
        
        check_func = self.checks[name]
        return await self._execute_check(name, check_func)
    
    async def _collect_system_metrics(self):
        """收集系统指标"""
        while self.running:
            try:
                # 获取系统指标
                cpu_usage = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                net_io = psutil.net_io_counters()
                
                # 获取负载平均值
                try:
                    load_avg = psutil.getloadavg()[0]
                except (AttributeError, OSError):
                    load_avg = cpu_usage / 100.0
                
                metrics = SystemMetrics(
                    timestamp=datetime.now(),
                    cpu_usage=cpu_usage,
                    memory_usage=memory.percent,
                    disk_usage=disk.percent,
                    network_io={
                        "bytes_sent": net_io.bytes_sent,
                        "bytes_recv": net_io.bytes_recv,
                        "packets_sent": net_io.packets_sent,
                        "packets_recv": net_io.packets_recv
                    },
                    process_count=len(psutil.pids()),
                    load_average=load_avg,
                    uptime_seconds=int(time.time() - self.start_time)
                )
                
                # 添加到历史记录
                self.metrics_history.append(metrics)
                
                # 限制历史记录大小
                if len(self.metrics_history) > self.max_history_size:
                    self.metrics_history = self.metrics_history[-self.max_history_size:]
                
                # 检查是否需要告警
                await self._check_metric_thresholds(metrics)
                
                await asyncio.sleep(10)  # 每10秒收集一次
                
            except Exception as e:
                logger.error(f"Error collecting system metrics: {str(e)}")
                await asyncio.sleep(30)
    
    async def _run_health_check(self, name: str, check_func: Callable, interval: int):
        """运行健康检查任务"""
        while self.running:
            try:
                result = await self._execute_check(name, check_func)
                self.check_results[name] = result
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"Error in health check {name}: {str(e)}")
                # 记录检查失败
                self.check_results[name] = HealthCheck(
                    name=name,
                    status=HealthStatus.CRITICAL,
                    message=f"Health check failed: {str(e)}",
                    error=str(e)
                )
                await asyncio.sleep(interval)
    
    async def _execute_check(self, name: str, check_func: Callable) -> HealthCheck:
        """执行单个健康检查"""
        start_time = time.time()
        
        try:
            if asyncio.iscoroutinefunction(check_func):
                result = await check_func()
            else:
                result = check_func()
            
            response_time = (time.time() - start_time) * 1000
            
            if isinstance(result, HealthCheck):
                result.response_time_ms = response_time
                return result
            elif isinstance(result, dict):
                return HealthCheck(
                    name=name,
                    status=HealthStatus(result.get("status", "healthy")),
                    message=result.get("message", "OK"),
                    response_time_ms=response_time,
                    details=result.get("details", {})
                )
            else:
                return HealthCheck(
                    name=name,
                    status=HealthStatus.HEALTHY,
                    message="OK",
                    response_time_ms=response_time
                )
                
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheck(
                name=name,
                status=HealthStatus.CRITICAL,
                message=f"Check failed: {str(e)}",
                response_time_ms=response_time,
                error=str(e)
            )
    
    async def _check_metric_thresholds(self, metrics: SystemMetrics):
        """检查指标阈值并生成告警"""
        alerts = []
        
        if metrics.cpu_usage > self.alert_thresholds["cpu_usage"]:
            alerts.append({
                "type": "cpu_usage_high",
                "message": f"CPU usage is {metrics.cpu_usage:.1f}%",
                "value": metrics.cpu_usage,
                "threshold": self.alert_thresholds["cpu_usage"]
            })
        
        if metrics.memory_usage > self.alert_thresholds["memory_usage"]:
            alerts.append({
                "type": "memory_usage_high",
                "message": f"Memory usage is {metrics.memory_usage:.1f}%",
                "value": metrics.memory_usage,
                "threshold": self.alert_thresholds["memory_usage"]
            })
        
        if metrics.disk_usage > self.alert_thresholds["disk_usage"]:
            alerts.append({
                "type": "disk_usage_high",
                "message": f"Disk usage is {metrics.disk_usage:.1f}%",
                "value": metrics.disk_usage,
                "threshold": self.alert_thresholds["disk_usage"]
            })
        
        # 这里可以发送告警到告警系统
        for alert in alerts:
            logger.warning(f"System alert: {alert['message']}")

# 全局健康监控实例
health_monitor = HealthMonitor()

# 预定义的健康检查函数
async def database_health_check():
    """数据库健康检查"""
    try:
        # 这里应该实际检查数据库连接
        # 模拟检查
        await asyncio.sleep(0.01)  # 模拟数据库查询时间
        
        return HealthCheck(
            name="database",
            status=HealthStatus.HEALTHY,
            message="Database connection is healthy",
            details={
                "connection_pool_size": 10,
                "active_connections": 3,
                "query_avg_time_ms": 15
            }
        )
    except Exception as e:
        return HealthCheck(
            name="database",
            status=HealthStatus.CRITICAL,
            message=f"Database connection failed: {str(e)}",
            error=str(e)
        )

async def data_sources_health_check():
    """数据源健康检查"""
    try:
        # 模拟检查各个数据源
        sources = ["yahoo_finance", "tushare", "binance", "fred"]
        source_status = {}
        
        for source in sources:
            # 模拟检查
            await asyncio.sleep(0.1)
            source_status[source] = "healthy"
        
        return HealthCheck(
            name="data_sources",
            status=HealthStatus.HEALTHY,
            message="All data sources are healthy",
            details={"sources": source_status}
        )
    except Exception as e:
        return HealthCheck(
            name="data_sources",
            status=HealthStatus.WARNING,
            message=f"Some data sources may be unavailable: {str(e)}",
            error=str(e)
        )

async def strategy_engine_health_check():
    """策略引擎健康检查"""
    try:
        # 模拟检查策略引擎状态
        await asyncio.sleep(0.05)
        
        return HealthCheck(
            name="strategy_engine",
            status=HealthStatus.HEALTHY,
            message="Strategy engine is running normally",
            details={
                "active_strategies": 5,
                "total_positions": 12,
                "engine_uptime_hours": 24.5
            }
        )
    except Exception as e:
        return HealthCheck(
            name="strategy_engine",
            status=HealthStatus.CRITICAL,
            message=f"Strategy engine error: {str(e)}",
            error=str(e)
        )

# 注册默认健康检查
def register_default_health_checks():
    """注册默认的健康检查"""
    health_monitor.register_check("database", database_health_check, 30)
    health_monitor.register_check("data_sources", data_sources_health_check, 60)
    health_monitor.register_check("strategy_engine", strategy_engine_health_check, 45)