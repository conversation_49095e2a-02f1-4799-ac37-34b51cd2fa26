"""
审计日志服务 - 记录系统操作和用户行为
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum
import asyncio
from contextlib import asynccontextmanager
import uuid

logger = logging.getLogger(__name__)

class AuditEventType(Enum):
    """审计事件类型"""
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    STRATEGY_CREATE = "strategy_create"
    STRATEGY_UPDATE = "strategy_update"
    STRATEGY_DELETE = "strategy_delete"
    STRATEGY_START = "strategy_start"
    STRATEGY_STOP = "strategy_stop"
    BACKTEST_START = "backtest_start"
    BACKTEST_COMPLETE = "backtest_complete"
    POSITION_MODIFY = "position_modify"
    ALERT_CREATE = "alert_create"
    ALERT_ACKNOWLEDGE = "alert_acknowledge"
    ALERT_RESOLVE = "alert_resolve"
    SYSTEM_CONFIG_CHANGE = "system_config_change"
    DATA_ACCESS = "data_access"
    REPORT_GENERATE = "report_generate"
    API_ACCESS = "api_access"

class AuditSeverity(Enum):
    """审计严重程度"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class AuditEvent:
    """审计事件数据结构"""
    id: str
    event_type: AuditEventType
    user_id: str
    username: str
    resource: str
    resource_id: Optional[str] = None
    action: str = ""
    details: Dict[str, Any] = None
    ip_address: str = ""
    user_agent: str = ""
    timestamp: datetime = None
    success: bool = True
    error_message: Optional[str] = None
    duration_ms: Optional[int] = None
    severity: AuditSeverity = AuditSeverity.INFO
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.details is None:
            self.details = {}
        if not self.id:
            self.id = str(uuid.uuid4())

class AuditLogger:
    """审计日志记录器"""
    
    def __init__(self):
        self.events_buffer: List[AuditEvent] = []
        self.buffer_size = 1000
        self.flush_interval = 30  # 秒
        self._flush_task = None
        self._running = False
    
    async def start(self):
        """启动审计日志服务"""
        if not self._running:
            self._running = True
            self._flush_task = asyncio.create_task(self._periodic_flush())
            logger.info("Audit logger started")
    
    async def stop(self):
        """停止审计日志服务"""
        if self._running:
            self._running = False
            if self._flush_task:
                self._flush_task.cancel()
                try:
                    await self._flush_task
                except asyncio.CancelledError:
                    pass
            await self._flush_events()
            logger.info("Audit logger stopped")
    
    async def log_event(self, event: AuditEvent):
        """记录审计事件"""
        try:
            self.events_buffer.append(event)
            
            # 如果是关键事件或错误，立即刷新
            if event.severity in [AuditSeverity.ERROR, AuditSeverity.CRITICAL] or not event.success:
                await self._flush_events()
            elif len(self.events_buffer) >= self.buffer_size:
                await self._flush_events()
                
            logger.debug(f"Audit event logged: {event.event_type.value} by {event.username}")
            
        except Exception as e:
            logger.error(f"Failed to log audit event: {str(e)}")
    
    async def log_user_action(
        self,
        event_type: AuditEventType,
        user_id: str,
        username: str,
        resource: str,
        action: str,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: str = "",
        user_agent: str = "",
        success: bool = True,
        error_message: Optional[str] = None,
        duration_ms: Optional[int] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None
    ):
        """记录用户操作"""
        severity = AuditSeverity.ERROR if not success else AuditSeverity.INFO
        
        event = AuditEvent(
            id=str(uuid.uuid4()),
            event_type=event_type,
            user_id=user_id,
            username=username,
            resource=resource,
            resource_id=resource_id,
            action=action,
            details=details or {},
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            error_message=error_message,
            duration_ms=duration_ms,
            severity=severity,
            session_id=session_id,
            request_id=request_id
        )
        
        await self.log_event(event)
    
    async def log_system_event(
        self,
        event_type: AuditEventType,
        resource: str,
        action: str,
        details: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        severity: AuditSeverity = AuditSeverity.INFO
    ):
        """记录系统事件"""
        event = AuditEvent(
            id=str(uuid.uuid4()),
            event_type=event_type,
            user_id="system",
            username="system",
            resource=resource,
            action=action,
            details=details or {},
            success=success,
            error_message=error_message,
            severity=severity
        )
        
        await self.log_event(event)
    
    async def get_events(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        user_id: Optional[str] = None,
        event_type: Optional[AuditEventType] = None,
        resource: Optional[str] = None,
        success: Optional[bool] = None,
        limit: int = 100
    ) -> List[AuditEvent]:
        """查询审计事件"""
        try:
            # 这里应该从数据库查询，现在返回缓冲区中的事件作为示例
            events = self.events_buffer.copy()
            
            # 应用筛选条件
            if start_date:
                events = [e for e in events if e.timestamp >= start_date]
            if end_date:
                events = [e for e in events if e.timestamp <= end_date]
            if user_id:
                events = [e for e in events if e.user_id == user_id]
            if event_type:
                events = [e for e in events if e.event_type == event_type]
            if resource:
                events = [e for e in events if e.resource == resource]
            if success is not None:
                events = [e for e in events if e.success == success]
            
            # 按时间倒序排列
            events.sort(key=lambda x: x.timestamp, reverse=True)
            
            return events[:limit]
            
        except Exception as e:
            logger.error(f"Failed to get audit events: {str(e)}")
            return []
    
    async def get_user_activity_summary(
        self,
        user_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """获取用户活动摘要"""
        try:
            events = await self.get_events(
                start_date=start_date,
                end_date=end_date,
                user_id=user_id
            )
            
            summary = {
                "user_id": user_id,
                "total_events": len(events),
                "successful_events": len([e for e in events if e.success]),
                "failed_events": len([e for e in events if not e.success]),
                "event_types": {},
                "resources_accessed": set(),
                "last_activity": None,
                "most_active_hour": None,
                "error_rate": 0.0
            }
            
            if events:
                # 统计事件类型
                for event in events:
                    event_type = event.event_type.value
                    summary["event_types"][event_type] = summary["event_types"].get(event_type, 0) + 1
                    summary["resources_accessed"].add(event.resource)
                
                summary["resources_accessed"] = list(summary["resources_accessed"])
                summary["last_activity"] = events[0].timestamp.isoformat()
                summary["error_rate"] = summary["failed_events"] / summary["total_events"]
                
                # 统计最活跃的小时
                hour_counts = {}
                for event in events:
                    hour = event.timestamp.hour
                    hour_counts[hour] = hour_counts.get(hour, 0) + 1
                
                if hour_counts:
                    summary["most_active_hour"] = max(hour_counts, key=hour_counts.get)
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to get user activity summary: {str(e)}")
            return {}
    
    async def _periodic_flush(self):
        """定期刷新事件缓冲区"""
        while self._running:
            try:
                await asyncio.sleep(self.flush_interval)
                if self.events_buffer:
                    await self._flush_events()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic flush: {str(e)}")
    
    async def _flush_events(self):
        """刷新事件到持久化存储"""
        if not self.events_buffer:
            return
        
        try:
            # 这里应该将事件写入数据库
            # 现在只是记录到日志文件
            events_to_flush = self.events_buffer.copy()
            self.events_buffer.clear()
            
            for event in events_to_flush:
                audit_data = {
                    "id": event.id,
                    "event_type": event.event_type.value,
                    "user_id": event.user_id,
                    "username": event.username,
                    "resource": event.resource,
                    "resource_id": event.resource_id,
                    "action": event.action,
                    "details": event.details,
                    "ip_address": event.ip_address,
                    "user_agent": event.user_agent,
                    "timestamp": event.timestamp.isoformat(),
                    "success": event.success,
                    "error_message": event.error_message,
                    "duration_ms": event.duration_ms,
                    "severity": event.severity.value,
                    "session_id": event.session_id,
                    "request_id": event.request_id
                }
                
                # 记录到专门的审计日志
                audit_logger = logging.getLogger("audit")
                audit_logger.info(json.dumps(audit_data, ensure_ascii=False))
            
            logger.debug(f"Flushed {len(events_to_flush)} audit events")
            
        except Exception as e:
            logger.error(f"Failed to flush audit events: {str(e)}")
            # 如果刷新失败，将事件放回缓冲区
            self.events_buffer.extend(events_to_flush)

# 全局审计日志实例
audit_logger = AuditLogger()

@asynccontextmanager
async def audit_context(
    event_type: AuditEventType,
    user_id: str,
    username: str,
    resource: str,
    action: str,
    resource_id: Optional[str] = None,
    ip_address: str = "",
    user_agent: str = "",
    session_id: Optional[str] = None,
    request_id: Optional[str] = None
):
    """审计上下文管理器，自动记录操作开始和结束"""
    start_time = datetime.now()
    success = True
    error_message = None
    
    try:
        yield
    except Exception as e:
        success = False
        error_message = str(e)
        raise
    finally:
        end_time = datetime.now()
        duration_ms = int((end_time - start_time).total_seconds() * 1000)
        
        await audit_logger.log_user_action(
            event_type=event_type,
            user_id=user_id,
            username=username,
            resource=resource,
            action=action,
            resource_id=resource_id,
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            error_message=error_message,
            duration_ms=duration_ms,
            session_id=session_id,
            request_id=request_id
        )

# 装饰器用于自动审计
def audit_action(
    event_type: AuditEventType,
    resource: str,
    action: str
):
    """审计装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 尝试从参数中提取用户信息
            user_id = "unknown"
            username = "unknown"
            
            # 查找current_user参数
            for arg in args:
                if hasattr(arg, 'id') and hasattr(arg, 'username'):
                    user_id = str(arg.id)
                    username = arg.username
                    break
            
            for key, value in kwargs.items():
                if key == 'current_user' and hasattr(value, 'id'):
                    user_id = str(value.id)
                    username = value.username
                    break
            
            async with audit_context(
                event_type=event_type,
                user_id=user_id,
                username=username,
                resource=resource,
                action=action
            ):
                return await func(*args, **kwargs)
        
        return wrapper
    return decorator