"""
Data coverage range query API for comprehensive data availability analysis.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import pandas as pd

from .manager import get_data_manager
from .adapters.manager import get_data_source_manager
from ..models.market_data import UnifiedMarketData, MarketInfo
from ..exceptions import DataException


logger = logging.getLogger(__name__)


@dataclass
class DateRange:
    """Date range representation."""
    start: datetime
    end: datetime
    days: int
    
    def __post_init__(self):
        if self.start > self.end:
            raise ValueError("Start date must be before end date")
        self.days = (self.end - self.start).days + 1
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'start': self.start.isoformat(),
            'end': self.end.isoformat(),
            'days': self.days
        }


@dataclass
class DataCoverage:
    """Data coverage information for a symbol."""
    symbol: str
    market: str
    exchange: str
    currency: str
    database_coverage: Optional[DateRange]
    source_coverage: Dict[str, DateRange]
    gaps: List[DateRange]
    completeness_ratio: float
    last_updated: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'market': self.market,
            'exchange': self.exchange,
            'currency': self.currency,
            'database_coverage': self.database_coverage.to_dict() if self.database_coverage else None,
            'source_coverage': {name: range_obj.to_dict() for name, range_obj in self.source_coverage.items()},
            'gaps': [gap.to_dict() for gap in self.gaps],
            'completeness_ratio': self.completeness_ratio,
            'last_updated': self.last_updated.isoformat()
        }


@dataclass
class MarketCoverage:
    """Market-wide coverage information."""
    market: str
    total_symbols: int
    symbols_with_data: int
    coverage_ratio: float
    date_range: Optional[DateRange]
    top_symbols: List[str]
    data_sources: List[str]
    last_updated: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'market': self.market,
            'total_symbols': self.total_symbols,
            'symbols_with_data': self.symbols_with_data,
            'coverage_ratio': self.coverage_ratio,
            'date_range': self.date_range.to_dict() if self.date_range else None,
            'top_symbols': self.top_symbols,
            'data_sources': self.data_sources,
            'last_updated': self.last_updated.isoformat()
        }


class DataCoverageAPI:
    """API for querying data coverage and availability."""
    
    def __init__(self):
        self.data_manager = get_data_manager()
        self.source_manager = get_data_source_manager()
    
    def get_symbol_coverage(self, symbol: str, market: Optional[str] = None) -> Optional[DataCoverage]:
        """Get comprehensive coverage information for a symbol."""
        try:
            # Get market info
            market_info = None
            if market:
                market_info = self.data_manager.get_market_info(symbol, market)
            else:
                # Try to find market info from any market
                for mkt in ['US', 'CN', 'CRYPTO']:
                    market_info = self.data_manager.get_market_info(symbol, mkt)
                    if market_info:
                        market = mkt
                        break
            
            if not market_info:
                # Try to get from data sources
                market_info = self.source_manager.get_market_info(symbol)
                if market_info:
                    market = market_info.market
            
            if not market_info:
                logger.warning(f"No market info found for symbol: {symbol}")
                return None
            
            # Get database coverage
            db_date_range = self.data_manager.get_data_date_range(symbol, market)
            database_coverage = None
            if db_date_range:
                database_coverage = DateRange(
                    start=db_date_range[0],
                    end=db_date_range[1],
                    days=(db_date_range[1] - db_date_range[0]).days + 1
                )
            
            # Get source coverage
            source_coverage = {}
            adapters = self.source_manager.get_adapters_for_market(market)
            for adapter in adapters:
                if adapter.validate_symbol(symbol):
                    # For now, assume sources have full coverage
                    # In practice, you'd query each source for their coverage
                    end_date = datetime.now().date()
                    start_date = end_date - timedelta(days=365 * 5)  # 5 years back
                    source_coverage[adapter.config.name] = DateRange(
                        start=datetime.combine(start_date, datetime.min.time()),
                        end=datetime.combine(end_date, datetime.min.time()),
                        days=(end_date - start_date).days + 1
                    )
            
            # Calculate gaps and completeness
            gaps = []
            completeness_ratio = 1.0
            
            if database_coverage:
                # Simple gap detection - in practice, you'd analyze actual data
                df = self.data_manager.get_market_data(
                    symbol, database_coverage.start, database_coverage.end, market
                )
                if not df.empty:
                    # Calculate completeness based on business days
                    expected_days = pd.bdate_range(
                        start=database_coverage.start,
                        end=database_coverage.end
                    )
                    actual_days = len(df)
                    completeness_ratio = actual_days / len(expected_days) if expected_days.size > 0 else 0
            
            return DataCoverage(
                symbol=symbol,
                market=market_info.market,
                exchange=market_info.exchange,
                currency=market_info.currency,
                database_coverage=database_coverage,
                source_coverage=source_coverage,
                gaps=gaps,
                completeness_ratio=completeness_ratio,
                last_updated=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Failed to get coverage for symbol {symbol}: {e}")
            return None
    
    def get_market_coverage(self, market: str) -> Optional[MarketCoverage]:
        """Get coverage information for an entire market."""
        try:
            # Get all symbols for the market
            market_symbols = self.data_manager.get_market_symbols(market, active_only=True)
            total_symbols = len(market_symbols)
            
            if total_symbols == 0:
                return None
            
            # Count symbols with data
            symbols_with_data = 0
            earliest_date = None
            latest_date = None
            
            for market_info in market_symbols[:100]:  # Limit to first 100 for performance
                date_range = self.data_manager.get_data_date_range(market_info.symbol, market)
                if date_range:
                    symbols_with_data += 1
                    if earliest_date is None or date_range[0] < earliest_date:
                        earliest_date = date_range[0]
                    if latest_date is None or date_range[1] > latest_date:
                        latest_date = date_range[1]
            
            coverage_ratio = symbols_with_data / total_symbols if total_symbols > 0 else 0
            
            # Get date range
            date_range_obj = None
            if earliest_date and latest_date:
                date_range_obj = DateRange(
                    start=earliest_date,
                    end=latest_date,
                    days=(latest_date - earliest_date).days + 1
                )
            
            # Get top symbols (by data availability)
            top_symbols = [info.symbol for info in market_symbols[:10]]
            
            # Get available data sources for this market
            adapters = self.source_manager.get_adapters_for_market(market)
            data_sources = [adapter.config.name for adapter in adapters]
            
            return MarketCoverage(
                market=market,
                total_symbols=total_symbols,
                symbols_with_data=symbols_with_data,
                coverage_ratio=coverage_ratio,
                date_range=date_range_obj,
                top_symbols=top_symbols,
                data_sources=data_sources,
                last_updated=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Failed to get market coverage for {market}: {e}")
            return None
    
    def get_all_markets_coverage(self) -> Dict[str, MarketCoverage]:
        """Get coverage information for all markets."""
        markets = self.data_manager.get_all_markets()
        coverage = {}
        
        for market in markets:
            market_coverage = self.get_market_coverage(market)
            if market_coverage:
                coverage[market] = market_coverage
        
        return coverage
    
    def find_data_gaps(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        market: Optional[str] = None
    ) -> List[DateRange]:
        """Find gaps in data coverage for a symbol."""
        try:
            df = self.data_manager.get_market_data(symbol, start_date, end_date, market)
            
            if df.empty:
                # No data at all - entire range is a gap
                return [DateRange(start=start_date, end=end_date, days=(end_date - start_date).days + 1)]
            
            # Generate expected business days
            expected_dates = pd.bdate_range(start=start_date, end=end_date)
            actual_dates = set(df.index.date)
            
            gaps = []
            gap_start = None
            
            for date in expected_dates:
                date_obj = date.date()
                if date_obj not in actual_dates:
                    if gap_start is None:
                        gap_start = date_obj
                else:
                    if gap_start is not None:
                        # End of gap
                        gap_end = date_obj - timedelta(days=1)
                        gaps.append(DateRange(
                            start=datetime.combine(gap_start, datetime.min.time()),
                            end=datetime.combine(gap_end, datetime.min.time()),
                            days=(gap_end - gap_start).days + 1
                        ))
                        gap_start = None
            
            # Handle gap at the end
            if gap_start is not None:
                gaps.append(DateRange(
                    start=datetime.combine(gap_start, datetime.min.time()),
                    end=end_date,
                    days=(end_date.date() - gap_start).days + 1
                ))
            
            return gaps
            
        except Exception as e:
            logger.error(f"Failed to find data gaps for {symbol}: {e}")
            return []
    
    def get_symbols_by_coverage(
        self, 
        market: str, 
        min_coverage_ratio: float = 0.8,
        min_days: int = 252  # ~1 year of trading days
    ) -> List[Dict[str, Any]]:
        """Get symbols filtered by coverage criteria."""
        try:
            market_symbols = self.data_manager.get_market_symbols(market, active_only=True)
            filtered_symbols = []
            
            for market_info in market_symbols:
                coverage = self.get_symbol_coverage(market_info.symbol, market)
                if coverage and coverage.database_coverage:
                    if (coverage.completeness_ratio >= min_coverage_ratio and 
                        coverage.database_coverage.days >= min_days):
                        filtered_symbols.append({
                            'symbol': coverage.symbol,
                            'coverage_ratio': coverage.completeness_ratio,
                            'days': coverage.database_coverage.days,
                            'date_range': coverage.database_coverage.to_dict()
                        })
            
            # Sort by coverage ratio descending
            filtered_symbols.sort(key=lambda x: x['coverage_ratio'], reverse=True)
            
            return filtered_symbols
            
        except Exception as e:
            logger.error(f"Failed to get symbols by coverage for {market}: {e}")
            return []
    
    def get_coverage_summary(self) -> Dict[str, Any]:
        """Get comprehensive coverage summary."""
        try:
            # Get database stats
            db_stats = self.data_manager.get_database_stats()
            
            # Get all markets coverage
            markets_coverage = self.get_all_markets_coverage()
            
            # Get data source status
            source_health = self.source_manager.health_check_all()
            
            # Calculate totals
            total_symbols = sum(coverage.total_symbols for coverage in markets_coverage.values())
            total_symbols_with_data = sum(coverage.symbols_with_data for coverage in markets_coverage.values())
            overall_coverage_ratio = total_symbols_with_data / total_symbols if total_symbols > 0 else 0
            
            return {
                'summary': {
                    'total_markets': len(markets_coverage),
                    'total_symbols': total_symbols,
                    'symbols_with_data': total_symbols_with_data,
                    'overall_coverage_ratio': overall_coverage_ratio
                },
                'markets': {name: coverage.to_dict() for name, coverage in markets_coverage.items()},
                'database_stats': db_stats,
                'data_sources': source_health,
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get coverage summary: {e}")
            return {'error': str(e)}
    
    def export_coverage_report(
        self, 
        output_format: str = 'json',
        include_details: bool = True
    ) -> Union[str, bytes]:
        """Export coverage report in specified format."""
        try:
            summary = self.get_coverage_summary()
            
            if output_format.lower() == 'json':
                import json
                return json.dumps(summary, indent=2, ensure_ascii=False)
            
            elif output_format.lower() == 'csv':
                # Create CSV with market-level data
                import io
                import csv
                
                output = io.StringIO()
                writer = csv.writer(output)
                
                # Write header
                writer.writerow([
                    'Market', 'Total Symbols', 'Symbols with Data', 
                    'Coverage Ratio', 'Date Range Start', 'Date Range End',
                    'Data Sources'
                ])
                
                # Write market data
                for market_name, market_data in summary.get('markets', {}).items():
                    date_range = market_data.get('date_range', {})
                    writer.writerow([
                        market_name,
                        market_data.get('total_symbols', 0),
                        market_data.get('symbols_with_data', 0),
                        f"{market_data.get('coverage_ratio', 0):.2%}",
                        date_range.get('start', ''),
                        date_range.get('end', ''),
                        ', '.join(market_data.get('data_sources', []))
                    ])
                
                return output.getvalue()
            
            elif output_format.lower() == 'html':
                # Create HTML report
                html_template = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Data Coverage Report</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        table { border-collapse: collapse; width: 100%; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                        .summary { background-color: #e7f3ff; padding: 15px; margin-bottom: 20px; }
                    </style>
                </head>
                <body>
                    <h1>Data Coverage Report</h1>
                    <div class="summary">
                        <h2>Summary</h2>
                        <p>Total Markets: {total_markets}</p>
                        <p>Total Symbols: {total_symbols}</p>
                        <p>Symbols with Data: {symbols_with_data}</p>
                        <p>Overall Coverage: {overall_coverage:.2%}</p>
                        <p>Generated: {generated_at}</p>
                    </div>
                    
                    <h2>Market Details</h2>
                    <table>
                        <tr>
                            <th>Market</th>
                            <th>Total Symbols</th>
                            <th>Symbols with Data</th>
                            <th>Coverage Ratio</th>
                            <th>Date Range</th>
                            <th>Data Sources</th>
                        </tr>
                        {market_rows}
                    </table>
                </body>
                </html>
                """
                
                # Generate market rows
                market_rows = []
                for market_name, market_data in summary.get('markets', {}).items():
                    date_range = market_data.get('date_range', {})
                    date_str = f"{date_range.get('start', '')} to {date_range.get('end', '')}"
                    
                    row = f"""
                    <tr>
                        <td>{market_name}</td>
                        <td>{market_data.get('total_symbols', 0)}</td>
                        <td>{market_data.get('symbols_with_data', 0)}</td>
                        <td>{market_data.get('coverage_ratio', 0):.2%}</td>
                        <td>{date_str}</td>
                        <td>{', '.join(market_data.get('data_sources', []))}</td>
                    </tr>
                    """
                    market_rows.append(row)
                
                return html_template.format(
                    total_markets=summary['summary']['total_markets'],
                    total_symbols=summary['summary']['total_symbols'],
                    symbols_with_data=summary['summary']['symbols_with_data'],
                    overall_coverage=summary['summary']['overall_coverage_ratio'],
                    generated_at=summary['generated_at'],
                    market_rows=''.join(market_rows)
                )
            
            else:
                raise ValueError(f"Unsupported output format: {output_format}")
                
        except Exception as e:
            logger.error(f"Failed to export coverage report: {e}")
            return f"Error generating report: {str(e)}"


# Global coverage API instance
_coverage_api: Optional[DataCoverageAPI] = None


def get_coverage_api() -> DataCoverageAPI:
    """Get global coverage API instance."""
    global _coverage_api
    
    if _coverage_api is None:
        _coverage_api = DataCoverageAPI()
    
    return _coverage_api