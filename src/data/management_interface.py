"""
Comprehensive data import and management interface.
This module provides a unified interface for all data management operations.
"""

import logging
import json
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
from dataclasses import dataclass, asdict
import asyncio
from concurrent.futures import ThreadPoolExecutor
import threading

from .manager import get_data_manager, DataManager
from .importers import get_import_manager, DataImportManager
from .cache import get_data_cache, DataCache
from .coverage_api import get_coverage_api, DataCoverageAPI
from .adapters.manager import get_data_source_manager, DataSourceManager
from ..models.market_data import UnifiedMarketData, MarketInfo
from ..exceptions import DataException


logger = logging.getLogger(__name__)


@dataclass
class ImportTask:
    """Data import task definition."""
    id: str
    file_path: str
    market: str
    exchange: str
    currency: str
    status: str  # 'pending', 'running', 'completed', 'failed'
    progress: float
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'file_path': self.file_path,
            'market': self.market,
            'exchange': self.exchange,
            'currency': self.currency,
            'status': self.status,
            'progress': self.progress,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'error_message': self.error_message,
            'result': self.result
        }


@dataclass
class SyncTask:
    """Data synchronization task definition."""
    id: str
    symbols: List[str]
    start_date: datetime
    end_date: datetime
    interval: str
    preferred_source: Optional[str]
    status: str
    progress: float
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'symbols': self.symbols,
            'start_date': self.start_date.isoformat(),
            'end_date': self.end_date.isoformat(),
            'interval': self.interval,
            'preferred_source': self.preferred_source,
            'status': self.status,
            'progress': self.progress,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'error_message': self.error_message,
            'result': self.result
        }


class DataManagementInterface:
    """Unified interface for all data management operations."""
    
    def __init__(self):
        self.data_manager = get_data_manager()
        self.import_manager = get_import_manager()
        self.cache = get_data_cache()
        self.coverage_api = get_coverage_api()
        self.source_manager = get_data_source_manager()
        
        # Task management
        self.import_tasks: Dict[str, ImportTask] = {}
        self.sync_tasks: Dict[str, SyncTask] = {}
        self.task_lock = threading.Lock()
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Event callbacks
        self.event_callbacks: Dict[str, List[Callable]] = {
            'import_started': [],
            'import_progress': [],
            'import_completed': [],
            'import_failed': [],
            'sync_started': [],
            'sync_progress': [],
            'sync_completed': [],
            'sync_failed': []
        }
    
    def register_event_callback(self, event: str, callback: Callable) -> None:
        """Register callback for data management events."""
        if event in self.event_callbacks:
            self.event_callbacks[event].append(callback)
    
    def _emit_event(self, event: str, data: Any) -> None:
        """Emit event to registered callbacks."""
        for callback in self.event_callbacks.get(event, []):
            try:
                callback(data)
            except Exception as e:
                logger.error(f"Event callback failed for {event}: {e}")
    
    # File Import Operations
    def validate_import_file(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """Validate file for import with comprehensive analysis."""
        return self.import_manager.validate_file_structure(file_path)
    
    def preview_import_file(
        self, 
        file_path: Union[str, Path], 
        rows: int = 10
    ) -> Tuple[bool, Union[pd.DataFrame, str]]:
        """Get preview of file to be imported."""
        return self.import_manager.get_import_preview(file_path, rows)
    
    def create_import_task(
        self,
        file_path: Union[str, Path],
        market: str = 'US',
        exchange: str = 'UNKNOWN',
        currency: str = 'USD',
        task_id: Optional[str] = None
    ) -> str:
        """Create a new import task."""
        import uuid
        
        task_id = task_id or str(uuid.uuid4())
        
        task = ImportTask(
            id=task_id,
            file_path=str(file_path),
            market=market,
            exchange=exchange,
            currency=currency,
            status='pending',
            progress=0.0,
            created_at=datetime.now()
        )
        
        with self.task_lock:
            self.import_tasks[task_id] = task
        
        return task_id
    
    def start_import_task(self, task_id: str) -> bool:
        """Start an import task asynchronously."""
        with self.task_lock:
            if task_id not in self.import_tasks:
                return False
            
            task = self.import_tasks[task_id]
            if task.status != 'pending':
                return False
            
            task.status = 'running'
            task.started_at = datetime.now()
        
        # Submit task to executor
        future = self.executor.submit(self._execute_import_task, task_id)
        return True
    
    def _execute_import_task(self, task_id: str) -> None:
        """Execute import task in background."""
        try:
            with self.task_lock:
                task = self.import_tasks[task_id]
            
            self._emit_event('import_started', task.to_dict())
            
            # Progress callback
            def progress_callback(progress: float, details: Dict[str, Any]):
                with self.task_lock:
                    task.progress = progress
                self._emit_event('import_progress', {
                    'task_id': task_id,
                    'progress': progress,
                    'details': details
                })
            
            # Check file size for batch processing
            file_size_mb = Path(task.file_path).stat().st_size / (1024 * 1024)
            
            if file_size_mb > 100:  # Use batch processing for large files
                success, result = self.import_manager.import_large_file_in_batches(
                    task.file_path,
                    market=task.market,
                    exchange=task.exchange,
                    currency=task.currency,
                    progress_callback=progress_callback
                )
            else:
                success, result = self.import_manager.import_with_auto_detection(
                    task.file_path,
                    market=task.market,
                    exchange=task.exchange,
                    currency=task.currency
                )
            
            with self.task_lock:
                task.completed_at = datetime.now()
                task.progress = 1.0
                
                if success:
                    task.status = 'completed'
                    if isinstance(result, list):
                        # Save to database
                        saved_count = self.data_manager.save_market_data_batch(result)
                        task.result = {
                            'imported_records': len(result),
                            'saved_records': saved_count,
                            'file_size_mb': file_size_mb
                        }
                    else:
                        task.result = result
                    
                    self._emit_event('import_completed', task.to_dict())
                else:
                    task.status = 'failed'
                    task.error_message = str(result)
                    self._emit_event('import_failed', task.to_dict())
            
        except Exception as e:
            logger.error(f"Import task {task_id} failed: {e}")
            with self.task_lock:
                task = self.import_tasks[task_id]
                task.status = 'failed'
                task.error_message = str(e)
                task.completed_at = datetime.now()
            
            self._emit_event('import_failed', task.to_dict())
    
    def get_import_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get status of an import task."""
        with self.task_lock:
            task = self.import_tasks.get(task_id)
            return task.to_dict() if task else None
    
    def list_import_tasks(self, status_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """List all import tasks, optionally filtered by status."""
        with self.task_lock:
            tasks = list(self.import_tasks.values())
        
        if status_filter:
            tasks = [task for task in tasks if task.status == status_filter]
        
        return [task.to_dict() for task in sorted(tasks, key=lambda x: x.created_at, reverse=True)]
    
    def cancel_import_task(self, task_id: str) -> bool:
        """Cancel a pending import task."""
        with self.task_lock:
            if task_id not in self.import_tasks:
                return False
            
            task = self.import_tasks[task_id]
            if task.status == 'pending':
                task.status = 'cancelled'
                return True
            
            return False
    
    # Data Source Synchronization
    def create_sync_task(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        interval: str = '1d',
        preferred_source: Optional[str] = None,
        task_id: Optional[str] = None
    ) -> str:
        """Create a new data synchronization task."""
        import uuid
        
        task_id = task_id or str(uuid.uuid4())
        
        task = SyncTask(
            id=task_id,
            symbols=symbols,
            start_date=start_date,
            end_date=end_date,
            interval=interval,
            preferred_source=preferred_source,
            status='pending',
            progress=0.0,
            created_at=datetime.now()
        )
        
        with self.task_lock:
            self.sync_tasks[task_id] = task
        
        return task_id
    
    def start_sync_task(self, task_id: str) -> bool:
        """Start a synchronization task asynchronously."""
        with self.task_lock:
            if task_id not in self.sync_tasks:
                return False
            
            task = self.sync_tasks[task_id]
            if task.status != 'pending':
                return False
            
            task.status = 'running'
            task.started_at = datetime.now()
        
        # Submit task to executor
        future = self.executor.submit(self._execute_sync_task, task_id)
        return True
    
    def _execute_sync_task(self, task_id: str) -> None:
        """Execute synchronization task in background."""
        try:
            with self.task_lock:
                task = self.sync_tasks[task_id]
            
            self._emit_event('sync_started', task.to_dict())
            
            total_symbols = len(task.symbols)
            processed_symbols = 0
            successful_syncs = []
            failed_syncs = []
            
            for symbol in task.symbols:
                try:
                    success, data_or_error = self.source_manager.fetch_historical_data(
                        symbol,
                        task.start_date,
                        task.end_date,
                        task.interval,
                        task.preferred_source
                    )
                    
                    if success:
                        data = data_or_error
                        # Save to database
                        saved_count = self.data_manager.save_market_data_batch(data)
                        successful_syncs.append({
                            'symbol': symbol,
                            'records': len(data),
                            'saved_records': saved_count
                        })
                    else:
                        failed_syncs.append({
                            'symbol': symbol,
                            'error': data_or_error
                        })
                    
                except Exception as e:
                    failed_syncs.append({
                        'symbol': symbol,
                        'error': str(e)
                    })
                
                processed_symbols += 1
                progress = processed_symbols / total_symbols
                
                with self.task_lock:
                    task.progress = progress
                
                self._emit_event('sync_progress', {
                    'task_id': task_id,
                    'progress': progress,
                    'processed': processed_symbols,
                    'total': total_symbols,
                    'current_symbol': symbol
                })
            
            with self.task_lock:
                task.completed_at = datetime.now()
                task.progress = 1.0
                task.status = 'completed'
                task.result = {
                    'successful_syncs': successful_syncs,
                    'failed_syncs': failed_syncs,
                    'total_symbols': total_symbols,
                    'success_rate': len(successful_syncs) / total_symbols if total_symbols > 0 else 0
                }
            
            self._emit_event('sync_completed', task.to_dict())
            
        except Exception as e:
            logger.error(f"Sync task {task_id} failed: {e}")
            with self.task_lock:
                task = self.sync_tasks[task_id]
                task.status = 'failed'
                task.error_message = str(e)
                task.completed_at = datetime.now()
            
            self._emit_event('sync_failed', task.to_dict())
    
    def get_sync_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a sync task."""
        with self.task_lock:
            task = self.sync_tasks.get(task_id)
            return task.to_dict() if task else None
    
    def list_sync_tasks(self, status_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """List all sync tasks, optionally filtered by status."""
        with self.task_lock:
            tasks = list(self.sync_tasks.values())
        
        if status_filter:
            tasks = [task for task in tasks if task.status == status_filter]
        
        return [task.to_dict() for task in sorted(tasks, key=lambda x: x.created_at, reverse=True)]
    
    # Data Coverage and Query Operations
    def get_data_coverage_summary(self) -> Dict[str, Any]:
        """Get comprehensive data coverage summary."""
        return self.coverage_api.get_coverage_summary()
    
    def get_symbol_coverage(self, symbol: str, market: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get coverage information for a specific symbol."""
        coverage = self.coverage_api.get_symbol_coverage(symbol, market)
        return coverage.to_dict() if coverage else None
    
    def get_market_coverage(self, market: str) -> Optional[Dict[str, Any]]:
        """Get coverage information for a market."""
        coverage = self.coverage_api.get_market_coverage(market)
        return coverage.to_dict() if coverage else None
    
    def find_data_gaps(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        market: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Find gaps in data coverage."""
        gaps = self.coverage_api.find_data_gaps(symbol, start_date, end_date, market)
        return [gap.to_dict() for gap in gaps]
    
    def get_symbols_by_coverage(
        self,
        market: str,
        min_coverage_ratio: float = 0.8,
        min_days: int = 252
    ) -> List[Dict[str, Any]]:
        """Get symbols filtered by coverage criteria."""
        return self.coverage_api.get_symbols_by_coverage(market, min_coverage_ratio, min_days)
    
    # Cache Management
    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return self.cache.get_stats()
    
    def clear_cache(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Clear cache data."""
        if symbol:
            removed = self.cache.invalidate_symbol(symbol)
            return {'removed_entries': removed, 'symbol': symbol}
        else:
            self.cache.clear_all()
            return {'action': 'cleared_all'}
    
    def cleanup_cache(self) -> Dict[str, int]:
        """Clean up expired cache entries."""
        return self.cache.cleanup()
    
    # Data Source Management
    def get_data_sources_status(self) -> Dict[str, Any]:
        """Get status of all data sources."""
        return self.source_manager.health_check_all()
    
    def get_available_symbols_from_sources(self, market: Optional[str] = None) -> Dict[str, List[str]]:
        """Get available symbols from data sources."""
        return self.source_manager.get_available_symbols(market)
    
    def test_data_source_connection(self, source_name: str) -> Tuple[bool, str]:
        """Test connection to a specific data source."""
        adapter = self.source_manager.get_adapter(source_name)
        if not adapter:
            return False, f"Data source '{source_name}' not found"
        
        return adapter.health_check()
    
    # Database Operations
    def get_database_statistics(self) -> Dict[str, Any]:
        """Get database statistics."""
        return self.data_manager.get_database_stats()
    
    def optimize_database(self) -> bool:
        """Optimize database performance."""
        try:
            self.data_manager.optimize_database()
            return True
        except Exception as e:
            logger.error(f"Database optimization failed: {e}")
            return False
    
    def get_data_quality_report(self, symbol: str, market: Optional[str] = None) -> Dict[str, Any]:
        """Get data quality report for a symbol."""
        return self.data_manager.get_data_quality_report(symbol, market)
    
    # Export and Reporting
    def export_coverage_report(
        self,
        output_format: str = 'json',
        output_path: Optional[str] = None
    ) -> Union[str, bool]:
        """Export data coverage report."""
        try:
            report_content = self.coverage_api.export_coverage_report(output_format)
            
            if output_path:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                return True
            else:
                return report_content
                
        except Exception as e:
            logger.error(f"Failed to export coverage report: {e}")
            return False
    
    def export_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        output_path: str,
        output_format: str = 'csv',
        market: Optional[str] = None
    ) -> bool:
        """Export market data to file."""
        try:
            df = self.data_manager.get_market_data(symbol, start_date, end_date, market)
            
            if df.empty:
                logger.warning(f"No data found for {symbol}")
                return False
            
            # Add metadata columns
            df['symbol'] = symbol
            df['market'] = market or 'UNKNOWN'
            
            # Reorder columns
            columns = ['symbol', 'market'] + [col for col in df.columns if col not in ['symbol', 'market']]
            df = df[columns]
            
            if output_format.lower() == 'csv':
                df.to_csv(output_path)
            elif output_format.lower() == 'excel':
                df.to_excel(output_path)
            elif output_format.lower() == 'json':
                df.to_json(output_path, orient='records', date_format='iso')
            else:
                raise ValueError(f"Unsupported output format: {output_format}")
            
            logger.info(f"Exported {len(df)} records to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export data: {e}")
            return False
    
    # System Health and Monitoring
    def get_system_health(self) -> Dict[str, Any]:
        """Get comprehensive system health status."""
        try:
            return {
                'database': {
                    'status': 'healthy',
                    'stats': self.get_database_statistics()
                },
                'cache': {
                    'status': 'healthy',
                    'stats': self.get_cache_statistics()
                },
                'data_sources': self.get_data_sources_status(),
                'tasks': {
                    'import_tasks': {
                        'total': len(self.import_tasks),
                        'running': len([t for t in self.import_tasks.values() if t.status == 'running']),
                        'pending': len([t for t in self.import_tasks.values() if t.status == 'pending'])
                    },
                    'sync_tasks': {
                        'total': len(self.sync_tasks),
                        'running': len([t for t in self.sync_tasks.values() if t.status == 'running']),
                        'pending': len([t for t in self.sync_tasks.values() if t.status == 'pending'])
                    }
                },
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Failed to get system health: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def cleanup_old_tasks(self, days: int = 7) -> Dict[str, int]:
        """Clean up old completed tasks."""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        with self.task_lock:
            # Clean import tasks
            old_import_tasks = [
                task_id for task_id, task in self.import_tasks.items()
                if task.completed_at and task.completed_at < cutoff_date
            ]
            
            for task_id in old_import_tasks:
                del self.import_tasks[task_id]
            
            # Clean sync tasks
            old_sync_tasks = [
                task_id for task_id, task in self.sync_tasks.items()
                if task.completed_at and task.completed_at < cutoff_date
            ]
            
            for task_id in old_sync_tasks:
                del self.sync_tasks[task_id]
        
        return {
            'import_tasks_removed': len(old_import_tasks),
            'sync_tasks_removed': len(old_sync_tasks)
        }
    
    def shutdown(self) -> None:
        """Shutdown the data management interface."""
        logger.info("Shutting down data management interface")
        self.executor.shutdown(wait=True)
        self.data_manager.close()


# Global data management interface instance
_data_management_interface: Optional[DataManagementInterface] = None


def get_data_management_interface() -> DataManagementInterface:
    """Get global data management interface instance."""
    global _data_management_interface
    
    if _data_management_interface is None:
        _data_management_interface = DataManagementInterface()
    
    return _data_management_interface