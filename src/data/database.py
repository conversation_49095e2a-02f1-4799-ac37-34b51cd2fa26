"""
Database connection and management for the quantitative trading system.
"""

import logging
from typing import Optional, Dict, Any, List
from contextlib import contextmanager
from datetime import datetime
import sqlite3
import threading

from config.settings import config

# Optional PostgreSQL imports
try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
    from psycopg2.pool import ThreadedConnectionPool
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False


logger = logging.getLogger(__name__)


class DatabaseManager:
    """Database connection and transaction manager."""
    
    def __init__(self, use_sqlite: bool = False):
        """Initialize database manager.
        
        Args:
            use_sqlite: If True, use SQLite for development/testing
        """
        self.use_sqlite = use_sqlite
        self._connection_pool: Optional[ThreadedConnectionPool] = None
        self._sqlite_connection: Optional[sqlite3.Connection] = None
        self._lock = threading.Lock()
        
        if not use_sqlite:
            self._init_postgresql_pool()
        else:
            self._init_sqlite_connection()
    
    def _init_postgresql_pool(self) -> None:
        """Initialize PostgreSQL connection pool."""
        if not POSTGRESQL_AVAILABLE:
            logger.warning("PostgreSQL not available, falling back to SQLite")
            self.use_sqlite = True
            self._init_sqlite_connection()
            return
            
        try:
            db_config = config.database
            self._connection_pool = ThreadedConnectionPool(
                minconn=1,
                maxconn=db_config.pool_size,
                host=db_config.host,
                port=db_config.port,
                database=db_config.database,
                user=db_config.username,
                password=db_config.password,
                cursor_factory=RealDictCursor
            )
            logger.info("PostgreSQL connection pool initialized")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL pool: {e}")
            logger.warning("Falling back to SQLite")
            self.use_sqlite = True
            self._init_sqlite_connection()
    
    def _init_sqlite_connection(self) -> None:
        """Initialize SQLite connection."""
        try:
            db_path = "data/trading_system.db"
            import os
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            
            self._sqlite_connection = sqlite3.connect(
                db_path, 
                check_same_thread=False,
                timeout=30.0
            )
            self._sqlite_connection.row_factory = sqlite3.Row
            # Enable WAL mode for better concurrency
            self._sqlite_connection.execute("PRAGMA journal_mode=WAL")
            self._sqlite_connection.execute("PRAGMA synchronous=NORMAL")
            self._sqlite_connection.execute("PRAGMA cache_size=10000")
            self._sqlite_connection.execute("PRAGMA temp_store=memory")
            logger.info("SQLite connection initialized")
        except Exception as e:
            logger.error(f"Failed to initialize SQLite connection: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get database connection context manager."""
        if self.use_sqlite:
            with self._lock:
                try:
                    yield self._sqlite_connection
                except Exception as e:
                    self._sqlite_connection.rollback()
                    raise
        else:
            connection = None
            try:
                connection = self._connection_pool.getconn()
                yield connection
            except Exception as e:
                if connection:
                    connection.rollback()
                raise
            finally:
                if connection:
                    self._connection_pool.putconn(connection)
    
    @contextmanager
    def get_cursor(self):
        """Get database cursor context manager."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                yield cursor
                conn.commit()
            except Exception as e:
                conn.rollback()
                raise
            finally:
                cursor.close()
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """Execute SELECT query and return results."""
        with self.get_cursor() as cursor:
            cursor.execute(query, params or ())
            if self.use_sqlite:
                return [dict(row) for row in cursor.fetchall()]
            else:
                return cursor.fetchall()
    
    def execute_command(self, command: str, params: tuple = None) -> int:
        """Execute INSERT/UPDATE/DELETE command and return affected rows."""
        with self.get_cursor() as cursor:
            cursor.execute(command, params or ())
            return cursor.rowcount
    
    def execute_many(self, command: str, params_list: List[tuple]) -> int:
        """Execute command with multiple parameter sets."""
        with self.get_cursor() as cursor:
            cursor.executemany(command, params_list)
            return cursor.rowcount
    
    def execute_script(self, script: str) -> None:
        """Execute SQL script."""
        with self.get_connection() as conn:
            if self.use_sqlite:
                conn.executescript(script)
            else:
                cursor = conn.cursor()
                cursor.execute(script)
                cursor.close()
            conn.commit()
    
    def table_exists(self, table_name: str) -> bool:
        """Check if table exists."""
        if self.use_sqlite:
            query = """
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """
        else:
            query = """
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema='public' AND table_name=%s
            """
        
        result = self.execute_query(query, (table_name,))
        return len(result) > 0
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """Get table column information."""
        if self.use_sqlite:
            query = f"PRAGMA table_info({table_name})"
        else:
            query = """
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_name = %s
                ORDER BY ordinal_position
            """
        
        return self.execute_query(query, () if self.use_sqlite else (table_name,))
    
    def create_index(self, index_name: str, table_name: str, columns: List[str], unique: bool = False) -> None:
        """Create database index."""
        unique_clause = "UNIQUE " if unique else ""
        columns_str = ", ".join(columns)
        
        if self.use_sqlite:
            query = f"CREATE {unique_clause}INDEX IF NOT EXISTS {index_name} ON {table_name} ({columns_str})"
        else:
            query = f"CREATE {unique_clause}INDEX IF NOT EXISTS {index_name} ON {table_name} ({columns_str})"
        
        self.execute_command(query)
        logger.info(f"Created index {index_name} on {table_name}({columns_str})")
    
    def optimize_table(self, table_name: str) -> None:
        """Optimize table performance."""
        if self.use_sqlite:
            self.execute_command("VACUUM")
            self.execute_command("ANALYZE")
        else:
            self.execute_command(f"VACUUM ANALYZE {table_name}")
        
        logger.info(f"Optimized table {table_name}")
    
    def get_table_size(self, table_name: str) -> Dict[str, Any]:
        """Get table size information."""
        if self.use_sqlite:
            # SQLite doesn't have built-in table size functions
            count_query = f"SELECT COUNT(*) as row_count FROM {table_name}"
            result = self.execute_query(count_query)
            return {
                'row_count': result[0]['row_count'],
                'size_bytes': None,
                'index_size_bytes': None
            }
        else:
            query = """
                SELECT 
                    schemaname,
                    tablename,
                    attname,
                    n_distinct,
                    correlation
                FROM pg_stats 
                WHERE tablename = %s
            """
            stats = self.execute_query(query, (table_name,))
            
            size_query = """
                SELECT 
                    pg_size_pretty(pg_total_relation_size(%s)) as total_size,
                    pg_size_pretty(pg_relation_size(%s)) as table_size,
                    pg_size_pretty(pg_indexes_size(%s)) as index_size
            """
            size_result = self.execute_query(size_query, (table_name, table_name, table_name))
            
            return {
                'stats': stats,
                'sizes': size_result[0] if size_result else {}
            }
    
    def close(self) -> None:
        """Close database connections."""
        if self._connection_pool:
            self._connection_pool.closeall()
            logger.info("PostgreSQL connection pool closed")
        
        if self._sqlite_connection:
            self._sqlite_connection.close()
            logger.info("SQLite connection closed")


# Global database manager instance
_db_manager: Optional[DatabaseManager] = None
_db_lock = threading.Lock()


def get_database_manager(use_sqlite: bool = False) -> DatabaseManager:
    """Get global database manager instance."""
    global _db_manager
    
    if _db_manager is None or (_db_manager.use_sqlite != use_sqlite):
        with _db_lock:
            if _db_manager is None or (_db_manager.use_sqlite != use_sqlite):
                if _db_manager:
                    _db_manager.close()
                _db_manager = DatabaseManager(use_sqlite=use_sqlite)
    
    return _db_manager


def close_database_manager() -> None:
    """Close global database manager."""
    global _db_manager
    
    if _db_manager:
        with _db_lock:
            if _db_manager:
                _db_manager.close()
                _db_manager = None