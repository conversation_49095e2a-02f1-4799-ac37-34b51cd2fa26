"""
Database migration management for the quantitative trading system.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from pathlib import Path

from .database import DatabaseManager


logger = logging.getLogger(__name__)


class Migration:
    """Base class for database migrations."""
    
    def __init__(self, version: str, description: str):
        self.version = version
        self.description = description
        self.timestamp = datetime.now()
    
    def up(self, db: DatabaseManager) -> None:
        """Apply migration."""
        raise NotImplementedError
    
    def down(self, db: DatabaseManager) -> None:
        """Rollback migration."""
        raise NotImplementedError


class InitialMigration(Migration):
    """Initial database schema migration."""
    
    def __init__(self):
        super().__init__("001", "Create initial database schema")
    
    def up(self, db: DatabaseManager) -> None:
        """Create initial tables."""
        # Create market_data table
        if db.use_sqlite:
            market_data_sql = """
                CREATE TABLE IF NOT EXISTS market_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    open REAL NOT NULL,
                    high REAL NOT NULL,
                    low REAL NOT NULL,
                    close REAL NOT NULL,
                    volume REAL NOT NULL,
                    market TEXT NOT NULL,
                    exchange TEXT NOT NULL,
                    currency TEXT NOT NULL,
                    adj_close REAL,
                    turnover REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, timestamp, market)
                )
            """
        else:
            market_data_sql = """
                CREATE TABLE IF NOT EXISTS market_data (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(50) NOT NULL,
                    timestamp TIMESTAMPTZ NOT NULL,
                    open DECIMAL(20,8) NOT NULL,
                    high DECIMAL(20,8) NOT NULL,
                    low DECIMAL(20,8) NOT NULL,
                    close DECIMAL(20,8) NOT NULL,
                    volume DECIMAL(20,8) NOT NULL,
                    market VARCHAR(20) NOT NULL,
                    exchange VARCHAR(50) NOT NULL,
                    currency VARCHAR(10) NOT NULL,
                    adj_close DECIMAL(20,8),
                    turnover DECIMAL(20,8),
                    created_at TIMESTAMPTZ DEFAULT NOW(),
                    updated_at TIMESTAMPTZ DEFAULT NOW(),
                    UNIQUE(symbol, timestamp, market)
                )
            """
        
        db.execute_script(market_data_sql)
        logger.info("Created market_data table")
        
        # Create economic_data table
        if db.use_sqlite:
            economic_data_sql = """
                CREATE TABLE IF NOT EXISTS economic_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    series_id TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    value REAL NOT NULL,
                    market TEXT DEFAULT 'ECONOMIC',
                    source TEXT DEFAULT 'FRED',
                    unit TEXT DEFAULT '',
                    frequency TEXT DEFAULT 'Daily',
                    seasonal_adjustment TEXT DEFAULT 'Not Seasonally Adjusted',
                    last_updated DATETIME,
                    notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(series_id, timestamp)
                )
            """
        else:
            economic_data_sql = """
                CREATE TABLE IF NOT EXISTS economic_data (
                    id SERIAL PRIMARY KEY,
                    series_id VARCHAR(100) NOT NULL,
                    timestamp TIMESTAMPTZ NOT NULL,
                    value DECIMAL(20,8) NOT NULL,
                    market VARCHAR(20) DEFAULT 'ECONOMIC',
                    source VARCHAR(50) DEFAULT 'FRED',
                    unit VARCHAR(100) DEFAULT '',
                    frequency VARCHAR(20) DEFAULT 'Daily',
                    seasonal_adjustment VARCHAR(50) DEFAULT 'Not Seasonally Adjusted',
                    last_updated TIMESTAMPTZ,
                    notes TEXT,
                    created_at TIMESTAMPTZ DEFAULT NOW(),
                    updated_at TIMESTAMPTZ DEFAULT NOW(),
                    UNIQUE(series_id, timestamp)
                )
            """
        
        db.execute_script(economic_data_sql)
        logger.info("Created economic_data table")
        
        # Create market_info table
        if db.use_sqlite:
            market_info_sql = """
                CREATE TABLE IF NOT EXISTS market_info (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    name TEXT NOT NULL,
                    market TEXT NOT NULL,
                    exchange TEXT NOT NULL,
                    currency TEXT NOT NULL,
                    lot_size REAL NOT NULL,
                    tick_size REAL NOT NULL,
                    trading_hours TEXT,
                    timezone TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, market)
                )
            """
        else:
            market_info_sql = """
                CREATE TABLE IF NOT EXISTS market_info (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(50) NOT NULL,
                    name VARCHAR(200) NOT NULL,
                    market VARCHAR(20) NOT NULL,
                    exchange VARCHAR(50) NOT NULL,
                    currency VARCHAR(10) NOT NULL,
                    lot_size DECIMAL(20,8) NOT NULL,
                    tick_size DECIMAL(20,8) NOT NULL,
                    trading_hours JSONB,
                    timezone VARCHAR(50) NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMPTZ DEFAULT NOW(),
                    updated_at TIMESTAMPTZ DEFAULT NOW(),
                    UNIQUE(symbol, market)
                )
            """
        
        db.execute_script(market_info_sql)
        logger.info("Created market_info table")
        
        # Create data_sources table
        if db.use_sqlite:
            data_sources_sql = """
                CREATE TABLE IF NOT EXISTS data_sources (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL UNIQUE,
                    adapter_class TEXT NOT NULL,
                    enabled BOOLEAN DEFAULT 1,
                    priority INTEGER DEFAULT 1,
                    rate_limit TEXT,
                    credentials TEXT,
                    default_params TEXT,
                    last_sync DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """
        else:
            data_sources_sql = """
                CREATE TABLE IF NOT EXISTS data_sources (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    name VARCHAR(100) NOT NULL UNIQUE,
                    adapter_class VARCHAR(200) NOT NULL,
                    enabled BOOLEAN DEFAULT TRUE,
                    priority INTEGER DEFAULT 1,
                    rate_limit JSONB,
                    credentials JSONB,
                    default_params JSONB,
                    last_sync TIMESTAMPTZ,
                    created_at TIMESTAMPTZ DEFAULT NOW(),
                    updated_at TIMESTAMPTZ DEFAULT NOW()
                )
            """
        
        db.execute_script(data_sources_sql)
        logger.info("Created data_sources table")
        
        # Create migration_history table
        if db.use_sqlite:
            migration_history_sql = """
                CREATE TABLE IF NOT EXISTS migration_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    version TEXT NOT NULL UNIQUE,
                    description TEXT NOT NULL,
                    applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """
        else:
            migration_history_sql = """
                CREATE TABLE IF NOT EXISTS migration_history (
                    id SERIAL PRIMARY KEY,
                    version VARCHAR(50) NOT NULL UNIQUE,
                    description TEXT NOT NULL,
                    applied_at TIMESTAMPTZ DEFAULT NOW()
                )
            """
        
        db.execute_script(migration_history_sql)
        logger.info("Created migration_history table")
    
    def down(self, db: DatabaseManager) -> None:
        """Drop all tables."""
        tables = ['market_data', 'economic_data', 'market_info', 'data_sources', 'migration_history']
        for table in tables:
            db.execute_command(f"DROP TABLE IF EXISTS {table}")
            logger.info(f"Dropped table {table}")


class IndexMigration(Migration):
    """Create database indexes for performance optimization."""
    
    def __init__(self):
        super().__init__("002", "Create database indexes")
    
    def up(self, db: DatabaseManager) -> None:
        """Create indexes."""
        # Market data indexes
        db.create_index("idx_market_data_symbol", "market_data", ["symbol"])
        db.create_index("idx_market_data_timestamp", "market_data", ["timestamp"])
        db.create_index("idx_market_data_market", "market_data", ["market"])
        db.create_index("idx_market_data_exchange", "market_data", ["exchange"])
        db.create_index("idx_market_data_symbol_timestamp", "market_data", ["symbol", "timestamp"])
        db.create_index("idx_market_data_market_timestamp", "market_data", ["market", "timestamp"])
        
        # Economic data indexes
        db.create_index("idx_economic_data_series_id", "economic_data", ["series_id"])
        db.create_index("idx_economic_data_timestamp", "economic_data", ["timestamp"])
        db.create_index("idx_economic_data_frequency", "economic_data", ["frequency"])
        db.create_index("idx_economic_data_series_timestamp", "economic_data", ["series_id", "timestamp"])
        
        # Market info indexes
        db.create_index("idx_market_info_symbol", "market_info", ["symbol"])
        db.create_index("idx_market_info_market", "market_info", ["market"])
        db.create_index("idx_market_info_exchange", "market_info", ["exchange"])
        db.create_index("idx_market_info_active", "market_info", ["is_active"])
        
        # Data sources indexes
        db.create_index("idx_data_sources_name", "data_sources", ["name"], unique=True)
        db.create_index("idx_data_sources_enabled", "data_sources", ["enabled"])
        db.create_index("idx_data_sources_priority", "data_sources", ["priority"])
        
        logger.info("Created database indexes")
    
    def down(self, db: DatabaseManager) -> None:
        """Drop indexes."""
        indexes = [
            "idx_market_data_symbol", "idx_market_data_timestamp", "idx_market_data_market",
            "idx_market_data_exchange", "idx_market_data_symbol_timestamp", "idx_market_data_market_timestamp",
            "idx_economic_data_series_id", "idx_economic_data_timestamp", "idx_economic_data_frequency",
            "idx_economic_data_series_timestamp", "idx_market_info_symbol", "idx_market_info_market",
            "idx_market_info_exchange", "idx_market_info_active", "idx_data_sources_name",
            "idx_data_sources_enabled", "idx_data_sources_priority"
        ]
        
        for index in indexes:
            try:
                db.execute_command(f"DROP INDEX IF EXISTS {index}")
            except Exception as e:
                logger.warning(f"Failed to drop index {index}: {e}")


class MigrationManager:
    """Database migration manager."""
    
    def __init__(self, db: DatabaseManager):
        self.db = db
        self.migrations = [
            InitialMigration(),
            IndexMigration()
        ]
    
    def get_applied_migrations(self) -> List[str]:
        """Get list of applied migration versions."""
        if not self.db.table_exists("migration_history"):
            return []
        
        try:
            result = self.db.execute_query("SELECT version FROM migration_history ORDER BY applied_at")
            return [row['version'] for row in result]
        except Exception as e:
            logger.error(f"Failed to get applied migrations: {e}")
            return []
    
    def apply_migration(self, migration: Migration) -> None:
        """Apply a single migration."""
        try:
            logger.info(f"Applying migration {migration.version}: {migration.description}")
            migration.up(self.db)
            
            # Record migration in history
            self.db.execute_command(
                "INSERT INTO migration_history (version, description) VALUES (?, ?)" if self.db.use_sqlite 
                else "INSERT INTO migration_history (version, description) VALUES (%s, %s)",
                (migration.version, migration.description)
            )
            
            logger.info(f"Successfully applied migration {migration.version}")
        except Exception as e:
            logger.error(f"Failed to apply migration {migration.version}: {e}")
            raise
    
    def rollback_migration(self, migration: Migration) -> None:
        """Rollback a single migration."""
        try:
            logger.info(f"Rolling back migration {migration.version}: {migration.description}")
            migration.down(self.db)
            
            # Remove migration from history
            self.db.execute_command(
                "DELETE FROM migration_history WHERE version = ?" if self.db.use_sqlite
                else "DELETE FROM migration_history WHERE version = %s",
                (migration.version,)
            )
            
            logger.info(f"Successfully rolled back migration {migration.version}")
        except Exception as e:
            logger.error(f"Failed to rollback migration {migration.version}: {e}")
            raise
    
    def migrate(self) -> None:
        """Apply all pending migrations."""
        applied_migrations = self.get_applied_migrations()
        
        for migration in self.migrations:
            if migration.version not in applied_migrations:
                self.apply_migration(migration)
        
        logger.info("All migrations applied successfully")
    
    def rollback_to(self, target_version: str) -> None:
        """Rollback to a specific migration version."""
        applied_migrations = self.get_applied_migrations()
        
        # Find migrations to rollback (in reverse order)
        migrations_to_rollback = []
        for migration in reversed(self.migrations):
            if migration.version in applied_migrations:
                migrations_to_rollback.append(migration)
                if migration.version == target_version:
                    break
        
        # Remove target version from rollback list
        if migrations_to_rollback and migrations_to_rollback[-1].version == target_version:
            migrations_to_rollback.pop()
        
        # Apply rollbacks
        for migration in migrations_to_rollback:
            self.rollback_migration(migration)
        
        logger.info(f"Rolled back to migration {target_version}")
    
    def get_migration_status(self) -> Dict[str, Any]:
        """Get current migration status."""
        applied_migrations = self.get_applied_migrations()
        
        status = {
            'total_migrations': len(self.migrations),
            'applied_migrations': len(applied_migrations),
            'pending_migrations': [],
            'applied_list': applied_migrations
        }
        
        for migration in self.migrations:
            if migration.version not in applied_migrations:
                status['pending_migrations'].append({
                    'version': migration.version,
                    'description': migration.description
                })
        
        return status