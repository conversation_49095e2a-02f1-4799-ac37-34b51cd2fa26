"""
Data import functionality for CSV/Excel files and other formats.
"""

import logging
import pandas as pd
from typing import List, Dict, Any, Optional, Union, Tuple
from datetime import datetime
from pathlib import Path
import io

from ..models.market_data import UnifiedMarketData, MarketInfo
from ..exceptions import DataException


logger = logging.getLogger(__name__)


class DataImporter:
    """Base class for data importers."""
    
    def __init__(self):
        self.supported_formats = []
    
    def validate_file(self, file_path: Union[str, Path]) -> bool:
        """Validate if file can be imported."""
        raise NotImplementedError
    
    def import_data(self, file_path: Union[str, Path], **kwargs) -> List[UnifiedMarketData]:
        """Import data from file."""
        raise NotImplementedError
    
    def get_import_preview(self, file_path: Union[str, Path], rows: int = 5) -> pd.DataFrame:
        """Get preview of data to be imported."""
        raise NotImplementedError


class CSVImporter(DataImporter):
    """CSV file importer for market data."""
    
    def __init__(self):
        super().__init__()
        self.supported_formats = ['.csv']
        self.required_columns = ['symbol', 'timestamp', 'open', 'high', 'low', 'close', 'volume']
        self.optional_columns = ['adj_close', 'turnover']
    
    def validate_file(self, file_path: Union[str, Path]) -> bool:
        """Validate CSV file format and required columns."""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                return False
            
            if file_path.suffix.lower() not in self.supported_formats:
                return False
            
            # Read first few rows to check structure
            df = pd.read_csv(file_path, nrows=5)
            
            # Check required columns
            missing_columns = set(self.required_columns) - set(df.columns)
            if missing_columns:
                logger.error(f"Missing required columns: {missing_columns}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to validate CSV file: {e}")
            return False
    
    def get_import_preview(self, file_path: Union[str, Path], rows: int = 5) -> pd.DataFrame:
        """Get preview of CSV data."""
        try:
            return pd.read_csv(file_path, nrows=rows)
        except Exception as e:
            logger.error(f"Failed to preview CSV file: {e}")
            return pd.DataFrame()
    
    def import_data(
        self, 
        file_path: Union[str, Path], 
        market: str = 'US',
        exchange: str = 'UNKNOWN',
        currency: str = 'USD',
        **kwargs
    ) -> List[UnifiedMarketData]:
        """Import market data from CSV file."""
        try:
            if not self.validate_file(file_path):
                raise DataException(f"Invalid CSV file: {file_path}")
            
            df = pd.read_csv(file_path)
            
            # Parse timestamp column
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Convert to UnifiedMarketData objects
            market_data_list = []
            for _, row in df.iterrows():
                try:
                    market_data = UnifiedMarketData(
                        symbol=str(row['symbol']),
                        timestamp=row['timestamp'].to_pydatetime(),
                        open=float(row['open']),
                        high=float(row['high']),
                        low=float(row['low']),
                        close=float(row['close']),
                        volume=float(row['volume']),
                        market=market,
                        exchange=exchange,
                        currency=currency,
                        adj_close=float(row['adj_close']) if 'adj_close' in row and pd.notna(row['adj_close']) else None,
                        turnover=float(row['turnover']) if 'turnover' in row and pd.notna(row['turnover']) else None
                    )
                    
                    if market_data.validate():
                        market_data_list.append(market_data)
                    else:
                        logger.warning(f"Invalid market data row: {row.to_dict()}")
                        
                except Exception as e:
                    logger.warning(f"Failed to parse row: {row.to_dict()}, error: {e}")
                    continue
            
            logger.info(f"Successfully imported {len(market_data_list)} records from CSV")
            return market_data_list
            
        except Exception as e:
            logger.error(f"Failed to import CSV data: {e}")
            raise DataException(f"CSV import failed: {e}")


class ExcelImporter(DataImporter):
    """Excel file importer for market data."""
    
    def __init__(self):
        super().__init__()
        self.supported_formats = ['.xlsx', '.xls']
        self.required_columns = ['symbol', 'timestamp', 'open', 'high', 'low', 'close', 'volume']
        self.optional_columns = ['adj_close', 'turnover']
    
    def validate_file(self, file_path: Union[str, Path]) -> bool:
        """Validate Excel file format and required columns."""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                return False
            
            if file_path.suffix.lower() not in self.supported_formats:
                return False
            
            # Read first few rows to check structure
            df = pd.read_excel(file_path, nrows=5)
            
            # Check required columns
            missing_columns = set(self.required_columns) - set(df.columns)
            if missing_columns:
                logger.error(f"Missing required columns: {missing_columns}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to validate Excel file: {e}")
            return False
    
    def get_import_preview(self, file_path: Union[str, Path], rows: int = 5) -> pd.DataFrame:
        """Get preview of Excel data."""
        try:
            return pd.read_excel(file_path, nrows=rows)
        except Exception as e:
            logger.error(f"Failed to preview Excel file: {e}")
            return pd.DataFrame()
    
    def import_data(
        self, 
        file_path: Union[str, Path], 
        market: str = 'US',
        exchange: str = 'UNKNOWN',
        currency: str = 'USD',
        sheet_name: Union[str, int] = 0,
        **kwargs
    ) -> List[UnifiedMarketData]:
        """Import market data from Excel file."""
        try:
            if not self.validate_file(file_path):
                raise DataException(f"Invalid Excel file: {file_path}")
            
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # Parse timestamp column
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Convert to UnifiedMarketData objects
            market_data_list = []
            for _, row in df.iterrows():
                try:
                    market_data = UnifiedMarketData(
                        symbol=str(row['symbol']),
                        timestamp=row['timestamp'].to_pydatetime(),
                        open=float(row['open']),
                        high=float(row['high']),
                        low=float(row['low']),
                        close=float(row['close']),
                        volume=float(row['volume']),
                        market=market,
                        exchange=exchange,
                        currency=currency,
                        adj_close=float(row['adj_close']) if 'adj_close' in row and pd.notna(row['adj_close']) else None,
                        turnover=float(row['turnover']) if 'turnover' in row and pd.notna(row['turnover']) else None
                    )
                    
                    if market_data.validate():
                        market_data_list.append(market_data)
                    else:
                        logger.warning(f"Invalid market data row: {row.to_dict()}")
                        
                except Exception as e:
                    logger.warning(f"Failed to parse row: {row.to_dict()}, error: {e}")
                    continue
            
            logger.info(f"Successfully imported {len(market_data_list)} records from Excel")
            return market_data_list
            
        except Exception as e:
            logger.error(f"Failed to import Excel data: {e}")
            raise DataException(f"Excel import failed: {e}")


class DataImportManager:
    """Manager for coordinating different data importers."""
    
    def __init__(self):
        self.importers = {
            'csv': CSVImporter(),
            'excel': ExcelImporter()
        }
        self.import_history = []
        self.supported_encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
        self.batch_size = 10000  # Default batch size for large imports
        self.max_file_size_mb = 500  # Maximum file size in MB
    
    def get_supported_formats(self) -> List[str]:
        """Get all supported file formats."""
        formats = []
        for importer in self.importers.values():
            formats.extend(importer.supported_formats)
        return list(set(formats))
    
    def detect_file_type(self, file_path: Union[str, Path]) -> Optional[str]:
        """Detect file type based on extension."""
        file_path = Path(file_path)
        suffix = file_path.suffix.lower()
        
        if suffix == '.csv':
            return 'csv'
        elif suffix in ['.xlsx', '.xls']:
            return 'excel'
        else:
            return None
    
    def validate_file(self, file_path: Union[str, Path]) -> Tuple[bool, str]:
        """Validate file and return validation result with message."""
        file_type = self.detect_file_type(file_path)
        
        if not file_type:
            return False, f"Unsupported file format: {Path(file_path).suffix}"
        
        if file_type not in self.importers:
            return False, f"No importer available for file type: {file_type}"
        
        importer = self.importers[file_type]
        is_valid = importer.validate_file(file_path)
        
        if is_valid:
            return True, "File validation successful"
        else:
            return False, "File validation failed"
    
    def get_import_preview(
        self, 
        file_path: Union[str, Path], 
        rows: int = 5
    ) -> Tuple[bool, Union[pd.DataFrame, str]]:
        """Get preview of data to be imported."""
        try:
            file_type = self.detect_file_type(file_path)
            
            if not file_type or file_type not in self.importers:
                return False, f"Unsupported file type: {file_type}"
            
            importer = self.importers[file_type]
            preview_df = importer.get_import_preview(file_path, rows)
            
            if preview_df.empty:
                return False, "Failed to generate preview"
            
            return True, preview_df
            
        except Exception as e:
            logger.error(f"Failed to get import preview: {e}")
            return False, str(e)
    
    def import_file(
        self, 
        file_path: Union[str, Path], 
        market: str = 'US',
        exchange: str = 'UNKNOWN',
        currency: str = 'USD',
        **kwargs
    ) -> Tuple[bool, Union[List[UnifiedMarketData], str]]:
        """Import data from file."""
        try:
            # Validate file first
            is_valid, message = self.validate_file(file_path)
            if not is_valid:
                return False, message
            
            file_type = self.detect_file_type(file_path)
            importer = self.importers[file_type]
            
            market_data_list = importer.import_data(
                file_path, 
                market=market, 
                exchange=exchange, 
                currency=currency,
                **kwargs
            )
            
            if not market_data_list:
                return False, "No valid data found in file"
            
            return True, market_data_list
            
        except Exception as e:
            logger.error(f"Failed to import file: {e}")
            return False, str(e)
    
    def import_from_dataframe(
        self,
        df: pd.DataFrame,
        market: str = 'US',
        exchange: str = 'UNKNOWN',
        currency: str = 'USD'
    ) -> Tuple[bool, Union[List[UnifiedMarketData], str]]:
        """Import data from pandas DataFrame."""
        try:
            required_columns = ['symbol', 'timestamp', 'open', 'high', 'low', 'close', 'volume']
            
            # Check required columns
            missing_columns = set(required_columns) - set(df.columns)
            if missing_columns:
                return False, f"Missing required columns: {missing_columns}"
            
            # Parse timestamp column
            df = df.copy()
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Convert to UnifiedMarketData objects
            market_data_list = []
            for _, row in df.iterrows():
                try:
                    market_data = UnifiedMarketData(
                        symbol=str(row['symbol']),
                        timestamp=row['timestamp'].to_pydatetime(),
                        open=float(row['open']),
                        high=float(row['high']),
                        low=float(row['low']),
                        close=float(row['close']),
                        volume=float(row['volume']),
                        market=market,
                        exchange=exchange,
                        currency=currency,
                        adj_close=float(row['adj_close']) if 'adj_close' in row and pd.notna(row['adj_close']) else None,
                        turnover=float(row['turnover']) if 'turnover' in row and pd.notna(row['turnover']) else None
                    )
                    
                    if market_data.validate():
                        market_data_list.append(market_data)
                    else:
                        logger.warning(f"Invalid market data row: {row.to_dict()}")
                        
                except Exception as e:
                    logger.warning(f"Failed to parse row: {row.to_dict()}, error: {e}")
                    continue
            
            if not market_data_list:
                return False, "No valid data found in DataFrame"
            
            logger.info(f"Successfully imported {len(market_data_list)} records from DataFrame")
            return True, market_data_list
            
        except Exception as e:
            logger.error(f"Failed to import DataFrame: {e}")
            return False, str(e)
    
    def get_import_statistics(self) -> Dict[str, Any]:
        """Get import statistics and history."""
        return {
            'total_imports': len(self.import_history),
            'successful_imports': sum(1 for h in self.import_history if h['success']),
            'failed_imports': sum(1 for h in self.import_history if not h['success']),
            'recent_imports': self.import_history[-10:] if self.import_history else [],
            'supported_formats': self.get_supported_formats(),
            'supported_encodings': self.supported_encodings
        }
    
    def detect_file_encoding(self, file_path: Union[str, Path]) -> str:
        """Detect file encoding."""
        try:
            import chardet
            
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # Read first 10KB
                result = chardet.detect(raw_data)
                encoding = result.get('encoding', 'utf-8')
                confidence = result.get('confidence', 0)
                
                if confidence > 0.7:
                    return encoding
                else:
                    # Fallback to common encodings
                    for enc in self.supported_encodings:
                        try:
                            with open(file_path, 'r', encoding=enc) as test_file:
                                test_file.read(1000)
                            return enc
                        except UnicodeDecodeError:
                            continue
                    
                    return 'utf-8'  # Final fallback
                    
        except ImportError:
            logger.warning("chardet not available, using utf-8 encoding")
            return 'utf-8'
        except Exception as e:
            logger.warning(f"Failed to detect encoding: {e}")
            return 'utf-8'
    
    def validate_data_integrity(self, data: List[UnifiedMarketData]) -> Dict[str, Any]:
        """Validate data integrity and return quality report."""
        if not data:
            return {'valid': False, 'error': 'No data provided'}
        
        issues = []
        warnings = []
        
        # Check for duplicate timestamps per symbol
        symbol_timestamps = {}
        for item in data:
            key = (item.symbol, item.timestamp)
            if key in symbol_timestamps:
                issues.append(f"Duplicate timestamp for {item.symbol}: {item.timestamp}")
            else:
                symbol_timestamps[key] = True
        
        # Check price consistency
        for item in data:
            if item.high < item.low:
                issues.append(f"High < Low for {item.symbol} at {item.timestamp}")
            
            if item.high < max(item.open, item.close):
                warnings.append(f"High < max(Open, Close) for {item.symbol} at {item.timestamp}")
            
            if item.low > min(item.open, item.close):
                warnings.append(f"Low > min(Open, Close) for {item.symbol} at {item.timestamp}")
            
            if any(price <= 0 for price in [item.open, item.high, item.low, item.close]):
                issues.append(f"Non-positive price for {item.symbol} at {item.timestamp}")
            
            if item.volume < 0:
                issues.append(f"Negative volume for {item.symbol} at {item.timestamp}")
        
        # Check temporal consistency
        symbol_data = {}
        for item in data:
            if item.symbol not in symbol_data:
                symbol_data[item.symbol] = []
            symbol_data[item.symbol].append(item)
        
        for symbol, items in symbol_data.items():
            sorted_items = sorted(items, key=lambda x: x.timestamp)
            for i in range(1, len(sorted_items)):
                prev_item = sorted_items[i-1]
                curr_item = sorted_items[i]
                
                # Check for large price jumps (>50%)
                price_change = abs(curr_item.close - prev_item.close) / prev_item.close
                if price_change > 0.5:
                    warnings.append(f"Large price jump for {symbol}: {price_change:.2%} from {prev_item.timestamp} to {curr_item.timestamp}")
        
        return {
            'valid': len(issues) == 0,
            'total_records': len(data),
            'unique_symbols': len(symbol_data),
            'issues': issues,
            'warnings': warnings,
            'quality_score': max(0, 1 - (len(issues) * 0.1 + len(warnings) * 0.02))
        }
    
    def auto_detect_columns(self, df: pd.DataFrame) -> Dict[str, str]:
        """Auto-detect column mappings for common variations."""
        column_mappings = {}
        
        # Common column name variations
        column_variations = {
            'symbol': ['symbol', 'code', 'ticker', 'stock_code', '代码', '股票代码'],
            'timestamp': ['timestamp', 'date', 'datetime', 'time', '日期', '时间'],
            'open': ['open', 'opening', 'open_price', '开盘', '开盘价'],
            'high': ['high', 'highest', 'high_price', '最高', '最高价'],
            'low': ['low', 'lowest', 'low_price', '最低', '最低价'],
            'close': ['close', 'closing', 'close_price', '收盘', '收盘价'],
            'volume': ['volume', 'vol', 'trading_volume', '成交量', '交易量'],
            'adj_close': ['adj_close', 'adjusted_close', 'adj_closing', '复权价'],
            'turnover': ['turnover', 'amount', 'trading_amount', '成交额', '交易额']
        }
        
        df_columns_lower = [col.lower() for col in df.columns]
        
        for standard_name, variations in column_variations.items():
            for variation in variations:
                if variation.lower() in df_columns_lower:
                    original_col = df.columns[df_columns_lower.index(variation.lower())]
                    column_mappings[standard_name] = original_col
                    break
        
        return column_mappings
    
    def import_with_auto_detection(
        self,
        file_path: Union[str, Path],
        market: str = 'US',
        exchange: str = 'UNKNOWN',
        currency: str = 'USD',
        **kwargs
    ) -> Tuple[bool, Union[List[UnifiedMarketData], str]]:
        """Import data with automatic column detection and encoding detection."""
        try:
            # Check file size
            file_size_mb = Path(file_path).stat().st_size / (1024 * 1024)
            if file_size_mb > self.max_file_size_mb:
                return False, f"File too large: {file_size_mb:.1f}MB (max: {self.max_file_size_mb}MB)"
            
            # Detect encoding
            encoding = self.detect_file_encoding(file_path)
            logger.info(f"Detected encoding: {encoding}")
            
            # Load data with detected encoding
            file_type = self.detect_file_type(file_path)
            if file_type == 'csv':
                df = pd.read_csv(file_path, encoding=encoding)
            elif file_type == 'excel':
                df = pd.read_excel(file_path)
            else:
                return False, f"Unsupported file type: {file_type}"
            
            # Auto-detect columns
            column_mappings = self.auto_detect_columns(df)
            logger.info(f"Detected column mappings: {column_mappings}")
            
            # Check if we have required columns
            required_columns = ['symbol', 'timestamp', 'open', 'high', 'low', 'close', 'volume']
            missing_columns = set(required_columns) - set(column_mappings.keys())
            
            if missing_columns:
                return False, f"Could not auto-detect required columns: {missing_columns}"
            
            # Rename columns
            df_renamed = df.rename(columns=column_mappings)
            
            # Import using standard method
            return self.import_from_dataframe(df_renamed, market, exchange, currency)
            
        except Exception as e:
            logger.error(f"Failed to import with auto-detection: {e}")
            return False, str(e)
    
    def import_large_file_in_batches(
        self,
        file_path: Union[str, Path],
        market: str = 'US',
        exchange: str = 'UNKNOWN',
        currency: str = 'USD',
        batch_size: Optional[int] = None,
        progress_callback: Optional[callable] = None
    ) -> Tuple[bool, Union[Dict[str, Any], str]]:
        """Import large files in batches to manage memory usage."""
        try:
            batch_size = batch_size or self.batch_size
            file_type = self.detect_file_type(file_path)
            
            if file_type != 'csv':
                return False, "Batch import only supported for CSV files"
            
            # Get total rows for progress tracking
            total_rows = sum(1 for _ in open(file_path, 'r')) - 1  # Subtract header
            
            results = {
                'total_rows': total_rows,
                'processed_rows': 0,
                'successful_rows': 0,
                'failed_rows': 0,
                'batches_processed': 0,
                'errors': []
            }
            
            # Process file in chunks
            chunk_iter = pd.read_csv(file_path, chunksize=batch_size)
            
            for batch_num, chunk in enumerate(chunk_iter):
                try:
                    # Auto-detect columns for first batch
                    if batch_num == 0:
                        column_mappings = self.auto_detect_columns(chunk)
                        required_columns = ['symbol', 'timestamp', 'open', 'high', 'low', 'close', 'volume']
                        missing_columns = set(required_columns) - set(column_mappings.keys())
                        
                        if missing_columns:
                            return False, f"Could not auto-detect required columns: {missing_columns}"
                    
                    # Rename columns
                    chunk_renamed = chunk.rename(columns=column_mappings)
                    
                    # Import batch
                    success, batch_result = self.import_from_dataframe(
                        chunk_renamed, market, exchange, currency
                    )
                    
                    if success:
                        results['successful_rows'] += len(batch_result)
                    else:
                        results['failed_rows'] += len(chunk)
                        results['errors'].append(f"Batch {batch_num}: {batch_result}")
                    
                    results['processed_rows'] += len(chunk)
                    results['batches_processed'] += 1
                    
                    # Call progress callback if provided
                    if progress_callback:
                        progress = results['processed_rows'] / total_rows
                        progress_callback(progress, results)
                    
                    logger.info(f"Processed batch {batch_num}: {len(chunk)} rows")
                    
                except Exception as e:
                    results['failed_rows'] += len(chunk) if 'chunk' in locals() else batch_size
                    results['errors'].append(f"Batch {batch_num}: {str(e)}")
                    logger.error(f"Failed to process batch {batch_num}: {e}")
            
            success = results['successful_rows'] > 0
            return success, results
            
        except Exception as e:
            logger.error(f"Failed to import large file in batches: {e}")
            return False, str(e)
    
    def validate_file_structure(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """Comprehensive file structure validation."""
        try:
            file_path = Path(file_path)
            
            # Basic file checks
            if not file_path.exists():
                return {'valid': False, 'error': 'File does not exist'}
            
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            if file_size_mb > self.max_file_size_mb:
                return {'valid': False, 'error': f'File too large: {file_size_mb:.1f}MB'}
            
            file_type = self.detect_file_type(file_path)
            if not file_type:
                return {'valid': False, 'error': 'Unsupported file format'}
            
            # Load sample data
            if file_type == 'csv':
                encoding = self.detect_file_encoding(file_path)
                sample_df = pd.read_csv(file_path, nrows=100, encoding=encoding)
            else:
                sample_df = pd.read_excel(file_path, nrows=100)
            
            if sample_df.empty:
                return {'valid': False, 'error': 'File is empty'}
            
            # Column analysis
            column_mappings = self.auto_detect_columns(sample_df)
            required_columns = ['symbol', 'timestamp', 'open', 'high', 'low', 'close', 'volume']
            missing_columns = set(required_columns) - set(column_mappings.keys())
            
            # Data type analysis
            data_types = {}
            for col in sample_df.columns:
                data_types[col] = str(sample_df[col].dtype)
            
            # Sample data preview
            preview_data = sample_df.head(5).to_dict('records')
            
            return {
                'valid': len(missing_columns) == 0,
                'file_info': {
                    'size_mb': file_size_mb,
                    'type': file_type,
                    'encoding': encoding if file_type == 'csv' else 'N/A',
                    'total_rows': len(sample_df),
                    'total_columns': len(sample_df.columns)
                },
                'columns': {
                    'detected': list(sample_df.columns),
                    'mappings': column_mappings,
                    'missing_required': list(missing_columns),
                    'data_types': data_types
                },
                'preview': preview_data,
                'recommendations': self._get_import_recommendations(sample_df, column_mappings)
            }
            
        except Exception as e:
            return {'valid': False, 'error': f'Validation failed: {str(e)}'}
    
    def _get_import_recommendations(self, df: pd.DataFrame, mappings: Dict[str, str]) -> List[str]:
        """Generate import recommendations based on data analysis."""
        recommendations = []
        
        # Check for potential issues
        if 'timestamp' in mappings:
            timestamp_col = mappings['timestamp']
            try:
                pd.to_datetime(df[timestamp_col])
            except:
                recommendations.append(f"Timestamp column '{timestamp_col}' may need format specification")
        
        # Check for missing values
        missing_pct = df.isnull().sum() / len(df) * 100
        high_missing = missing_pct[missing_pct > 10]
        if not high_missing.empty:
            recommendations.append(f"High missing values in columns: {list(high_missing.index)}")
        
        # Check for duplicate rows
        if df.duplicated().sum() > 0:
            recommendations.append("File contains duplicate rows")
        
        # Check price consistency
        if all(col in mappings for col in ['open', 'high', 'low', 'close']):
            price_cols = [mappings[col] for col in ['open', 'high', 'low', 'close']]
            for col in price_cols:
                if (df[col] <= 0).any():
                    recommendations.append(f"Non-positive prices found in {col}")
        
        return recommendations


# Global import manager instance
_import_manager: Optional[DataImportManager] = None


def get_import_manager() -> DataImportManager:
    """Get global import manager instance."""
    global _import_manager
    
    if _import_manager is None:
        _import_manager = DataImportManager()
    
    return _import_manager