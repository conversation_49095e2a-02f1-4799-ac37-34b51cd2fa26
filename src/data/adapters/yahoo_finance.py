"""
Yahoo Finance data source adapter for US stock market data.
"""

import logging
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import time

from .base import DataSourceAdapter, DataSourceConfig
from ...models.market_data import UnifiedMarketData, MarketInfo
from ...exceptions import DataException


logger = logging.getLogger(__name__)


class YahooFinanceAdapter(DataSourceAdapter):
    """Yahoo Finance adapter for US stock market data."""
    
    # S&P 500 symbols (sample list - in production this would be more comprehensive)
    SP500_SYMBOLS = [
        'AAPL', 'MSFT', 'AMZN', 'GOOGL', 'GOOG', 'TSLA', 'BRK-B', 'UNH', 'JNJ', 'META',
        'NVDA', 'JPM', 'V', 'PG', 'HD', 'CVX', 'MA', 'BAC', 'ABBV', 'PFE',
        'AVGO', 'KO', 'COST', 'DIS', 'TMO', 'DHR', 'ABT', 'VZ', 'ADBE', 'CRM',
        'NFLX', 'CMCSA', 'NKE', 'ACN', 'TXN', 'RTX', 'QCOM', 'WMT', 'NEE', 'HON',
        'PM', 'UPS', 'T', 'SPGI', 'LOW', 'IBM', 'AMGN', 'CAT', 'GS', 'INTU'
    ]
    
    # NASDAQ 100 symbols (sample list)
    NASDAQ100_SYMBOLS = [
        'AAPL', 'MSFT', 'AMZN', 'GOOGL', 'GOOG', 'TSLA', 'META', 'NVDA', 'AVGO', 'COST',
        'NFLX', 'ADBE', 'PYPL', 'INTC', 'CMCSA', 'TXN', 'QCOM', 'AMGN', 'HON', 'INTU',
        'SBUX', 'GILD', 'AMD', 'BKNG', 'MDLZ', 'ADP', 'ISRG', 'CSX', 'REGN', 'VRTX',
        'FISV', 'ATVI', 'MU', 'AMAT', 'ADI', 'LRCX', 'EXC', 'BIIB', 'XLNX', 'MELI'
    ]
    
    # Market holidays (US stock market)
    US_MARKET_HOLIDAYS = [
        # 2024 holidays
        '2024-01-01',  # New Year's Day
        '2024-01-15',  # Martin Luther King Jr. Day
        '2024-02-19',  # Presidents' Day
        '2024-03-29',  # Good Friday
        '2024-05-27',  # Memorial Day
        '2024-06-19',  # Juneteenth
        '2024-07-04',  # Independence Day
        '2024-09-02',  # Labor Day
        '2024-11-28',  # Thanksgiving
        '2024-12-25',  # Christmas
        # 2025 holidays
        '2025-01-01',  # New Year's Day
        '2025-01-20',  # Martin Luther King Jr. Day
        '2025-02-17',  # Presidents' Day
        '2025-04-18',  # Good Friday
        '2025-05-26',  # Memorial Day
        '2025-06-19',  # Juneteenth
        '2025-07-04',  # Independence Day
        '2025-09-01',  # Labor Day
        '2025-11-27',  # Thanksgiving
        '2025-12-25',  # Christmas
    ]
    
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        
        # Cache for symbol validation and market info
        self._symbol_cache = {}
        self._market_info_cache = {}
        self._cache_expiry = {}
        
        logger.info("Yahoo Finance adapter initialized")
    
    def fetch_historical_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime, 
        interval: str = '1d'
    ) -> List[UnifiedMarketData]:
        """Fetch historical data from Yahoo Finance."""
        try:
            if not self.validate_symbol(symbol):
                raise DataException(f"Invalid symbol: {symbol}")
            
            # Map interval to yfinance format
            yf_interval = self._map_interval(interval)
            
            # Create ticker object
            ticker = yf.Ticker(symbol)
            
            # Fetch historical data with rate limiting
            def fetch_data():
                return ticker.history(
                    start=start_date.strftime('%Y-%m-%d'),
                    end=end_date.strftime('%Y-%m-%d'),
                    interval=yf_interval,
                    auto_adjust=False,  # We want both Close and Adj Close
                    prepost=False,      # Regular trading hours only
                    actions=False       # Don't include dividends/splits in main data
                )
            
            df = self.make_request(fetch_data)
            
            if df.empty:
                logger.warning(f"No data returned for {symbol} from {start_date} to {end_date}")
                return []
            
            # Convert to UnifiedMarketData
            market_data_list = []
            for timestamp, row in df.iterrows():
                try:
                    # Skip rows with NaN values
                    if pd.isna(row['Close']) or pd.isna(row['Volume']):
                        continue
                    
                    market_data = UnifiedMarketData(
                        symbol=symbol,
                        timestamp=timestamp.to_pydatetime(),
                        open=float(row['Open']),
                        high=float(row['High']),
                        low=float(row['Low']),
                        close=float(row['Close']),
                        volume=float(row['Volume']),
                        market=self.get_market_type(),
                        exchange=self.get_exchange_name(),
                        currency=self.get_default_currency(),
                        adj_close=float(row['Adj Close']) if 'Adj Close' in row and pd.notna(row['Adj Close']) else None
                    )
                    
                    if market_data.validate():
                        market_data_list.append(market_data)
                    else:
                        logger.warning(f"Invalid market data for {symbol} at {timestamp}")
                        
                except Exception as e:
                    logger.warning(f"Failed to process row for {symbol} at {timestamp}: {e}")
                    continue
            
            logger.info(f"Fetched {len(market_data_list)} records for {symbol}")
            return market_data_list
            
        except Exception as e:
            logger.error(f"Failed to fetch data for {symbol}: {e}")
            raise DataException(f"Yahoo Finance fetch failed: {str(e)}")
    
    def _map_interval(self, interval: str) -> str:
        """Map standard interval to Yahoo Finance interval format."""
        interval_map = {
            '1m': '1m',
            '2m': '2m',
            '5m': '5m',
            '15m': '15m',
            '30m': '30m',
            '60m': '60m',
            '90m': '90m',
            '1h': '1h',
            '1d': '1d',
            '5d': '5d',
            '1wk': '1wk',
            '1mo': '1mo',
            '3mo': '3mo'
        }
        return interval_map.get(interval, '1d')
    
    def get_available_symbols(self) -> List[str]:
        """Get list of available symbols (S&P 500 + NASDAQ 100)."""
        # Combine S&P 500 and NASDAQ 100 symbols, remove duplicates
        all_symbols = list(set(self.SP500_SYMBOLS + self.NASDAQ100_SYMBOLS))
        return sorted(all_symbols)
    
    def validate_symbol(self, symbol: str) -> bool:
        """Validate if symbol is available and tradeable."""
        # Check cache first
        cache_key = f"validate_{symbol}"
        if cache_key in self._symbol_cache:
            cache_time = self._cache_expiry.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=1):  # Cache for 1 hour
                return self._symbol_cache[cache_key]
        
        try:
            # Quick validation - try to get basic info
            ticker = yf.Ticker(symbol)
            
            def get_info():
                return ticker.info
            
            info = self.make_request(get_info)
            
            # Check if we got valid info
            is_valid = (
                info is not None and 
                isinstance(info, dict) and 
                'symbol' in info and 
                info.get('regularMarketPrice') is not None
            )
            
            # Cache result
            self._symbol_cache[cache_key] = is_valid
            self._cache_expiry[cache_key] = datetime.now()
            
            return is_valid
            
        except Exception as e:
            logger.warning(f"Symbol validation failed for {symbol}: {e}")
            # Cache negative result for shorter time
            self._symbol_cache[cache_key] = False
            self._cache_expiry[cache_key] = datetime.now()
            return False
    
    def get_market_info(self, symbol: str) -> Optional[MarketInfo]:
        """Get market information for a symbol."""
        if not self.validate_symbol(symbol):
            return None
        
        # Check cache first
        cache_key = f"market_info_{symbol}"
        if cache_key in self._market_info_cache:
            cache_time = self._cache_expiry.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=4):  # Cache for 4 hours
                return self._market_info_cache[cache_key]
        
        try:
            ticker = yf.Ticker(symbol)
            
            def get_info():
                return ticker.info
            
            info = self.make_request(get_info)
            
            if not info or not isinstance(info, dict):
                return None
            
            # Extract market info
            market_info = MarketInfo(
                symbol=symbol,
                name=info.get('longName', info.get('shortName', symbol)),
                market=self.get_market_type(),
                exchange=self._get_exchange_from_info(info),
                currency=info.get('currency', self.get_default_currency()),
                lot_size=1.0,  # US stocks trade in single shares
                tick_size=0.01,  # US stocks have $0.01 minimum price increment
                trading_hours=self.get_market_hours(),
                timezone='US/Eastern',
                is_active=info.get('regularMarketPrice') is not None
            )
            
            if market_info.validate():
                # Cache result
                self._market_info_cache[cache_key] = market_info
                self._cache_expiry[cache_key] = datetime.now()
                return market_info
            else:
                logger.warning(f"Invalid market info generated for {symbol}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to get market info for {symbol}: {e}")
            return None
    
    def _get_exchange_from_info(self, info: Dict[str, Any]) -> str:
        """Extract exchange name from ticker info."""
        exchange = info.get('exchange', 'UNKNOWN')
        
        # Map Yahoo Finance exchange codes to standard names
        exchange_map = {
            'NMS': 'NASDAQ',
            'NYQ': 'NYSE',
            'NGM': 'NASDAQ',
            'NCM': 'NASDAQ',
            'ASE': 'AMEX',
            'PCX': 'ARCA'
        }
        
        return exchange_map.get(exchange, exchange)
    
    def get_market_hours(self) -> Dict[str, str]:
        """Get US market trading hours."""
        return {
            'open': '09:30',
            'close': '16:00',
            'timezone': 'US/Eastern'
        }
    
    def is_market_open(self, check_time: Optional[datetime] = None) -> bool:
        """Check if US market is currently open."""
        if check_time is None:
            check_time = datetime.now()
        
        # Convert to Eastern Time
        import pytz
        eastern = pytz.timezone('US/Eastern')
        if check_time.tzinfo is None:
            check_time = eastern.localize(check_time)
        else:
            check_time = check_time.astimezone(eastern)
        
        # Check if it's a weekend
        if check_time.weekday() >= 5:  # Saturday = 5, Sunday = 6
            return False
        
        # Check if it's a holiday
        date_str = check_time.strftime('%Y-%m-%d')
        if date_str in self.US_MARKET_HOLIDAYS:
            return False
        
        # Check trading hours (9:30 AM - 4:00 PM ET)
        market_open = check_time.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = check_time.replace(hour=16, minute=0, second=0, microsecond=0)
        
        return market_open <= check_time <= market_close
    
    def fetch_dividends(self, symbol: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Fetch dividend data for a symbol."""
        try:
            if not self.validate_symbol(symbol):
                raise DataException(f"Invalid symbol: {symbol}")
            
            ticker = yf.Ticker(symbol)
            
            def get_dividends():
                return ticker.dividends
            
            dividends = self.make_request(get_dividends)
            
            if dividends.empty:
                return pd.DataFrame()
            
            # Filter by date range - handle timezone-aware dates
            if dividends.index.tz is not None:
                # Convert start_date and end_date to timezone-aware if needed
                if start_date.tzinfo is None:
                    start_date = start_date.replace(tzinfo=dividends.index.tz)
                if end_date.tzinfo is None:
                    end_date = end_date.replace(tzinfo=dividends.index.tz)
            
            mask = (dividends.index >= start_date) & (dividends.index <= end_date)
            filtered_dividends = dividends[mask]
            
            # Convert to DataFrame with standard columns
            result = pd.DataFrame({
                'symbol': symbol,
                'date': filtered_dividends.index,
                'dividend': filtered_dividends.values,
                'currency': self.get_default_currency()
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to fetch dividends for {symbol}: {e}")
            return pd.DataFrame()
    
    def fetch_splits(self, symbol: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Fetch stock split data for a symbol."""
        try:
            if not self.validate_symbol(symbol):
                raise DataException(f"Invalid symbol: {symbol}")
            
            ticker = yf.Ticker(symbol)
            
            def get_splits():
                return ticker.splits
            
            splits = self.make_request(get_splits)
            
            if splits.empty:
                return pd.DataFrame()
            
            # Filter by date range - handle timezone-aware dates
            if splits.index.tz is not None:
                # Convert start_date and end_date to timezone-aware if needed
                if start_date.tzinfo is None:
                    start_date = start_date.replace(tzinfo=splits.index.tz)
                if end_date.tzinfo is None:
                    end_date = end_date.replace(tzinfo=splits.index.tz)
            
            mask = (splits.index >= start_date) & (splits.index <= end_date)
            filtered_splits = splits[mask]
            
            # Convert to DataFrame with standard columns
            result = pd.DataFrame({
                'symbol': symbol,
                'date': filtered_splits.index,
                'split_ratio': filtered_splits.values
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to fetch splits for {symbol}: {e}")
            return pd.DataFrame()
    
    def get_market_type(self) -> str:
        """Get market type."""
        return 'US'
    
    def get_exchange_name(self) -> str:
        """Get default exchange name."""
        return 'NASDAQ'  # Default, actual exchange determined per symbol
    
    def get_default_currency(self) -> str:
        """Get default currency."""
        return 'USD'
    
    def get_supported_intervals(self) -> List[str]:
        """Get list of supported time intervals."""
        return ['1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '1d', '5d', '1wk', '1mo', '3mo']
    
    def get_real_time_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get real-time quote for a symbol."""
        try:
            if not self.validate_symbol(symbol):
                return None
            
            ticker = yf.Ticker(symbol)
            
            def get_info():
                return ticker.info
            
            info = self.make_request(get_info)
            
            if not info:
                return None
            
            return {
                'symbol': symbol,
                'price': info.get('regularMarketPrice'),
                'change': info.get('regularMarketChange'),
                'change_percent': info.get('regularMarketChangePercent'),
                'volume': info.get('regularMarketVolume'),
                'market_cap': info.get('marketCap'),
                'pe_ratio': info.get('trailingPE'),
                'timestamp': datetime.now(),
                'currency': info.get('currency', 'USD')
            }
            
        except Exception as e:
            logger.error(f"Failed to get real-time quote for {symbol}: {e}")
            return None
    
    def search_symbols(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search for symbols matching query."""
        try:
            # Simple search in our known symbols
            query_upper = query.upper()
            matches = []
            
            all_symbols = self.get_available_symbols()
            for symbol in all_symbols:
                if query_upper in symbol:
                    # Get basic info
                    market_info = self.get_market_info(symbol)
                    if market_info:
                        matches.append({
                            'symbol': symbol,
                            'name': market_info.name,
                            'exchange': market_info.exchange,
                            'currency': market_info.currency
                        })
                    
                    if len(matches) >= limit:
                        break
            
            return matches
            
        except Exception as e:
            logger.error(f"Symbol search failed for query '{query}': {e}")
            return []
    
    def health_check(self) -> tuple[bool, Optional[str]]:
        """Perform health check by fetching data for a test symbol."""
        try:
            # Use AAPL as test symbol
            test_symbol = 'AAPL'
            end_date = datetime.now()
            start_date = end_date - timedelta(days=5)
            
            data = self.fetch_historical_data(test_symbol, start_date, end_date)
            
            if data:
                self.is_healthy = True
                self.last_error = None
                return True, f"Health check passed with {len(data)} records for {test_symbol}"
            else:
                self.is_healthy = False
                self.last_error = "No data returned for test symbol"
                return False, self.last_error
                
        except Exception as e:
            self.is_healthy = False
            self.last_error = f"Health check failed: {str(e)}"
            logger.error(f"Yahoo Finance health check failed: {e}")
            return False, self.last_error