"""
Base classes for data source adapters.
"""

import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import pandas as pd
import time
from threading import Lock

from ...models.market_data import UnifiedMarketData, MarketInfo
from ...exceptions import DataException


logger = logging.getLogger(__name__)


@dataclass
class DataSourceConfig:
    """Configuration for data source adapters."""
    
    name: str
    adapter_class: str
    enabled: bool
    priority: int  # Lower number = higher priority
    rate_limit: Dict[str, int]  # API rate limits
    credentials: Dict[str, str]  # API credentials
    default_params: Dict[str, Any]  # Default parameters
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        if self.priority < 0:
            raise ValueError("Priority must be non-negative")
        
        if not self.rate_limit:
            self.rate_limit = {}
        
        if not self.credentials:
            self.credentials = {}
        
        if not self.default_params:
            self.default_params = {}


class RateLimiter:
    """Rate limiter for API calls."""
    
    def __init__(self, rate_limit_config: Dict[str, int]):
        self.config = rate_limit_config
        self.call_history = []
        self.lock = Lock()
    
    def can_make_request(self) -> bool:
        """Check if a request can be made without exceeding rate limits."""
        with self.lock:
            now = time.time()
            
            # Clean old entries
            self._clean_history(now)
            
            # Check each rate limit
            for period, limit in self.config.items():
                if period == 'requests_per_second':
                    window = 1
                elif period == 'requests_per_minute':
                    window = 60
                elif period == 'requests_per_hour':
                    window = 3600
                elif period == 'requests_per_day':
                    window = 86400
                else:
                    continue
                
                # Count requests in the window
                count = sum(1 for timestamp in self.call_history 
                           if now - timestamp <= window)
                
                if count >= limit:
                    return False
            
            return True
    
    def record_request(self) -> None:
        """Record a request timestamp."""
        with self.lock:
            self.call_history.append(time.time())
    
    def _clean_history(self, now: float) -> None:
        """Remove old entries from call history."""
        # Keep entries from the last day (longest possible window)
        cutoff = now - 86400
        self.call_history = [t for t in self.call_history if t > cutoff]
    
    def get_wait_time(self) -> float:
        """Get time to wait before next request can be made."""
        with self.lock:
            now = time.time()
            self._clean_history(now)
            
            max_wait = 0
            
            for period, limit in self.config.items():
                if period == 'requests_per_second':
                    window = 1
                elif period == 'requests_per_minute':
                    window = 60
                elif period == 'requests_per_hour':
                    window = 3600
                elif period == 'requests_per_day':
                    window = 86400
                else:
                    continue
                
                # Count requests in the window
                window_requests = [t for t in self.call_history 
                                 if now - t <= window]
                
                if len(window_requests) >= limit and window_requests:
                    # Calculate when the oldest request in the window will expire
                    oldest_request = min(window_requests)
                    wait_time = window - (now - oldest_request) + 0.1  # Add small buffer
                    max_wait = max(max_wait, wait_time)
            
            return max(0, max_wait)


class DataSourceAdapter(ABC):
    """Abstract base class for data source adapters."""
    
    def __init__(self, config: DataSourceConfig):
        self.config = config
        self.rate_limiter = RateLimiter(config.rate_limit)
        self.is_healthy = True
        self.last_error = None
        self.last_health_check = None
    
    @abstractmethod
    def fetch_historical_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime, 
        interval: str = '1d'
    ) -> List[UnifiedMarketData]:
        """Fetch historical market data."""
        pass
    
    @abstractmethod
    def get_available_symbols(self) -> List[str]:
        """Get list of available symbols."""
        pass
    
    @abstractmethod
    def validate_symbol(self, symbol: str) -> bool:
        """Validate if symbol is supported."""
        pass
    
    @abstractmethod
    def get_market_info(self, symbol: str) -> Optional[MarketInfo]:
        """Get market information for a symbol."""
        pass
    
    def normalize_data(self, raw_data: Any) -> List[UnifiedMarketData]:
        """Normalize raw data to UnifiedMarketData format."""
        # Default implementation - should be overridden by specific adapters
        if isinstance(raw_data, pd.DataFrame):
            return self._dataframe_to_unified_data(raw_data)
        else:
            raise NotImplementedError("Subclasses must implement normalize_data")
    
    def _dataframe_to_unified_data(self, df: pd.DataFrame) -> List[UnifiedMarketData]:
        """Convert DataFrame to UnifiedMarketData list."""
        market_data_list = []
        
        for _, row in df.iterrows():
            try:
                market_data = UnifiedMarketData(
                    symbol=str(row.get('symbol', '')),
                    timestamp=pd.to_datetime(row['timestamp']).to_pydatetime(),
                    open=float(row['open']),
                    high=float(row['high']),
                    low=float(row['low']),
                    close=float(row['close']),
                    volume=float(row['volume']),
                    market=self.get_market_type(),
                    exchange=self.get_exchange_name(),
                    currency=self.get_default_currency(),
                    adj_close=float(row['adj_close']) if 'adj_close' in row and pd.notna(row['adj_close']) else None,
                    turnover=float(row['turnover']) if 'turnover' in row and pd.notna(row['turnover']) else None
                )
                
                if market_data.validate():
                    market_data_list.append(market_data)
                
            except Exception as e:
                logger.warning(f"Failed to convert row to UnifiedMarketData: {e}")
                continue
        
        return market_data_list
    
    @abstractmethod
    def get_market_type(self) -> str:
        """Get market type (US, CN, CRYPTO, etc.)."""
        pass
    
    @abstractmethod
    def get_exchange_name(self) -> str:
        """Get exchange name."""
        pass
    
    @abstractmethod
    def get_default_currency(self) -> str:
        """Get default currency for this market."""
        pass
    
    def wait_for_rate_limit(self) -> None:
        """Wait if rate limit would be exceeded."""
        if not self.rate_limiter.can_make_request():
            wait_time = self.rate_limiter.get_wait_time()
            if wait_time > 0:
                logger.info(f"Rate limit reached, waiting {wait_time:.2f} seconds")
                time.sleep(wait_time)
    
    def make_request(self, request_func, *args, **kwargs):
        """Make a rate-limited request."""
        self.wait_for_rate_limit()
        
        try:
            result = request_func(*args, **kwargs)
            self.rate_limiter.record_request()
            self.is_healthy = True
            self.last_error = None
            return result
            
        except Exception as e:
            self.is_healthy = False
            self.last_error = str(e)
            logger.error(f"Request failed for {self.config.name}: {e}")
            raise
    
    def health_check(self) -> Tuple[bool, Optional[str]]:
        """Perform health check on the data source."""
        try:
            self.last_health_check = datetime.now()
            
            # Try to get a small amount of data to test connectivity
            symbols = self.get_available_symbols()
            if not symbols:
                self.is_healthy = False
                self.last_error = "No symbols available"
                return False, "No symbols available"
            
            # Try to fetch recent data for the first symbol
            test_symbol = symbols[0]
            end_date = datetime.now()
            start_date = datetime(end_date.year, end_date.month, end_date.day)
            
            try:
                data = self.fetch_historical_data(test_symbol, start_date, end_date)
                self.is_healthy = True
                self.last_error = None
                return True, f"Health check passed with {len(data)} records"
                
            except Exception as e:
                self.is_healthy = False
                self.last_error = f"Data fetch failed: {str(e)}"
                return False, self.last_error
            
        except Exception as e:
            self.is_healthy = False
            self.last_error = f"Health check failed: {str(e)}"
            logger.error(f"Health check failed for {self.config.name}: {e}")
            return False, self.last_error
    
    def get_status(self) -> Dict[str, Any]:
        """Get adapter status information."""
        return {
            'name': self.config.name,
            'enabled': self.config.enabled,
            'healthy': self.is_healthy,
            'last_error': self.last_error,
            'last_health_check': self.last_health_check.isoformat() if self.last_health_check else None,
            'priority': self.config.priority,
            'rate_limits': self.config.rate_limit
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        return {
            'total_requests': len(self.rate_limiter.call_history),
            'requests_last_hour': sum(1 for t in self.rate_limiter.call_history 
                                    if time.time() - t <= 3600),
            'requests_last_day': sum(1 for t in self.rate_limiter.call_history 
                                   if time.time() - t <= 86400),
            'can_make_request': self.rate_limiter.can_make_request(),
            'wait_time': self.rate_limiter.get_wait_time()
        }


class MockDataSourceAdapter(DataSourceAdapter):
    """Mock adapter for testing purposes."""
    
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        self.mock_symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA']
    
    def fetch_historical_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime, 
        interval: str = '1d'
    ) -> List[UnifiedMarketData]:
        """Fetch mock historical data."""
        if not self.validate_symbol(symbol):
            raise DataException(f"Invalid symbol: {symbol}")
        
        # Generate mock data
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        mock_data = []
        
        base_price = 100.0
        for i, date in enumerate(dates):
            price = base_price + (i * 0.5)  # Simple upward trend
            
            market_data = UnifiedMarketData(
                symbol=symbol,
                timestamp=date.to_pydatetime(),
                open=price,
                high=price * 1.02,
                low=price * 0.98,
                close=price * 1.01,
                volume=1000000,
                market=self.get_market_type(),
                exchange=self.get_exchange_name(),
                currency=self.get_default_currency()
            )
            mock_data.append(market_data)
        
        return mock_data
    
    def get_available_symbols(self) -> List[str]:
        """Get mock available symbols."""
        return self.mock_symbols.copy()
    
    def validate_symbol(self, symbol: str) -> bool:
        """Validate mock symbol."""
        return symbol in self.mock_symbols
    
    def get_market_info(self, symbol: str) -> Optional[MarketInfo]:
        """Get mock market info."""
        if not self.validate_symbol(symbol):
            return None
        
        return MarketInfo(
            symbol=symbol,
            name=f"Mock Company {symbol}",
            market=self.get_market_type(),
            exchange=self.get_exchange_name(),
            currency=self.get_default_currency(),
            lot_size=1.0,
            tick_size=0.01,
            trading_hours={'open': '09:30', 'close': '16:00'},
            timezone='US/Eastern'
        )
    
    def get_market_type(self) -> str:
        return 'US'
    
    def get_exchange_name(self) -> str:
        return 'MOCK'
    
    def get_default_currency(self) -> str:
        return 'USD'