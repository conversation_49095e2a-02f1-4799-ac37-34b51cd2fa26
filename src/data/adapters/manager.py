"""
Data source manager for coordinating multiple data adapters.
"""

import logging
import yaml
import os
import json
import threading
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timedelta
from pathlib import Path
import importlib
from threading import Lock, Thread
import time
from collections import defaultdict
import pandas as pd

from .base import DataSourceAdapter, DataSourceConfig, MockDataSourceAdapter
from ...models.market_data import UnifiedMarketData, MarketInfo, EconomicData
from ...exceptions import DataException


logger = logging.getLogger(__name__)


class DataSourceManager:
    """Manager for coordinating multiple data source adapters."""
    
    def _load_configuration(self) -> None:
        """Load data source configuration from YAML file."""
        try:
            config_file = Path(self.config_path)
            if not config_file.exists():
                logger.warning(f"Config file not found: {self.config_path}, using defaults")
                self._create_default_config()
                return
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            self.configs = {}
            for name, config in config_data.items():
                try:
                    # Skip adapters that don't exist yet
                    adapter_class = config.get('adapter_class', '')
                    if adapter_class and not adapter_class.endswith('MockDataSourceAdapter'):
                        # Check if the adapter class exists
                        try:
                            module_path, class_name = adapter_class.rsplit('.', 1)
                            module = importlib.import_module(module_path)
                            getattr(module, class_name)
                        except (ImportError, AttributeError):
                            logger.warning(f"Adapter class {adapter_class} not found, skipping {name}")
                            continue
                    
                    self.configs[name] = DataSourceConfig(
                        name=name,
                        adapter_class=adapter_class,
                        enabled=config.get('enabled', False),
                        priority=config.get('priority', 999),
                        rate_limit=config.get('rate_limit', {}),
                        credentials=config.get('credentials', {}),
                        default_params=config.get('default_params', {})
                    )
                except Exception as e:
                    logger.error(f"Failed to load config for {name}: {e}")
                    continue
            
            logger.info(f"Loaded {len(self.configs)} data source configurations")
            
            # If no valid configurations were loaded, use default
            if not self.configs:
                logger.warning("No valid data source configurations found, using defaults")
                self._create_default_config()
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            self._create_default_config()
    
    def _create_default_config(self) -> None:
        """Create default configuration."""
        self.configs = {
            'mock': DataSourceConfig(
                name='mock',
                adapter_class='src.data.adapters.base.MockDataSourceAdapter',
                enabled=True,
                priority=999,
                rate_limit={'requests_per_minute': 60},
                credentials={},
                default_params={}
            )
        }
        logger.info("Using default configuration with mock adapter")
    
    def _initialize_adapters(self) -> None:
        """Initialize data source adapters."""
        for name, config in self.configs.items():
            if not config.enabled:
                logger.info(f"Skipping disabled adapter: {name}")
                continue
            
            try:
                adapter = self._create_adapter(config)
                if adapter:
                    self.adapters[name] = adapter
                    logger.info(f"Initialized adapter: {name}")
                else:
                    logger.error(f"Failed to create adapter: {name}")
                    
            except Exception as e:
                logger.error(f"Failed to initialize adapter {name}: {e}")
    
    def _create_adapter(self, config: DataSourceConfig) -> Optional[DataSourceAdapter]:
        """Create adapter instance from configuration."""
        try:
            # Handle built-in adapters
            if config.adapter_class == 'src.data.adapters.base.MockDataSourceAdapter':
                return MockDataSourceAdapter(config)
            
            # Dynamic import for custom adapters
            module_path, class_name = config.adapter_class.rsplit('.', 1)
            module = importlib.import_module(module_path)
            adapter_class = getattr(module, class_name)
            
            return adapter_class(config)
            
        except Exception as e:
            logger.error(f"Failed to create adapter {config.name}: {e}")
            return None
    
    def get_adapter(self, name: str) -> Optional[DataSourceAdapter]:
        """Get adapter by name."""
        return self.adapters.get(name)
    
    def get_enabled_adapters(self) -> List[DataSourceAdapter]:
        """Get all enabled adapters sorted by priority."""
        adapters = [adapter for adapter in self.adapters.values() 
                   if adapter.config.enabled]
        return sorted(adapters, key=lambda x: x.config.priority)
    
    def get_adapters_for_market(self, market: str) -> List[DataSourceAdapter]:
        """Get adapters that support a specific market."""
        market_adapters = []
        for adapter in self.get_enabled_adapters():
            try:
                if adapter.get_market_type() == market:
                    market_adapters.append(adapter)
            except Exception as e:
                logger.warning(f"Failed to check market type for {adapter.config.name}: {e}")
        
        return market_adapters
    
    def get_best_adapter_for_symbol(self, symbol: str) -> Optional[DataSourceAdapter]:
        """Get the best adapter for a specific symbol based on priority and availability."""
        for adapter in self.get_enabled_adapters():
            try:
                if adapter.is_healthy and adapter.validate_symbol(symbol):
                    return adapter
            except Exception as e:
                logger.warning(f"Failed to validate symbol {symbol} for {adapter.config.name}: {e}")
                continue
        
        return None
    
    def fetch_historical_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        interval: str = '1d',
        preferred_adapter: Optional[str] = None
    ) -> Tuple[bool, Union[List[UnifiedMarketData], str]]:
        """Fetch historical data using the best available adapter."""
        try:
            # Try preferred adapter first
            if preferred_adapter and preferred_adapter in self.adapters:
                adapter = self.adapters[preferred_adapter]
                if adapter.is_healthy and adapter.config.enabled:
                    try:
                        data = adapter.fetch_historical_data(symbol, start_date, end_date, interval)
                        logger.info(f"Fetched {len(data)} records from {preferred_adapter}")
                        return True, data
                    except Exception as e:
                        logger.warning(f"Preferred adapter {preferred_adapter} failed: {e}")
            
            # Try adapters in priority order
            for adapter in self.get_enabled_adapters():
                if not adapter.is_healthy:
                    continue
                
                try:
                    if adapter.validate_symbol(symbol):
                        data = adapter.fetch_historical_data(symbol, start_date, end_date, interval)
                        logger.info(f"Fetched {len(data)} records from {adapter.config.name}")
                        return True, data
                        
                except Exception as e:
                    logger.warning(f"Adapter {adapter.config.name} failed: {e}")
                    continue
            
            return False, f"No adapter could fetch data for symbol: {symbol}"
            
        except Exception as e:
            logger.error(f"Failed to fetch historical data: {e}")
            return False, str(e)
    
    def get_available_symbols(self, market: Optional[str] = None) -> Dict[str, List[str]]:
        """Get available symbols from all adapters."""
        symbols_by_adapter = {}
        
        adapters = (self.get_adapters_for_market(market) if market 
                   else self.get_enabled_adapters())
        
        for adapter in adapters:
            try:
                if adapter.is_healthy:
                    symbols = adapter.get_available_symbols()
                    symbols_by_adapter[adapter.config.name] = symbols
            except Exception as e:
                logger.warning(f"Failed to get symbols from {adapter.config.name}: {e}")
                symbols_by_adapter[adapter.config.name] = []
        
        return symbols_by_adapter
    
    def get_all_symbols(self, market: Optional[str] = None) -> List[str]:
        """Get unique list of all available symbols."""
        all_symbols = set()
        symbols_by_adapter = self.get_available_symbols(market)
        
        for symbols in symbols_by_adapter.values():
            all_symbols.update(symbols)
        
        return sorted(list(all_symbols))
    
    def get_market_info(self, symbol: str) -> Optional[MarketInfo]:
        """Get market information for a symbol."""
        adapter = self.get_best_adapter_for_symbol(symbol)
        if adapter:
            try:
                return adapter.get_market_info(symbol)
            except Exception as e:
                logger.error(f"Failed to get market info for {symbol}: {e}")
        
        return None
    
    def health_check_all(self) -> Dict[str, Dict[str, Any]]:
        """Perform health check on all adapters."""
        results = {}
        
        for name, adapter in self.adapters.items():
            try:
                is_healthy, message = adapter.health_check()
                results[name] = {
                    'healthy': is_healthy,
                    'message': message,
                    'status': adapter.get_status(),
                    'performance': adapter.get_performance_stats()
                }
            except Exception as e:
                results[name] = {
                    'healthy': False,
                    'message': f"Health check failed: {str(e)}",
                    'status': adapter.get_status(),
                    'performance': {}
                }
        
        return results
    
    def sync_data_for_symbols(
        self, 
        symbols: List[str], 
        start_date: datetime, 
        end_date: datetime,
        interval: str = '1d'
    ) -> Dict[str, Any]:
        """Sync data for multiple symbols."""
        results = {
            'success': [],
            'failed': [],
            'total_records': 0,
            'sync_time': datetime.now().isoformat()
        }
        
        for symbol in symbols:
            try:
                success, data_or_error = self.fetch_historical_data(
                    symbol, start_date, end_date, interval
                )
                
                if success:
                    data = data_or_error
                    results['success'].append({
                        'symbol': symbol,
                        'records': len(data),
                        'date_range': {
                            'start': start_date.isoformat(),
                            'end': end_date.isoformat()
                        }
                    })
                    results['total_records'] += len(data)
                else:
                    results['failed'].append({
                        'symbol': symbol,
                        'error': data_or_error
                    })
                    
            except Exception as e:
                results['failed'].append({
                    'symbol': symbol,
                    'error': str(e)
                })
        
        # Update sync times
        with self.lock:
            for symbol in symbols:
                self.last_sync_times[symbol] = datetime.now()
        
        return results
    
    def get_sync_status(self) -> Dict[str, Any]:
        """Get synchronization status."""
        with self.lock:
            return {
                'last_sync_times': {
                    symbol: sync_time.isoformat() 
                    for symbol, sync_time in self.last_sync_times.items()
                },
                'total_synced_symbols': len(self.last_sync_times),
                'adapters_status': {
                    name: adapter.get_status() 
                    for name, adapter in self.adapters.items()
                }
            }
    
    def get_data_coverage_report(self) -> Dict[str, Any]:
        """Get comprehensive data coverage report."""
        coverage = {}
        
        for name, adapter in self.adapters.items():
            try:
                if adapter.is_healthy:
                    symbols = adapter.get_available_symbols()
                    coverage[name] = {
                        'market': adapter.get_market_type(),
                        'exchange': adapter.get_exchange_name(),
                        'symbol_count': len(symbols),
                        'symbols': symbols[:10],  # First 10 symbols as sample
                        'status': adapter.get_status()
                    }
                else:
                    coverage[name] = {
                        'market': 'unknown',
                        'exchange': 'unknown',
                        'symbol_count': 0,
                        'symbols': [],
                        'status': adapter.get_status()
                    }
            except Exception as e:
                coverage[name] = {
                    'error': str(e),
                    'status': adapter.get_status()
                }
        
        return {
            'adapters': coverage,
            'total_adapters': len(self.adapters),
            'healthy_adapters': sum(1 for a in self.adapters.values() if a.is_healthy),
            'generated_at': datetime.now().isoformat()
        }
    
    def reload_configuration(self) -> bool:
        """Reload configuration and reinitialize adapters."""
        try:
            logger.info("Reloading data source configuration")
            
            # Save current state
            old_adapters = self.adapters.copy()
            
            # Reload configuration
            self._load_configuration()
            self._initialize_adapters()
            
            # Clean up old adapters that are no longer configured
            for name in old_adapters:
                if name not in self.adapters:
                    logger.info(f"Removed adapter: {name}")
            
            logger.info("Configuration reloaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to reload configuration: {e}")
            return False
    
    def add_adapter(self, config: DataSourceConfig) -> bool:
        """Add a new adapter dynamically."""
        try:
            adapter = self._create_adapter(config)
            if adapter:
                self.adapters[config.name] = adapter
                self.configs[config.name] = config
                logger.info(f"Added adapter: {config.name}")
                return True
            else:
                logger.error(f"Failed to create adapter: {config.name}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to add adapter {config.name}: {e}")
            return False
    
    def remove_adapter(self, name: str) -> bool:
        """Remove an adapter."""
        try:
            if name in self.adapters:
                del self.adapters[name]
                if name in self.configs:
                    del self.configs[name]
                logger.info(f"Removed adapter: {name}")
                return True
            else:
                logger.warning(f"Adapter not found: {name}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to remove adapter {name}: {e}")
            return False
    
    # Enhanced functionality for task 2.8
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize enhanced data source manager."""
        self.adapters: Dict[str, DataSourceAdapter] = {}
        self.config_path = config_path or "config/data_sources.yaml"
        self.lock = Lock()
        self.last_sync_times: Dict[str, datetime] = {}
        
        # Enhanced features for task 2.8
        self.scheduler_thread: Optional[Thread] = None
        self.scheduler_running = False
        self.performance_stats: Dict[str, Dict[str, Any]] = defaultdict(dict)
        self.health_check_interval = 300  # 5 minutes
        self.sync_schedules: Dict[str, Dict[str, Any]] = {}
        self.correlation_cache: Dict[str, pd.DataFrame] = {}
        self.correlation_cache_expiry: Dict[str, datetime] = {}
        
        # Load configuration and initialize adapters
        self._load_configuration()
        self._initialize_adapters()
        
        # Start background scheduler
        self._start_scheduler()
    
    def _start_scheduler(self) -> None:
        """Start background task scheduler."""
        if not self.scheduler_running:
            self.scheduler_running = True
            self.scheduler_thread = Thread(target=self._scheduler_loop, daemon=True)
            self.scheduler_thread.start()
            logger.info("Data source scheduler started")
    
    def _scheduler_loop(self) -> None:
        """Background scheduler loop."""
        while self.scheduler_running:
            try:
                # Run scheduled health checks
                self._scheduled_health_check()
                
                # Run scheduled data synchronization
                self._scheduled_data_sync()
                
                # Update performance statistics
                self._update_performance_stats()
                
                # Sleep for 60 seconds before next iteration
                time.sleep(60)
                
            except Exception as e:
                logger.error(f"Scheduler error: {e}")
                time.sleep(60)
    
    def _scheduled_health_check(self) -> None:
        """Perform scheduled health checks on all adapters."""
        current_time = datetime.now()
        
        for name, adapter in self.adapters.items():
            try:
                # Check if health check is due
                last_check = adapter.last_health_check
                if (not last_check or 
                    (current_time - last_check).total_seconds() > self.health_check_interval):
                    
                    is_healthy, message = adapter.health_check()
                    
                    # Log health status changes
                    if adapter.is_healthy != is_healthy:
                        status = "healthy" if is_healthy else "unhealthy"
                        logger.info(f"Adapter {name} status changed to {status}: {message}")
                    
            except Exception as e:
                logger.error(f"Health check failed for {name}: {e}")
    
    def _scheduled_data_sync(self) -> None:
        """Perform scheduled data synchronization."""
        current_time = datetime.now()
        
        for schedule_id, schedule_config in self.sync_schedules.items():
            try:
                # Check if sync is due
                last_sync = schedule_config.get('last_sync')
                interval = schedule_config.get('interval_minutes', 60)
                
                if (not last_sync or 
                    (current_time - last_sync).total_seconds() > interval * 60):
                    
                    # Perform sync
                    symbols = schedule_config.get('symbols', [])
                    start_date = current_time - timedelta(days=schedule_config.get('days_back', 1))
                    
                    result = self.sync_data_for_symbols(symbols, start_date, current_time)
                    
                    # Update last sync time
                    schedule_config['last_sync'] = current_time
                    
                    logger.info(f"Scheduled sync {schedule_id} completed: "
                              f"{len(result['success'])} success, {len(result['failed'])} failed")
                    
            except Exception as e:
                logger.error(f"Scheduled sync {schedule_id} failed: {e}")
    
    def _update_performance_stats(self) -> None:
        """Update performance statistics for all adapters."""
        current_time = datetime.now()
        
        for name, adapter in self.adapters.items():
            try:
                stats = adapter.get_performance_stats()
                self.performance_stats[name] = {
                    **stats,
                    'last_updated': current_time.isoformat(),
                    'health_status': adapter.is_healthy,
                    'last_error': adapter.last_error
                }
            except Exception as e:
                logger.error(f"Failed to update performance stats for {name}: {e}")
    
    def add_sync_schedule(
        self, 
        schedule_id: str, 
        symbols: List[str], 
        interval_minutes: int = 60,
        days_back: int = 1
    ) -> bool:
        """Add a data synchronization schedule."""
        try:
            self.sync_schedules[schedule_id] = {
                'symbols': symbols,
                'interval_minutes': interval_minutes,
                'days_back': days_back,
                'created_at': datetime.now(),
                'last_sync': None
            }
            
            logger.info(f"Added sync schedule {schedule_id} for {len(symbols)} symbols")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add sync schedule {schedule_id}: {e}")
            return False
    
    def remove_sync_schedule(self, schedule_id: str) -> bool:
        """Remove a data synchronization schedule."""
        try:
            if schedule_id in self.sync_schedules:
                del self.sync_schedules[schedule_id]
                logger.info(f"Removed sync schedule {schedule_id}")
                return True
            else:
                logger.warning(f"Sync schedule not found: {schedule_id}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to remove sync schedule {schedule_id}: {e}")
            return False
    
    def get_adapter_priority_order(self, symbol: str) -> List[str]:
        """Get adapters in priority order for a specific symbol."""
        suitable_adapters = []
        
        for name, adapter in self.adapters.items():
            if adapter.config.enabled and adapter.is_healthy:
                try:
                    if adapter.validate_symbol(symbol):
                        suitable_adapters.append((name, adapter.config.priority))
                except Exception as e:
                    logger.warning(f"Failed to validate symbol {symbol} for {name}: {e}")
        
        # Sort by priority (lower number = higher priority)
        suitable_adapters.sort(key=lambda x: x[1])
        return [name for name, _ in suitable_adapters]
    
    def get_failover_adapter(self, primary_adapter: str, symbol: str) -> Optional[str]:
        """Get failover adapter for a symbol when primary adapter fails."""
        priority_order = self.get_adapter_priority_order(symbol)
        
        try:
            primary_index = priority_order.index(primary_adapter)
            # Return next adapter in priority order
            if primary_index + 1 < len(priority_order):
                return priority_order[primary_index + 1]
        except ValueError:
            # Primary adapter not in list, return first available
            if priority_order:
                return priority_order[0]
        
        return None
    
    def fetch_with_failover(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = '1d',
        max_attempts: int = 3
    ) -> Tuple[bool, Union[List[UnifiedMarketData], str], Optional[str]]:
        """Fetch data with automatic failover between adapters."""
        priority_order = self.get_adapter_priority_order(symbol)
        
        if not priority_order:
            return False, f"No suitable adapters found for symbol: {symbol}", None
        
        last_error = ""
        
        for attempt, adapter_name in enumerate(priority_order[:max_attempts]):
            try:
                adapter = self.adapters[adapter_name]
                
                logger.info(f"Attempting to fetch {symbol} from {adapter_name} (attempt {attempt + 1})")
                
                data = adapter.fetch_historical_data(symbol, start_date, end_date, interval)
                
                if data:
                    logger.info(f"Successfully fetched {len(data)} records from {adapter_name}")
                    return True, data, adapter_name
                else:
                    last_error = f"No data returned from {adapter_name}"
                    logger.warning(last_error)
                    
            except Exception as e:
                last_error = f"Adapter {adapter_name} failed: {str(e)}"
                logger.warning(last_error)
                continue
        
        return False, f"All adapters failed. Last error: {last_error}", None
    
    def get_comprehensive_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report for all adapters."""
        report = {
            'generated_at': datetime.now().isoformat(),
            'total_adapters': len(self.adapters),
            'healthy_adapters': sum(1 for a in self.adapters.values() if a.is_healthy),
            'adapters': {},
            'summary': {
                'total_requests_last_hour': 0,
                'total_requests_last_day': 0,
                'average_response_time': 0,
                'error_rate': 0
            }
        }
        
        total_requests_hour = 0
        total_requests_day = 0
        total_errors = 0
        
        for name, adapter in self.adapters.items():
            try:
                perf_stats = adapter.get_performance_stats()
                status = adapter.get_status()
                
                adapter_report = {
                    'name': name,
                    'market': adapter.get_market_type(),
                    'exchange': adapter.get_exchange_name(),
                    'enabled': adapter.config.enabled,
                    'healthy': adapter.is_healthy,
                    'priority': adapter.config.priority,
                    'performance': perf_stats,
                    'status': status,
                    'rate_limits': adapter.config.rate_limit
                }
                
                report['adapters'][name] = adapter_report
                
                # Aggregate statistics
                total_requests_hour += perf_stats.get('requests_last_hour', 0)
                total_requests_day += perf_stats.get('requests_last_day', 0)
                
                if not adapter.is_healthy:
                    total_errors += 1
                    
            except Exception as e:
                logger.error(f"Failed to get performance report for {name}: {e}")
                report['adapters'][name] = {'error': str(e)}
        
        # Calculate summary statistics
        report['summary']['total_requests_last_hour'] = total_requests_hour
        report['summary']['total_requests_last_day'] = total_requests_day
        report['summary']['error_rate'] = (total_errors / len(self.adapters)) * 100 if self.adapters else 0
        
        return report
    
    def analyze_market_correlations(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        cache_duration_hours: int = 24
    ) -> Optional[pd.DataFrame]:
        """Analyze correlations between different markets and assets."""
        cache_key = f"correlation_{hash(tuple(sorted(symbols)))}_{start_date.date()}_{end_date.date()}"
        
        # Check cache
        if cache_key in self.correlation_cache:
            cache_time = self.correlation_cache_expiry.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=cache_duration_hours):
                return self.correlation_cache[cache_key]
        
        try:
            # Fetch data for all symbols
            data_dict = {}
            
            for symbol in symbols:
                success, data_or_error, adapter_name = self.fetch_with_failover(
                    symbol, start_date, end_date
                )
                
                if success:
                    data = data_or_error
                    # Convert to price series
                    if data:
                        dates = [d.timestamp for d in data]
                        prices = [d.close for d in data]
                        data_dict[symbol] = pd.Series(prices, index=dates)
                else:
                    logger.warning(f"Failed to fetch data for correlation analysis: {symbol}")
            
            if len(data_dict) < 2:
                logger.warning("Need at least 2 symbols for correlation analysis")
                return None
            
            # Create DataFrame and calculate correlations
            df = pd.DataFrame(data_dict)
            df = df.ffill().bfill()  # Forward/backward fill
            
            # Calculate returns for correlation
            returns_df = df.pct_change().dropna()
            correlation_matrix = returns_df.corr()
            
            # Cache result
            self.correlation_cache[cache_key] = correlation_matrix
            self.correlation_cache_expiry[cache_key] = datetime.now()
            
            return correlation_matrix
            
        except Exception as e:
            logger.error(f"Failed to analyze market correlations: {e}")
            return None
    
    def get_economic_market_analysis(
        self,
        economic_indicators: List[str],
        market_symbols: List[str],
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Analyze relationships between economic data and market data."""
        try:
            analysis_result = {
                'generated_at': datetime.now().isoformat(),
                'period': {
                    'start': start_date.isoformat(),
                    'end': end_date.isoformat()
                },
                'economic_indicators': {},
                'market_symbols': {},
                'correlations': {},
                'summary': {}
            }
            
            # Fetch economic data
            economic_data = {}
            for indicator in economic_indicators:
                success, data_or_error, adapter_name = self.fetch_with_failover(
                    indicator, start_date, end_date
                )
                
                if success and isinstance(data_or_error, list):
                    # Convert economic data to series
                    if data_or_error and hasattr(data_or_error[0], 'value'):
                        dates = [d.timestamp for d in data_or_error]
                        values = [d.value for d in data_or_error]
                        economic_data[indicator] = pd.Series(values, index=dates)
                        
                        analysis_result['economic_indicators'][indicator] = {
                            'data_points': len(data_or_error),
                            'latest_value': values[-1] if values else None,
                            'source_adapter': adapter_name
                        }
            
            # Fetch market data
            market_data = {}
            for symbol in market_symbols:
                success, data_or_error, adapter_name = self.fetch_with_failover(
                    symbol, start_date, end_date
                )
                
                if success and isinstance(data_or_error, list):
                    # Convert market data to series
                    if data_or_error:
                        dates = [d.timestamp for d in data_or_error]
                        prices = [d.close for d in data_or_error]
                        market_data[symbol] = pd.Series(prices, index=dates)
                        
                        analysis_result['market_symbols'][symbol] = {
                            'data_points': len(data_or_error),
                            'latest_price': prices[-1] if prices else None,
                            'source_adapter': adapter_name
                        }
            
            # Calculate cross-correlations between economic and market data
            correlations = {}
            for econ_indicator, econ_series in economic_data.items():
                for market_symbol, market_series in market_data.items():
                    try:
                        # Align data by date and calculate correlation
                        aligned_data = pd.DataFrame({
                            'economic': econ_series,
                            'market': market_series
                        }).dropna()
                        
                        if len(aligned_data) > 10:  # Need sufficient data points
                            correlation = aligned_data['economic'].corr(aligned_data['market'])
                            correlations[f"{econ_indicator}_vs_{market_symbol}"] = {
                                'correlation': correlation,
                                'data_points': len(aligned_data),
                                'significance': 'high' if abs(correlation) > 0.7 else 'medium' if abs(correlation) > 0.3 else 'low'
                            }
                    except Exception as e:
                        logger.warning(f"Failed to calculate correlation between {econ_indicator} and {market_symbol}: {e}")
            
            analysis_result['correlations'] = correlations
            
            # Generate summary
            if correlations:
                correlation_values = [c['correlation'] for c in correlations.values() if not pd.isna(c['correlation'])]
                analysis_result['summary'] = {
                    'total_correlations': len(correlations),
                    'average_correlation': sum(correlation_values) / len(correlation_values) if correlation_values else 0,
                    'strong_correlations': len([c for c in correlation_values if abs(c) > 0.7]),
                    'weak_correlations': len([c for c in correlation_values if abs(c) < 0.3])
                }
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Failed to perform economic-market analysis: {e}")
            return {'error': str(e)}
    
    def stop_scheduler(self) -> None:
        """Stop the background scheduler."""
        self.scheduler_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        logger.info("Data source scheduler stopped")
    
    def __del__(self):
        """Cleanup when manager is destroyed."""
        try:
            self.stop_scheduler()
        except:
            pass


# Global data source manager instance
_data_source_manager: Optional[DataSourceManager] = None


def get_data_source_manager(config_path: Optional[str] = None) -> DataSourceManager:
    """Get global data source manager instance."""
    global _data_source_manager
    
    if _data_source_manager is None:
        _data_source_manager = DataSourceManager(config_path)
    
    return _data_source_manager