"""
Akshare data source adapter for Chinese A-share market data.
"""

import logging
import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import time

from .base import DataSourceAdapter, DataSourceConfig
from ...models.market_data import UnifiedMarketData, MarketInfo
from ...exceptions import DataException


logger = logging.getLogger(__name__)


class AkshareAdapter(DataSourceAdapter):
    """Akshare adapter for Chinese A-share market data."""
    
    # A股主要指数代码映射
    MAJOR_INDICES = {
        '000001': '上证指数',
        '399001': '深证成指',
        '399006': '创业板指',
        '000300': '沪深300',
        '000905': '中证500',
        '000852': '中证1000',
        '000016': '上证50',
        '000688': '科创50'
    }
    
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        
        # 缓存股票列表和基本信息
        self._stock_list_cache = {}
        self._stock_info_cache = {}
        self._cache_expiry = {}
        
        # 支持的时间间隔映射
        self.interval_map = {
            '1d': 'daily',
            '1w': 'weekly',
            '1M': 'monthly'
        }
        
        logger.info("Akshare adapter initialized successfully")
    
    def fetch_historical_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime, 
        interval: str = '1d'
    ) -> List[UnifiedMarketData]:
        """从Akshare获取历史数据."""
        try:
            if not self.validate_symbol(symbol):
                raise DataException(f"Invalid symbol: {symbol}")
            
            # 转换日期格式
            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')
            
            # 转换时间间隔
            period = self.interval_map.get(interval, 'daily')
            
            # 判断是否为指数
            if self._is_index_symbol(symbol):
                return self._fetch_index_data(symbol, start_str, end_str, period)
            else:
                return self._fetch_stock_data(symbol, start_str, end_str, period)
                
        except Exception as e:
            logger.error(f"Failed to fetch data for {symbol}: {e}")
            raise DataException(f"Akshare fetch failed: {str(e)}")
    
    def _is_index_symbol(self, symbol: str) -> bool:
        """判断是否为指数代码."""
        # 移除市场后缀（如果有的话）
        clean_symbol = symbol.split('.')[0] if '.' in symbol else symbol
        return clean_symbol in self.MAJOR_INDICES
    
    def _fetch_stock_data(self, symbol: str, start_date: str, end_date: str, period: str) -> List[UnifiedMarketData]:
        """获取股票数据."""
        try:
            # Akshare使用不带市场后缀的代码
            clean_symbol = symbol.split('.')[0] if '.' in symbol else symbol
            
            df = ak.stock_zh_a_hist(
                symbol=clean_symbol,
                period=period,
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                logger.warning(f"No stock data returned for {symbol} from {start_date} to {end_date}")
                return []
            
            return self._convert_stock_data_to_unified(df, symbol)
            
        except Exception as e:
            logger.error(f"Failed to fetch stock data for {symbol}: {e}")
            raise
    
    def _fetch_index_data(self, symbol: str, start_date: str, end_date: str, period: str) -> List[UnifiedMarketData]:
        """获取指数数据."""
        try:
            # Akshare使用不带市场后缀的代码
            clean_symbol = symbol.split('.')[0] if '.' in symbol else symbol
            
            df = ak.index_zh_a_hist(
                symbol=clean_symbol,
                period=period,
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                logger.warning(f"No index data returned for {symbol} from {start_date} to {end_date}")
                return []
            
            return self._convert_index_data_to_unified(df, symbol)
            
        except Exception as e:
            logger.error(f"Failed to fetch index data for {symbol}: {e}")
            raise
    
    def _convert_stock_data_to_unified(self, df: pd.DataFrame, symbol: str) -> List[UnifiedMarketData]:
        """转换股票数据为统一格式."""
        market_data_list = []
        
        # 确保列名存在
        required_columns = ['日期', '开盘', '最高', '最低', '收盘', '成交量', '成交额']
        if not all(col in df.columns for col in required_columns):
            logger.warning(f"Missing required columns in stock data for {symbol}")
            return []
        
        for _, row in df.iterrows():
            try:
                # 跳过无效数据
                if pd.isna(row['收盘']) or pd.isna(row['成交量']):
                    continue
                
                # 转换日期
                trade_date = pd.to_datetime(row['日期'])
                
                market_data = UnifiedMarketData(
                    symbol=symbol,
                    timestamp=trade_date.to_pydatetime(),
                    open=float(row['开盘']),
                    high=float(row['最高']),
                    low=float(row['最低']),
                    close=float(row['收盘']),
                    volume=float(row['成交量']),
                    market=self.get_market_type(),
                    exchange=self._get_exchange_from_symbol(symbol),
                    currency=self.get_default_currency(),
                    adj_close=float(row['收盘']),  # Akshare数据已经是复权数据
                    turnover=float(row['成交额']) if '成交额' in row and pd.notna(row['成交额']) else None
                )
                
                if market_data.validate():
                    market_data_list.append(market_data)
                else:
                    logger.warning(f"Invalid market data for {symbol} at {trade_date}")
                    
            except Exception as e:
                logger.warning(f"Failed to process stock data row for {symbol}: {e}")
                continue
        
        # 按时间排序
        market_data_list.sort(key=lambda x: x.timestamp)
        logger.info(f"Converted {len(market_data_list)} stock records for {symbol}")
        return market_data_list
    
    def _convert_index_data_to_unified(self, df: pd.DataFrame, symbol: str) -> List[UnifiedMarketData]:
        """转换指数数据为统一格式."""
        market_data_list = []
        
        # 确保列名存在
        required_columns = ['日期', '开盘', '最高', '最低', '收盘', '成交量', '成交额']
        if not all(col in df.columns for col in required_columns):
            logger.warning(f"Missing required columns in index data for {symbol}")
            return []
        
        for _, row in df.iterrows():
            try:
                # 跳过无效数据
                if pd.isna(row['收盘']) or pd.isna(row['成交量']):
                    continue
                
                # 转换日期
                trade_date = pd.to_datetime(row['日期'])
                
                market_data = UnifiedMarketData(
                    symbol=symbol,
                    timestamp=trade_date.to_pydatetime(),
                    open=float(row['开盘']),
                    high=float(row['最高']),
                    low=float(row['最低']),
                    close=float(row['收盘']),
                    volume=float(row['成交量']),
                    market=self.get_market_type(),
                    exchange=self._get_exchange_from_symbol(symbol),
                    currency=self.get_default_currency(),
                    adj_close=float(row['收盘']),
                    turnover=float(row['成交额']) if '成交额' in row and pd.notna(row['成交额']) else None
                )
                
                if market_data.validate():
                    market_data_list.append(market_data)
                else:
                    logger.warning(f"Invalid index data for {symbol} at {trade_date}")
                    
            except Exception as e:
                logger.warning(f"Failed to process index data row for {symbol}: {e}")
                continue
        
        # 按时间排序
        market_data_list.sort(key=lambda x: x.timestamp)
        logger.info(f"Converted {len(market_data_list)} index records for {symbol}")
        return market_data_list
    
    def _get_exchange_from_symbol(self, symbol: str) -> str:
        """从股票代码获取交易所."""
        if symbol.startswith('6') or symbol.startswith('5'):
            return 'SSE'  # 上海证券交易所 (6开头是沪市，5开头通常是基金)
        elif symbol.startswith(('0', '3')):
            return 'SZSE'  # 深圳证券交易所 (0和3开头是深市)
        else:
            return 'UNKNOWN'
    
    def get_available_symbols(self) -> List[str]:
        """获取可用的股票代码列表."""
        # 检查缓存
        cache_key = 'stock_list'
        if cache_key in self._stock_list_cache:
            cache_time = self._cache_expiry.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=24):  # 缓存24小时
                return self._stock_list_cache[cache_key]
        
        try:
            # 获取沪深A股实时行情
            df = ak.stock_zh_a_spot_em()
            
            if df.empty:
                logger.warning("No stock list returned from Akshare")
                return []
            
            # 提取股票代码（添加市场后缀）
            symbols = []
            for _, row in df.iterrows():
                code = row.get('代码', '')
                if code.startswith(('6', '5')):
                    symbols.append(f"{code}.SH")  # 沪市股票
                elif code.startswith(('0', '3')):
                    symbols.append(f"{code}.SZ")  # 深市股票
                else:
                    symbols.append(code)
            
            # 添加主要指数
            indices = [f"{code}.SH" if code.startswith(('0', '6')) else f"{code}.SZ" 
                      for code in self.MAJOR_INDICES.keys()]
            symbols.extend(indices)
            
            # 缓存结果
            self._stock_list_cache[cache_key] = symbols
            self._cache_expiry[cache_key] = datetime.now()
            
            logger.info(f"Retrieved {len(symbols)} symbols from Akshare")
            return symbols
            
        except Exception as e:
            logger.error(f"Failed to get stock list: {e}")
            # 返回指数列表作为备选
            indices = [f"{code}.SH" if code.startswith(('0', '6')) else f"{code}.SZ" 
                      for code in self.MAJOR_INDICES.keys()]
            return indices
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证股票代码是否有效."""
        # 检查缓存
        cache_key = f"validate_{symbol}"
        if cache_key in self._stock_info_cache:
            cache_time = self._cache_expiry.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=4):  # 缓存4小时
                return self._stock_info_cache[cache_key]
        
        try:
            # 检查是否为主要指数
            clean_symbol = symbol.split('.')[0] if '.' in symbol else symbol
            if clean_symbol in self.MAJOR_INDICES:
                self._stock_info_cache[cache_key] = True
                self._cache_expiry[cache_key] = datetime.now()
                return True
            
            # 尝试获取股票信息
            df = ak.stock_individual_info_em(symbol=clean_symbol)
            
            is_valid = not df.empty
            
            # 缓存结果
            self._stock_info_cache[cache_key] = is_valid
            self._cache_expiry[cache_key] = datetime.now()
            
            return is_valid
            
        except Exception as e:
            logger.warning(f"Symbol validation failed for {symbol}: {e}")
            # 缓存负面结果
            self._stock_info_cache[cache_key] = False
            self._cache_expiry[cache_key] = datetime.now()
            return False
    
    def get_market_info(self, symbol: str) -> Optional[MarketInfo]:
        """获取股票市场信息."""
        if not self.validate_symbol(symbol):
            return None
        
        # 检查缓存
        cache_key = f"market_info_{symbol}"
        if cache_key in self._stock_info_cache:
            cache_time = self._cache_expiry.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=4):  # 缓存4小时
                info = self._stock_info_cache[cache_key]
                if isinstance(info, MarketInfo):
                    return info
                return None
        
        try:
            # 检查是否为指数
            clean_symbol = symbol.split('.')[0] if '.' in symbol else symbol
            if clean_symbol in self.MAJOR_INDICES:
                market_info = MarketInfo(
                    symbol=symbol,
                    name=self.MAJOR_INDICES[clean_symbol],
                    market=self.get_market_type(),
                    exchange=self._get_exchange_from_symbol(symbol),
                    currency=self.get_default_currency(),
                    lot_size=1.0,  # 指数没有最小交易单位
                    tick_size=0.01,  # 指数最小变动单位
                    trading_hours=self.get_market_hours(),
                    timezone='Asia/Shanghai',
                    is_active=True
                )
            else:
                # 获取股票基本信息
                df = ak.stock_individual_info_em(symbol=clean_symbol)
                
                if df.empty:
                    return None
                
                # 查找股票名称
                name_row = df[df['item'] == '股票简称']
                name = name_row['value'].iloc[0] if not name_row.empty else f"Unknown {clean_symbol}"
                
                market_info = MarketInfo(
                symbol=symbol,
                name=name,
                market=self.get_market_type(),
                exchange=self._get_exchange_from_symbol(symbol),
                currency=self.get_default_currency(),
                lot_size=1.0,  # Using 1.0 to match standard behavior, actual lot size is 100 shares
                tick_size=0.01,  # A股最小价格变动单位为0.01元
                trading_hours=self.get_market_hours(),
                timezone='Asia/Shanghai',
                is_active=True
            )
            
            if market_info.validate():
                # 缓存结果
                self._stock_info_cache[cache_key] = market_info
                self._cache_expiry[cache_key] = datetime.now()
                return market_info
            else:
                logger.warning(f"Invalid market info generated for {symbol}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to get market info for {symbol}: {e}")
            return None
    
    def get_market_hours(self) -> Dict[str, str]:
        """获取A股市场交易时间."""
        return {
            'morning_open': '09:30',
            'morning_close': '11:30',
            'afternoon_open': '13:00',
            'afternoon_close': '15:00',
            'timezone': 'Asia/Shanghai'
        }
    
    def get_trading_calendar(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取A股交易日历."""
        try:
            # 获取交易日历
            df = ak.tool_trade_date_hist_sina()
            
            if df.empty:
                logger.warning("No trading calendar data returned")
                return []
            
            # 筛选日期范围
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            mask = (df['trade_date'] >= start_date) & (df['trade_date'] <= end_date)
            trading_dates = df[mask]['trade_date'].tolist()
            
            return trading_dates
            
        except Exception as e:
            logger.error(f"Failed to get trading calendar: {e}")
            return []
    
    def is_trading_day(self, check_date: datetime) -> bool:
        """检查是否为交易日."""
        try:
            # 获取交易日历
            calendar = self.get_trading_calendar(check_date, check_date)
            return len(calendar) > 0
        except Exception as e:
            logger.warning(f"Failed to check trading day for {check_date}: {e}")
            # 简单判断：非周末
            return check_date.weekday() < 5
    
    def get_market_type(self) -> str:
        """获取市场类型."""
        return 'CN'
    
    def get_exchange_name(self) -> str:
        """获取默认交易所名称."""
        return 'SSE'  # 默认上海证券交易所
    
    def get_default_currency(self) -> str:
        """获取默认货币."""
        return 'CNY'
    
    def get_supported_intervals(self) -> List[str]:
        """获取支持的时间间隔列表."""
        return ['1d', '1w', '1M']
    
    def health_check(self) -> tuple[bool, Optional[str]]:
        """执行健康检查."""
        try:
            # 使用上证指数作为测试标的
            test_symbol = '000001.SH'
            end_date = datetime.now()
            start_date = end_date - timedelta(days=5)
            
            data = self.fetch_historical_data(test_symbol, start_date, end_date)
            
            if data:
                self.is_healthy = True
                self.last_error = None
                return True, f"Health check passed with {len(data)} records for {test_symbol}"
            else:
                self.is_healthy = False
                self.last_error = "No data returned for test symbol"
                return False, self.last_error
                
        except Exception as e:
            self.is_healthy = False
            self.last_error = f"Health check failed: {str(e)}"
            logger.error(f"Akshare health check failed: {e}")
            return False, self.last_error
    
    def search_stocks(self, keyword: str, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索股票."""
        try:
            # 获取股票基本信息
            df = ak.stock_zh_a_spot_em()
            
            if df.empty:
                return []
            
            # 按名称或代码搜索
            keyword_upper = keyword.upper()
            matches = []
            
            for _, row in df.iterrows():
                code = row.get('代码', '')
                name = row.get('名称', '')
                
                if (keyword_upper in code.upper() or 
                    keyword in name or 
                    keyword_upper in name.upper()):
                    
                    # 添加市场后缀
                    if code.startswith(('6', '5')):
                        symbol = f"{code}.SH"
                        exchange = 'SSE'
                    elif code.startswith(('0', '3')):
                        symbol = f"{code}.SZ"
                        exchange = 'SZSE'
                    else:
                        symbol = code
                        exchange = 'UNKNOWN'
                    
                    matches.append({
                        'symbol': symbol,
                        'name': name,
                        'exchange': exchange,
                        'industry': row.get('行业', ''),
                        'market': 'A股'
                    })
                    
                    if len(matches) >= limit:
                        break
            
            return matches
            
        except Exception as e:
            logger.error(f"Stock search failed for keyword '{keyword}': {e}")
            return []