"""
Binance data source adapter for cryptocurrency market data.
"""

import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import time
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceRequestException

from .base import DataSourceAdapter, DataSourceConfig
from ...models.market_data import UnifiedMarketData, MarketInfo
from ...exceptions import DataException


logger = logging.getLogger(__name__)


class BinanceAdapter(DataSourceAdapter):
    """Binance adapter for cryptocurrency market data."""
    
    # Popular trading pairs
    POPULAR_PAIRS = [
        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT', 'SOLUSDT',
        'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'SHIBUSDT', 'MATICUSDT', 'LTCUSDT',
        'UNIUSDT', 'LINKUSDT', 'ATOMUSDT', 'ETCUSDT', 'XLMUSDT', 'BCHUSDT',
        'FILUSDT', 'TRXUSDT', 'VETUSDT', 'FTMUSDT', 'MANAUSDT', 'SANDUSDT',
        'AXSUSDT', 'THETAUSDT', 'ALGOUSDT', 'KSMUSDT', 'WAVESUSDT', 'MKRUSDT'
    ]
    
    # Binance kline intervals mapping
    INTERVAL_MAP = {
        '1m': Client.KLINE_INTERVAL_1MINUTE,
        '3m': Client.KLINE_INTERVAL_3MINUTE,
        '5m': Client.KLINE_INTERVAL_5MINUTE,
        '15m': Client.KLINE_INTERVAL_15MINUTE,
        '30m': Client.KLINE_INTERVAL_30MINUTE,
        '1h': Client.KLINE_INTERVAL_1HOUR,
        '2h': Client.KLINE_INTERVAL_2HOUR,
        '4h': Client.KLINE_INTERVAL_4HOUR,
        '6h': Client.KLINE_INTERVAL_6HOUR,
        '8h': Client.KLINE_INTERVAL_8HOUR,
        '12h': Client.KLINE_INTERVAL_12HOUR,
        '1d': Client.KLINE_INTERVAL_1DAY,
        '3d': Client.KLINE_INTERVAL_3DAY,
        '1w': Client.KLINE_INTERVAL_1WEEK,
        '1M': Client.KLINE_INTERVAL_1MONTH
    }
    
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        
        # Initialize Binance client
        api_key = config.credentials.get('api_key', '')
        api_secret = config.credentials.get('api_secret', '')
        
        try:
            # Create client (can work without API keys for public data)
            self.client = Client(api_key, api_secret)
            
            # Test connection
            self.client.ping()
            logger.info("Binance client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Binance client: {e}")
            raise DataException(f"Binance initialization failed: {str(e)}")
        
        # Cache for symbol validation and market info
        self._symbol_cache = {}
        self._market_info_cache = {}
        self._exchange_info_cache = None
        self._cache_expiry = {}
        
        # Request retry configuration
        self.max_retries = 3
        self.retry_delay = 1.0
    
    def fetch_historical_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime, 
        interval: str = '1d'
    ) -> List[UnifiedMarketData]:
        """Fetch historical kline data from Binance."""
        try:
            if not self.validate_symbol(symbol):
                raise DataException(f"Invalid symbol: {symbol}")
            
            # Map interval to Binance format
            binance_interval = self.INTERVAL_MAP.get(interval, Client.KLINE_INTERVAL_1DAY)
            
            # Convert dates to milliseconds
            start_str = str(int(start_date.timestamp() * 1000))
            end_str = str(int(end_date.timestamp() * 1000))
            
            # Fetch klines with rate limiting and retry
            def fetch_klines():
                return self.client.get_historical_klines(
                    symbol=symbol,
                    interval=binance_interval,
                    start_str=start_str,
                    end_str=end_str,
                    limit=1000  # Binance limit
                )
            
            klines = self._make_request_with_retry(fetch_klines)
            
            if not klines:
                logger.warning(f"No kline data returned for {symbol} from {start_date} to {end_date}")
                return []
            
            # Convert to UnifiedMarketData
            market_data_list = []
            for kline in klines:
                try:
                    # Binance kline format:
                    # [timestamp, open, high, low, close, volume, close_time, quote_asset_volume, 
                    #  number_of_trades, taker_buy_base_asset_volume, taker_buy_quote_asset_volume, ignore]
                    
                    timestamp = datetime.fromtimestamp(int(kline[0]) / 1000)
                    
                    market_data = UnifiedMarketData(
                        symbol=symbol,
                        timestamp=timestamp,
                        open=float(kline[1]),
                        high=float(kline[2]),
                        low=float(kline[3]),
                        close=float(kline[4]),
                        volume=float(kline[5]),
                        market=self.get_market_type(),
                        exchange=self.get_exchange_name(),
                        currency=self._get_quote_currency(symbol),
                        adj_close=float(kline[4]),  # Crypto doesn't have adjusted close
                        turnover=float(kline[7])  # Quote asset volume (turnover)
                    )
                    
                    if market_data.validate():
                        market_data_list.append(market_data)
                    else:
                        logger.warning(f"Invalid market data for {symbol} at {timestamp}")
                        
                except Exception as e:
                    logger.warning(f"Failed to process kline for {symbol}: {e}")
                    continue
            
            logger.info(f"Fetched {len(market_data_list)} kline records for {symbol}")
            return market_data_list
            
        except Exception as e:
            logger.error(f"Failed to fetch data for {symbol}: {e}")
            raise DataException(f"Binance fetch failed: {str(e)}")
    
    def _make_request_with_retry(self, request_func, *args, **kwargs):
        """Make request with retry logic and rate limiting."""
        for attempt in range(self.max_retries):
            try:
                # Apply rate limiting
                self.wait_for_rate_limit()
                
                # Make request
                result = request_func(*args, **kwargs)
                self.rate_limiter.record_request()
                
                return result
                
            except BinanceAPIException as e:
                if e.code == -1121:  # Invalid symbol
                    raise DataException(f"Invalid symbol: {e}")
                elif e.code == -1003:  # Too many requests
                    wait_time = (2 ** attempt) * self.retry_delay
                    logger.warning(f"Rate limit hit, waiting {wait_time}s before retry {attempt + 1}")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.error(f"Binance API error: {e}")
                    raise DataException(f"Binance API error: {e}")
                    
            except BinanceRequestException as e:
                logger.error(f"Binance request error: {e}")
                if attempt < self.max_retries - 1:
                    wait_time = (2 ** attempt) * self.retry_delay
                    logger.info(f"Retrying in {wait_time}s (attempt {attempt + 1})")
                    time.sleep(wait_time)
                    continue
                else:
                    raise DataException(f"Binance request failed after {self.max_retries} attempts: {e}")
                    
            except Exception as e:
                logger.error(f"Unexpected error in Binance request: {e}")
                if attempt < self.max_retries - 1:
                    wait_time = (2 ** attempt) * self.retry_delay
                    logger.info(f"Retrying in {wait_time}s (attempt {attempt + 1})")
                    time.sleep(wait_time)
                    continue
                else:
                    raise DataException(f"Binance request failed after {self.max_retries} attempts: {e}")
        
        raise DataException(f"Request failed after {self.max_retries} attempts")
    
    def _get_quote_currency(self, symbol: str) -> str:
        """Extract quote currency from trading pair symbol."""
        # Common quote currencies in order of preference
        quote_currencies = ['USDT', 'BUSD', 'USDC', 'BTC', 'ETH', 'BNB', 'FDUSD']
        
        for quote in quote_currencies:
            if symbol.endswith(quote):
                return quote
        
        # Fallback: assume last 3-4 characters are quote currency
        if len(symbol) >= 6:
            return symbol[-4:] if symbol[-4:] in ['USDT', 'BUSD', 'USDC'] else symbol[-3:]
        
        return 'USDT'  # Default fallback
    
    def get_available_symbols(self) -> List[str]:
        """Get list of available trading pairs."""
        try:
            exchange_info = self._get_exchange_info()
            
            if not exchange_info or 'symbols' not in exchange_info:
                logger.warning("No exchange info available, returning popular pairs")
                return self.POPULAR_PAIRS.copy()
            
            # Extract active trading pairs
            active_symbols = []
            for symbol_info in exchange_info['symbols']:
                if symbol_info.get('status') == 'TRADING':
                    active_symbols.append(symbol_info['symbol'])
            
            logger.info(f"Retrieved {len(active_symbols)} active trading pairs from Binance")
            return active_symbols
            
        except Exception as e:
            logger.error(f"Failed to get available symbols: {e}")
            return self.POPULAR_PAIRS.copy()
    
    def _get_exchange_info(self) -> Optional[Dict[str, Any]]:
        """Get exchange information with caching."""
        # Check cache
        if self._exchange_info_cache:
            cache_time = self._cache_expiry.get('exchange_info', datetime.min)
            if datetime.now() - cache_time < timedelta(hours=1):  # Cache for 1 hour
                return self._exchange_info_cache
        
        try:
            def get_info():
                return self.client.get_exchange_info()
            
            exchange_info = self._make_request_with_retry(get_info)
            
            # Cache result
            self._exchange_info_cache = exchange_info
            self._cache_expiry['exchange_info'] = datetime.now()
            
            return exchange_info
            
        except Exception as e:
            logger.error(f"Failed to get exchange info: {e}")
            return None
    
    def validate_symbol(self, symbol: str) -> bool:
        """Validate if symbol is a valid trading pair."""
        # Check cache first
        cache_key = f"validate_{symbol}"
        if cache_key in self._symbol_cache:
            cache_time = self._cache_expiry.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=1):  # Cache for 1 hour
                return self._symbol_cache[cache_key]
        
        try:
            exchange_info = self._get_exchange_info()
            
            if not exchange_info or 'symbols' not in exchange_info:
                # Fallback: check if symbol is in popular pairs
                is_valid = symbol in self.POPULAR_PAIRS
            else:
                # Check if symbol exists and is trading
                is_valid = False
                for symbol_info in exchange_info['symbols']:
                    if (symbol_info['symbol'] == symbol and 
                        symbol_info.get('status') == 'TRADING'):
                        is_valid = True
                        break
            
            # Cache result
            self._symbol_cache[cache_key] = is_valid
            self._cache_expiry[cache_key] = datetime.now()
            
            return is_valid
            
        except Exception as e:
            logger.warning(f"Symbol validation failed for {symbol}: {e}")
            # Cache negative result for shorter time
            self._symbol_cache[cache_key] = False
            self._cache_expiry[cache_key] = datetime.now()
            return False
    
    def get_market_info(self, symbol: str) -> Optional[MarketInfo]:
        """Get market information for a trading pair."""
        if not self.validate_symbol(symbol):
            return None
        
        # Check cache first
        cache_key = f"market_info_{symbol}"
        if cache_key in self._market_info_cache:
            cache_time = self._cache_expiry.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=4):  # Cache for 4 hours
                return self._market_info_cache[cache_key]
        
        try:
            exchange_info = self._get_exchange_info()
            
            if not exchange_info or 'symbols' not in exchange_info:
                return None
            
            # Find symbol info
            symbol_info = None
            for info in exchange_info['symbols']:
                if info['symbol'] == symbol:
                    symbol_info = info
                    break
            
            if not symbol_info:
                return None
            
            # Extract trading rules
            lot_size = 1.0
            tick_size = 0.00000001  # Default minimum
            
            for filter_info in symbol_info.get('filters', []):
                if filter_info['filterType'] == 'LOT_SIZE':
                    lot_size = float(filter_info['stepSize'])
                elif filter_info['filterType'] == 'PRICE_FILTER':
                    tick_size = float(filter_info['tickSize'])
            
            market_info = MarketInfo(
                symbol=symbol,
                name=f"{symbol_info['baseAsset']}/{symbol_info['quoteAsset']}",
                market=self.get_market_type(),
                exchange=self.get_exchange_name(),
                currency=symbol_info['quoteAsset'],
                lot_size=lot_size,
                tick_size=tick_size,
                trading_hours={'open': '00:00', 'close': '23:59'},  # 24/7 trading
                timezone='UTC',
                is_active=symbol_info.get('status') == 'TRADING'
            )
            
            if market_info.validate():
                # Cache result
                self._market_info_cache[cache_key] = market_info
                self._cache_expiry[cache_key] = datetime.now()
                return market_info
            else:
                logger.warning(f"Invalid market info generated for {symbol}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to get market info for {symbol}: {e}")
            return None
    
    def get_24hr_ticker(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Get 24hr ticker statistics."""
        try:
            def get_ticker():
                if symbol:
                    return self.client.get_ticker(symbol=symbol)
                else:
                    return self.client.get_ticker()
            
            ticker_data = self._make_request_with_retry(get_ticker)
            
            if symbol:
                # Single symbol response
                return {
                    'symbol': ticker_data['symbol'],
                    'price_change': float(ticker_data['priceChange']),
                    'price_change_percent': float(ticker_data['priceChangePercent']),
                    'last_price': float(ticker_data['lastPrice']),
                    'volume': float(ticker_data['volume']),
                    'quote_volume': float(ticker_data['quoteVolume']),
                    'high_price': float(ticker_data['highPrice']),
                    'low_price': float(ticker_data['lowPrice']),
                    'open_price': float(ticker_data['openPrice']),
                    'timestamp': datetime.now()
                }
            else:
                # All symbols response
                result = {}
                for ticker in ticker_data:
                    result[ticker['symbol']] = {
                        'price_change': float(ticker['priceChange']),
                        'price_change_percent': float(ticker['priceChangePercent']),
                        'last_price': float(ticker['lastPrice']),
                        'volume': float(ticker['volume']),
                        'quote_volume': float(ticker['quoteVolume']),
                        'high_price': float(ticker['highPrice']),
                        'low_price': float(ticker['lowPrice']),
                        'open_price': float(ticker['openPrice'])
                    }
                result['timestamp'] = datetime.now()
                return result
                
        except Exception as e:
            logger.error(f"Failed to get 24hr ticker for {symbol}: {e}")
            return {}
    
    def get_trading_pairs(self) -> List[str]:
        """Get all available trading pairs."""
        return self.get_available_symbols()
    
    def get_kline_intervals(self) -> List[str]:
        """Get supported kline intervals."""
        return list(self.INTERVAL_MAP.keys())
    
    def get_server_time(self) -> datetime:
        """Get Binance server time."""
        try:
            def get_time():
                return self.client.get_server_time()
            
            server_time = self._make_request_with_retry(get_time)
            return datetime.fromtimestamp(server_time['serverTime'] / 1000)
            
        except Exception as e:
            logger.error(f"Failed to get server time: {e}")
            return datetime.now()
    
    def get_market_type(self) -> str:
        """Get market type."""
        return 'CRYPTO'
    
    def get_exchange_name(self) -> str:
        """Get exchange name."""
        return 'BINANCE'
    
    def get_default_currency(self) -> str:
        """Get default currency."""
        return 'USDT'
    
    def get_supported_intervals(self) -> List[str]:
        """Get list of supported time intervals."""
        return list(self.INTERVAL_MAP.keys())
    
    def health_check(self) -> tuple[bool, Optional[str]]:
        """Perform health check by testing API connectivity."""
        try:
            # Test basic connectivity
            def ping():
                return self.client.ping()
            
            self._make_request_with_retry(ping)
            
            # Test data fetch with a popular pair
            test_symbol = 'BTCUSDT'
            end_date = datetime.now()
            start_date = end_date - timedelta(days=1)
            
            data = self.fetch_historical_data(test_symbol, start_date, end_date, '1h')
            
            if data:
                self.is_healthy = True
                self.last_error = None
                return True, f"Health check passed with {len(data)} records for {test_symbol}"
            else:
                self.is_healthy = False
                self.last_error = "No data returned for test symbol"
                return False, self.last_error
                
        except Exception as e:
            self.is_healthy = False
            self.last_error = f"Health check failed: {str(e)}"
            logger.error(f"Binance health check failed: {e}")
            return False, self.last_error
    
    def search_symbols(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search for trading pairs matching query."""
        try:
            query_upper = query.upper()
            matches = []
            
            available_symbols = self.get_available_symbols()
            
            for symbol in available_symbols:
                if query_upper in symbol:
                    market_info = self.get_market_info(symbol)
                    if market_info:
                        matches.append({
                            'symbol': symbol,
                            'name': market_info.name,
                            'exchange': market_info.exchange,
                            'currency': market_info.currency,
                            'base_asset': symbol.replace(self._get_quote_currency(symbol), ''),
                            'quote_asset': self._get_quote_currency(symbol)
                        })
                    
                    if len(matches) >= limit:
                        break
            
            return matches
            
        except Exception as e:
            logger.error(f"Symbol search failed for query '{query}': {e}")
            return []
    
    def get_order_book(self, symbol: str, limit: int = 100) -> Optional[Dict[str, Any]]:
        """Get order book for a symbol."""
        try:
            if not self.validate_symbol(symbol):
                return None
            
            def get_depth():
                return self.client.get_order_book(symbol=symbol, limit=limit)
            
            order_book = self._make_request_with_retry(get_depth)
            
            return {
                'symbol': symbol,
                'bids': [[float(price), float(qty)] for price, qty in order_book['bids']],
                'asks': [[float(price), float(qty)] for price, qty in order_book['asks']],
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Failed to get order book for {symbol}: {e}")
            return None
    
    def get_recent_trades(self, symbol: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent trades for a symbol."""
        try:
            if not self.validate_symbol(symbol):
                return []
            
            def get_trades():
                return self.client.get_recent_trades(symbol=symbol, limit=limit)
            
            trades = self._make_request_with_retry(get_trades)
            
            result = []
            for trade in trades:
                result.append({
                    'id': trade['id'],
                    'price': float(trade['price']),
                    'qty': float(trade['qty']),
                    'time': datetime.fromtimestamp(trade['time'] / 1000),
                    'is_buyer_maker': trade['isBuyerMaker']
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get recent trades for {symbol}: {e}")
            return []