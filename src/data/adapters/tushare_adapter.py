"""
Tushare data source adapter for Chinese A-share market data.
"""

import logging
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import time

from .base import DataSourceAdapter, DataSourceConfig
from ...models.market_data import UnifiedMarketData, MarketInfo
from ...exceptions import DataException


logger = logging.getLogger(__name__)


class TushareAdapter(DataSourceAdapter):
    """Tushare adapter for Chinese A-share market data."""
    
    # A股主要指数代码
    MAJOR_INDICES = {
        '000001.SH': '上证指数',
        '399001.SZ': '深证成指',
        '399006.SZ': '创业板指',
        '000300.SH': '沪深300',
        '000905.SH': '中证500',
        '000852.SH': '中证1000',
        '399905.SZ': '中证500',
        '000016.SH': '上证50',
        '000688.SH': '科创50'
    }
    
    # A股交易日历缓存
    _trading_calendar_cache = {}
    _calendar_cache_expiry = None
    
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        
        # 初始化Tushare API
        token = config.credentials.get('token')
        if not token:
            raise DataException("Tushare token is required in credentials")
        
        try:
            ts.set_token(token)
            self.pro = ts.pro_api()
            logger.info("Tushare API initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Tushare API: {e}")
            raise DataException(f"Tushare initialization failed: {str(e)}")
        
        # 缓存股票列表和基本信息
        self._stock_list_cache = {}
        self._stock_info_cache = {}
        self._cache_expiry = {}
        
        # 支持的时间间隔映射
        self.interval_map = {
            '1d': 'D',
            '1w': 'W',
            '1M': 'M'
        }
    
    def fetch_historical_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime, 
        interval: str = '1d'
    ) -> List[UnifiedMarketData]:
        """从Tushare获取历史数据."""
        try:
            if not self.validate_symbol(symbol):
                raise DataException(f"Invalid symbol: {symbol}")
            
            # 转换日期格式
            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')
            
            # 转换时间间隔
            freq = self.interval_map.get(interval, 'D')
            
            # 判断是否为指数
            if self._is_index_symbol(symbol):
                return self._fetch_index_data(symbol, start_str, end_str, freq)
            else:
                return self._fetch_stock_data(symbol, start_str, end_str, freq)
                
        except Exception as e:
            logger.error(f"Failed to fetch data for {symbol}: {e}")
            raise DataException(f"Tushare fetch failed: {str(e)}")
    
    def _is_index_symbol(self, symbol: str) -> bool:
        """判断是否为指数代码."""
        return symbol in self.MAJOR_INDICES
    
    def _fetch_stock_data(self, symbol: str, start_date: str, end_date: str, freq: str) -> List[UnifiedMarketData]:
        """获取股票数据."""
        def fetch_data():
            return self.pro.daily(
                ts_code=symbol,
                start_date=start_date,
                end_date=end_date
            )
        
        df = self.make_request(fetch_data)
        
        if df.empty:
            logger.warning(f"No stock data returned for {symbol} from {start_date} to {end_date}")
            return []
        
        return self._convert_stock_data_to_unified(df, symbol)
    
    def _fetch_index_data(self, symbol: str, start_date: str, end_date: str, freq: str) -> List[UnifiedMarketData]:
        """获取指数数据."""
        def fetch_data():
            return self.pro.index_daily(
                ts_code=symbol,
                start_date=start_date,
                end_date=end_date
            )
        
        df = self.make_request(fetch_data)
        
        if df.empty:
            logger.warning(f"No index data returned for {symbol} from {start_date} to {end_date}")
            return []
        
        return self._convert_index_data_to_unified(df, symbol)
    
    def _convert_stock_data_to_unified(self, df: pd.DataFrame, symbol: str) -> List[UnifiedMarketData]:
        """转换股票数据为统一格式."""
        market_data_list = []
        
        for _, row in df.iterrows():
            try:
                # 跳过无效数据
                if pd.isna(row['close']) or pd.isna(row['vol']):
                    continue
                
                # 转换日期
                trade_date = pd.to_datetime(row['trade_date'], format='%Y%m%d')
                
                market_data = UnifiedMarketData(
                    symbol=symbol,
                    timestamp=trade_date.to_pydatetime(),
                    open=float(row['open']),
                    high=float(row['high']),
                    low=float(row['low']),
                    close=float(row['close']),
                    volume=float(row['vol']) * 100,  # Tushare的成交量单位是手，需要乘以100
                    market=self.get_market_type(),
                    exchange=self._get_exchange_from_symbol(symbol),
                    currency=self.get_default_currency(),
                    adj_close=float(row['close']),  # 默认使用收盘价作为复权价
                    turnover=float(row['amount']) * 1000 if 'amount' in row and pd.notna(row['amount']) else None  # 成交额，单位千元
                )
                
                if market_data.validate():
                    market_data_list.append(market_data)
                else:
                    logger.warning(f"Invalid market data for {symbol} at {trade_date}")
                    
            except Exception as e:
                logger.warning(f"Failed to process stock data row for {symbol}: {e}")
                continue
        
        # 按时间排序
        market_data_list.sort(key=lambda x: x.timestamp)
        logger.info(f"Converted {len(market_data_list)} stock records for {symbol}")
        return market_data_list
    
    def _convert_index_data_to_unified(self, df: pd.DataFrame, symbol: str) -> List[UnifiedMarketData]:
        """转换指数数据为统一格式."""
        market_data_list = []
        
        for _, row in df.iterrows():
            try:
                # 跳过无效数据
                if pd.isna(row['close']) or pd.isna(row['vol']):
                    continue
                
                # 转换日期
                trade_date = pd.to_datetime(row['trade_date'], format='%Y%m%d')
                
                market_data = UnifiedMarketData(
                    symbol=symbol,
                    timestamp=trade_date.to_pydatetime(),
                    open=float(row['open']),
                    high=float(row['high']),
                    low=float(row['low']),
                    close=float(row['close']),
                    volume=float(row['vol']) * 100 if pd.notna(row['vol']) else 0,  # 指数成交量
                    market=self.get_market_type(),
                    exchange=self._get_exchange_from_symbol(symbol),
                    currency=self.get_default_currency(),
                    adj_close=float(row['close']),
                    turnover=float(row['amount']) * 1000 if 'amount' in row and pd.notna(row['amount']) else None
                )
                
                if market_data.validate():
                    market_data_list.append(market_data)
                else:
                    logger.warning(f"Invalid index data for {symbol} at {trade_date}")
                    
            except Exception as e:
                logger.warning(f"Failed to process index data row for {symbol}: {e}")
                continue
        
        # 按时间排序
        market_data_list.sort(key=lambda x: x.timestamp)
        logger.info(f"Converted {len(market_data_list)} index records for {symbol}")
        return market_data_list
    
    def _get_exchange_from_symbol(self, symbol: str) -> str:
        """从股票代码获取交易所."""
        if symbol.endswith('.SH'):
            return 'SSE'  # 上海证券交易所
        elif symbol.endswith('.SZ'):
            return 'SZSE'  # 深圳证券交易所
        else:
            return 'UNKNOWN'
    
    def get_available_symbols(self) -> List[str]:
        """获取可用的股票代码列表."""
        # 检查缓存
        cache_key = 'stock_list'
        if cache_key in self._stock_list_cache:
            cache_time = self._cache_expiry.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=24):  # 缓存24小时
                return self._stock_list_cache[cache_key]
        
        try:
            # 获取沪深股票列表
            def get_stock_basic():
                return self.pro.stock_basic(
                    exchange='',
                    list_status='L',  # 上市状态
                    fields='ts_code,symbol,name,area,industry,list_date'
                )
            
            df = self.make_request(get_stock_basic)
            
            if df.empty:
                logger.warning("No stock list returned from Tushare")
                return []
            
            # 提取股票代码
            symbols = df['ts_code'].tolist()
            
            # 添加主要指数
            symbols.extend(list(self.MAJOR_INDICES.keys()))
            
            # 缓存结果
            self._stock_list_cache[cache_key] = symbols
            self._cache_expiry[cache_key] = datetime.now()
            
            logger.info(f"Retrieved {len(symbols)} symbols from Tushare")
            return symbols
            
        except Exception as e:
            logger.error(f"Failed to get stock list: {e}")
            return list(self.MAJOR_INDICES.keys())  # 返回指数列表作为备选
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证股票代码是否有效."""
        # 检查缓存
        cache_key = f"validate_{symbol}"
        if cache_key in self._stock_info_cache:
            cache_time = self._cache_expiry.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=4):  # 缓存4小时
                return self._stock_info_cache[cache_key]
        
        try:
            # 检查是否为主要指数
            if symbol in self.MAJOR_INDICES:
                self._stock_info_cache[cache_key] = True
                self._cache_expiry[cache_key] = datetime.now()
                return True
            
            # 检查股票基本信息
            def get_stock_info():
                return self.pro.stock_basic(ts_code=symbol, fields='ts_code,name,list_status')
            
            df = self.make_request(get_stock_info)
            
            is_valid = not df.empty and df.iloc[0]['list_status'] == 'L'
            
            # 缓存结果
            self._stock_info_cache[cache_key] = is_valid
            self._cache_expiry[cache_key] = datetime.now()
            
            return is_valid
            
        except Exception as e:
            logger.warning(f"Symbol validation failed for {symbol}: {e}")
            # 缓存负面结果
            self._stock_info_cache[cache_key] = False
            self._cache_expiry[cache_key] = datetime.now()
            return False
    
    def get_market_info(self, symbol: str) -> Optional[MarketInfo]:
        """获取股票市场信息."""
        if not self.validate_symbol(symbol):
            return None
        
        # 检查缓存
        cache_key = f"market_info_{symbol}"
        if cache_key in self._stock_info_cache:
            cache_time = self._cache_expiry.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=4):  # 缓存4小时
                return self._stock_info_cache[cache_key]
        
        try:
            # 检查是否为指数
            if symbol in self.MAJOR_INDICES:
                market_info = MarketInfo(
                    symbol=symbol,
                    name=self.MAJOR_INDICES[symbol],
                    market=self.get_market_type(),
                    exchange=self._get_exchange_from_symbol(symbol),
                    currency=self.get_default_currency(),
                    lot_size=1.0,  # 指数没有最小交易单位
                    tick_size=0.01,  # 指数最小变动单位
                    trading_hours=self.get_market_hours(),
                    timezone='Asia/Shanghai',
                    is_active=True
                )
            else:
                # 获取股票基本信息
                def get_stock_info():
                    return self.pro.stock_basic(
                        ts_code=symbol,
                        fields='ts_code,symbol,name,area,industry,market,list_date'
                    )
                
                df = self.make_request(get_stock_info)
                
                if df.empty:
                    return None
                
                stock_info = df.iloc[0]
                
                market_info = MarketInfo(
                    symbol=symbol,
                    name=stock_info['name'],
                    market=self.get_market_type(),
                    exchange=self._get_exchange_from_symbol(symbol),
                    currency=self.get_default_currency(),
                    lot_size=100.0,  # A股最小交易单位为100股（1手）
                    tick_size=0.01,  # A股最小价格变动单位为0.01元
                    trading_hours=self.get_market_hours(),
                    timezone='Asia/Shanghai',
                    is_active=stock_info.get('list_status', 'L') == 'L'
                )
            
            if market_info.validate():
                # 缓存结果
                self._stock_info_cache[cache_key] = market_info
                self._cache_expiry[cache_key] = datetime.now()
                return market_info
            else:
                logger.warning(f"Invalid market info generated for {symbol}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to get market info for {symbol}: {e}")
            return None
    
    def get_market_hours(self) -> Dict[str, str]:
        """获取A股市场交易时间."""
        return {
            'morning_open': '09:30',
            'morning_close': '11:30',
            'afternoon_open': '13:00',
            'afternoon_close': '15:00',
            'timezone': 'Asia/Shanghai'
        }
    
    def get_trading_calendar(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取A股交易日历."""
        # 检查缓存
        cache_key = f"calendar_{start_date.year}"
        if cache_key in self._trading_calendar_cache:
            if self._calendar_cache_expiry and datetime.now() - self._calendar_cache_expiry < timedelta(days=1):
                calendar_data = self._trading_calendar_cache[cache_key]
                # 过滤日期范围
                return [date for date in calendar_data if start_date <= date <= end_date]
        
        try:
            # 获取交易日历
            def get_calendar():
                return self.pro.trade_cal(
                    exchange='SSE',
                    start_date=start_date.strftime('%Y%m%d'),
                    end_date=end_date.strftime('%Y%m%d')
                )
            
            df = self.make_request(get_calendar)
            
            if df.empty:
                logger.warning("No trading calendar data returned")
                return []
            
            # 筛选交易日
            trading_days = df[df['is_open'] == 1]['cal_date'].tolist()
            trading_dates = [pd.to_datetime(date, format='%Y%m%d').to_pydatetime() for date in trading_days]
            
            # 缓存结果
            self._trading_calendar_cache[cache_key] = trading_dates
            self._calendar_cache_expiry = datetime.now()
            
            return trading_dates
            
        except Exception as e:
            logger.error(f"Failed to get trading calendar: {e}")
            return []
    
    def is_trading_day(self, check_date: datetime) -> bool:
        """检查是否为交易日."""
        try:
            # 获取当天的交易日历
            calendar = self.get_trading_calendar(check_date, check_date)
            return len(calendar) > 0
        except Exception as e:
            logger.warning(f"Failed to check trading day for {check_date}: {e}")
            # 简单判断：非周末
            return check_date.weekday() < 5
    
    def fetch_financial_data(self, symbol: str, period: str = 'annual') -> Optional[pd.DataFrame]:
        """获取财务数据."""
        try:
            if not self.validate_symbol(symbol) or symbol in self.MAJOR_INDICES:
                return None
            
            # 获取财务数据
            def get_financial_data():
                if period == 'annual':
                    return self.pro.fina_indicator(ts_code=symbol, period='20231231')
                else:
                    return self.pro.fina_indicator(ts_code=symbol)
            
            df = self.make_request(get_financial_data)
            
            if df.empty:
                logger.warning(f"No financial data returned for {symbol}")
                return None
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to fetch financial data for {symbol}: {e}")
            return None
    
    def get_stock_list_by_exchange(self, exchange: str) -> List[str]:
        """按交易所获取股票列表."""
        try:
            # 映射交易所代码
            exchange_map = {
                'SSE': 'SSE',  # 上海证券交易所
                'SZSE': 'SZSE'  # 深圳证券交易所
            }
            
            tushare_exchange = exchange_map.get(exchange, exchange)
            
            def get_stocks():
                return self.pro.stock_basic(
                    exchange=tushare_exchange,
                    list_status='L',
                    fields='ts_code,symbol,name'
                )
            
            df = self.make_request(get_stocks)
            
            if df.empty:
                return []
            
            return df['ts_code'].tolist()
            
        except Exception as e:
            logger.error(f"Failed to get stock list for exchange {exchange}: {e}")
            return []
    
    def get_index_components(self, index_code: str) -> List[str]:
        """获取指数成分股."""
        try:
            if index_code not in self.MAJOR_INDICES:
                return []
            
            def get_components():
                return self.pro.index_weight(index_code=index_code)
            
            df = self.make_request(get_components)
            
            if df.empty:
                return []
            
            return df['con_code'].tolist()
            
        except Exception as e:
            logger.error(f"Failed to get index components for {index_code}: {e}")
            return []
    
    def get_market_type(self) -> str:
        """获取市场类型."""
        return 'CN'
    
    def get_exchange_name(self) -> str:
        """获取默认交易所名称."""
        return 'SSE'  # 默认上海证券交易所
    
    def get_default_currency(self) -> str:
        """获取默认货币."""
        return 'CNY'
    
    def get_supported_intervals(self) -> List[str]:
        """获取支持的时间间隔列表."""
        return ['1d', '1w', '1M']
    
    def health_check(self) -> tuple[bool, Optional[str]]:
        """执行健康检查."""
        try:
            # 使用上证指数作为测试标的
            test_symbol = '000001.SH'
            end_date = datetime.now()
            start_date = end_date - timedelta(days=5)
            
            data = self.fetch_historical_data(test_symbol, start_date, end_date)
            
            if data:
                self.is_healthy = True
                self.last_error = None
                return True, f"Health check passed with {len(data)} records for {test_symbol}"
            else:
                self.is_healthy = False
                self.last_error = "No data returned for test symbol"
                return False, self.last_error
                
        except Exception as e:
            self.is_healthy = False
            self.last_error = f"Health check failed: {str(e)}"
            logger.error(f"Tushare health check failed: {e}")
            return False, self.last_error
    
    def get_real_time_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取实时行情（Tushare需要高级权限）."""
        try:
            if not self.validate_symbol(symbol):
                return None
            
            # 注意：实时行情需要Tushare高级权限
            # 这里提供基础实现，实际使用需要相应权限
            logger.warning("Real-time quote requires Tushare premium subscription")
            return None
            
        except Exception as e:
            logger.error(f"Failed to get real-time quote for {symbol}: {e}")
            return None
    
    def search_stocks(self, keyword: str, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索股票."""
        try:
            # 获取股票基本信息
            def search_basic():
                return self.pro.stock_basic(
                    fields='ts_code,symbol,name,area,industry,market'
                )
            
            df = self.make_request(search_basic)
            
            if df.empty:
                return []
            
            # 按名称或代码搜索
            keyword_upper = keyword.upper()
            matches = []
            
            for _, row in df.iterrows():
                if (keyword_upper in row['ts_code'].upper() or 
                    keyword in row['name'] or 
                    keyword_upper in row['symbol'].upper()):
                    
                    matches.append({
                        'symbol': row['ts_code'],
                        'name': row['name'],
                        'exchange': self._get_exchange_from_symbol(row['ts_code']),
                        'industry': row.get('industry', ''),
                        'area': row.get('area', ''),
                        'market': row.get('market', '')
                    })
                    
                    if len(matches) >= limit:
                        break
            
            return matches
            
        except Exception as e:
            logger.error(f"Stock search failed for keyword '{keyword}': {e}")
            return []