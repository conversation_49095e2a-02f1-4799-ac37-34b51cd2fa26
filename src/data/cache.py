"""
Data caching mechanism for improved performance and memory management.
"""

import logging
import pickle
import hashlib
import time
from typing import Any, Optional, Dict, List, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from threading import Lock, RLock
import pandas as pd
from collections import OrderedDict

from ..models.market_data import UnifiedMarketData
from ..exceptions import DataException


logger = logging.getLogger(__name__)


class CacheEntry:
    """Cache entry with metadata."""
    
    def __init__(self, data: Any, ttl_seconds: int = 3600):
        self.data = data
        self.created_at = time.time()
        self.ttl_seconds = ttl_seconds
        self.access_count = 0
        self.last_accessed = self.created_at
    
    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        return time.time() - self.created_at > self.ttl_seconds
    
    def access(self) -> Any:
        """Access cached data and update statistics."""
        self.access_count += 1
        self.last_accessed = time.time()
        return self.data
    
    def get_age_seconds(self) -> float:
        """Get age of cache entry in seconds."""
        return time.time() - self.created_at
    
    def get_size_bytes(self) -> int:
        """Estimate size of cached data in bytes."""
        try:
            return len(pickle.dumps(self.data))
        except Exception:
            return 0


class MemoryCache:
    """In-memory LRU cache with TTL support."""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.lock = RLock()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expired_removals': 0
        }
    
    def _generate_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments."""
        key_data = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """Get data from cache."""
        with self.lock:
            if key not in self.cache:
                self.stats['misses'] += 1
                return None
            
            entry = self.cache[key]
            
            # Check if expired
            if entry.is_expired():
                del self.cache[key]
                self.stats['expired_removals'] += 1
                self.stats['misses'] += 1
                return None
            
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            self.stats['hits'] += 1
            
            return entry.access()
    
    def put(self, key: str, data: Any, ttl_seconds: Optional[int] = None) -> None:
        """Put data into cache."""
        with self.lock:
            ttl = ttl_seconds or self.default_ttl
            entry = CacheEntry(data, ttl)
            
            # Remove existing entry if present
            if key in self.cache:
                del self.cache[key]
            
            # Add new entry
            self.cache[key] = entry
            
            # Evict oldest entries if cache is full
            while len(self.cache) > self.max_size:
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
                self.stats['evictions'] += 1
    
    def remove(self, key: str) -> bool:
        """Remove entry from cache."""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """Clear all cache entries."""
        with self.lock:
            self.cache.clear()
    
    def cleanup_expired(self) -> int:
        """Remove expired entries and return count removed."""
        with self.lock:
            expired_keys = []
            for key, entry in self.cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
                self.stats['expired_removals'] += 1
            
            return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self.lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0
            
            total_size = sum(entry.get_size_bytes() for entry in self.cache.values())
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hits': self.stats['hits'],
                'misses': self.stats['misses'],
                'hit_rate': hit_rate,
                'evictions': self.stats['evictions'],
                'expired_removals': self.stats['expired_removals'],
                'total_size_bytes': total_size,
                'avg_size_bytes': total_size / len(self.cache) if self.cache else 0
            }


class DiskCache:
    """Disk-based cache for persistent storage."""
    
    def __init__(self, cache_dir: str = "data/cache", max_size_mb: int = 1000):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.lock = Lock()
        self.index_file = self.cache_dir / "cache_index.pkl"
        self.index = self._load_index()
    
    def _load_index(self) -> Dict[str, Dict[str, Any]]:
        """Load cache index from disk."""
        try:
            if self.index_file.exists():
                with open(self.index_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            logger.warning(f"Failed to load cache index: {e}")
        
        return {}
    
    def _save_index(self) -> None:
        """Save cache index to disk."""
        try:
            with open(self.index_file, 'wb') as f:
                pickle.dump(self.index, f)
        except Exception as e:
            logger.error(f"Failed to save cache index: {e}")
    
    def _get_file_path(self, key: str) -> Path:
        """Get file path for cache key."""
        return self.cache_dir / f"{key}.pkl"
    
    def get(self, key: str) -> Optional[Any]:
        """Get data from disk cache."""
        with self.lock:
            if key not in self.index:
                return None
            
            entry_info = self.index[key]
            
            # Check if expired
            if time.time() - entry_info['created_at'] > entry_info['ttl_seconds']:
                self.remove(key)
                return None
            
            try:
                file_path = self._get_file_path(key)
                if not file_path.exists():
                    # File missing, remove from index
                    del self.index[key]
                    self._save_index()
                    return None
                
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
                
                # Update access statistics
                entry_info['access_count'] += 1
                entry_info['last_accessed'] = time.time()
                self._save_index()
                
                return data
                
            except Exception as e:
                logger.error(f"Failed to load cached data for key {key}: {e}")
                self.remove(key)
                return None
    
    def put(self, key: str, data: Any, ttl_seconds: int = 3600) -> bool:
        """Put data into disk cache."""
        with self.lock:
            try:
                file_path = self._get_file_path(key)
                
                # Save data to file
                with open(file_path, 'wb') as f:
                    pickle.dump(data, f)
                
                # Update index
                file_size = file_path.stat().st_size
                self.index[key] = {
                    'created_at': time.time(),
                    'ttl_seconds': ttl_seconds,
                    'access_count': 0,
                    'last_accessed': time.time(),
                    'file_size': file_size
                }
                
                self._save_index()
                
                # Clean up if cache is too large
                self._cleanup_if_needed()
                
                return True
                
            except Exception as e:
                logger.error(f"Failed to cache data for key {key}: {e}")
                return False
    
    def remove(self, key: str) -> bool:
        """Remove entry from disk cache."""
        with self.lock:
            try:
                if key in self.index:
                    file_path = self._get_file_path(key)
                    if file_path.exists():
                        file_path.unlink()
                    
                    del self.index[key]
                    self._save_index()
                    return True
                
                return False
                
            except Exception as e:
                logger.error(f"Failed to remove cached data for key {key}: {e}")
                return False
    
    def _cleanup_if_needed(self) -> None:
        """Clean up cache if it exceeds size limit."""
        total_size = sum(entry['file_size'] for entry in self.index.values())
        
        if total_size <= self.max_size_bytes:
            return
        
        # Sort by last accessed time (oldest first)
        sorted_entries = sorted(
            self.index.items(),
            key=lambda x: x[1]['last_accessed']
        )
        
        # Remove oldest entries until under size limit
        for key, entry_info in sorted_entries:
            if total_size <= self.max_size_bytes:
                break
            
            self.remove(key)
            total_size -= entry_info['file_size']
    
    def cleanup_expired(self) -> int:
        """Remove expired entries."""
        with self.lock:
            expired_keys = []
            current_time = time.time()
            
            for key, entry_info in self.index.items():
                if current_time - entry_info['created_at'] > entry_info['ttl_seconds']:
                    expired_keys.append(key)
            
            for key in expired_keys:
                self.remove(key)
            
            return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self.lock:
            total_size = sum(entry['file_size'] for entry in self.index.values())
            total_accesses = sum(entry['access_count'] for entry in self.index.values())
            
            return {
                'entries': len(self.index),
                'total_size_bytes': total_size,
                'total_size_mb': total_size / (1024 * 1024),
                'max_size_mb': self.max_size_bytes / (1024 * 1024),
                'total_accesses': total_accesses,
                'avg_accesses_per_entry': total_accesses / len(self.index) if self.index else 0
            }


class DataCache:
    """Unified data cache with memory and disk tiers."""
    
    def __init__(
        self,
        memory_cache_size: int = 1000,
        memory_ttl: int = 1800,  # 30 minutes
        disk_cache_size_mb: int = 1000,
        disk_ttl: int = 86400,  # 24 hours
        cache_dir: str = "data/cache"
    ):
        self.memory_cache = MemoryCache(memory_cache_size, memory_ttl)
        self.disk_cache = DiskCache(cache_dir, disk_cache_size_mb)
        self.memory_ttl = memory_ttl
        self.disk_ttl = disk_ttl
    
    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate cache key."""
        key_data = f"{prefix}:{str(args)}:{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get_market_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        market: Optional[str] = None
    ) -> Optional[List[UnifiedMarketData]]:
        """Get cached market data."""
        key = self._generate_key(
            "market_data",
            symbol=symbol,
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            market=market
        )
        
        # Try memory cache first
        data = self.memory_cache.get(key)
        if data is not None:
            logger.debug(f"Cache hit (memory) for market data: {symbol}")
            return data
        
        # Try disk cache
        data = self.disk_cache.get(key)
        if data is not None:
            logger.debug(f"Cache hit (disk) for market data: {symbol}")
            # Promote to memory cache
            self.memory_cache.put(key, data, self.memory_ttl)
            return data
        
        logger.debug(f"Cache miss for market data: {symbol}")
        return None
    
    def put_market_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        data: List[UnifiedMarketData],
        market: Optional[str] = None
    ) -> None:
        """Cache market data."""
        key = self._generate_key(
            "market_data",
            symbol=symbol,
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            market=market
        )
        
        # Store in both memory and disk cache
        self.memory_cache.put(key, data, self.memory_ttl)
        self.disk_cache.put(key, data, self.disk_ttl)
        
        logger.debug(f"Cached market data: {symbol} ({len(data)} records)")
    
    def get_dataframe(
        self,
        cache_key: str
    ) -> Optional[pd.DataFrame]:
        """Get cached DataFrame."""
        # Try memory cache first
        data = self.memory_cache.get(cache_key)
        if data is not None:
            return data
        
        # Try disk cache
        data = self.disk_cache.get(cache_key)
        if data is not None:
            # Promote to memory cache
            self.memory_cache.put(cache_key, data, self.memory_ttl)
            return data
        
        return None
    
    def put_dataframe(
        self,
        cache_key: str,
        df: pd.DataFrame,
        memory_ttl: Optional[int] = None,
        disk_ttl: Optional[int] = None
    ) -> None:
        """Cache DataFrame."""
        mem_ttl = memory_ttl or self.memory_ttl
        disk_ttl_val = disk_ttl or self.disk_ttl
        
        self.memory_cache.put(cache_key, df, mem_ttl)
        self.disk_cache.put(cache_key, df, disk_ttl_val)
    
    def invalidate_symbol(self, symbol: str) -> int:
        """Invalidate all cached data for a symbol."""
        # This is a simplified implementation
        # In practice, you'd need to track keys by symbol
        removed_count = 0
        
        # Clear memory cache entries (simplified)
        self.memory_cache.clear()
        removed_count += 1
        
        logger.info(f"Invalidated cache for symbol: {symbol}")
        return removed_count
    
    def cleanup(self) -> Dict[str, int]:
        """Clean up expired entries from both caches."""
        memory_removed = self.memory_cache.cleanup_expired()
        disk_removed = self.disk_cache.cleanup_expired()
        
        return {
            'memory_removed': memory_removed,
            'disk_removed': disk_removed
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        return {
            'memory_cache': self.memory_cache.get_stats(),
            'disk_cache': self.disk_cache.get_stats()
        }
    
    def clear_all(self) -> None:
        """Clear all cached data."""
        self.memory_cache.clear()
        # Note: Not clearing disk cache as it's persistent
        logger.info("Cleared memory cache")


# Global cache instance
_data_cache: Optional[DataCache] = None


def get_data_cache() -> DataCache:
    """Get global data cache instance."""
    global _data_cache
    
    if _data_cache is None:
        _data_cache = DataCache()
    
    return _data_cache