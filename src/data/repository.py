"""
Repository classes for data access and CRUD operations.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import json
import pandas as pd

from .database import DatabaseManager
from ..models.market_data import UnifiedMarketData, EconomicData, MarketInfo


logger = logging.getLogger(__name__)


class BaseRepository:
    """Base repository class with common functionality."""
    
    def __init__(self, db: DatabaseManager):
        self.db = db
    
    def _dict_to_model(self, data: Dict[str, Any], model_class):
        """Convert dictionary to model instance."""
        # Remove database-specific fields
        model_data = {k: v for k, v in data.items() 
                     if k not in ['id', 'created_at', 'updated_at']}
        
        # Convert datetime strings back to datetime objects
        for key, value in model_data.items():
            if isinstance(value, str) and 'timestamp' in key.lower():
                try:
                    model_data[key] = datetime.fromisoformat(value.replace('Z', '+00:00'))
                except (ValueError, AttributeError):
                    pass
        
        return model_class(**model_data)
    
    def _model_to_dict(self, model) -> Dict[str, Any]:
        """Convert model instance to dictionary for database storage."""
        data = model.to_dict()
        
        # Convert datetime objects to ISO format strings
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
            elif isinstance(value, dict):
                data[key] = json.dumps(value) if self.db.use_sqlite else value
        
        return data


class MarketDataRepository(BaseRepository):
    """Repository for market data operations."""
    
    def save(self, market_data: UnifiedMarketData) -> bool:
        """Save market data to database."""
        try:
            data = self._model_to_dict(market_data)
            
            if self.db.use_sqlite:
                query = """
                    INSERT OR REPLACE INTO market_data 
                    (symbol, timestamp, open, high, low, close, volume, market, exchange, 
                     currency, adj_close, turnover, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """
            else:
                query = """
                    INSERT INTO market_data 
                    (symbol, timestamp, open, high, low, close, volume, market, exchange, 
                     currency, adj_close, turnover, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    ON CONFLICT (symbol, timestamp, market) 
                    DO UPDATE SET 
                        open = EXCLUDED.open,
                        high = EXCLUDED.high,
                        low = EXCLUDED.low,
                        close = EXCLUDED.close,
                        volume = EXCLUDED.volume,
                        exchange = EXCLUDED.exchange,
                        currency = EXCLUDED.currency,
                        adj_close = EXCLUDED.adj_close,
                        turnover = EXCLUDED.turnover,
                        updated_at = NOW()
                """
            
            params = (
                data['symbol'], data['timestamp'], data['open'], data['high'], 
                data['low'], data['close'], data['volume'], data['market'], 
                data['exchange'], data['currency'], data.get('adj_close'), 
                data.get('turnover')
            )
            
            self.db.execute_command(query, params)
            return True
            
        except Exception as e:
            logger.error(f"Failed to save market data: {e}")
            return False
    
    def save_batch(self, market_data_list: List[UnifiedMarketData]) -> int:
        """Save multiple market data records in batch."""
        if not market_data_list:
            return 0
        
        try:
            if self.db.use_sqlite:
                query = """
                    INSERT OR REPLACE INTO market_data 
                    (symbol, timestamp, open, high, low, close, volume, market, exchange, 
                     currency, adj_close, turnover, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """
            else:
                query = """
                    INSERT INTO market_data 
                    (symbol, timestamp, open, high, low, close, volume, market, exchange, 
                     currency, adj_close, turnover, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    ON CONFLICT (symbol, timestamp, market) 
                    DO UPDATE SET 
                        open = EXCLUDED.open,
                        high = EXCLUDED.high,
                        low = EXCLUDED.low,
                        close = EXCLUDED.close,
                        volume = EXCLUDED.volume,
                        exchange = EXCLUDED.exchange,
                        currency = EXCLUDED.currency,
                        adj_close = EXCLUDED.adj_close,
                        turnover = EXCLUDED.turnover,
                        updated_at = NOW()
                """
            
            params_list = []
            for data in market_data_list:
                data_dict = self._model_to_dict(data)
                params = (
                    data_dict['symbol'], data_dict['timestamp'], data_dict['open'], 
                    data_dict['high'], data_dict['low'], data_dict['close'], 
                    data_dict['volume'], data_dict['market'], data_dict['exchange'], 
                    data_dict['currency'], data_dict.get('adj_close'), 
                    data_dict.get('turnover')
                )
                params_list.append(params)
            
            return self.db.execute_many(query, params_list)
            
        except Exception as e:
            logger.error(f"Failed to save market data batch: {e}")
            return 0
    
    def get_by_symbol_and_date_range(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        market: Optional[str] = None
    ) -> List[UnifiedMarketData]:
        """Get market data by symbol and date range."""
        try:
            base_query = """
                SELECT * FROM market_data 
                WHERE symbol = {} AND timestamp >= {} AND timestamp <= {}
            """.format(
                '?' if self.db.use_sqlite else '%s',
                '?' if self.db.use_sqlite else '%s',
                '?' if self.db.use_sqlite else '%s'
            )
            
            params = [symbol, start_date.isoformat(), end_date.isoformat()]
            
            if market:
                base_query += f" AND market = {'?' if self.db.use_sqlite else '%s'}"
                params.append(market)
            
            base_query += " ORDER BY timestamp ASC"
            
            results = self.db.execute_query(base_query, tuple(params))
            return [self._dict_to_model(row, UnifiedMarketData) for row in results]
            
        except Exception as e:
            logger.error(f"Failed to get market data: {e}")
            return []
    
    def get_latest_by_symbol(self, symbol: str, market: Optional[str] = None) -> Optional[UnifiedMarketData]:
        """Get latest market data for a symbol."""
        try:
            base_query = """
                SELECT * FROM market_data 
                WHERE symbol = {}
            """.format('?' if self.db.use_sqlite else '%s')
            
            params = [symbol]
            
            if market:
                base_query += f" AND market = {'?' if self.db.use_sqlite else '%s'}"
                params.append(market)
            
            base_query += " ORDER BY timestamp DESC LIMIT 1"
            
            results = self.db.execute_query(base_query, tuple(params))
            if results:
                return self._dict_to_model(results[0], UnifiedMarketData)
            return None
            
        except Exception as e:
            logger.error(f"Failed to get latest market data: {e}")
            return None
    
    def get_symbols_by_market(self, market: str) -> List[str]:
        """Get all symbols for a specific market."""
        try:
            query = """
                SELECT DISTINCT symbol FROM market_data 
                WHERE market = {}
                ORDER BY symbol
            """.format('?' if self.db.use_sqlite else '%s')
            
            results = self.db.execute_query(query, (market,))
            return [row['symbol'] for row in results]
            
        except Exception as e:
            logger.error(f"Failed to get symbols by market: {e}")
            return []
    
    def get_date_range_by_symbol(self, symbol: str, market: Optional[str] = None) -> Optional[Tuple[datetime, datetime]]:
        """Get date range for a symbol."""
        try:
            base_query = """
                SELECT MIN(timestamp) as min_date, MAX(timestamp) as max_date 
                FROM market_data 
                WHERE symbol = {}
            """.format('?' if self.db.use_sqlite else '%s')
            
            params = [symbol]
            
            if market:
                base_query += f" AND market = {'?' if self.db.use_sqlite else '%s'}"
                params.append(market)
            
            results = self.db.execute_query(base_query, tuple(params))
            if results and results[0]['min_date'] and results[0]['max_date']:
                min_date = datetime.fromisoformat(results[0]['min_date'].replace('Z', '+00:00'))
                max_date = datetime.fromisoformat(results[0]['max_date'].replace('Z', '+00:00'))
                return (min_date, max_date)
            return None
            
        except Exception as e:
            logger.error(f"Failed to get date range: {e}")
            return None
    
    def delete_by_symbol_and_date_range(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        market: Optional[str] = None
    ) -> int:
        """Delete market data by symbol and date range."""
        try:
            base_query = """
                DELETE FROM market_data 
                WHERE symbol = {} AND timestamp >= {} AND timestamp <= {}
            """.format(
                '?' if self.db.use_sqlite else '%s',
                '?' if self.db.use_sqlite else '%s',
                '?' if self.db.use_sqlite else '%s'
            )
            
            params = [symbol, start_date.isoformat(), end_date.isoformat()]
            
            if market:
                base_query += f" AND market = {'?' if self.db.use_sqlite else '%s'}"
                params.append(market)
            
            return self.db.execute_command(base_query, tuple(params))
            
        except Exception as e:
            logger.error(f"Failed to delete market data: {e}")
            return 0
    
    def get_data_coverage(self) -> Dict[str, Any]:
        """Get data coverage statistics."""
        try:
            query = """
                SELECT 
                    market,
                    exchange,
                    COUNT(DISTINCT symbol) as symbol_count,
                    COUNT(*) as record_count,
                    MIN(timestamp) as earliest_date,
                    MAX(timestamp) as latest_date
                FROM market_data 
                GROUP BY market, exchange
                ORDER BY market, exchange
            """
            
            results = self.db.execute_query(query)
            return {
                'coverage': results,
                'total_symbols': sum(row['symbol_count'] for row in results),
                'total_records': sum(row['record_count'] for row in results)
            }
            
        except Exception as e:
            logger.error(f"Failed to get data coverage: {e}")
            return {'coverage': [], 'total_symbols': 0, 'total_records': 0}


class EconomicDataRepository(BaseRepository):
    """Repository for economic data operations."""
    
    def save(self, economic_data: EconomicData) -> bool:
        """Save economic data to database."""
        try:
            data = self._model_to_dict(economic_data)
            
            if self.db.use_sqlite:
                query = """
                    INSERT OR REPLACE INTO economic_data 
                    (series_id, timestamp, value, market, source, unit, frequency, 
                     seasonal_adjustment, last_updated, notes, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """
            else:
                query = """
                    INSERT INTO economic_data 
                    (series_id, timestamp, value, market, source, unit, frequency, 
                     seasonal_adjustment, last_updated, notes, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    ON CONFLICT (series_id, timestamp) 
                    DO UPDATE SET 
                        value = EXCLUDED.value,
                        market = EXCLUDED.market,
                        source = EXCLUDED.source,
                        unit = EXCLUDED.unit,
                        frequency = EXCLUDED.frequency,
                        seasonal_adjustment = EXCLUDED.seasonal_adjustment,
                        last_updated = EXCLUDED.last_updated,
                        notes = EXCLUDED.notes,
                        updated_at = NOW()
                """
            
            params = (
                data['series_id'], data['timestamp'], data['value'], data['market'],
                data['source'], data['unit'], data['frequency'], 
                data['seasonal_adjustment'], data.get('last_updated'), data.get('notes')
            )
            
            self.db.execute_command(query, params)
            return True
            
        except Exception as e:
            logger.error(f"Failed to save economic data: {e}")
            return False
    
    def save_batch(self, economic_data_list: List[EconomicData]) -> int:
        """Save multiple economic data records in batch."""
        if not economic_data_list:
            return 0
        
        try:
            if self.db.use_sqlite:
                query = """
                    INSERT OR REPLACE INTO economic_data 
                    (series_id, timestamp, value, market, source, unit, frequency, 
                     seasonal_adjustment, last_updated, notes, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """
            else:
                query = """
                    INSERT INTO economic_data 
                    (series_id, timestamp, value, market, source, unit, frequency, 
                     seasonal_adjustment, last_updated, notes, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    ON CONFLICT (series_id, timestamp) 
                    DO UPDATE SET 
                        value = EXCLUDED.value,
                        market = EXCLUDED.market,
                        source = EXCLUDED.source,
                        unit = EXCLUDED.unit,
                        frequency = EXCLUDED.frequency,
                        seasonal_adjustment = EXCLUDED.seasonal_adjustment,
                        last_updated = EXCLUDED.last_updated,
                        notes = EXCLUDED.notes,
                        updated_at = NOW()
                """
            
            params_list = []
            for data in economic_data_list:
                data_dict = self._model_to_dict(data)
                params = (
                    data_dict['series_id'], data_dict['timestamp'], data_dict['value'],
                    data_dict['market'], data_dict['source'], data_dict['unit'],
                    data_dict['frequency'], data_dict['seasonal_adjustment'],
                    data_dict.get('last_updated'), data_dict.get('notes')
                )
                params_list.append(params)
            
            return self.db.execute_many(query, params_list)
            
        except Exception as e:
            logger.error(f"Failed to save economic data batch: {e}")
            return 0
    
    def get_by_series_and_date_range(
        self, 
        series_id: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> List[EconomicData]:
        """Get economic data by series ID and date range."""
        try:
            query = """
                SELECT * FROM economic_data 
                WHERE series_id = {} AND timestamp >= {} AND timestamp <= {}
                ORDER BY timestamp ASC
            """.format(
                '?' if self.db.use_sqlite else '%s',
                '?' if self.db.use_sqlite else '%s',
                '?' if self.db.use_sqlite else '%s'
            )
            
            params = (series_id, start_date.isoformat(), end_date.isoformat())
            results = self.db.execute_query(query, params)
            return [self._dict_to_model(row, EconomicData) for row in results]
            
        except Exception as e:
            logger.error(f"Failed to get economic data: {e}")
            return []
    
    def get_latest_by_series(self, series_id: str) -> Optional[EconomicData]:
        """Get latest economic data for a series."""
        try:
            query = """
                SELECT * FROM economic_data 
                WHERE series_id = {}
                ORDER BY timestamp DESC LIMIT 1
            """.format('?' if self.db.use_sqlite else '%s')
            
            results = self.db.execute_query(query, (series_id,))
            if results:
                return self._dict_to_model(results[0], EconomicData)
            return None
            
        except Exception as e:
            logger.error(f"Failed to get latest economic data: {e}")
            return None
    
    def get_all_series(self) -> List[Dict[str, Any]]:
        """Get all available economic data series."""
        try:
            query = """
                SELECT 
                    series_id,
                    source,
                    unit,
                    frequency,
                    seasonal_adjustment,
                    COUNT(*) as record_count,
                    MIN(timestamp) as earliest_date,
                    MAX(timestamp) as latest_date,
                    MAX(last_updated) as last_updated
                FROM economic_data 
                GROUP BY series_id, source, unit, frequency, seasonal_adjustment
                ORDER BY series_id
            """
            
            return self.db.execute_query(query)
            
        except Exception as e:
            logger.error(f"Failed to get economic data series: {e}")
            return []


class MarketInfoRepository(BaseRepository):
    """Repository for market information operations."""
    
    def save(self, market_info: MarketInfo) -> bool:
        """Save market info to database."""
        try:
            data = self._model_to_dict(market_info)
            
            if self.db.use_sqlite:
                query = """
                    INSERT OR REPLACE INTO market_info 
                    (symbol, name, market, exchange, currency, lot_size, tick_size, 
                     trading_hours, timezone, is_active, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """
            else:
                query = """
                    INSERT INTO market_info 
                    (symbol, name, market, exchange, currency, lot_size, tick_size, 
                     trading_hours, timezone, is_active, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    ON CONFLICT (symbol, market) 
                    DO UPDATE SET 
                        name = EXCLUDED.name,
                        exchange = EXCLUDED.exchange,
                        currency = EXCLUDED.currency,
                        lot_size = EXCLUDED.lot_size,
                        tick_size = EXCLUDED.tick_size,
                        trading_hours = EXCLUDED.trading_hours,
                        timezone = EXCLUDED.timezone,
                        is_active = EXCLUDED.is_active,
                        updated_at = NOW()
                """
            
            params = (
                data['symbol'], data['name'], data['market'], data['exchange'],
                data['currency'], data['lot_size'], data['tick_size'],
                data.get('trading_hours'), data['timezone'], data.get('is_active', True)
            )
            
            self.db.execute_command(query, params)
            return True
            
        except Exception as e:
            logger.error(f"Failed to save market info: {e}")
            return False
    
    def get_by_symbol_and_market(self, symbol: str, market: str) -> Optional[MarketInfo]:
        """Get market info by symbol and market."""
        try:
            query = """
                SELECT * FROM market_info 
                WHERE symbol = {} AND market = {}
            """.format(
                '?' if self.db.use_sqlite else '%s',
                '?' if self.db.use_sqlite else '%s'
            )
            
            results = self.db.execute_query(query, (symbol, market))
            if results:
                row = results[0]
                # Parse trading_hours JSON if it's a string
                if isinstance(row.get('trading_hours'), str):
                    try:
                        row['trading_hours'] = json.loads(row['trading_hours'])
                    except (json.JSONDecodeError, TypeError):
                        row['trading_hours'] = {}
                
                return self._dict_to_model(row, MarketInfo)
            return None
            
        except Exception as e:
            logger.error(f"Failed to get market info: {e}")
            return None
    
    def get_by_market(self, market: str, active_only: bool = True) -> List[MarketInfo]:
        """Get all market info for a specific market."""
        try:
            base_query = """
                SELECT * FROM market_info 
                WHERE market = {}
            """.format('?' if self.db.use_sqlite else '%s')
            
            params = [market]
            
            if active_only:
                base_query += f" AND is_active = {'1' if self.db.use_sqlite else 'TRUE'}"
            
            base_query += " ORDER BY symbol"
            
            results = self.db.execute_query(base_query, tuple(params))
            market_infos = []
            
            for row in results:
                # Parse trading_hours JSON if it's a string
                if isinstance(row.get('trading_hours'), str):
                    try:
                        row['trading_hours'] = json.loads(row['trading_hours'])
                    except (json.JSONDecodeError, TypeError):
                        row['trading_hours'] = {}
                
                market_infos.append(self._dict_to_model(row, MarketInfo))
            
            return market_infos
            
        except Exception as e:
            logger.error(f"Failed to get market info by market: {e}")
            return []
    
    def get_all_markets(self) -> List[str]:
        """Get all available markets."""
        try:
            query = "SELECT DISTINCT market FROM market_info ORDER BY market"
            results = self.db.execute_query(query)
            return [row['market'] for row in results]
            
        except Exception as e:
            logger.error(f"Failed to get all markets: {e}")
            return []
    
    def update_active_status(self, symbol: str, market: str, is_active: bool) -> bool:
        """Update active status for a symbol."""
        try:
            query = """
                UPDATE market_info 
                SET is_active = {}, updated_at = {}
                WHERE symbol = {} AND market = {}
            """.format(
                '?' if self.db.use_sqlite else '%s',
                'CURRENT_TIMESTAMP' if self.db.use_sqlite else 'NOW()',
                '?' if self.db.use_sqlite else '%s',
                '?' if self.db.use_sqlite else '%s'
            )
            
            params = (is_active, symbol, market) if self.db.use_sqlite else (is_active, symbol, market)
            affected_rows = self.db.execute_command(query, params)
            return affected_rows > 0
            
        except Exception as e:
            logger.error(f"Failed to update active status: {e}")
            return False