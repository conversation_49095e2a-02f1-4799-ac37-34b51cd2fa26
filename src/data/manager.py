"""
量化交易系统 - 数据管理模块

本模块提供统一的数据管理接口，协调数据操作并提供高级数据访问功能。
主要功能包括：
- 多数据源适配器管理和协调
- 市场数据和经济数据的统一存储和查询
- 数据缓存和性能优化
- 数据导入导出和格式转换
- 数据质量验证和清洗

支持的数据源：
- Yahoo Finance (美股数据)
- Tushare (A股数据)  
- Binance (加密货币数据)
- FRED (美联储经济数据)

数据类型：
- 市场数据: OHLCV价格数据、成交量、复权价格
- 经济数据: GDP、CPI、利率等宏观经济指标
- 市场信息: 交易所信息、交易时间、货币等

作者: 量化交易系统开发团队
版本: 1.2.0
创建日期: 2024-01-01
最后修改: 2024-07-24

示例:
    基础使用:
    >>> manager = DataManager()
    >>> data = manager.get_market_data('AAPL', '2023-01-01', '2023-12-31')
    >>> print(f"获取到 {len(data)} 条数据记录")
    
    多数据源同步:
    >>> manager.sync_data_from_sources(['AAPL', 'GOOGL'], '2023-01-01', '2023-12-31')
    
    经济数据查询:
    >>> gdp_data = manager.get_economic_data('GDP', '2020-01-01', '2023-12-31')
"""

import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd

from .database import DatabaseManager, get_database_manager
from .repository import MarketDataRepository, EconomicDataRepository, MarketInfoRepository
from .migrations import MigrationManager
from .importers import get_import_manager, DataImportManager
from .cache import get_data_cache, DataCache
from .adapters.manager import get_data_source_manager, DataSourceManager
from ..models.market_data import UnifiedMarketData, EconomicData, MarketInfo
from ..exceptions import DataException


logger = logging.getLogger(__name__)


class DataManager:
    """
    数据管理器 - 统一的数据访问和管理接口
    
    DataManager是量化交易系统的核心数据管理组件，提供统一的数据访问接口，
    协调多个数据源的数据获取、存储、缓存和查询操作。
    
    主要特性:
        - 多数据源支持: 集成Yahoo Finance、Tushare、Binance、FRED等数据源
        - 统一数据模型: 将不同数据源的数据标准化为统一格式
        - 智能缓存: 多层缓存机制提升查询性能
        - 数据验证: 自动检测和修复数据质量问题
        - 异步处理: 支持大批量数据的异步获取和处理
        - 数据库管理: 自动化的数据库初始化和迁移
    
    架构组件:
        - DatabaseManager: 数据库连接和事务管理
        - Repository层: 数据访问对象，封装SQL操作
        - DataSourceManager: 数据源适配器管理
        - CacheManager: 多层缓存管理
        - ImportManager: 数据导入导出管理
        - MigrationManager: 数据库迁移管理
    
    属性:
        db (DatabaseManager): 数据库管理器
        market_data_repo (MarketDataRepository): 市场数据仓库
        economic_data_repo (EconomicDataRepository): 经济数据仓库
        market_info_repo (MarketInfoRepository): 市场信息仓库
        migration_manager (MigrationManager): 数据库迁移管理器
        import_manager (DataImportManager): 数据导入管理器
        cache (DataCache): 数据缓存管理器
        data_source_manager (DataSourceManager): 数据源管理器
    
    使用场景:
        - 策略回测: 为回测引擎提供历史数据
        - 实时分析: 获取最新的市场数据和经济指标
        - 数据研究: 进行量化研究和数据分析
        - 系统集成: 为其他系统组件提供数据服务
    
    示例:
        基础初始化:
        >>> manager = DataManager()
        >>> print("数据管理器初始化完成")
        
        使用SQLite数据库:
        >>> manager = DataManager(use_sqlite=True)
        >>> print("使用SQLite数据库")
        
        获取市场数据:
        >>> data = manager.get_market_data(
        ...     symbol='AAPL',
        ...     start_date='2023-01-01',
        ...     end_date='2023-12-31'
        ... )
        >>> print(f"获取到 {len(data)} 条AAPL数据")
        
        批量数据同步:
        >>> symbols = ['AAPL', 'GOOGL', 'MSFT']
        >>> manager.sync_data_from_sources(symbols, '2023-01-01', '2023-12-31')
        >>> print("批量数据同步完成")
    
    注意事项:
        - 首次使用需要配置数据源API密钥
        - 大量数据查询可能需要较长时间
        - 建议在生产环境使用PostgreSQL数据库
        - 数据源API可能有频率限制，注意合理使用
    
    性能优化:
        - 启用缓存可以显著提升重复查询性能
        - 使用批量操作处理大量数据
        - 定期清理过期缓存数据
        - 合理设置数据库连接池大小
    
    版本历史:
        1.0.0: 基础数据管理功能
        1.1.0: 增加多数据源支持和缓存机制
        1.2.0: 增加经济数据支持和性能优化
    """
    
    def __init__(self, use_sqlite: bool = False):
        """
        初始化数据管理器
        
        创建数据管理器实例，初始化所有必要的组件包括数据库连接、
        数据仓库、缓存管理器和数据源管理器等。
        
        Args:
            use_sqlite (bool, optional): 是否使用SQLite数据库，默认False
                - True: 使用SQLite数据库，适合开发和测试环境
                - False: 使用PostgreSQL数据库，适合生产环境
        
        Raises:
            DataException: 当数据库初始化失败时抛出
            ConnectionError: 当数据库连接失败时抛出
            
        示例:
            生产环境初始化:
            >>> manager = DataManager()  # 使用PostgreSQL
            
            开发环境初始化:
            >>> manager = DataManager(use_sqlite=True)  # 使用SQLite
            
        注意:
            - SQLite适合开发测试，但性能有限
            - PostgreSQL推荐用于生产环境
            - 初始化过程会自动执行数据库迁移
        """
        self.db = get_database_manager(use_sqlite=use_sqlite)
        self.market_data_repo = MarketDataRepository(self.db)
        self.economic_data_repo = EconomicDataRepository(self.db)
        self.market_info_repo = MarketInfoRepository(self.db)
        self.migration_manager = MigrationManager(self.db)
        
        # Initialize additional components
        self.import_manager = get_import_manager()
        self.cache = get_data_cache()
        self.data_source_manager = get_data_source_manager()
        
        # Initialize database schema
        self._initialize_database()
    
    def _initialize_database(self) -> None:
        """Initialize database schema if needed."""
        try:
            self.migration_manager.migrate()
            logger.info("Database schema initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize database schema: {e}")
            raise
    
    # Market Data Operations
    def save_market_data(self, market_data: UnifiedMarketData) -> bool:
        """Save single market data record."""
        if not market_data.validate():
            logger.warning(f"Invalid market data for {market_data.symbol}")
            return False
        
        return self.market_data_repo.save(market_data)
    
    def save_market_data_batch(self, market_data_list: List[UnifiedMarketData]) -> int:
        """Save multiple market data records."""
        # Validate all records first
        valid_data = []
        for data in market_data_list:
            if data.validate():
                valid_data.append(data)
            else:
                logger.warning(f"Skipping invalid market data for {data.symbol}")
        
        if not valid_data:
            logger.warning("No valid market data to save")
            return 0
        
        return self.market_data_repo.save_batch(valid_data)
    
    def get_market_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        market: Optional[str] = None,
        use_cache: bool = True
    ) -> pd.DataFrame:
        """Get market data as pandas DataFrame."""
        # Try cache first if enabled
        if use_cache:
            cached_data = self.cache.get_market_data(symbol, start_date, end_date, market)
            if cached_data:
                logger.debug(f"Using cached data for {symbol}")
                return self._market_data_list_to_dataframe(cached_data)
        
        # Get from database
        data_list = self.market_data_repo.get_by_symbol_and_date_range(
            symbol, start_date, end_date, market
        )
        
        if not data_list:
            return pd.DataFrame()
        
        # Cache the data if caching is enabled
        if use_cache:
            self.cache.put_market_data(symbol, start_date, end_date, data_list, market)
        
        return self._market_data_list_to_dataframe(data_list)
    
    def _market_data_list_to_dataframe(self, data_list: List[UnifiedMarketData]) -> pd.DataFrame:
        """Convert market data list to DataFrame."""
        if not data_list:
            return pd.DataFrame()
        
        # Convert to DataFrame
        records = []
        for data in data_list:
            record = {
                'timestamp': data.timestamp,
                'open': data.open,
                'high': data.high,
                'low': data.low,
                'close': data.close,
                'volume': data.volume,
                'adj_close': data.adj_close,
                'turnover': data.turnover
            }
            records.append(record)
        
        df = pd.DataFrame(records)
        df.set_index('timestamp', inplace=True)
        df.sort_index(inplace=True)
        
        return df
    
    def get_latest_market_data(self, symbol: str, market: Optional[str] = None) -> Optional[UnifiedMarketData]:
        """Get latest market data for a symbol."""
        return self.market_data_repo.get_latest_by_symbol(symbol, market)
    
    def get_available_symbols(self, market: Optional[str] = None) -> List[str]:
        """Get all available symbols, optionally filtered by market."""
        if market:
            return self.market_data_repo.get_symbols_by_market(market)
        else:
            # Get symbols from all markets
            all_symbols = set()
            markets = ['US', 'CN', 'CRYPTO']
            for mkt in markets:
                symbols = self.market_data_repo.get_symbols_by_market(mkt)
                all_symbols.update(symbols)
            return sorted(list(all_symbols))
    
    def get_data_date_range(self, symbol: str, market: Optional[str] = None) -> Optional[Tuple[datetime, datetime]]:
        """Get available date range for a symbol."""
        return self.market_data_repo.get_date_range_by_symbol(symbol, market)
    
    def delete_market_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        market: Optional[str] = None
    ) -> int:
        """Delete market data for a symbol and date range."""
        return self.market_data_repo.delete_by_symbol_and_date_range(
            symbol, start_date, end_date, market
        )
    
    # Economic Data Operations
    def save_economic_data(self, economic_data: EconomicData) -> bool:
        """Save single economic data record."""
        if not economic_data.validate():
            logger.warning(f"Invalid economic data for {economic_data.series_id}")
            return False
        
        return self.economic_data_repo.save(economic_data)
    
    def save_economic_data_batch(self, economic_data_list: List[EconomicData]) -> int:
        """Save multiple economic data records."""
        # Validate all records first
        valid_data = []
        for data in economic_data_list:
            if data.validate():
                valid_data.append(data)
            else:
                logger.warning(f"Skipping invalid economic data for {data.series_id}")
        
        if not valid_data:
            logger.warning("No valid economic data to save")
            return 0
        
        return self.economic_data_repo.save_batch(valid_data)
    
    def get_economic_data(
        self, 
        series_id: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> pd.DataFrame:
        """Get economic data as pandas DataFrame."""
        data_list = self.economic_data_repo.get_by_series_and_date_range(
            series_id, start_date, end_date
        )
        
        if not data_list:
            return pd.DataFrame()
        
        # Convert to DataFrame
        records = []
        for data in data_list:
            record = {
                'timestamp': data.timestamp,
                'value': data.value,
                'unit': data.unit,
                'frequency': data.frequency,
                'seasonal_adjustment': data.seasonal_adjustment,
                'last_updated': data.last_updated,
                'notes': data.notes
            }
            records.append(record)
        
        df = pd.DataFrame(records)
        df.set_index('timestamp', inplace=True)
        df.sort_index(inplace=True)
        
        return df
    
    def get_latest_economic_data(self, series_id: str) -> Optional[EconomicData]:
        """Get latest economic data for a series."""
        return self.economic_data_repo.get_latest_by_series(series_id)
    
    def get_available_economic_series(self) -> List[Dict[str, Any]]:
        """Get all available economic data series."""
        return self.economic_data_repo.get_all_series()
    
    # Market Info Operations
    def save_market_info(self, market_info: MarketInfo) -> bool:
        """Save market information."""
        if not market_info.validate():
            logger.warning(f"Invalid market info for {market_info.symbol}")
            return False
        
        return self.market_info_repo.save(market_info)
    
    def get_market_info(self, symbol: str, market: str) -> Optional[MarketInfo]:
        """Get market information for a symbol."""
        return self.market_info_repo.get_by_symbol_and_market(symbol, market)
    
    def get_market_symbols(self, market: str, active_only: bool = True) -> List[MarketInfo]:
        """Get all symbols for a market."""
        return self.market_info_repo.get_by_market(market, active_only)
    
    def get_all_markets(self) -> List[str]:
        """Get all available markets."""
        return self.market_info_repo.get_all_markets()
    
    def update_symbol_status(self, symbol: str, market: str, is_active: bool) -> bool:
        """Update active status for a symbol."""
        return self.market_info_repo.update_active_status(symbol, market, is_active)
    
    # Data Coverage and Statistics
    def get_data_coverage_report(self) -> Dict[str, Any]:
        """Get comprehensive data coverage report."""
        market_coverage = self.market_data_repo.get_data_coverage()
        economic_series = self.get_available_economic_series()
        all_markets = self.get_all_markets()
        
        return {
            'market_data': market_coverage,
            'economic_data': {
                'series_count': len(economic_series),
                'series': economic_series
            },
            'markets': all_markets,
            'generated_at': datetime.now().isoformat()
        }
    
    def get_data_quality_report(self, symbol: str, market: Optional[str] = None) -> Dict[str, Any]:
        """Get data quality report for a symbol."""
        # Get date range
        date_range = self.get_data_date_range(symbol, market)
        if not date_range:
            return {'error': 'No data found for symbol'}
        
        start_date, end_date = date_range
        
        # Get data
        df = self.get_market_data(symbol, start_date, end_date, market)
        if df.empty:
            return {'error': 'No data found for symbol'}
        
        # Calculate quality metrics
        total_days = (end_date - start_date).days + 1
        actual_records = len(df)
        
        # Check for missing values
        missing_values = df.isnull().sum().to_dict()
        
        # Check for price anomalies
        price_anomalies = {
            'negative_prices': (df[['open', 'high', 'low', 'close']] < 0).sum().sum(),
            'zero_volume': (df['volume'] == 0).sum(),
            'high_low_inconsistency': (df['high'] < df['low']).sum(),
            'ohlc_inconsistency': ((df['high'] < df[['open', 'close']].max(axis=1)) | 
                                  (df['low'] > df[['open', 'close']].min(axis=1))).sum()
        }
        
        # Calculate completeness
        completeness = actual_records / total_days if total_days > 0 else 0
        
        return {
            'symbol': symbol,
            'market': market,
            'date_range': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat(),
                'total_days': total_days
            },
            'records': {
                'total': actual_records,
                'completeness': completeness
            },
            'missing_values': missing_values,
            'price_anomalies': price_anomalies,
            'generated_at': datetime.now().isoformat()
        }
    
    # Database Management
    def optimize_database(self) -> None:
        """Optimize database performance."""
        try:
            # Optimize main tables
            tables = ['market_data', 'economic_data', 'market_info']
            for table in tables:
                if self.db.table_exists(table):
                    self.db.optimize_table(table)
            
            logger.info("Database optimization completed")
        except Exception as e:
            logger.error(f"Database optimization failed: {e}")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        try:
            stats = {}
            tables = ['market_data', 'economic_data', 'market_info', 'data_sources']
            
            for table in tables:
                if self.db.table_exists(table):
                    stats[table] = self.db.get_table_size(table)
            
            return stats
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            return {}
    
    def backup_data(self, backup_path: str) -> bool:
        """Backup database data."""
        # This is a placeholder - implement based on database type
        logger.warning("Backup functionality not yet implemented")
        return False
    
    def restore_data(self, backup_path: str) -> bool:
        """Restore database data from backup."""
        # This is a placeholder - implement based on database type
        logger.warning("Restore functionality not yet implemented")
        return False
    
    # Data Import Operations
    def import_from_file(
        self,
        file_path: Union[str, Path],
        market: str = 'US',
        exchange: str = 'UNKNOWN',
        currency: str = 'USD',
        save_to_db: bool = True,
        **kwargs
    ) -> Tuple[bool, Union[int, str]]:
        """Import market data from file."""
        try:
            success, result = self.import_manager.import_file(
                file_path, market=market, exchange=exchange, currency=currency, **kwargs
            )
            
            if not success:
                return False, result
            
            market_data_list = result
            
            if save_to_db:
                saved_count = self.save_market_data_batch(market_data_list)
                logger.info(f"Imported and saved {saved_count} records from {file_path}")
                return True, saved_count
            else:
                logger.info(f"Imported {len(market_data_list)} records from {file_path} (not saved)")
                return True, len(market_data_list)
                
        except Exception as e:
            logger.error(f"Failed to import from file {file_path}: {e}")
            return False, str(e)
    
    def import_from_dataframe(
        self,
        df: pd.DataFrame,
        market: str = 'US',
        exchange: str = 'UNKNOWN',
        currency: str = 'USD',
        save_to_db: bool = True
    ) -> Tuple[bool, Union[int, str]]:
        """Import market data from pandas DataFrame."""
        try:
            success, result = self.import_manager.import_from_dataframe(
                df, market=market, exchange=exchange, currency=currency
            )
            
            if not success:
                return False, result
            
            market_data_list = result
            
            if save_to_db:
                saved_count = self.save_market_data_batch(market_data_list)
                logger.info(f"Imported and saved {saved_count} records from DataFrame")
                return True, saved_count
            else:
                logger.info(f"Imported {len(market_data_list)} records from DataFrame (not saved)")
                return True, len(market_data_list)
                
        except Exception as e:
            logger.error(f"Failed to import from DataFrame: {e}")
            return False, str(e)
    
    def get_import_preview(
        self,
        file_path: Union[str, Path],
        rows: int = 5
    ) -> Tuple[bool, Union[pd.DataFrame, str]]:
        """Get preview of data to be imported from file."""
        return self.import_manager.get_import_preview(file_path, rows)
    
    def validate_import_file(
        self,
        file_path: Union[str, Path]
    ) -> Tuple[bool, str]:
        """Validate file for import."""
        return self.import_manager.validate_file(file_path)
    
    def get_supported_import_formats(self) -> List[str]:
        """Get supported file formats for import."""
        return self.import_manager.get_supported_formats()
    
    # Data Source Operations
    def fetch_data_from_source(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = '1d',
        preferred_source: Optional[str] = None,
        save_to_db: bool = True
    ) -> Tuple[bool, Union[List[UnifiedMarketData], str]]:
        """Fetch data from external data sources."""
        try:
            success, result = self.data_source_manager.fetch_historical_data(
                symbol, start_date, end_date, interval, preferred_source
            )
            
            if not success:
                return False, result
            
            market_data_list = result
            
            if save_to_db and market_data_list:
                saved_count = self.save_market_data_batch(market_data_list)
                logger.info(f"Fetched and saved {saved_count} records for {symbol}")
            
            return True, market_data_list
            
        except Exception as e:
            logger.error(f"Failed to fetch data from source: {e}")
            return False, str(e)
    
    def get_available_data_sources(self) -> Dict[str, Any]:
        """Get information about available data sources."""
        return self.data_source_manager.get_data_coverage_report()
    
    def get_data_source_symbols(self, market: Optional[str] = None) -> Dict[str, List[str]]:
        """Get available symbols from data sources."""
        return self.data_source_manager.get_available_symbols(market)
    
    def sync_symbols_from_sources(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        interval: str = '1d'
    ) -> Dict[str, Any]:
        """Sync data for multiple symbols from external sources."""
        return self.data_source_manager.sync_data_for_symbols(
            symbols, start_date, end_date, interval
        )
    
    def health_check_data_sources(self) -> Dict[str, Dict[str, Any]]:
        """Perform health check on all data sources."""
        return self.data_source_manager.health_check_all()
    
    # Cache Operations
    def clear_cache(self) -> None:
        """Clear all cached data."""
        self.cache.clear_all()
        logger.info("Cleared data cache")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return self.cache.get_stats()
    
    def cleanup_cache(self) -> Dict[str, int]:
        """Clean up expired cache entries."""
        return self.cache.cleanup()
    
    def invalidate_symbol_cache(self, symbol: str) -> int:
        """Invalidate cached data for a specific symbol."""
        return self.cache.invalidate_symbol(symbol)
    
    # Data Coverage API
    def get_data_coverage_summary(self) -> Dict[str, Any]:
        """Get comprehensive data coverage summary."""
        # Get database coverage
        db_coverage = self.get_data_coverage_report()
        
        # Get data source coverage
        source_coverage = self.get_available_data_sources()
        
        # Get cache statistics
        cache_stats = self.get_cache_stats()
        
        return {
            'database': db_coverage,
            'data_sources': source_coverage,
            'cache': cache_stats,
            'generated_at': datetime.now().isoformat()
        }
    
    def get_symbol_coverage_details(self, symbol: str) -> Dict[str, Any]:
        """Get detailed coverage information for a specific symbol."""
        coverage_details = {}
        
        # Check database coverage
        for market in ['US', 'CN', 'CRYPTO']:
            date_range = self.get_data_date_range(symbol, market)
            if date_range:
                coverage_details[f'database_{market}'] = {
                    'available': True,
                    'start_date': date_range[0].isoformat(),
                    'end_date': date_range[1].isoformat(),
                    'days': (date_range[1] - date_range[0]).days + 1
                }
            else:
                coverage_details[f'database_{market}'] = {'available': False}
        
        # Check data source availability
        market_info = self.data_source_manager.get_market_info(symbol)
        if market_info:
            coverage_details['data_sources'] = {
                'available': True,
                'market': market_info.market,
                'exchange': market_info.exchange,
                'currency': market_info.currency
            }
        else:
            coverage_details['data_sources'] = {'available': False}
        
        return coverage_details
    
    def close(self) -> None:
        """Close database connections."""
        self.db.close()


# Global data manager instance
_data_manager: Optional[DataManager] = None


def get_data_manager(use_sqlite: bool = False) -> DataManager:
    """Get global data manager instance."""
    global _data_manager
    
    if _data_manager is None:
        _data_manager = DataManager(use_sqlite=use_sqlite)
    
    return _data_manager