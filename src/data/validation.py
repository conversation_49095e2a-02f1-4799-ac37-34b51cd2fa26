"""
Data validation and cleaning functionality for market data.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass
from enum import Enum

from ..models.market_data import UnifiedMarketData, EconomicData
from ..exceptions import DataValidationException, DataException


logger = logging.getLogger(__name__)


class ValidationSeverity(Enum):
    """Severity levels for validation issues."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ValidationIssue:
    """Represents a data validation issue."""
    severity: ValidationSeverity
    issue_type: str
    message: str
    timestamp: Optional[datetime] = None
    value: Optional[float] = None
    expected_value: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ValidationResult:
    """Result of data validation process."""
    is_valid: bool
    issues: List[ValidationIssue]
    summary: Dict[str, int]
    
    def __post_init__(self):
        """Calculate summary statistics."""
        self.summary = {
            'total_issues': len(self.issues),
            'critical': len([i for i in self.issues if i.severity == ValidationSeverity.CRITICAL]),
            'errors': len([i for i in self.issues if i.severity == ValidationSeverity.ERROR]),
            'warnings': len([i for i in self.issues if i.severity == ValidationSeverity.WARNING]),
            'info': len([i for i in self.issues if i.severity == ValidationSeverity.INFO])
        }
        
        # Data is invalid if there are critical issues or errors
        self.is_valid = self.summary['critical'] == 0 and self.summary['errors'] == 0


class OHLCVValidator:
    """Validator for OHLCV market data format."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize validator with configuration.
        
        Args:
            config: Validation configuration parameters
        """
        self.config = config or {}
        self.min_price = self.config.get('min_price', 0.0001)
        self.max_price = self.config.get('max_price', 1000000)
        self.min_volume = self.config.get('min_volume', 0)
        self.max_volume = self.config.get('max_volume', 1e12)
        self.max_price_change_pct = self.config.get('max_price_change_pct', 50.0)
        self.max_volume_change_pct = self.config.get('max_volume_change_pct', 1000.0)
    
    def validate_single_record(self, data: UnifiedMarketData) -> ValidationResult:
        """Validate a single market data record."""
        issues = []
        
        # Basic field validation
        if not data.symbol:
            issues.append(ValidationIssue(
                ValidationSeverity.CRITICAL,
                "missing_symbol",
                "Symbol is missing or empty"
            ))
        
        if not isinstance(data.timestamp, datetime):
            issues.append(ValidationIssue(
                ValidationSeverity.CRITICAL,
                "invalid_timestamp",
                "Timestamp is not a valid datetime object"
            ))
        
        # Price validation
        prices = [data.open, data.high, data.low, data.close]
        price_names = ['open', 'high', 'low', 'close']
        
        for price, name in zip(prices, price_names):
            if not isinstance(price, (int, float, np.integer, np.floating)) or np.isnan(price):
                issues.append(ValidationIssue(
                    ValidationSeverity.ERROR,
                    "invalid_price",
                    f"{name} price is not a valid number",
                    timestamp=data.timestamp,
                    value=price
                ))
            elif price <= 0:
                issues.append(ValidationIssue(
                    ValidationSeverity.ERROR,
                    "negative_price",
                    f"{name} price is negative or zero",
                    timestamp=data.timestamp,
                    value=price
                ))
            elif price < self.min_price or price > self.max_price:
                issues.append(ValidationIssue(
                    ValidationSeverity.WARNING,
                    "price_out_of_range",
                    f"{name} price is outside expected range",
                    timestamp=data.timestamp,
                    value=price
                ))
        
        # OHLC relationship validation
        if all(isinstance(p, (int, float, np.integer, np.floating)) and not np.isnan(p) for p in prices):
            if data.high < max(data.open, data.close):
                issues.append(ValidationIssue(
                    ValidationSeverity.ERROR,
                    "high_price_inconsistent",
                    "High price is less than open or close price",
                    timestamp=data.timestamp,
                    value=data.high,
                    expected_value=max(data.open, data.close)
                ))
            
            if data.low > min(data.open, data.close):
                issues.append(ValidationIssue(
                    ValidationSeverity.ERROR,
                    "low_price_inconsistent",
                    "Low price is greater than open or close price",
                    timestamp=data.timestamp,
                    value=data.low,
                    expected_value=min(data.open, data.close)
                ))
            
            if data.high < data.low:
                issues.append(ValidationIssue(
                    ValidationSeverity.CRITICAL,
                    "high_low_inverted",
                    "High price is less than low price",
                    timestamp=data.timestamp,
                    value=data.high,
                    expected_value=data.low
                ))
        
        # Volume validation
        if not isinstance(data.volume, (int, float, np.integer, np.floating)) or np.isnan(data.volume):
            issues.append(ValidationIssue(
                ValidationSeverity.ERROR,
                "invalid_volume",
                "Volume is not a valid number",
                timestamp=data.timestamp,
                value=data.volume
            ))
        elif data.volume < 0:
            issues.append(ValidationIssue(
                ValidationSeverity.ERROR,
                "negative_volume",
                "Volume is negative",
                timestamp=data.timestamp,
                value=data.volume
            ))
        elif data.volume > self.max_volume:
            issues.append(ValidationIssue(
                ValidationSeverity.WARNING,
                "volume_out_of_range",
                "Volume is unusually high",
                timestamp=data.timestamp,
                value=data.volume
            ))
        
        # Adjusted close validation
        if data.adj_close is not None:
            if not isinstance(data.adj_close, (int, float, np.integer, np.floating)) or np.isnan(data.adj_close):
                issues.append(ValidationIssue(
                    ValidationSeverity.WARNING,
                    "invalid_adj_close",
                    "Adjusted close is not a valid number",
                    timestamp=data.timestamp,
                    value=data.adj_close
                ))
            elif data.adj_close <= 0:
                issues.append(ValidationIssue(
                    ValidationSeverity.WARNING,
                    "negative_adj_close",
                    "Adjusted close is negative or zero",
                    timestamp=data.timestamp,
                    value=data.adj_close
                ))
        
        return ValidationResult(True, issues, {})
    
    def validate_dataframe(self, df: pd.DataFrame, symbol: str = "") -> ValidationResult:
        """Validate a pandas DataFrame containing OHLCV data."""
        issues = []
        
        # Check required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            issues.append(ValidationIssue(
                ValidationSeverity.CRITICAL,
                "missing_columns",
                f"Missing required columns: {missing_columns}"
            ))
            return ValidationResult(False, issues, {})
        
        # Check for empty DataFrame
        if df.empty:
            issues.append(ValidationIssue(
                ValidationSeverity.ERROR,
                "empty_data",
                "DataFrame is empty"
            ))
            return ValidationResult(False, issues, {})
        
        # Validate each row
        for idx, row in df.iterrows():
            timestamp = idx if isinstance(idx, datetime) else None
            
            # Create temporary UnifiedMarketData for validation
            try:
                temp_data = UnifiedMarketData(
                    symbol=symbol or "UNKNOWN",
                    timestamp=timestamp or datetime.now(),
                    open=row['open'],
                    high=row['high'],
                    low=row['low'],
                    close=row['close'],
                    volume=row['volume'],
                    market="UNKNOWN",
                    exchange="UNKNOWN",
                    currency="USD",
                    adj_close=row.get('adj_close'),
                    turnover=row.get('turnover')
                )
                
                row_result = self.validate_single_record(temp_data)
                issues.extend(row_result.issues)
                
            except Exception as e:
                issues.append(ValidationIssue(
                    ValidationSeverity.ERROR,
                    "validation_error",
                    f"Error validating row {idx}: {str(e)}",
                    timestamp=timestamp
                ))
        
        return ValidationResult(True, issues, {})


class TimeSeriesValidator:
    """Validator for time series data completeness and consistency."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize validator with configuration."""
        self.config = config or {}
        self.expected_frequency = self.config.get('expected_frequency', 'D')  # Daily by default
        self.max_gap_days = self.config.get('max_gap_days', 7)
        self.min_data_points = self.config.get('min_data_points', 10)
    
    def validate_completeness(self, df: pd.DataFrame, symbol: str = "") -> ValidationResult:
        """Validate time series completeness."""
        issues = []
        
        if df.empty:
            issues.append(ValidationIssue(
                ValidationSeverity.CRITICAL,
                "empty_timeseries",
                "Time series data is empty"
            ))
            return ValidationResult(False, issues, {})
        
        # Check minimum data points
        if len(df) < self.min_data_points:
            issues.append(ValidationIssue(
                ValidationSeverity.WARNING,
                "insufficient_data",
                f"Time series has only {len(df)} data points, minimum recommended: {self.min_data_points}"
            ))
        
        # Check for duplicate timestamps
        if not df.index.is_unique:
            duplicate_count = len(df) - len(df.index.unique())
            issues.append(ValidationIssue(
                ValidationSeverity.ERROR,
                "duplicate_timestamps",
                f"Found {duplicate_count} duplicate timestamps"
            ))
        
        # Check for proper time ordering
        if not df.index.is_monotonic_increasing:
            issues.append(ValidationIssue(
                ValidationSeverity.ERROR,
                "unordered_timestamps",
                "Timestamps are not in chronological order"
            ))
        
        # Check for gaps in time series
        if len(df) > 1:
            time_diffs = df.index.to_series().diff().dropna()
            
            # Expected frequency in days
            freq_map = {'D': 1, 'H': 1/24, 'T': 1/1440, 'S': 1/86400}
            expected_diff = timedelta(days=freq_map.get(self.expected_frequency, 1))
            
            # Find gaps larger than expected
            large_gaps = time_diffs[time_diffs > expected_diff * self.max_gap_days]
            
            for timestamp, gap in large_gaps.items():
                issues.append(ValidationIssue(
                    ValidationSeverity.WARNING,
                    "data_gap",
                    f"Large gap in data: {gap.days} days",
                    timestamp=timestamp
                ))
        
        return ValidationResult(True, issues, {})


class AnomalyDetector:
    """Detector for price and volume anomalies in market data."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize anomaly detector with configuration."""
        self.config = config or {}
        self.price_jump_threshold = self.config.get('price_jump_threshold', 0.2)  # 20%
        self.volume_spike_threshold = self.config.get('volume_spike_threshold', 5.0)  # 5x normal
        self.rolling_window = self.config.get('rolling_window', 20)
        self.z_score_threshold = self.config.get('z_score_threshold', 3.0)
    
    def detect_price_anomalies(self, df: pd.DataFrame) -> List[ValidationIssue]:
        """Detect price anomalies in market data."""
        issues = []
        
        if len(df) < 2:
            return issues
        
        # Calculate price changes
        df = df.copy()
        df['price_change'] = df['close'].pct_change()
        df['price_change_abs'] = df['price_change'].abs()
        
        # Detect large price jumps
        large_jumps = df[df['price_change_abs'] > self.price_jump_threshold]
        
        for idx, row in large_jumps.iterrows():
            issues.append(ValidationIssue(
                ValidationSeverity.WARNING,
                "price_jump",
                f"Large price jump detected: {row['price_change']:.2%}",
                timestamp=idx,
                value=row['close'],
                metadata={'price_change': row['price_change']}
            ))
        
        # Detect statistical outliers using z-score
        if len(df) >= self.rolling_window:
            df['price_zscore'] = (df['close'] - df['close'].rolling(self.rolling_window).mean()) / df['close'].rolling(self.rolling_window).std()
            
            outliers = df[df['price_zscore'].abs() > self.z_score_threshold]
            
            for idx, row in outliers.iterrows():
                issues.append(ValidationIssue(
                    ValidationSeverity.INFO,
                    "price_outlier",
                    f"Price outlier detected (z-score: {row['price_zscore']:.2f})",
                    timestamp=idx,
                    value=row['close'],
                    metadata={'z_score': row['price_zscore']}
                ))
        
        return issues
    
    def detect_volume_anomalies(self, df: pd.DataFrame) -> List[ValidationIssue]:
        """Detect volume anomalies in market data."""
        issues = []
        
        if len(df) == 0:
            return issues
        
        df = df.copy()
        
        # Detect unusually low volume (potential data issues) - check this first
        zero_volume = df[df['volume'] == 0]
        
        for idx, row in zero_volume.iterrows():
            issues.append(ValidationIssue(
                ValidationSeverity.WARNING,
                "zero_volume",
                "Zero volume detected",
                timestamp=idx,
                value=0
            ))
        
        # Only do rolling window analysis if we have enough data
        if len(df) >= self.rolling_window:
            # Calculate rolling average volume
            df['volume_avg'] = df['volume'].rolling(self.rolling_window).mean()
            df['volume_ratio'] = df['volume'] / df['volume_avg']
            
            # Detect volume spikes
            volume_spikes = df[df['volume_ratio'] > self.volume_spike_threshold]
            
            for idx, row in volume_spikes.iterrows():
                issues.append(ValidationIssue(
                    ValidationSeverity.WARNING,
                    "volume_spike",
                    f"Volume spike detected: {row['volume_ratio']:.1f}x normal volume",
                    timestamp=idx,
                    value=row['volume'],
                    metadata={'volume_ratio': row['volume_ratio']}
                ))
        
        return issues
    
    def detect_all_anomalies(self, df: pd.DataFrame) -> List[ValidationIssue]:
        """Detect all types of anomalies."""
        issues = []
        issues.extend(self.detect_price_anomalies(df))
        issues.extend(self.detect_volume_anomalies(df))
        return issues


class DataCleaner:
    """Data cleaning utilities for market data."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize data cleaner with configuration."""
        self.config = config or {}
        self.fill_method = self.config.get('fill_method', 'forward')
        self.outlier_method = self.config.get('outlier_method', 'clip')
        self.max_fill_limit = self.config.get('max_fill_limit', 5)
    
    def handle_missing_values(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """Handle missing values in market data."""
        df_cleaned = df.copy()
        operations = []
        
        # Check for missing values
        missing_counts = df_cleaned.isnull().sum()
        
        if missing_counts.sum() == 0:
            return df_cleaned, operations
        
        # Handle missing values based on method
        if self.fill_method == 'forward':
            df_cleaned = df_cleaned.ffill(limit=self.max_fill_limit)
            operations.append(f"Forward filled missing values (limit: {self.max_fill_limit})")
        
        elif self.fill_method == 'backward':
            df_cleaned = df_cleaned.bfill(limit=self.max_fill_limit)
            operations.append(f"Backward filled missing values (limit: {self.max_fill_limit})")
        
        elif self.fill_method == 'interpolate':
            numeric_columns = df_cleaned.select_dtypes(include=[np.number]).columns
            df_cleaned[numeric_columns] = df_cleaned[numeric_columns].interpolate(method='linear', limit=self.max_fill_limit)
            operations.append(f"Interpolated missing values (limit: {self.max_fill_limit})")
        
        elif self.fill_method == 'drop':
            initial_len = len(df_cleaned)
            df_cleaned = df_cleaned.dropna()
            dropped_count = initial_len - len(df_cleaned)
            operations.append(f"Dropped {dropped_count} rows with missing values")
        
        # Check remaining missing values
        remaining_missing = df_cleaned.isnull().sum()
        if remaining_missing.sum() > 0:
            operations.append(f"Warning: {remaining_missing.sum()} missing values remain")
        
        return df_cleaned, operations
    
    def handle_outliers(self, df: pd.DataFrame, columns: Optional[List[str]] = None) -> Tuple[pd.DataFrame, List[str]]:
        """Handle outliers in market data."""
        df_cleaned = df.copy()
        operations = []
        
        if columns is None:
            columns = ['open', 'high', 'low', 'close', 'volume']
        
        columns = [col for col in columns if col in df_cleaned.columns]
        
        for column in columns:
            if self.outlier_method == 'clip':
                # Use IQR method to identify outliers
                Q1 = df_cleaned[column].quantile(0.25)
                Q3 = df_cleaned[column].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                # Clip outliers
                outliers_count = ((df_cleaned[column] < lower_bound) | (df_cleaned[column] > upper_bound)).sum()
                if outliers_count > 0:
                    df_cleaned[column] = df_cleaned[column].clip(lower_bound, upper_bound)
                    operations.append(f"Clipped {outliers_count} outliers in {column}")
            
            elif self.outlier_method == 'remove':
                # Remove outliers
                Q1 = df_cleaned[column].quantile(0.25)
                Q3 = df_cleaned[column].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                initial_len = len(df_cleaned)
                df_cleaned = df_cleaned[(df_cleaned[column] >= lower_bound) & (df_cleaned[column] <= upper_bound)]
                removed_count = initial_len - len(df_cleaned)
                
                if removed_count > 0:
                    operations.append(f"Removed {removed_count} outlier rows based on {column}")
        
        return df_cleaned, operations
    
    def fix_ohlc_inconsistencies(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """Fix OHLC price inconsistencies."""
        df_cleaned = df.copy()
        operations = []
        
        required_cols = ['open', 'high', 'low', 'close']
        if not all(col in df_cleaned.columns for col in required_cols):
            return df_cleaned, operations
        
        fixes_count = 0
        
        # Fix high < max(open, close)
        mask = df_cleaned['high'] < df_cleaned[['open', 'close']].max(axis=1)
        if mask.any():
            df_cleaned.loc[mask, 'high'] = df_cleaned.loc[mask, ['open', 'close']].max(axis=1)
            fixes_count += mask.sum()
        
        # Fix low > min(open, close)
        mask = df_cleaned['low'] > df_cleaned[['open', 'close']].min(axis=1)
        if mask.any():
            df_cleaned.loc[mask, 'low'] = df_cleaned.loc[mask, ['open', 'close']].min(axis=1)
            fixes_count += mask.sum()
        
        # Fix high < low (more complex case)
        mask = df_cleaned['high'] < df_cleaned['low']
        if mask.any():
            # Swap high and low values
            temp_high = df_cleaned.loc[mask, 'high'].copy()
            df_cleaned.loc[mask, 'high'] = df_cleaned.loc[mask, 'low']
            df_cleaned.loc[mask, 'low'] = temp_high
            fixes_count += mask.sum()
        
        if fixes_count > 0:
            operations.append(f"Fixed {fixes_count} OHLC inconsistencies")
        
        return df_cleaned, operations
    
    def clean_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """Comprehensive data cleaning."""
        df_cleaned = df.copy()
        all_operations = []
        
        # Handle missing values
        df_cleaned, missing_ops = self.handle_missing_values(df_cleaned)
        all_operations.extend(missing_ops)
        
        # Fix OHLC inconsistencies
        df_cleaned, ohlc_ops = self.fix_ohlc_inconsistencies(df_cleaned)
        all_operations.extend(ohlc_ops)
        
        # Handle outliers
        df_cleaned, outlier_ops = self.handle_outliers(df_cleaned)
        all_operations.extend(outlier_ops)
        
        # Generate cleaning report
        cleaning_report = {
            'original_rows': len(df),
            'cleaned_rows': len(df_cleaned),
            'rows_removed': len(df) - len(df_cleaned),
            'operations': all_operations,
            'cleaning_timestamp': datetime.now().isoformat()
        }
        
        return df_cleaned, cleaning_report


class DataQualityReporter:
    """Generate comprehensive data quality reports."""
    
    def __init__(self):
        """Initialize data quality reporter."""
        self.ohlcv_validator = OHLCVValidator()
        self.timeseries_validator = TimeSeriesValidator()
        self.anomaly_detector = AnomalyDetector()
    
    def generate_quality_report(self, df: pd.DataFrame, symbol: str = "", market: str = "") -> Dict[str, Any]:
        """Generate comprehensive data quality report."""
        report = {
            'symbol': symbol,
            'market': market,
            'generated_at': datetime.now().isoformat(),
            'data_summary': {},
            'validation_results': {},
            'anomaly_detection': {},
            'recommendations': []
        }
        
        # Basic data summary
        if not df.empty:
            report['data_summary'] = {
                'total_records': len(df),
                'date_range': {
                    'start': df.index.min().isoformat() if hasattr(df.index.min(), 'isoformat') else str(df.index.min()),
                    'end': df.index.max().isoformat() if hasattr(df.index.max(), 'isoformat') else str(df.index.max())
                },
                'missing_values': df.isnull().sum().to_dict(),
                'data_types': df.dtypes.astype(str).to_dict()
            }
        else:
            report['data_summary'] = {'total_records': 0, 'error': 'Empty dataset'}
            return report
        
        # OHLCV validation
        ohlcv_result = self.ohlcv_validator.validate_dataframe(df, symbol)
        report['validation_results']['ohlcv'] = {
            'is_valid': ohlcv_result.is_valid,
            'summary': ohlcv_result.summary,
            'issues': [
                {
                    'severity': issue.severity.value,
                    'type': issue.issue_type,
                    'message': issue.message,
                    'timestamp': issue.timestamp.isoformat() if issue.timestamp else None,
                    'value': issue.value
                }
                for issue in ohlcv_result.issues[:50]  # Limit to first 50 issues
            ]
        }
        
        # Time series validation
        timeseries_result = self.timeseries_validator.validate_completeness(df, symbol)
        report['validation_results']['timeseries'] = {
            'is_valid': timeseries_result.is_valid,
            'summary': timeseries_result.summary,
            'issues': [
                {
                    'severity': issue.severity.value,
                    'type': issue.issue_type,
                    'message': issue.message,
                    'timestamp': issue.timestamp.isoformat() if issue.timestamp else None
                }
                for issue in timeseries_result.issues
            ]
        }
        
        # Anomaly detection
        anomalies = self.anomaly_detector.detect_all_anomalies(df)
        report['anomaly_detection'] = {
            'total_anomalies': len(anomalies),
            'anomaly_types': {},
            'anomalies': [
                {
                    'severity': anomaly.severity.value,
                    'type': anomaly.issue_type,
                    'message': anomaly.message,
                    'timestamp': anomaly.timestamp.isoformat() if anomaly.timestamp else None,
                    'value': anomaly.value,
                    'metadata': anomaly.metadata
                }
                for anomaly in anomalies[:100]  # Limit to first 100 anomalies
            ]
        }
        
        # Count anomaly types
        for anomaly in anomalies:
            anomaly_type = anomaly.issue_type
            if anomaly_type not in report['anomaly_detection']['anomaly_types']:
                report['anomaly_detection']['anomaly_types'][anomaly_type] = 0
            report['anomaly_detection']['anomaly_types'][anomaly_type] += 1
        
        # Generate recommendations
        recommendations = self._generate_recommendations(report)
        report['recommendations'] = recommendations
        
        return report
    
    def _generate_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """Generate data quality improvement recommendations."""
        recommendations = []
        
        # Check validation results
        ohlcv_summary = report['validation_results']['ohlcv']['summary']
        if ohlcv_summary['critical'] > 0:
            recommendations.append("Critical OHLCV validation issues found - data may be corrupted")
        if ohlcv_summary['errors'] > 0:
            recommendations.append("OHLCV validation errors found - consider data cleaning")
        
        timeseries_summary = report['validation_results']['timeseries']['summary']
        if timeseries_summary['errors'] > 0:
            recommendations.append("Time series validation errors found - check data ordering and duplicates")
        
        # Check missing values
        missing_values = report['data_summary'].get('missing_values', {})
        total_missing = sum(missing_values.values())
        if total_missing > 0:
            recommendations.append(f"Found {total_missing} missing values - consider data imputation")
        
        # Check anomalies
        anomaly_count = report['anomaly_detection']['total_anomalies']
        if anomaly_count > 0:
            recommendations.append(f"Found {anomaly_count} anomalies - review for data quality issues")
        
        # Check data completeness
        total_records = report['data_summary'].get('total_records', 0)
        if total_records < 100:
            recommendations.append("Limited data available - consider expanding data collection period")
        
        if not recommendations:
            recommendations.append("Data quality appears good - no major issues detected")
        
        return recommendations


# Main validation interface
class DataValidator:
    """Main interface for data validation and cleaning."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize data validator."""
        self.config = config or {}
        self.ohlcv_validator = OHLCVValidator(self.config.get('ohlcv', {}))
        self.timeseries_validator = TimeSeriesValidator(self.config.get('timeseries', {}))
        self.anomaly_detector = AnomalyDetector(self.config.get('anomaly', {}))
        self.data_cleaner = DataCleaner(self.config.get('cleaning', {}))
        self.quality_reporter = DataQualityReporter()
    
    def validate_market_data(self, data: Union[UnifiedMarketData, pd.DataFrame], symbol: str = "") -> ValidationResult:
        """Validate market data (single record or DataFrame)."""
        if isinstance(data, UnifiedMarketData):
            return self.ohlcv_validator.validate_single_record(data)
        elif isinstance(data, pd.DataFrame):
            # Combine OHLCV and time series validation
            ohlcv_result = self.ohlcv_validator.validate_dataframe(data, symbol)
            timeseries_result = self.timeseries_validator.validate_completeness(data, symbol)
            
            # Merge results
            all_issues = ohlcv_result.issues + timeseries_result.issues
            return ValidationResult(True, all_issues, {})
        else:
            raise DataValidationException("Unsupported data type for validation")
    
    def clean_market_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """Clean market data DataFrame."""
        return self.data_cleaner.clean_data(df)
    
    def generate_quality_report(self, df: pd.DataFrame, symbol: str = "", market: str = "") -> Dict[str, Any]:
        """Generate comprehensive data quality report."""
        return self.quality_reporter.generate_quality_report(df, symbol, market)
    
    def validate_and_clean(self, df: pd.DataFrame, symbol: str = "") -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """Validate and clean data in one step."""
        # First validate
        validation_result = self.validate_market_data(df, symbol)
        
        # Then clean
        cleaned_df, cleaning_report = self.clean_market_data(df)
        
        # Generate final quality report
        quality_report = self.generate_quality_report(cleaned_df, symbol)
        
        # Combine reports
        combined_report = {
            'validation': {
                'is_valid': validation_result.is_valid,
                'summary': validation_result.summary,
                'issues_count': len(validation_result.issues)
            },
            'cleaning': cleaning_report,
            'final_quality': quality_report
        }
        
        return cleaned_df, combined_report