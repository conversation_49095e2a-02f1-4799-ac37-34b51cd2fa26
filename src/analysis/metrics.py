"""
Core performance metrics calculation module.

This module implements comprehensive performance metrics for trading strategies
and portfolios, including return calculations, risk-adjusted metrics, drawdown
analysis, and statistical measures.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.models.portfolio import Portfolio
from src.models.trading import Trade


class PerformanceFrequency(Enum):
    """Performance calculation frequency."""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    ANNUAL = "annual"


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    
    # Basic Return Metrics
    total_return: float = 0.0
    annualized_return: float = 0.0
    cagr: float = 0.0  # Compound Annual Growth Rate
    daily_return_mean: float = 0.0
    daily_return_std: float = 0.0
    
    # Risk-Adjusted Metrics
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0
    information_ratio: float = 0.0
    treynor_ratio: float = 0.0
    
    # Drawdown Metrics
    max_drawdown: float = 0.0
    max_drawdown_duration: int = 0  # days
    avg_drawdown: float = 0.0
    avg_drawdown_duration: int = 0  # days
    recovery_factor: float = 0.0
    
    # Trade Statistics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    profit_factor: float = 0.0
    expectancy: float = 0.0
    
    # Risk Metrics
    volatility: float = 0.0
    downside_volatility: float = 0.0
    var_95: float = 0.0  # Value at Risk at 95% confidence
    cvar_95: float = 0.0  # Conditional VaR at 95% confidence
    skewness: float = 0.0
    kurtosis: float = 0.0
    
    # Benchmark Comparison
    beta: float = 0.0
    alpha: float = 0.0
    tracking_error: float = 0.0
    correlation: float = 0.0
    
    # Time-based Metrics
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    period_length: int = 0  # days
    
    # Additional Metrics
    kelly_criterion: float = 0.0
    gain_to_pain_ratio: float = 0.0
    sterling_ratio: float = 0.0
    burke_ratio: float = 0.0
    
    # Portfolio specific
    avg_positions: float = 0.0
    max_positions: int = 0
    turnover: float = 0.0
    
    def to_dict(self) -> Dict:
        """Convert metrics to dictionary."""
        return {
            'basic_returns': {
                'total_return': self.total_return,
                'annualized_return': self.annualized_return,
                'cagr': self.cagr,
                'daily_return_mean': self.daily_return_mean,
                'daily_return_std': self.daily_return_std
            },
            'risk_adjusted': {
                'sharpe_ratio': self.sharpe_ratio,
                'sortino_ratio': self.sortino_ratio,
                'calmar_ratio': self.calmar_ratio,
                'information_ratio': self.information_ratio,
                'treynor_ratio': self.treynor_ratio
            },
            'drawdowns': {
                'max_drawdown': self.max_drawdown,
                'max_drawdown_duration': self.max_drawdown_duration,
                'avg_drawdown': self.avg_drawdown,
                'avg_drawdown_duration': self.avg_drawdown_duration,
                'recovery_factor': self.recovery_factor
            },
            'trade_stats': {
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'losing_trades': self.losing_trades,
                'win_rate': self.win_rate,
                'avg_win': self.avg_win,
                'avg_loss': self.avg_loss,
                'profit_factor': self.profit_factor,
                'expectancy': self.expectancy
            },
            'risk_metrics': {
                'volatility': self.volatility,
                'downside_volatility': self.downside_volatility,
                'var_95': self.var_95,
                'cvar_95': self.cvar_95,
                'skewness': self.skewness,
                'kurtosis': self.kurtosis
            },
            'benchmark': {
                'beta': self.beta,
                'alpha': self.alpha,
                'tracking_error': self.tracking_error,
                'correlation': self.correlation
            }
        }


class MetricsCalculator:
    """Performance metrics calculator."""
    
    def __init__(self, risk_free_rate: float = 0.02):
        """
        Initialize metrics calculator.
        
        Args:
            risk_free_rate: Annual risk-free rate for Sharpe ratio calculation
        """
        self.risk_free_rate = risk_free_rate
        
    def calculate_metrics(
        self,
        portfolio_values: pd.Series,
        trades: Optional[List[Trade]] = None,
        benchmark_values: Optional[pd.Series] = None,
        frequency: PerformanceFrequency = PerformanceFrequency.DAILY
    ) -> PerformanceMetrics:
        """
        Calculate comprehensive performance metrics.
        
        Args:
            portfolio_values: Time series of portfolio values
            trades: List of trades for trade-specific metrics
            benchmark_values: Benchmark values for comparison
            frequency: Data frequency
            
        Returns:
            PerformanceMetrics object with calculated metrics
        """
        if len(portfolio_values) < 2:
            raise ValueError("Need at least 2 data points for calculation")
            
        # Calculate returns
        returns = self._calculate_returns(portfolio_values)
        
        # Initialize metrics
        metrics = PerformanceMetrics()
        
        # Basic metrics
        self._calculate_basic_metrics(metrics, portfolio_values, returns, frequency)
        
        # Risk-adjusted metrics
        self._calculate_risk_adjusted_metrics(metrics, returns, frequency)
        
        # Drawdown metrics
        self._calculate_drawdown_metrics(metrics, portfolio_values)
        
        # Trade statistics
        if trades:
            self._calculate_trade_statistics(metrics, trades)
            
        # Risk metrics
        self._calculate_risk_metrics(metrics, returns)
        
        # Benchmark comparison
        if benchmark_values is not None:
            self._calculate_benchmark_metrics(metrics, returns, benchmark_values)
            
        # Additional metrics
        self._calculate_additional_metrics(metrics, returns, portfolio_values)
        
        # Time metrics
        metrics.start_date = portfolio_values.index[0]
        metrics.end_date = portfolio_values.index[-1]
        metrics.period_length = (metrics.end_date - metrics.start_date).days
        
        return metrics
    
    def _calculate_returns(self, portfolio_values: pd.Series) -> pd.Series:
        """Calculate portfolio returns."""
        return portfolio_values.pct_change().dropna()
    
    def _calculate_basic_metrics(
        self,
        metrics: PerformanceMetrics,
        portfolio_values: pd.Series,
        returns: pd.Series,
        frequency: PerformanceFrequency
    ):
        """Calculate basic return metrics."""
        # Total return
        metrics.total_return = (portfolio_values.iloc[-1] / portfolio_values.iloc[0]) - 1
        
        # Annualized return
        days = len(portfolio_values)
        years = days / 252  # Assuming 252 trading days per year
        
        if years > 0:
            metrics.cagr = (portfolio_values.iloc[-1] / portfolio_values.iloc[0]) ** (1/years) - 1
            metrics.annualized_return = metrics.cagr
        
        # Daily statistics
        metrics.daily_return_mean = returns.mean()
        metrics.daily_return_std = returns.std()
        
    def _calculate_risk_adjusted_metrics(
        self,
        metrics: PerformanceMetrics,
        returns: pd.Series,
        frequency: PerformanceFrequency
    ):
        """Calculate risk-adjusted performance metrics."""
        if len(returns) == 0 or returns.std() == 0:
            return
            
        # Sharpe Ratio
        excess_returns = returns - self.risk_free_rate / 252
        metrics.sharpe_ratio = np.sqrt(252) * excess_returns.mean() / returns.std()
        
        # Sortino Ratio
        downside_returns = returns[returns < 0]
        if len(downside_returns) > 0:
            downside_std = downside_returns.std()
            if downside_std > 0:
                metrics.sortino_ratio = np.sqrt(252) * excess_returns.mean() / downside_std
        
        # Calmar Ratio
        if metrics.max_drawdown != 0:
            metrics.calmar_ratio = metrics.annualized_return / abs(metrics.max_drawdown)
            
    def _calculate_drawdown_metrics(
        self,
        metrics: PerformanceMetrics,
        portfolio_values: pd.Series
    ):
        """Calculate drawdown-related metrics."""
        # Calculate running maximum
        running_max = portfolio_values.expanding().max()
        
        # Calculate drawdown
        drawdown = (portfolio_values - running_max) / running_max
        
        # Max drawdown
        metrics.max_drawdown = drawdown.min()
        
        # Max drawdown duration
        drawdown_periods = []
        in_drawdown = False
        drawdown_start = None
        
        for i, dd in enumerate(drawdown):
            if dd < 0 and not in_drawdown:
                in_drawdown = True
                drawdown_start = i
            elif dd >= 0 and in_drawdown:
                in_drawdown = False
                if drawdown_start is not None:
                    drawdown_periods.append(i - drawdown_start)
        
        # Handle case where we end in drawdown
        if in_drawdown and drawdown_start is not None:
            drawdown_periods.append(len(drawdown) - drawdown_start)
        
        if drawdown_periods:
            metrics.max_drawdown_duration = max(drawdown_periods)
            metrics.avg_drawdown_duration = int(np.mean(drawdown_periods))
        
        # Average drawdown
        negative_drawdowns = drawdown[drawdown < 0]
        if len(negative_drawdowns) > 0:
            metrics.avg_drawdown = negative_drawdowns.mean()
            
        # Recovery factor
        if metrics.max_drawdown != 0:
            metrics.recovery_factor = metrics.total_return / abs(metrics.max_drawdown)
    
    def _calculate_trade_statistics(self, metrics: PerformanceMetrics, trades: List[Trade]):
        """Calculate trade-based statistics."""
        if not trades:
            return
            
        metrics.total_trades = len(trades)
        
        # Analyze P&L for each trade
        trade_pnls = []
        for trade in trades:
            if hasattr(trade, 'pnl') and trade.pnl is not None:
                trade_pnls.append(trade.pnl)
        
        if not trade_pnls:
            return
            
        trade_pnls = np.array(trade_pnls)
        
        # Win/Loss statistics
        winning_trades = trade_pnls[trade_pnls > 0]
        losing_trades = trade_pnls[trade_pnls < 0]
        
        metrics.winning_trades = len(winning_trades)
        metrics.losing_trades = len(losing_trades)
        metrics.win_rate = metrics.winning_trades / metrics.total_trades if metrics.total_trades > 0 else 0
        
        # Average win/loss
        metrics.avg_win = winning_trades.mean() if len(winning_trades) > 0 else 0
        metrics.avg_loss = losing_trades.mean() if len(losing_trades) > 0 else 0
        
        # Profit factor
        total_wins = winning_trades.sum() if len(winning_trades) > 0 else 0
        total_losses = abs(losing_trades.sum()) if len(losing_trades) > 0 else 0
        metrics.profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
        
        # Expectancy
        metrics.expectancy = trade_pnls.mean()
        
        # Kelly Criterion
        if metrics.avg_loss != 0:
            win_loss_ratio = abs(metrics.avg_win / metrics.avg_loss)
            metrics.kelly_criterion = metrics.win_rate - (1 - metrics.win_rate) / win_loss_ratio
    
    def _calculate_risk_metrics(self, metrics: PerformanceMetrics, returns: pd.Series):
        """Calculate risk-related metrics."""
        if len(returns) == 0:
            return
            
        # Volatility
        metrics.volatility = returns.std() * np.sqrt(252)
        
        # Downside volatility
        downside_returns = returns[returns < returns.mean()]
        if len(downside_returns) > 0:
            metrics.downside_volatility = downside_returns.std() * np.sqrt(252)
        
        # VaR and CVaR at 95% confidence
        if len(returns) > 0:
            metrics.var_95 = np.percentile(returns, 5)
            # CVaR is the mean of returns below VaR
            cvar_returns = returns[returns <= metrics.var_95]
            metrics.cvar_95 = cvar_returns.mean() if len(cvar_returns) > 0 else 0
        
        # Skewness and Kurtosis
        if len(returns) > 2:
            metrics.skewness = returns.skew()
            metrics.kurtosis = returns.kurtosis()
    
    def _calculate_benchmark_metrics(
        self,
        metrics: PerformanceMetrics,
        returns: pd.Series,
        benchmark_values: pd.Series
    ):
        """Calculate benchmark comparison metrics."""
        # Align the series
        common_index = returns.index.intersection(benchmark_values.index)
        if len(common_index) < 2:
            return
            
        portfolio_returns = returns.loc[common_index]
        benchmark_returns = benchmark_values.pct_change().dropna().loc[common_index]
        
        # Remove any remaining NaN values
        valid_data = pd.DataFrame({
            'portfolio': portfolio_returns,
            'benchmark': benchmark_returns
        }).dropna()
        
        if len(valid_data) < 2:
            return
            
        portfolio_returns = valid_data['portfolio']
        benchmark_returns = valid_data['benchmark']
        
        # Beta
        covariance = np.cov(portfolio_returns, benchmark_returns)[0, 1]
        benchmark_variance = np.var(benchmark_returns)
        
        if benchmark_variance > 0:
            metrics.beta = covariance / benchmark_variance
        
        # Alpha
        risk_free_daily = self.risk_free_rate / 252
        portfolio_excess = portfolio_returns.mean() - risk_free_daily
        benchmark_excess = benchmark_returns.mean() - risk_free_daily
        
        metrics.alpha = (portfolio_excess - metrics.beta * benchmark_excess) * 252
        
        # Tracking Error
        tracking_diff = portfolio_returns - benchmark_returns
        metrics.tracking_error = tracking_diff.std() * np.sqrt(252)
        
        # Correlation
        metrics.correlation = np.corrcoef(portfolio_returns, benchmark_returns)[0, 1]
        
        # Information Ratio
        if metrics.tracking_error > 0:
            metrics.information_ratio = (portfolio_returns.mean() - benchmark_returns.mean()) * 252 / metrics.tracking_error
        
        # Treynor Ratio
        if metrics.beta != 0:
            portfolio_excess_annual = portfolio_returns.mean() * 252 - self.risk_free_rate
            metrics.treynor_ratio = portfolio_excess_annual / metrics.beta
    
    def _calculate_additional_metrics(
        self,
        metrics: PerformanceMetrics,
        returns: pd.Series,
        portfolio_values: pd.Series
    ):
        """Calculate additional performance metrics."""
        if len(returns) == 0:
            return
            
        # Gain to Pain Ratio
        positive_returns = returns[returns > 0].sum()
        negative_returns = abs(returns[returns < 0].sum())
        
        if negative_returns > 0:
            metrics.gain_to_pain_ratio = positive_returns / negative_returns
        
        # Sterling Ratio
        if metrics.avg_drawdown != 0:
            metrics.sterling_ratio = metrics.annualized_return / abs(metrics.avg_drawdown)
        
        # Burke Ratio (using squared drawdowns)
        drawdown_series = self._calculate_drawdown_series(portfolio_values)
        squared_drawdowns = (drawdown_series[drawdown_series < 0] ** 2).sum()
        
        if squared_drawdowns > 0:
            metrics.burke_ratio = metrics.annualized_return / np.sqrt(squared_drawdowns / len(drawdown_series))
    
    def _calculate_drawdown_series(self, portfolio_values: pd.Series) -> pd.Series:
        """Calculate drawdown time series."""
        running_max = portfolio_values.expanding().max()
        return (portfolio_values - running_max) / running_max
    
    def calculate_rolling_metrics(
        self,
        portfolio_values: pd.Series,
        window: int = 252,
        frequency: PerformanceFrequency = PerformanceFrequency.DAILY
    ) -> pd.DataFrame:
        """
        Calculate rolling performance metrics.
        
        Args:
            portfolio_values: Time series of portfolio values
            window: Rolling window size in days
            frequency: Data frequency
            
        Returns:
            DataFrame with rolling metrics
        """
        returns = self._calculate_returns(portfolio_values)
        
        rolling_metrics = pd.DataFrame(index=returns.index)
        
        # Rolling Sharpe ratio
        excess_returns = returns - self.risk_free_rate / 252
        rolling_metrics['sharpe_ratio'] = (
            excess_returns.rolling(window).mean() / 
            returns.rolling(window).std() * np.sqrt(252)
        )
        
        # Rolling volatility
        rolling_metrics['volatility'] = returns.rolling(window).std() * np.sqrt(252)
        
        # Rolling max drawdown
        rolling_max_dd = []
        for i in range(len(portfolio_values)):
            start_idx = max(0, i - window + 1)
            window_values = portfolio_values.iloc[start_idx:i+1]
            if len(window_values) > 1:
                window_running_max = window_values.expanding().max()
                window_drawdown = (window_values - window_running_max) / window_running_max
                rolling_max_dd.append(window_drawdown.min())
            else:
                rolling_max_dd.append(0)
        
        rolling_metrics['max_drawdown'] = pd.Series(rolling_max_dd, index=portfolio_values.index)
        
        return rolling_metrics.dropna()
    
    def calculate_monthly_returns(
        self, 
        portfolio_values: pd.Series
    ) -> pd.DataFrame:
        """
        Calculate monthly returns table.
        
        Args:
            portfolio_values: Time series of portfolio values
            
        Returns:
            DataFrame with monthly returns
        """
        returns = self._calculate_returns(portfolio_values)
        
        # Group by year and month
        monthly_returns = returns.groupby([
            returns.index.year,
            returns.index.month
        ]).apply(lambda x: (1 + x).prod() - 1)
        
        # Create pivot table
        monthly_table = monthly_returns.unstack(level=1, fill_value=0)
        
        # Add month names as columns
        month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        
        monthly_table.columns = [month_names[i-1] for i in monthly_table.columns]
        
        # Add annual returns
        annual_returns = []
        for year in monthly_table.index:
            year_data = portfolio_values[portfolio_values.index.year == year]
            if len(year_data) > 1:
                annual_return = (year_data.iloc[-1] / year_data.iloc[0]) - 1
                annual_returns.append(annual_return)
            else:
                annual_returns.append(0)
        
        monthly_table['Annual'] = annual_returns
        
        return monthly_table