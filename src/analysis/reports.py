"""
Performance report generation module.

This module provides comprehensive performance report generation capabilities
including HTML/PDF reports, chart generation, and detailed statistical analysis.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import json

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.analysis.metrics import PerformanceMetrics, MetricsCalculator, PerformanceFrequency
from src.models.portfolio import Portfolio
from src.models.trading import Trade


class ReportFormat(Enum):
    """Report output format."""
    HTML = "html"
    PDF = "pdf"
    JSON = "json"
    DICT = "dict"


@dataclass
class ReportConfig:
    """Configuration for performance reports."""
    
    # Report content settings
    include_charts: bool = True
    include_monthly_returns: bool = True
    include_trade_analysis: bool = True
    include_rolling_metrics: bool = True
    include_benchmark_comparison: bool = True
    
    # Chart settings
    chart_width: int = 800
    chart_height: int = 400
    chart_theme: str = "default"
    
    # Report formatting
    decimal_places: int = 4
    percentage_format: str = ".2%"
    currency_format: str = "$,.2f"
    
    # Rolling metrics settings
    rolling_window: int = 252  # 1 year
    
    # Additional settings
    title: str = "Performance Analysis Report"
    subtitle: str = ""
    author: str = ""
    
    def to_dict(self) -> Dict:
        """Convert config to dictionary."""
        return {
            'include_charts': self.include_charts,
            'include_monthly_returns': self.include_monthly_returns,
            'include_trade_analysis': self.include_trade_analysis,
            'include_rolling_metrics': self.include_rolling_metrics,
            'include_benchmark_comparison': self.include_benchmark_comparison,
            'chart_width': self.chart_width,
            'chart_height': self.chart_height,
            'decimal_places': self.decimal_places,
            'title': self.title,
            'subtitle': self.subtitle
        }


class ChartGenerator:
    """Chart generation utilities for performance reports."""
    
    def __init__(self, config: ReportConfig):
        self.config = config
    
    def generate_equity_curve_data(
        self,
        portfolio_values: pd.Series,
        benchmark_values: Optional[pd.Series] = None
    ) -> Dict:
        """
        Generate data for equity curve chart.
        
        Args:
            portfolio_values: Portfolio value time series
            benchmark_values: Optional benchmark values
            
        Returns:
            Chart data dictionary
        """
        # Normalize values to start at 100
        normalized_portfolio = (portfolio_values / portfolio_values.iloc[0]) * 100
        
        chart_data = {
            'type': 'line_chart',
            'title': 'Equity Curve',
            'data': {
                'dates': portfolio_values.index.strftime('%Y-%m-%d').tolist(),
                'portfolio': normalized_portfolio.round(2).tolist(),
            },
            'config': {
                'width': self.config.chart_width,
                'height': self.config.chart_height
            }
        }
        
        if benchmark_values is not None:
            # Align benchmark with portfolio
            common_index = portfolio_values.index.intersection(benchmark_values.index)
            if len(common_index) > 1:
                aligned_benchmark = benchmark_values.loc[common_index]
                normalized_benchmark = (aligned_benchmark / aligned_benchmark.iloc[0]) * 100
                chart_data['data']['benchmark'] = normalized_benchmark.round(2).tolist()
        
        return chart_data
    
    def generate_drawdown_chart_data(self, portfolio_values: pd.Series) -> Dict:
        """
        Generate data for drawdown chart.
        
        Args:
            portfolio_values: Portfolio value time series
            
        Returns:
            Chart data dictionary
        """
        # Calculate drawdown
        running_max = portfolio_values.expanding().max()
        drawdown = (portfolio_values - running_max) / running_max * 100
        
        return {
            'type': 'area_chart',
            'title': 'Drawdown Analysis',
            'data': {
                'dates': portfolio_values.index.strftime('%Y-%m-%d').tolist(),
                'drawdown': drawdown.round(2).tolist()
            },
            'config': {
                'width': self.config.chart_width,
                'height': self.config.chart_height,
                'fill_negative': True
            }
        }
    
    def generate_returns_distribution_data(self, returns: pd.Series) -> Dict:
        """
        Generate data for returns distribution chart.
        
        Args:
            returns: Returns time series
            
        Returns:
            Chart data dictionary
        """
        # Calculate histogram data
        hist, bin_edges = np.histogram(returns * 100, bins=50, density=True)
        bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
        
        return {
            'type': 'histogram',
            'title': 'Returns Distribution',
            'data': {
                'bins': bin_centers.round(2).tolist(),
                'density': hist.round(4).tolist(),
                'mean': (returns.mean() * 100),
                'std': (returns.std() * 100)
            },
            'config': {
                'width': self.config.chart_width,
                'height': self.config.chart_height
            }
        }
    
    def generate_rolling_metrics_data(
        self,
        rolling_metrics: pd.DataFrame
    ) -> Dict:
        """
        Generate data for rolling metrics chart.
        
        Args:
            rolling_metrics: Rolling metrics DataFrame
            
        Returns:
            Chart data dictionary
        """
        return {
            'type': 'multi_line_chart',
            'title': 'Rolling Performance Metrics',
            'data': {
                'dates': rolling_metrics.index.strftime('%Y-%m-%d').tolist(),
                'sharpe_ratio': rolling_metrics['sharpe_ratio'].round(2).tolist(),
                'volatility': (rolling_metrics['volatility'] * 100).round(2).tolist(),
                'max_drawdown': (rolling_metrics['max_drawdown'] * 100).round(2).tolist()
            },
            'config': {
                'width': self.config.chart_width,
                'height': self.config.chart_height
            }
        }


class PerformanceReporter:
    """Performance report generator."""
    
    def __init__(self, config: Optional[ReportConfig] = None):
        """
        Initialize performance reporter.
        
        Args:
            config: Report configuration
        """
        self.config = config or ReportConfig()
        self.chart_generator = ChartGenerator(self.config)
        self.calculator = MetricsCalculator()
    
    def generate_report(
        self,
        portfolio_values: pd.Series,
        trades: Optional[List[Trade]] = None,
        benchmark_values: Optional[pd.Series] = None,
        format: ReportFormat = ReportFormat.DICT
    ) -> Union[Dict, str]:
        """
        Generate comprehensive performance report.
        
        Args:
            portfolio_values: Portfolio value time series
            trades: List of trades
            benchmark_values: Benchmark values for comparison
            format: Output format
            
        Returns:
            Report in specified format
        """
        # Calculate metrics
        metrics = self.calculator.calculate_metrics(
            portfolio_values, trades, benchmark_values
        )
        
        # Build report structure
        report = {
            'metadata': self._generate_metadata(),
            'summary': self._generate_summary(metrics),
            'performance_metrics': self._generate_performance_section(metrics),
            'risk_metrics': self._generate_risk_section(metrics),
            'trade_analysis': self._generate_trade_analysis(metrics, trades) if trades else None,
            'benchmark_comparison': self._generate_benchmark_section(metrics) if benchmark_values is not None else None,
            'monthly_returns': self._generate_monthly_returns(portfolio_values) if self.config.include_monthly_returns else None,
            'rolling_metrics': self._generate_rolling_analysis(portfolio_values) if self.config.include_rolling_metrics else None,
            'charts': self._generate_charts(portfolio_values, benchmark_values) if self.config.include_charts else None
        }
        
        # Format output
        if format == ReportFormat.JSON:
            return self._format_as_json(report)
        elif format == ReportFormat.HTML:
            return self._format_as_html(report)
        elif format == ReportFormat.PDF:
            return self._format_as_pdf(report, portfolio_values, benchmark_values)
        else:
            return report
    
    def _generate_metadata(self) -> Dict:
        """Generate report metadata."""
        return {
            'generated_at': datetime.now().isoformat(),
            'title': self.config.title,
            'subtitle': self.config.subtitle,
            'author': self.config.author,
            'version': '1.0.0'
        }
    
    def _generate_summary(self, metrics: PerformanceMetrics) -> Dict:
        """Generate executive summary."""
        return {
            'period': {
                'start_date': metrics.start_date.strftime('%Y-%m-%d') if metrics.start_date else None,
                'end_date': metrics.end_date.strftime('%Y-%m-%d') if metrics.end_date else None,
                'duration_days': metrics.period_length
            },
            'key_metrics': {
                'total_return': round(metrics.total_return * 100, 2),
                'annualized_return': round(metrics.annualized_return * 100, 2),
                'max_drawdown': round(metrics.max_drawdown * 100, 2),
                'sharpe_ratio': round(metrics.sharpe_ratio, 3),
                'volatility': round(metrics.volatility * 100, 2),
                'win_rate': round(metrics.win_rate * 100, 1) if metrics.total_trades > 0 else None
            }
        }
    
    def _generate_performance_section(self, metrics: PerformanceMetrics) -> Dict:
        """Generate performance metrics section."""
        return {
            'returns': {
                'total_return_pct': round(metrics.total_return * 100, 2),
                'annualized_return_pct': round(metrics.annualized_return * 100, 2),
                'cagr_pct': round(metrics.cagr * 100, 2),
                'daily_return_mean_pct': round(metrics.daily_return_mean * 100, 4),
                'daily_return_std_pct': round(metrics.daily_return_std * 100, 4)
            },
            'risk_adjusted': {
                'sharpe_ratio': round(metrics.sharpe_ratio, 3),
                'sortino_ratio': round(metrics.sortino_ratio, 3),
                'calmar_ratio': round(metrics.calmar_ratio, 3),
                'information_ratio': round(metrics.information_ratio, 3),
                'treynor_ratio': round(metrics.treynor_ratio, 3)
            },
            'drawdowns': {
                'max_drawdown_pct': round(metrics.max_drawdown * 100, 2),
                'max_drawdown_duration_days': metrics.max_drawdown_duration,
                'avg_drawdown_pct': round(metrics.avg_drawdown * 100, 2),
                'avg_drawdown_duration_days': metrics.avg_drawdown_duration,
                'recovery_factor': round(metrics.recovery_factor, 2)
            }
        }
    
    def _generate_risk_section(self, metrics: PerformanceMetrics) -> Dict:
        """Generate risk metrics section."""
        return {
            'volatility_metrics': {
                'volatility_pct': round(metrics.volatility * 100, 2),
                'downside_volatility_pct': round(metrics.downside_volatility * 100, 2)
            },
            'var_metrics': {
                'var_95_pct': round(metrics.var_95 * 100, 2),
                'cvar_95_pct': round(metrics.cvar_95 * 100, 2)
            },
            'distribution': {
                'skewness': round(metrics.skewness, 3),
                'kurtosis': round(metrics.kurtosis, 3)
            },
            'additional': {
                'gain_to_pain_ratio': round(metrics.gain_to_pain_ratio, 2),
                'sterling_ratio': round(metrics.sterling_ratio, 2),
                'burke_ratio': round(metrics.burke_ratio, 2)
            }
        }
    
    def _generate_trade_analysis(
        self,
        metrics: PerformanceMetrics,
        trades: Optional[List[Trade]]
    ) -> Optional[Dict]:
        """Generate trade analysis section."""
        if not trades or metrics.total_trades == 0:
            return None
            
        return {
            'trade_count': {
                'total_trades': metrics.total_trades,
                'winning_trades': metrics.winning_trades,
                'losing_trades': metrics.losing_trades,
                'win_rate_pct': round(metrics.win_rate * 100, 1)
            },
            'trade_performance': {
                'avg_win': round(metrics.avg_win, 2),
                'avg_loss': round(metrics.avg_loss, 2),
                'profit_factor': round(metrics.profit_factor, 2),
                'expectancy': round(metrics.expectancy, 2)
            },
            'position_sizing': {
                'kelly_criterion': round(metrics.kelly_criterion, 3)
            }
        }
    
    def _generate_benchmark_section(self, metrics: PerformanceMetrics) -> Dict:
        """Generate benchmark comparison section."""
        return {
            'relative_performance': {
                'alpha_pct': round(metrics.alpha * 100, 2),
                'beta': round(metrics.beta, 3),
                'correlation': round(metrics.correlation, 3),
                'tracking_error_pct': round(metrics.tracking_error * 100, 2),
                'information_ratio': round(metrics.information_ratio, 3)
            }
        }
    
    def _generate_monthly_returns(self, portfolio_values: pd.Series) -> Dict:
        """Generate monthly returns table."""
        monthly_table = self.calculator.calculate_monthly_returns(portfolio_values)
        
        return {
            'table': monthly_table.round(4).to_dict(),
            'summary': {
                'positive_months': (monthly_table.iloc[:, :-1] > 0).sum().sum(),
                'negative_months': (monthly_table.iloc[:, :-1] < 0).sum().sum(),
                'best_month': monthly_table.iloc[:, :-1].max().max(),
                'worst_month': monthly_table.iloc[:, :-1].min().min()
            }
        }
    
    def _generate_rolling_analysis(self, portfolio_values: pd.Series) -> Dict:
        """Generate rolling metrics analysis."""
        rolling_metrics = self.calculator.calculate_rolling_metrics(
            portfolio_values, self.config.rolling_window
        )
        
        return {
            'metrics': {
                'avg_rolling_sharpe': rolling_metrics['sharpe_ratio'].mean(),
                'avg_rolling_volatility': rolling_metrics['volatility'].mean(),
                'avg_rolling_max_drawdown': rolling_metrics['max_drawdown'].mean()
            },
            'trends': {
                'sharpe_trend': self._calculate_trend(rolling_metrics['sharpe_ratio']),
                'volatility_trend': self._calculate_trend(rolling_metrics['volatility']),
                'drawdown_trend': self._calculate_trend(rolling_metrics['max_drawdown'])
            }
        }
    
    def _generate_charts(
        self,
        portfolio_values: pd.Series,
        benchmark_values: Optional[pd.Series]
    ) -> Dict:
        """Generate chart data."""
        returns = portfolio_values.pct_change().dropna()
        
        charts = {
            'equity_curve': self.chart_generator.generate_equity_curve_data(
                portfolio_values, benchmark_values
            ),
            'drawdown': self.chart_generator.generate_drawdown_chart_data(portfolio_values),
            'returns_distribution': self.chart_generator.generate_returns_distribution_data(returns)
        }
        
        # Add rolling metrics chart if enabled
        if self.config.include_rolling_metrics:
            rolling_metrics = self.calculator.calculate_rolling_metrics(
                portfolio_values, self.config.rolling_window
            )
            charts['rolling_metrics'] = self.chart_generator.generate_rolling_metrics_data(
                rolling_metrics
            )
        
        return charts
    
    def _calculate_trend(self, series: pd.Series) -> str:
        """Calculate trend direction for a time series."""
        if len(series) < 2:
            return "insufficient_data"
            
        # Use linear regression to determine trend
        x = np.arange(len(series))
        slope = np.polyfit(x, series, 1)[0]
        
        if slope > 0.001:
            return "increasing"
        elif slope < -0.001:
            return "decreasing"
        else:
            return "stable"
    
    def _format_as_json(self, report: Dict) -> str:
        """Format report as JSON string."""
        def json_serializer(obj):
            if isinstance(obj, (np.integer, np.floating)):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif pd.isna(obj):
                return None
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
        
        return json.dumps(report, indent=2, default=json_serializer)
    
    def _format_as_html(self, report: Dict) -> str:
        """Format report as HTML string."""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{report['metadata']['title']}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .section {{ margin-bottom: 30px; }}
                .metric {{ display: inline-block; margin: 10px; padding: 15px; border: 1px solid #ddd; }}
                .metric-name {{ font-weight: bold; }}
                .metric-value {{ font-size: 1.2em; color: #333; }}
                table {{ width: 100%; border-collapse: collapse; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{report['metadata']['title']}</h1>
                <p>{report['metadata']['subtitle']}</p>
                <p>Generated on {report['metadata']['generated_at'][:10]}</p>
            </div>
            
            <div class="section">
                <h2>Executive Summary</h2>
                <div class="metric">
                    <div class="metric-name">Total Return</div>
                    <div class="metric-value">{report['summary']['key_metrics']['total_return']:.2f}%</div>
                </div>
                <div class="metric">
                    <div class="metric-name">Sharpe Ratio</div>
                    <div class="metric-value">{report['summary']['key_metrics']['sharpe_ratio']:.3f}</div>
                </div>
                <div class="metric">
                    <div class="metric-name">Max Drawdown</div>
                    <div class="metric-value">{report['summary']['key_metrics']['max_drawdown']:.2f}%</div>
                </div>
            </div>
            
            <div class="section">
                <h2>Performance Details</h2>
                <p>This is a simplified HTML view. Full report data is available in JSON format.</p>
            </div>
        </body>
        </html>
        """
        return html
    
    def _format_as_pdf(
        self, 
        report: Dict, 
        portfolio_values: pd.Series,
        benchmark_values: Optional[pd.Series] = None
    ) -> str:
        """
        Format report as PDF document.
        
        Args:
            report: Report data dictionary
            portfolio_values: Portfolio value series for charts
            benchmark_values: Optional benchmark values
            
        Returns:
            PDF file path (saved to disk)
        """
        try:
            # Try to import required libraries
            try:
                from reportlab.lib.pagesizes import letter, A4
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib.units import inch
                from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
                from reportlab.lib import colors
                from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
                import matplotlib.pyplot as plt
                import io
                from reportlab.platypus import Image
            except ImportError:
                # If reportlab is not available, return a simple text-based report
                return self._format_as_text_pdf_fallback(report)
            
            # Create PDF buffer
            pdf_path = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            doc = SimpleDocTemplate(pdf_path, pagesize=letter, topMargin=1*inch)
            
            # Build the story (content)
            story = []
            styles = getSampleStyleSheet()
            
            # Title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                alignment=TA_CENTER,
                spaceAfter=20
            )
            story.append(Paragraph(self.config.title, title_style))
            story.append(Spacer(1, 12))
            
            # Executive Summary
            story.append(Paragraph("Executive Summary", styles['Heading2']))
            
            summary_data = [
                ["Metric", "Value"],
                ["Period", f"{report['summary']['period']['start_date']} to {report['summary']['period']['end_date']}"],
                ["Total Return", f"{report['summary']['key_metrics']['total_return']:.2f}%"],
                ["Annualized Return", f"{report['summary']['key_metrics']['annualized_return']:.2f}%"],
                ["Max Drawdown", f"{report['summary']['key_metrics']['max_drawdown']:.2f}%"],
                ["Sharpe Ratio", f"{report['summary']['key_metrics']['sharpe_ratio']:.3f}"],
                ["Volatility", f"{report['summary']['key_metrics']['volatility']:.2f}%"]
            ]
            
            summary_table = Table(summary_data, colWidths=[2.5*inch, 2*inch])
            summary_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(summary_table)
            story.append(Spacer(1, 20))
            
            # Performance Metrics
            story.append(Paragraph("Performance Metrics", styles['Heading2']))
            
            perf_data = [
                ["Return Metric", "Value"],
                ["Total Return", f"{report['performance_metrics']['returns']['total_return_pct']:.2f}%"],
                ["Annualized Return", f"{report['performance_metrics']['returns']['annualized_return_pct']:.2f}%"],
                ["CAGR", f"{report['performance_metrics']['returns']['cagr_pct']:.2f}%"],
                ["Daily Mean Return", f"{report['performance_metrics']['returns']['daily_return_mean_pct']:.4f}%"],
                ["Daily Volatility", f"{report['performance_metrics']['returns']['daily_return_std_pct']:.4f}%"]
            ]
            
            perf_table = Table(perf_data, colWidths=[2.5*inch, 2*inch])
            perf_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightcyan),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(perf_table)
            story.append(Spacer(1, 20))
            
            # Risk-Adjusted Metrics
            story.append(Paragraph("Risk-Adjusted Metrics", styles['Heading2']))
            
            risk_adj_data = [
                ["Metric", "Value"],
                ["Sharpe Ratio", f"{report['performance_metrics']['risk_adjusted']['sharpe_ratio']:.3f}"],
                ["Sortino Ratio", f"{report['performance_metrics']['risk_adjusted']['sortino_ratio']:.3f}"],
                ["Calmar Ratio", f"{report['performance_metrics']['risk_adjusted']['calmar_ratio']:.3f}"],
                ["Information Ratio", f"{report['performance_metrics']['risk_adjusted']['information_ratio']:.3f}"],
                ["Treynor Ratio", f"{report['performance_metrics']['risk_adjusted']['treynor_ratio']:.3f}"]
            ]
            
            risk_adj_table = Table(risk_adj_data, colWidths=[2.5*inch, 2*inch])
            risk_adj_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgreen),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(risk_adj_table)
            story.append(Spacer(1, 20))
            
            # Add chart if possible
            if self.config.include_charts:
                try:
                    # Create a simple equity curve chart
                    plt.figure(figsize=(8, 4))
                    normalized_portfolio = (portfolio_values / portfolio_values.iloc[0]) * 100
                    plt.plot(normalized_portfolio.index, normalized_portfolio.values, 
                            linewidth=2, label='Portfolio')
                    
                    if benchmark_values is not None:
                        normalized_benchmark = (benchmark_values / benchmark_values.iloc[0]) * 100
                        plt.plot(normalized_benchmark.index, normalized_benchmark.values, 
                                linewidth=2, label='Benchmark', alpha=0.7)
                        plt.legend()
                    
                    plt.title('Portfolio Performance')
                    plt.xlabel('Date')
                    plt.ylabel('Normalized Value')
                    plt.grid(True, alpha=0.3)
                    plt.tight_layout()
                    
                    # Save chart to buffer
                    img_buffer = io.BytesIO()
                    plt.savefig(img_buffer, format='png', dpi=150, bbox_inches='tight')
                    img_buffer.seek(0)
                    plt.close()
                    
                    # Add chart to report
                    story.append(Paragraph("Portfolio Performance Chart", styles['Heading2']))
                    chart_image = Image(img_buffer, width=6*inch, height=3*inch)
                    story.append(chart_image)
                    
                except Exception as e:
                    # If chart creation fails, add a note
                    story.append(Paragraph(f"Chart generation failed: {str(e)}", styles['Normal']))
            
            # Build PDF
            doc.build(story)
            return pdf_path
            
        except Exception as e:
            # Fallback to text-based PDF
            return self._format_as_text_pdf_fallback(report)
    
    def _format_as_text_pdf_fallback(self, report: Dict) -> str:
        """
        Fallback method for PDF generation when reportlab is not available.
        
        Args:
            report: Report data dictionary
            
        Returns:
            Text-based report content (not actual PDF)
        """
        lines = [
            "PERFORMANCE ANALYSIS REPORT",
            "=" * 50,
            "",
            f"Generated: {report['metadata']['generated_at']}",
            f"Title: {report['metadata']['title']}",
            "",
            "EXECUTIVE SUMMARY",
            "-" * 20,
            f"Period: {report['summary']['period']['start_date']} to {report['summary']['period']['end_date']}",
            f"Total Return: {report['summary']['key_metrics']['total_return']:.2f}%",
            f"Annualized Return: {report['summary']['key_metrics']['annualized_return']:.2f}%",
            f"Max Drawdown: {report['summary']['key_metrics']['max_drawdown']:.2f}%",
            f"Sharpe Ratio: {report['summary']['key_metrics']['sharpe_ratio']:.3f}",
            f"Volatility: {report['summary']['key_metrics']['volatility']:.2f}%",
            "",
            "RISK-ADJUSTED METRICS",
            "-" * 20,
            f"Sharpe Ratio: {report['performance_metrics']['risk_adjusted']['sharpe_ratio']:.3f}",
            f"Sortino Ratio: {report['performance_metrics']['risk_adjusted']['sortino_ratio']:.3f}",
            f"Calmar Ratio: {report['performance_metrics']['risk_adjusted']['calmar_ratio']:.3f}",
            f"Information Ratio: {report['performance_metrics']['risk_adjusted']['information_ratio']:.3f}",
            f"Treynor Ratio: {report['performance_metrics']['risk_adjusted']['treynor_ratio']:.3f}",
            "",
            "Note: Full PDF generation requires reportlab library.",
            "This is a text-based fallback report.",
        ]
        
        return "\n".join(lines)
    
    def save_report(
        self,
        report: Union[Dict, str],
        filepath: str,
        format: ReportFormat = ReportFormat.JSON
    ):
        """
        Save report to file.
        
        Args:
            report: Report data
            filepath: Output file path
            format: Output format
        """
        if format == ReportFormat.JSON:
            content = report if isinstance(report, str) else self._format_as_json(report)
            with open(filepath, 'w') as f:
                f.write(content)
        elif format == ReportFormat.HTML:
            content = report if isinstance(report, str) else self._format_as_html(report)
            with open(filepath, 'w') as f:
                f.write(content)
        elif format == ReportFormat.PDF:
            if isinstance(report, str) and report.endswith('.pdf'):
                # PDF was already saved, just return the path
                return report
            else:
                # This shouldn't happen with current implementation, but handle it
                raise ValueError("PDF format requires calling generate_report with ReportFormat.PDF first")
        else:
            content = json.dumps(report, indent=2)
            with open(filepath, 'w') as f:
                f.write(content)