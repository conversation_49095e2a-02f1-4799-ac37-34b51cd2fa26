"""
Strategy comparison analysis module.

This module provides comprehensive strategy comparison capabilities including
multi-strategy performance comparison, correlation analysis, and portfolio
optimization recommendations.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import itertools

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.analysis.metrics import PerformanceMetrics, MetricsCalculator
from src.models.trading import Trade


class ComparisonMethod(Enum):
    """Strategy comparison methods."""
    SHARPE_RATIO = "sharpe_ratio"
    SORTINO_RATIO = "sortino_ratio" 
    CALMAR_RATIO = "calmar_ratio"
    TOTAL_RETURN = "total_return"
    MAX_DRAWDOWN = "max_drawdown"
    WIN_RATE = "win_rate"
    PROFIT_FACTOR = "profit_factor"


class RankingMethod(Enum):
    """Strategy ranking methods."""
    WEIGHTED_SCORE = "weighted_score"
    RISK_ADJUSTED_RETURN = "risk_adjusted_return"
    CONSISTENCY = "consistency"
    DIVERSIFICATION_BENEFIT = "diversification_benefit"


@dataclass
class StrategyData:
    """Container for strategy data."""
    name: str
    portfolio_values: pd.Series
    trades: Optional[List[Trade]] = None
    benchmark_values: Optional[pd.Series] = None
    metadata: Dict = field(default_factory=dict)


@dataclass
class ComparisonResult:
    """Results of strategy comparison analysis."""
    
    # Individual strategy metrics
    strategy_metrics: Dict[str, PerformanceMetrics] = field(default_factory=dict)
    
    # Comparison matrix
    comparison_matrix: pd.DataFrame = field(default_factory=pd.DataFrame)
    
    # Rankings
    rankings: Dict[str, List[str]] = field(default_factory=dict)
    
    # Correlation analysis
    correlation_matrix: pd.DataFrame = field(default_factory=pd.DataFrame)
    
    # Portfolio optimization
    optimal_weights: Dict[str, float] = field(default_factory=dict)
    
    # Combined portfolio metrics
    combined_metrics: Optional[PerformanceMetrics] = None
    
    # Statistical significance tests
    significance_tests: Dict = field(default_factory=dict)
    
    # Recommendations
    recommendations: List[Dict] = field(default_factory=list)
    
    def to_dict(self) -> Dict:
        """Convert result to dictionary."""
        return {
            'strategy_metrics': {
                name: metrics.to_dict() 
                for name, metrics in self.strategy_metrics.items()
            },
            'comparison_matrix': self.comparison_matrix.to_dict() if not self.comparison_matrix.empty else {},
            'rankings': self.rankings,
            'correlation_matrix': self.correlation_matrix.to_dict() if not self.correlation_matrix.empty else {},
            'optimal_weights': self.optimal_weights,
            'combined_metrics': self.combined_metrics.to_dict() if self.combined_metrics else None,
            'significance_tests': self.significance_tests,
            'recommendations': self.recommendations
        }


class StrategyComparator:
    """Strategy comparison and analysis engine."""
    
    def __init__(self, risk_free_rate: float = 0.02):
        """
        Initialize strategy comparator.
        
        Args:
            risk_free_rate: Risk-free rate for calculations
        """
        self.risk_free_rate = risk_free_rate
        self.calculator = MetricsCalculator(risk_free_rate)
    
    def compare_strategies(
        self,
        strategies: List[StrategyData],
        comparison_methods: List[ComparisonMethod] = None,
        ranking_method: RankingMethod = RankingMethod.WEIGHTED_SCORE
    ) -> ComparisonResult:
        """
        Perform comprehensive strategy comparison.
        
        Args:
            strategies: List of strategy data
            comparison_methods: Methods to use for comparison
            ranking_method: Method for ranking strategies
            
        Returns:
            ComparisonResult with analysis
        """
        if len(strategies) < 2:
            raise ValueError("Need at least 2 strategies for comparison")
        
        if comparison_methods is None:
            comparison_methods = [
                ComparisonMethod.SHARPE_RATIO,
                ComparisonMethod.TOTAL_RETURN,
                ComparisonMethod.MAX_DRAWDOWN,
                ComparisonMethod.WIN_RATE
            ]
        
        result = ComparisonResult()
        
        # Calculate individual strategy metrics
        result.strategy_metrics = self._calculate_individual_metrics(strategies)
        
        # Create comparison matrix
        result.comparison_matrix = self._create_comparison_matrix(
            result.strategy_metrics, comparison_methods
        )
        
        # Calculate rankings
        result.rankings = self._calculate_rankings(
            result.strategy_metrics, comparison_methods, ranking_method
        )
        
        # Correlation analysis
        result.correlation_matrix = self._calculate_correlation_matrix(strategies)
        
        # Portfolio optimization
        result.optimal_weights = self._calculate_optimal_weights(
            strategies, result.strategy_metrics
        )
        
        # Combined portfolio analysis
        if result.optimal_weights:
            result.combined_metrics = self._calculate_combined_metrics(
                strategies, result.optimal_weights
            )
        
        # Statistical significance tests
        result.significance_tests = self._perform_significance_tests(strategies)
        
        # Generate recommendations
        result.recommendations = self._generate_recommendations(result)
        
        return result
    
    def _calculate_individual_metrics(
        self,
        strategies: List[StrategyData]
    ) -> Dict[str, PerformanceMetrics]:
        """Calculate metrics for individual strategies."""
        metrics = {}
        
        for strategy in strategies:
            metrics[strategy.name] = self.calculator.calculate_metrics(
                strategy.portfolio_values,
                strategy.trades,
                strategy.benchmark_values
            )
        
        return metrics
    
    def _create_comparison_matrix(
        self,
        strategy_metrics: Dict[str, PerformanceMetrics],
        comparison_methods: List[ComparisonMethod]
    ) -> pd.DataFrame:
        """Create comparison matrix for strategies."""
        strategy_names = list(strategy_metrics.keys())
        metric_data = []
        
        for method in comparison_methods:
            row_data = {}
            for name in strategy_names:
                metrics = strategy_metrics[name]
                
                if method == ComparisonMethod.SHARPE_RATIO:
                    row_data[name] = metrics.sharpe_ratio
                elif method == ComparisonMethod.SORTINO_RATIO:
                    row_data[name] = metrics.sortino_ratio
                elif method == ComparisonMethod.CALMAR_RATIO:
                    row_data[name] = metrics.calmar_ratio
                elif method == ComparisonMethod.TOTAL_RETURN:
                    row_data[name] = metrics.total_return
                elif method == ComparisonMethod.MAX_DRAWDOWN:
                    row_data[name] = metrics.max_drawdown
                elif method == ComparisonMethod.WIN_RATE:
                    row_data[name] = metrics.win_rate
                elif method == ComparisonMethod.PROFIT_FACTOR:
                    row_data[name] = metrics.profit_factor
            
            metric_data.append(row_data)
        
        comparison_df = pd.DataFrame(
            metric_data,
            index=[method.value for method in comparison_methods]
        )
        
        return comparison_df
    
    def _calculate_rankings(
        self,
        strategy_metrics: Dict[str, PerformanceMetrics],
        comparison_methods: List[ComparisonMethod],
        ranking_method: RankingMethod
    ) -> Dict[str, List[str]]:
        """Calculate strategy rankings using various methods."""
        rankings = {}
        strategy_names = list(strategy_metrics.keys())
        
        # Individual metric rankings
        for method in comparison_methods:
            metric_values = []
            
            for name in strategy_names:
                metrics = strategy_metrics[name]
                
                if method == ComparisonMethod.SHARPE_RATIO:
                    metric_values.append((name, metrics.sharpe_ratio))
                elif method == ComparisonMethod.SORTINO_RATIO:
                    metric_values.append((name, metrics.sortino_ratio))
                elif method == ComparisonMethod.CALMAR_RATIO:
                    metric_values.append((name, metrics.calmar_ratio))
                elif method == ComparisonMethod.TOTAL_RETURN:
                    metric_values.append((name, metrics.total_return))
                elif method == ComparisonMethod.MAX_DRAWDOWN:
                    # For drawdown, lower (less negative) is better
                    metric_values.append((name, -metrics.max_drawdown))
                elif method == ComparisonMethod.WIN_RATE:
                    metric_values.append((name, metrics.win_rate))
                elif method == ComparisonMethod.PROFIT_FACTOR:
                    metric_values.append((name, metrics.profit_factor))
            
            # Sort by value (descending for most metrics)
            metric_values.sort(key=lambda x: x[1], reverse=True)
            rankings[method.value] = [name for name, _ in metric_values]
        
        # Overall ranking based on method
        if ranking_method == RankingMethod.WEIGHTED_SCORE:
            rankings['overall'] = self._calculate_weighted_ranking(strategy_metrics)
        elif ranking_method == RankingMethod.RISK_ADJUSTED_RETURN:
            rankings['overall'] = self._calculate_risk_adjusted_ranking(strategy_metrics)
        elif ranking_method == RankingMethod.CONSISTENCY:
            rankings['overall'] = self._calculate_consistency_ranking(strategy_metrics)
        
        return rankings
    
    def _calculate_weighted_ranking(
        self,
        strategy_metrics: Dict[str, PerformanceMetrics]
    ) -> List[str]:
        """Calculate weighted score ranking."""
        # Define weights for different metrics
        weights = {
            'sharpe_ratio': 0.3,
            'total_return': 0.2,
            'max_drawdown': 0.2,  # Will be inverted
            'win_rate': 0.1,
            'calmar_ratio': 0.1,
            'sortino_ratio': 0.1
        }
        
        scores = {}
        
        # Normalize metrics across strategies
        all_metrics = list(strategy_metrics.values())
        
        # Get ranges for normalization
        sharpe_values = [m.sharpe_ratio for m in all_metrics]
        return_values = [m.total_return for m in all_metrics]
        drawdown_values = [m.max_drawdown for m in all_metrics]
        winrate_values = [m.win_rate for m in all_metrics]
        calmar_values = [m.calmar_ratio for m in all_metrics]
        sortino_values = [m.sortino_ratio for m in all_metrics]
        
        sharpe_range = max(sharpe_values) - min(sharpe_values)
        return_range = max(return_values) - min(return_values)
        drawdown_range = max(drawdown_values) - min(drawdown_values)
        winrate_range = max(winrate_values) - min(winrate_values)
        calmar_range = max(calmar_values) - min(calmar_values)
        sortino_range = max(sortino_values) - min(sortino_values)
        
        for name, metrics in strategy_metrics.items():
            score = 0
            
            # Normalize and weight each metric
            if sharpe_range > 0:
                norm_sharpe = (metrics.sharpe_ratio - min(sharpe_values)) / sharpe_range
                score += weights['sharpe_ratio'] * norm_sharpe
            
            if return_range > 0:
                norm_return = (metrics.total_return - min(return_values)) / return_range
                score += weights['total_return'] * norm_return
            
            if drawdown_range > 0:
                # For drawdown, lower (less negative) is better
                norm_drawdown = (max(drawdown_values) - metrics.max_drawdown) / drawdown_range
                score += weights['max_drawdown'] * norm_drawdown
            
            if winrate_range > 0:
                norm_winrate = (metrics.win_rate - min(winrate_values)) / winrate_range
                score += weights['win_rate'] * norm_winrate
            
            if calmar_range > 0:
                norm_calmar = (metrics.calmar_ratio - min(calmar_values)) / calmar_range
                score += weights['calmar_ratio'] * norm_calmar
                
            if sortino_range > 0:
                norm_sortino = (metrics.sortino_ratio - min(sortino_values)) / sortino_range
                score += weights['sortino_ratio'] * norm_sortino
            
            scores[name] = score
        
        # Sort by score (descending)
        return sorted(scores.keys(), key=lambda x: scores[x], reverse=True)
    
    def _calculate_risk_adjusted_ranking(
        self,
        strategy_metrics: Dict[str, PerformanceMetrics]
    ) -> List[str]:
        """Calculate ranking based on risk-adjusted returns."""
        risk_adjusted_scores = {}
        
        for name, metrics in strategy_metrics.items():
            # Use Sharpe ratio as primary, but consider other factors
            score = metrics.sharpe_ratio
            
            # Penalty for high drawdown
            if metrics.max_drawdown < -0.1:  # More than 10% drawdown
                score *= (1 + metrics.max_drawdown)  # Reduce score
            
            # Bonus for low volatility
            if metrics.volatility < 0.15:  # Less than 15% volatility
                score *= 1.1
            
            risk_adjusted_scores[name] = score
        
        return sorted(risk_adjusted_scores.keys(), 
                     key=lambda x: risk_adjusted_scores[x], reverse=True)
    
    def _calculate_consistency_ranking(
        self,
        strategy_metrics: Dict[str, PerformanceMetrics]
    ) -> List[str]:
        """Calculate ranking based on consistency metrics."""
        consistency_scores = {}
        
        for name, metrics in strategy_metrics.items():
            score = 0
            
            # Reward high win rate
            score += metrics.win_rate * 0.4
            
            # Reward low volatility
            if metrics.volatility > 0:
                score += (1 / metrics.volatility) * 0.2
            
            # Reward low drawdown
            score += (1 / max(abs(metrics.max_drawdown), 0.01)) * 0.2
            
            # Reward positive skewness
            if metrics.skewness > 0:
                score += metrics.skewness * 0.1
            
            # Reward low kurtosis (less extreme returns)
            if metrics.kurtosis < 3:
                score += (3 - metrics.kurtosis) * 0.1
            
            consistency_scores[name] = score
        
        return sorted(consistency_scores.keys(),
                     key=lambda x: consistency_scores[x], reverse=True)
    
    def _calculate_correlation_matrix(
        self,
        strategies: List[StrategyData]
    ) -> pd.DataFrame:
        """Calculate correlation matrix between strategies."""
        returns_data = {}
        
        # Calculate returns for each strategy
        for strategy in strategies:
            returns = strategy.portfolio_values.pct_change().dropna()
            returns_data[strategy.name] = returns
        
        # Create DataFrame and calculate correlation
        returns_df = pd.DataFrame(returns_data)
        
        # Handle misaligned indices
        returns_df = returns_df.dropna()
        
        if returns_df.empty:
            return pd.DataFrame()
        
        return returns_df.corr()
    
    def _calculate_optimal_weights(
        self,
        strategies: List[StrategyData],
        strategy_metrics: Dict[str, PerformanceMetrics]
    ) -> Dict[str, float]:
        """Calculate optimal portfolio weights using mean-variance optimization."""
        try:
            # Get returns data
            returns_data = {}
            for strategy in strategies:
                returns = strategy.portfolio_values.pct_change().dropna()
                returns_data[strategy.name] = returns
            
            returns_df = pd.DataFrame(returns_data).dropna()
            
            if len(returns_df) < 10:  # Need sufficient data
                return self._calculate_equal_weights(strategies)
            
            # Calculate expected returns and covariance matrix
            expected_returns = returns_df.mean() * 252  # Annualized
            cov_matrix = returns_df.cov() * 252  # Annualized
            
            # Simple mean-variance optimization (equal risk contribution)
            n_assets = len(strategies)
            
            # Equal risk contribution approach
            volatilities = np.sqrt(np.diag(cov_matrix))
            inv_vol_weights = 1.0 / volatilities
            weights = inv_vol_weights / inv_vol_weights.sum()
            
            return dict(zip([s.name for s in strategies], weights))
            
        except Exception:
            # Fallback to equal weights
            return self._calculate_equal_weights(strategies)
    
    def _calculate_equal_weights(self, strategies: List[StrategyData]) -> Dict[str, float]:
        """Calculate equal weights for strategies."""
        n_strategies = len(strategies)
        equal_weight = 1.0 / n_strategies
        return {strategy.name: equal_weight for strategy in strategies}
    
    def _calculate_combined_metrics(
        self,
        strategies: List[StrategyData],
        weights: Dict[str, float]
    ) -> PerformanceMetrics:
        """Calculate metrics for the combined portfolio."""
        # Create combined portfolio values
        combined_values = None
        
        for strategy in strategies:
            weight = weights.get(strategy.name, 0)
            if weight > 0:
                strategy_contribution = strategy.portfolio_values * weight
                if combined_values is None:
                    combined_values = strategy_contribution
                else:
                    # Align indices
                    common_index = combined_values.index.intersection(strategy_contribution.index)
                    combined_values = combined_values.loc[common_index] + strategy_contribution.loc[common_index]
        
        if combined_values is None or len(combined_values) < 2:
            return PerformanceMetrics()
        
        # Calculate metrics for combined portfolio
        return self.calculator.calculate_metrics(combined_values)
    
    def _perform_significance_tests(
        self,
        strategies: List[StrategyData]
    ) -> Dict:
        """Perform statistical significance tests."""
        # This is a simplified implementation
        # In practice, you'd use proper statistical tests like t-tests
        
        results = {}
        
        # Compare each pair of strategies
        for i, strategy1 in enumerate(strategies):
            for j, strategy2 in enumerate(strategies[i+1:], i+1):
                returns1 = strategy1.portfolio_values.pct_change().dropna()
                returns2 = strategy2.portfolio_values.pct_change().dropna()
                
                # Align the returns
                common_index = returns1.index.intersection(returns2.index)
                if len(common_index) > 30:  # Need sufficient data
                    aligned_returns1 = returns1.loc[common_index]
                    aligned_returns2 = returns2.loc[common_index]
                    
                    # Simple t-test approximation
                    diff_returns = aligned_returns1 - aligned_returns2
                    mean_diff = diff_returns.mean()
                    std_diff = diff_returns.std()
                    
                    if std_diff > 0:
                        t_stat = mean_diff / (std_diff / np.sqrt(len(diff_returns)))
                        # Very simplified p-value approximation
                        p_value = 2 * (1 - 0.95) if abs(t_stat) > 1.96 else 0.5
                    else:
                        t_stat = 0
                        p_value = 1.0
                    
                    pair_key = f"{strategy1.name}_vs_{strategy2.name}"
                    results[pair_key] = {
                        'mean_difference': mean_diff,
                        't_statistic': t_stat,
                        'p_value': p_value,
                        'significant': p_value < 0.05
                    }
        
        return results
    
    def _generate_recommendations(self, result: ComparisonResult) -> List[Dict]:
        """Generate strategy recommendations based on analysis."""
        recommendations = []
        
        # Get best performing strategy
        if 'overall' in result.rankings:
            best_strategy = result.rankings['overall'][0]
            recommendations.append({
                'type': 'best_single_strategy',
                'strategy': best_strategy,
                'reason': f"Highest overall ranking based on multiple metrics",
                'action': 'consider_primary_allocation'
            })
        
        # Portfolio diversification recommendation
        if not result.correlation_matrix.empty:
            # Find strategies with low correlation
            correlation_values = result.correlation_matrix.values
            np.fill_diagonal(correlation_values, np.nan)  # Ignore self-correlation
            
            min_correlation = np.nanmin(correlation_values)
            if min_correlation < 0.7:
                recommendations.append({
                    'type': 'diversification_benefit',
                    'value': min_correlation,
                    'reason': f"Low correlation ({min_correlation:.3f}) suggests diversification benefits",
                    'action': 'consider_multi_strategy_portfolio'
                })
        
        # High drawdown warning
        for name, metrics in result.strategy_metrics.items():
            if metrics.max_drawdown < -0.25:  # More than 25% drawdown
                recommendations.append({
                    'type': 'high_risk_warning',
                    'strategy': name,
                    'value': metrics.max_drawdown,
                    'reason': f"Maximum drawdown of {metrics.max_drawdown:.1%} may be too high",
                    'action': 'review_risk_management'
                })
        
        # Consistency recommendation
        consistent_strategies = []
        for name, metrics in result.strategy_metrics.items():
            if (metrics.sharpe_ratio > 1.0 and 
                metrics.max_drawdown > -0.15 and 
                metrics.win_rate > 0.5):
                consistent_strategies.append(name)
        
        if consistent_strategies:
            recommendations.append({
                'type': 'consistent_performance',
                'strategies': consistent_strategies,
                'reason': "These strategies show consistent risk-adjusted returns",
                'action': 'consider_core_allocation'
            })
        
        return recommendations
    
    def generate_comparison_report(
        self,
        result: ComparisonResult,
        include_details: bool = True
    ) -> Dict:
        """
        Generate formatted comparison report.
        
        Args:
            result: Comparison result
            include_details: Whether to include detailed analysis
            
        Returns:
            Formatted report dictionary
        """
        report = {
            'summary': {
                'strategies_compared': len(result.strategy_metrics),
                'comparison_date': datetime.now().isoformat(),
                'top_strategy': result.rankings.get('overall', ['Unknown'])[0] if result.rankings else 'Unknown'
            },
            'rankings': result.rankings,
            'key_metrics': {}
        }
        
        # Add key metrics for each strategy
        for name, metrics in result.strategy_metrics.items():
            report['key_metrics'][name] = {
                'total_return_pct': round(metrics.total_return * 100, 2),
                'sharpe_ratio': round(metrics.sharpe_ratio, 3),
                'max_drawdown_pct': round(metrics.max_drawdown * 100, 2),
                'volatility_pct': round(metrics.volatility * 100, 2),
                'win_rate_pct': round(metrics.win_rate * 100, 1) if metrics.total_trades > 0 else None
            }
        
        if include_details:
            report['detailed_analysis'] = {
                'correlation_matrix': result.correlation_matrix.round(3).to_dict() if not result.correlation_matrix.empty else {},
                'optimal_weights': {k: round(v, 3) for k, v in result.optimal_weights.items()},
                'combined_portfolio': result.combined_metrics.to_dict() if result.combined_metrics else None,
                'recommendations': result.recommendations
            }
        
        return report