"""
Performance analysis module for quantitative trading system.

This module provides comprehensive performance analysis capabilities including:
- Core performance metrics calculation (returns, risk-adjusted metrics, drawdowns)
- Performance report generation with charts and statistics
- Multi-strategy comparison and analysis
- Benchmark comparison and relative performance analysis
"""

from .metrics import PerformanceMetrics, MetricsCalculator
from .reports import PerformanceReporter, ReportConfig
from .comparison import StrategyComparator, ComparisonResult

__all__ = [
    'PerformanceMetrics',
    'MetricsCalculator',
    'PerformanceReporter', 
    'ReportConfig',
    'StrategyComparator',
    'ComparisonResult'
]