"""
Risk management data models and structures.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
import pandas as pd
import numpy as np


class RiskLevel(Enum):
    """Risk level enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskRuleType(Enum):
    """Risk rule type enumeration."""
    POSITION_SIZE = "position_size"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"
    DRAWDOWN = "drawdown"
    RISK_BUDGET = "risk_budget"
    LEVERAGE = "leverage"
    CONCENTRATION = "concentration"
    CORRELATION = "correlation"


@dataclass
class RiskConfig:
    """Risk management configuration."""
    
    # Position size limits
    max_position_size_pct: float = 0.1  # 10% max position size
    max_portfolio_risk_pct: float = 0.02  # 2% max portfolio risk per trade
    
    # Stop loss and take profit
    default_stop_loss_pct: float = 0.05  # 5% stop loss
    default_take_profit_pct: float = 0.10  # 10% take profit
    enable_trailing_stop: bool = True
    trailing_stop_pct: float = 0.03  # 3% trailing stop
    
    # Drawdown protection
    max_drawdown_pct: float = 0.15  # 15% max drawdown
    daily_loss_limit_pct: float = 0.05  # 5% daily loss limit
    enable_drawdown_protection: bool = True
    
    # Risk budget
    daily_risk_budget: float = 0.02  # 2% daily risk budget
    weekly_risk_budget: float = 0.08  # 8% weekly risk budget
    monthly_risk_budget: float = 0.20  # 20% monthly risk budget
    
    # Leverage and concentration
    max_leverage: float = 2.0  # 2:1 max leverage
    max_sector_concentration_pct: float = 0.3  # 30% max sector concentration
    max_single_stock_pct: float = 0.15  # 15% max single stock
    
    # Correlation limits
    max_correlation_exposure_pct: float = 0.5  # 50% max in correlated positions
    correlation_threshold: float = 0.7  # High correlation threshold
    
    # Emergency controls
    enable_emergency_stop: bool = True
    emergency_stop_drawdown_pct: float = 0.25  # 25% emergency stop
    
    def validate(self) -> bool:
        """Validate risk configuration."""
        if self.max_position_size_pct <= 0 or self.max_position_size_pct > 1:
            return False
        if self.max_drawdown_pct <= 0 or self.max_drawdown_pct > 1:
            return False
        if self.max_leverage <= 0:
            return False
        return True


@dataclass
class RiskViolation:
    """Risk violation record."""
    
    rule_name: str
    rule_type: RiskRuleType
    violation_type: str
    current_value: float
    limit_value: float
    severity: RiskLevel
    timestamp: datetime
    symbol: Optional[str] = None
    strategy_id: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'rule_name': self.rule_name,
            'rule_type': self.rule_type.value,
            'violation_type': self.violation_type,
            'current_value': self.current_value,
            'limit_value': self.limit_value,
            'severity': self.severity.value,
            'timestamp': self.timestamp.isoformat(),
            'symbol': self.symbol,
            'strategy_id': self.strategy_id,
            'details': self.details
        }


@dataclass
class RiskMetrics:
    """Risk metrics calculation results."""
    
    timestamp: datetime
    portfolio_value: float
    cash: float
    
    # Position metrics
    total_positions: int
    largest_position_pct: float
    largest_position_symbol: Optional[str]
    
    # Risk metrics
    portfolio_beta: float
    portfolio_var_1d: float  # 1-day Value at Risk
    portfolio_var_5d: float  # 5-day Value at Risk
    expected_shortfall: float
    
    # Drawdown metrics
    current_drawdown_pct: float
    max_drawdown_pct: float
    drawdown_duration_days: int
    
    # Leverage and exposure
    gross_leverage: float
    net_leverage: float
    long_exposure_pct: float
    short_exposure_pct: float
    
    # Concentration metrics
    top_5_positions_pct: float
    
    # Risk budget utilization
    daily_risk_used_pct: float
    weekly_risk_used_pct: float
    monthly_risk_used_pct: float
    
    # Fields with defaults must come last
    sector_concentration: Dict[str, float] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'timestamp': self.timestamp.isoformat(),
            'portfolio_value': self.portfolio_value,
            'cash': self.cash,
            'total_positions': self.total_positions,
            'largest_position_pct': self.largest_position_pct,
            'largest_position_symbol': self.largest_position_symbol,
            'portfolio_beta': self.portfolio_beta,
            'portfolio_var_1d': self.portfolio_var_1d,
            'portfolio_var_5d': self.portfolio_var_5d,
            'expected_shortfall': self.expected_shortfall,
            'current_drawdown_pct': self.current_drawdown_pct,
            'max_drawdown_pct': self.max_drawdown_pct,
            'drawdown_duration_days': self.drawdown_duration_days,
            'gross_leverage': self.gross_leverage,
            'net_leverage': self.net_leverage,
            'long_exposure_pct': self.long_exposure_pct,
            'short_exposure_pct': self.short_exposure_pct,
            'top_5_positions_pct': self.top_5_positions_pct,
            'sector_concentration': self.sector_concentration,
            'daily_risk_used_pct': self.daily_risk_used_pct,
            'weekly_risk_used_pct': self.weekly_risk_used_pct,
            'monthly_risk_used_pct': self.monthly_risk_used_pct
        }


@dataclass
class StopLossOrder:
    """Stop loss order configuration."""
    
    symbol: str
    stop_price: float
    stop_type: str  # 'fixed', 'trailing', 'percentage'
    original_price: float
    quantity: float
    created_at: datetime
    strategy_id: Optional[str] = None
    is_active: bool = True
    
    # Trailing stop specific
    trail_amount: Optional[float] = None
    highest_price: Optional[float] = None
    
    def update_trailing_stop(self, current_price: float) -> bool:
        """Update trailing stop price. Returns True if stop was updated."""
        if self.stop_type != 'trailing' or not self.trail_amount:
            return False
        
        # Update highest price for long positions
        if self.quantity > 0:  # Long position
            if self.highest_price is None or current_price > self.highest_price:
                self.highest_price = current_price
                new_stop = self.highest_price - self.trail_amount
                if new_stop > self.stop_price:
                    self.stop_price = new_stop
                    return True
        else:  # Short position
            if self.highest_price is None or current_price < self.highest_price:
                self.highest_price = current_price
                new_stop = self.highest_price + self.trail_amount
                if new_stop < self.stop_price:
                    self.stop_price = new_stop
                    return True
        
        return False
    
    def should_trigger(self, current_price: float) -> bool:
        """Check if stop loss should trigger."""
        if not self.is_active:
            return False
        
        if self.quantity > 0:  # Long position
            return current_price <= self.stop_price
        else:  # Short position
            return current_price >= self.stop_price


@dataclass
class TakeProfitOrder:
    """Take profit order configuration."""
    
    symbol: str
    target_price: float
    quantity: float
    created_at: datetime
    strategy_id: Optional[str] = None
    is_active: bool = True
    
    def should_trigger(self, current_price: float) -> bool:
        """Check if take profit should trigger."""
        if not self.is_active:
            return False
        
        if self.quantity > 0:  # Long position
            return current_price >= self.target_price
        else:  # Short position
            return current_price <= self.target_price


@dataclass
class RiskBudgetPeriod:
    """Risk budget tracking for a specific period."""
    
    period_type: str  # 'daily', 'weekly', 'monthly'
    start_date: datetime
    end_date: datetime
    budget_amount: float
    used_amount: float = 0.0
    remaining_amount: float = 0.0
    trades_count: int = 0
    violations_count: int = 0
    
    def __post_init__(self):
        """Calculate remaining amount after initialization."""
        self.remaining_amount = self.budget_amount - self.used_amount
    
    def add_risk_usage(self, amount: float) -> None:
        """Add risk usage to the period."""
        self.used_amount += amount
        self.remaining_amount = self.budget_amount - self.used_amount
        self.trades_count += 1
    
    def get_utilization_pct(self) -> float:
        """Get budget utilization percentage."""
        if self.budget_amount == 0:
            return 0.0
        return (self.used_amount / self.budget_amount) * 100.0
    
    def is_exceeded(self) -> bool:
        """Check if budget is exceeded."""
        return self.used_amount > self.budget_amount
    
    def is_near_limit(self, threshold_pct: float = 0.8) -> bool:
        """Check if budget is near limit."""
        return self.get_utilization_pct() >= (threshold_pct * 100.0)