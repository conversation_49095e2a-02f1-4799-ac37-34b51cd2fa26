"""
Risk management rules and validators.
"""

from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
import pandas as pd
import numpy as np

from .models import (
    RiskConfig, RiskViolation, RiskLevel, RiskRuleType, 
    StopLossOrder, TakeProfitOrder, RiskBudgetPeriod
)
from ..models.portfolio import Port<PERSON>lio, Position
from ..models.trading import Order, Signal


class RiskRule(ABC):
    """Abstract base class for risk rules."""
    
    def __init__(self, name: str, rule_type: RiskRuleType, enabled: bool = True):
        self.name = name
        self.rule_type = rule_type
        self.enabled = enabled
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def check_order(self, order: Order, portfolio: Portfolio, 
                   config: RiskConfig) -> Optional[RiskViolation]:
        """Check if an order violates this risk rule."""
        pass
    
    @abstractmethod
    def check_portfolio(self, portfolio: Portfolio, 
                       config: RiskConfig) -> List[RiskViolation]:
        """Check if portfolio violates this risk rule."""
        pass
    
    def is_enabled(self) -> bool:
        """Check if rule is enabled."""
        return self.enabled
    
    def enable(self) -> None:
        """Enable the rule."""
        self.enabled = True
    
    def disable(self) -> None:
        """Disable the rule."""
        self.enabled = False


class PositionSizeRule(RiskRule):
    """Position size and capital management rule."""
    
    def __init__(self, enabled: bool = True):
        super().__init__("Position Size Control", RiskRuleType.POSITION_SIZE, enabled)
    
    def check_order(self, order: Order, portfolio: Portfolio, 
                   config: RiskConfig) -> Optional[RiskViolation]:
        """Check position size limits for new order."""
        if not self.enabled:
            return None
        
        try:
            # Calculate order value
            order_value = abs(order.quantity * (order.price or 0))
            
            # Check available cash for buy orders first (most critical)
            if order.side.value == "BUY":
                required_cash = order_value
                if required_cash > portfolio.cash:
                    return RiskViolation(
                        rule_name=self.name,
                        rule_type=self.rule_type,
                        violation_type="insufficient_cash",
                        current_value=required_cash,
                        limit_value=portfolio.cash,
                        severity=RiskLevel.CRITICAL,
                        timestamp=datetime.now(),
                        symbol=order.symbol,
                        strategy_id=order.strategy_id,
                        details={
                            'required_cash': required_cash,
                            'available_cash': portfolio.cash
                        }
                    )
            
            # Check position size after order execution
            current_position = portfolio.get_position(order.symbol)
            if current_position:
                if order.side.value == "BUY":
                    new_quantity = current_position.quantity + order.quantity
                    new_value = new_quantity * (order.price or current_position.avg_price)
                else:
                    new_quantity = current_position.quantity - order.quantity
                    if new_quantity < 0:
                        return RiskViolation(
                            rule_name=self.name,
                            rule_type=self.rule_type,
                            violation_type="short_selling_not_allowed",
                            current_value=new_quantity,
                            limit_value=0,
                            severity=RiskLevel.CRITICAL,
                            timestamp=datetime.now(),
                            symbol=order.symbol,
                            strategy_id=order.strategy_id,
                            details={'attempted_quantity': order.quantity, 'current_quantity': current_position.quantity}
                        )
                    new_value = new_quantity * (order.price or current_position.avg_price)
            else:
                if order.side.value == "BUY":
                    new_value = order_value
                else:
                    # Cannot sell what we don't have
                    return RiskViolation(
                        rule_name=self.name,
                        rule_type=self.rule_type,
                        violation_type="short_selling_not_allowed",
                        current_value=0,
                        limit_value=0,
                        severity=RiskLevel.CRITICAL,
                        timestamp=datetime.now(),
                        symbol=order.symbol,
                        strategy_id=order.strategy_id,
                        details={'attempted_quantity': order.quantity}
                    )
            
            # Check max position size
            position_size_pct = abs(new_value) / portfolio.total_value if portfolio.total_value > 0 else 0
            if position_size_pct > config.max_position_size_pct:
                return RiskViolation(
                    rule_name=self.name,
                    rule_type=self.rule_type,
                    violation_type="max_position_size",
                    current_value=position_size_pct,
                    limit_value=config.max_position_size_pct,
                    severity=RiskLevel.HIGH,
                    timestamp=datetime.now(),
                    symbol=order.symbol,
                    strategy_id=order.strategy_id,
                    details={
                        'new_position_value': new_value,
                        'portfolio_value': portfolio.total_value
                    }
                )
            
            # Check portfolio risk per trade (less critical, check last)
            portfolio_risk_pct = order_value / portfolio.total_value if portfolio.total_value > 0 else 0
            if portfolio_risk_pct > config.max_portfolio_risk_pct:
                return RiskViolation(
                    rule_name=self.name,
                    rule_type=self.rule_type,
                    violation_type="portfolio_risk_per_trade",
                    current_value=portfolio_risk_pct,
                    limit_value=config.max_portfolio_risk_pct,
                    severity=RiskLevel.HIGH if portfolio_risk_pct > config.max_portfolio_risk_pct * 1.5 else RiskLevel.MEDIUM,
                    timestamp=datetime.now(),
                    symbol=order.symbol,
                    strategy_id=order.strategy_id,
                    details={
                        'order_value': order_value,
                        'portfolio_value': portfolio.total_value
                    }
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error checking position size rule: {e}")
            return None
    
    def check_portfolio(self, portfolio: Portfolio, 
                       config: RiskConfig) -> List[RiskViolation]:
        """Check portfolio-level position size violations."""
        violations = []
        
        if not self.enabled:
            return violations
        
        try:
            # Check individual position sizes
            for symbol, position in portfolio.positions.items():
                position_pct = position.market_value / portfolio.total_value if portfolio.total_value > 0 else 0
                
                if position_pct > config.max_position_size_pct:
                    violations.append(RiskViolation(
                        rule_name=self.name,
                        rule_type=self.rule_type,
                        violation_type="max_position_size",
                        current_value=position_pct,
                        limit_value=config.max_position_size_pct,
                        severity=RiskLevel.MEDIUM,
                        timestamp=datetime.now(),
                        symbol=symbol,
                        details={
                            'position_value': position.market_value,
                            'portfolio_value': portfolio.total_value
                        }
                    ))
            
            # Check leverage
            total_position_value = sum(abs(pos.market_value) for pos in portfolio.positions.values())
            leverage = total_position_value / portfolio.total_value if portfolio.total_value > 0 else 0
            
            if leverage > config.max_leverage:
                violations.append(RiskViolation(
                    rule_name=self.name,
                    rule_type=self.rule_type,
                    violation_type="max_leverage",
                    current_value=leverage,
                    limit_value=config.max_leverage,
                    severity=RiskLevel.HIGH if leverage > config.max_leverage * 1.5 else RiskLevel.MEDIUM,
                    timestamp=datetime.now(),
                    details={
                        'total_position_value': total_position_value,
                        'portfolio_value': portfolio.total_value
                    }
                ))
            
        except Exception as e:
            self.logger.error(f"Error checking portfolio position sizes: {e}")
        
        return violations


class StopLossRule(RiskRule):
    """Stop loss and take profit rule."""
    
    def __init__(self, enabled: bool = True):
        super().__init__("Stop Loss Control", RiskRuleType.STOP_LOSS, enabled)
        self.stop_orders: Dict[str, StopLossOrder] = {}
        self.take_profit_orders: Dict[str, TakeProfitOrder] = {}
    
    def check_order(self, order: Order, portfolio: Portfolio, 
                   config: RiskConfig) -> Optional[RiskViolation]:
        """Check if order needs stop loss protection."""
        if not self.enabled:
            return None
        
        # This rule doesn't prevent orders, but ensures stop losses are set
        return None
    
    def check_portfolio(self, portfolio: Portfolio, 
                       config: RiskConfig) -> List[RiskViolation]:
        """Check portfolio positions for stop loss violations."""
        violations = []
        
        if not self.enabled:
            return violations
        
        try:
            for symbol, position in portfolio.positions.items():
                # Check if position has stop loss protection
                if symbol not in self.stop_orders:
                    violations.append(RiskViolation(
                        rule_name=self.name,
                        rule_type=self.rule_type,
                        violation_type="missing_stop_loss",
                        current_value=0,
                        limit_value=1,
                        severity=RiskLevel.MEDIUM,
                        timestamp=datetime.now(),
                        symbol=symbol,
                        details={
                            'position_value': position.market_value,
                            'position_quantity': position.quantity
                        }
                    ))
        
        except Exception as e:
            self.logger.error(f"Error checking stop loss rules: {e}")
        
        return violations
    
    def add_stop_loss(self, symbol: str, stop_price: float, quantity: float,
                     stop_type: str = 'fixed', trail_amount: Optional[float] = None,
                     strategy_id: Optional[str] = None) -> str:
        """Add stop loss order."""
        stop_order = StopLossOrder(
            symbol=symbol,
            stop_price=stop_price,
            stop_type=stop_type,
            original_price=stop_price,
            quantity=quantity,
            created_at=datetime.now(),
            strategy_id=strategy_id,
            trail_amount=trail_amount
        )
        
        self.stop_orders[symbol] = stop_order
        self.logger.info(f"Added stop loss for {symbol} at {stop_price}")
        return symbol
    
    def add_take_profit(self, symbol: str, target_price: float, quantity: float,
                       strategy_id: Optional[str] = None) -> str:
        """Add take profit order."""
        take_profit_order = TakeProfitOrder(
            symbol=symbol,
            target_price=target_price,
            quantity=quantity,
            created_at=datetime.now(),
            strategy_id=strategy_id
        )
        
        self.take_profit_orders[symbol] = take_profit_order
        self.logger.info(f"Added take profit for {symbol} at {target_price}")
        return symbol
    
    def check_triggers(self, market_prices: Dict[str, float]) -> List[Dict[str, Any]]:
        """Check if any stop loss or take profit orders should trigger."""
        triggers = []
        
        # Check stop losses
        for symbol, stop_order in self.stop_orders.items():
            if symbol in market_prices:
                current_price = market_prices[symbol]
                
                # Update trailing stops
                if stop_order.stop_type == 'trailing':
                    stop_order.update_trailing_stop(current_price)
                
                # Check if should trigger
                if stop_order.should_trigger(current_price):
                    triggers.append({
                        'type': 'stop_loss',
                        'symbol': symbol,
                        'current_price': current_price,
                        'trigger_price': stop_order.stop_price,
                        'quantity': stop_order.quantity,
                        'strategy_id': stop_order.strategy_id
                    })
        
        # Check take profits
        for symbol, tp_order in self.take_profit_orders.items():
            if symbol in market_prices:
                current_price = market_prices[symbol]
                
                if tp_order.should_trigger(current_price):
                    triggers.append({
                        'type': 'take_profit',
                        'symbol': symbol,
                        'current_price': current_price,
                        'trigger_price': tp_order.target_price,
                        'quantity': tp_order.quantity,
                        'strategy_id': tp_order.strategy_id
                    })
        
        return triggers
    
    def remove_stop_loss(self, symbol: str) -> bool:
        """Remove stop loss order."""
        if symbol in self.stop_orders:
            del self.stop_orders[symbol]
            return True
        return False
    
    def remove_take_profit(self, symbol: str) -> bool:
        """Remove take profit order."""
        if symbol in self.take_profit_orders:
            del self.take_profit_orders[symbol]
            return True
        return False


class DrawdownRule(RiskRule):
    """Maximum drawdown monitoring and protection rule."""
    
    def __init__(self, enabled: bool = True):
        super().__init__("Drawdown Protection", RiskRuleType.DRAWDOWN, enabled)
        self.peak_value = 0.0
        self.daily_start_value = 0.0
        self.daily_start_date = datetime.now().date()
    
    def check_order(self, order: Order, portfolio: Portfolio, 
                   config: RiskConfig) -> Optional[RiskViolation]:
        """Check if order would violate drawdown limits."""
        if not self.enabled:
            return None
        
        # Update peak value
        if portfolio.total_value > self.peak_value:
            self.peak_value = portfolio.total_value
        
        # Check current drawdown
        current_drawdown = (self.peak_value - portfolio.total_value) / self.peak_value if self.peak_value > 0 else 0
        
        if current_drawdown > config.max_drawdown_pct:
            return RiskViolation(
                rule_name=self.name,
                rule_type=self.rule_type,
                violation_type="max_drawdown_exceeded",
                current_value=current_drawdown,
                limit_value=config.max_drawdown_pct,
                severity=RiskLevel.CRITICAL if current_drawdown > config.emergency_stop_drawdown_pct else RiskLevel.HIGH,
                timestamp=datetime.now(),
                symbol=order.symbol,
                strategy_id=order.strategy_id,
                details={
                    'peak_value': self.peak_value,
                    'current_value': portfolio.total_value,
                    'drawdown_amount': self.peak_value - portfolio.total_value
                }
            )
        
        return None
    
    def check_portfolio(self, portfolio: Portfolio, 
                       config: RiskConfig) -> List[RiskViolation]:
        """Check portfolio drawdown violations."""
        violations = []
        
        if not self.enabled:
            return violations
        
        try:
            # Update peak value
            if portfolio.total_value > self.peak_value:
                self.peak_value = portfolio.total_value
            
            # Check daily loss limit
            current_date = datetime.now().date()
            if current_date != self.daily_start_date:
                self.daily_start_value = portfolio.total_value
                self.daily_start_date = current_date
            
            daily_loss = (self.daily_start_value - portfolio.total_value) / self.daily_start_value if self.daily_start_value > 0 else 0
            
            if daily_loss > config.daily_loss_limit_pct:
                violations.append(RiskViolation(
                    rule_name=self.name,
                    rule_type=self.rule_type,
                    violation_type="daily_loss_limit",
                    current_value=daily_loss,
                    limit_value=config.daily_loss_limit_pct,
                    severity=RiskLevel.HIGH,
                    timestamp=datetime.now(),
                    details={
                        'daily_start_value': self.daily_start_value,
                        'current_value': portfolio.total_value,
                        'daily_loss_amount': self.daily_start_value - portfolio.total_value
                    }
                ))
            
            # Check maximum drawdown
            current_drawdown = (self.peak_value - portfolio.total_value) / self.peak_value if self.peak_value > 0 else 0
            
            if current_drawdown > config.max_drawdown_pct:
                severity = RiskLevel.CRITICAL if current_drawdown > config.emergency_stop_drawdown_pct else RiskLevel.HIGH
                
                violations.append(RiskViolation(
                    rule_name=self.name,
                    rule_type=self.rule_type,
                    violation_type="max_drawdown_exceeded",
                    current_value=current_drawdown,
                    limit_value=config.max_drawdown_pct,
                    severity=severity,
                    timestamp=datetime.now(),
                    details={
                        'peak_value': self.peak_value,
                        'current_value': portfolio.total_value,
                        'drawdown_amount': self.peak_value - portfolio.total_value
                    }
                ))
        
        except Exception as e:
            self.logger.error(f"Error checking drawdown rules: {e}")
        
        return violations
    
    def reset_peak(self, new_peak: Optional[float] = None) -> None:
        """Reset peak value."""
        if new_peak is not None:
            self.peak_value = new_peak
        else:
            self.peak_value = 0.0
        self.logger.info(f"Reset peak value to {self.peak_value}")


class RiskBudgetRule(RiskRule):
    """Risk budget allocation and control rule."""
    
    def __init__(self, enabled: bool = True):
        super().__init__("Risk Budget Control", RiskRuleType.RISK_BUDGET, enabled)
        self.daily_budget: Optional[RiskBudgetPeriod] = None
        self.weekly_budget: Optional[RiskBudgetPeriod] = None
        self.monthly_budget: Optional[RiskBudgetPeriod] = None
        self.risk_usage_history: List[Dict[str, Any]] = []
    
    def check_order(self, order: Order, portfolio: Portfolio, 
                   config: RiskConfig) -> Optional[RiskViolation]:
        """Check if order would exceed risk budget."""
        if not self.enabled:
            return None
        
        try:
            # Calculate order risk (as percentage of portfolio)
            order_value = abs(order.quantity * (order.price or 0))
            order_risk = order_value / portfolio.total_value if portfolio.total_value > 0 else 0
            
            # Initialize budgets if needed
            self._initialize_budgets(config, portfolio.total_value)
            
            # Check daily budget
            if self.daily_budget and (self.daily_budget.used_amount + order_risk) > self.daily_budget.budget_amount:
                return RiskViolation(
                    rule_name=self.name,
                    rule_type=self.rule_type,
                    violation_type="daily_risk_budget_exceeded",
                    current_value=self.daily_budget.used_amount + order_risk,
                    limit_value=self.daily_budget.budget_amount,
                    severity=RiskLevel.HIGH,
                    timestamp=datetime.now(),
                    symbol=order.symbol,
                    strategy_id=order.strategy_id,
                    details={
                        'order_risk': order_risk,
                        'current_usage': self.daily_budget.used_amount,
                        'budget_limit': self.daily_budget.budget_amount
                    }
                )
            
            # Check weekly budget
            if self.weekly_budget and (self.weekly_budget.used_amount + order_risk) > self.weekly_budget.budget_amount:
                return RiskViolation(
                    rule_name=self.name,
                    rule_type=self.rule_type,
                    violation_type="weekly_risk_budget_exceeded",
                    current_value=self.weekly_budget.used_amount + order_risk,
                    limit_value=self.weekly_budget.budget_amount,
                    severity=RiskLevel.MEDIUM,
                    timestamp=datetime.now(),
                    symbol=order.symbol,
                    strategy_id=order.strategy_id,
                    details={
                        'order_risk': order_risk,
                        'current_usage': self.weekly_budget.used_amount,
                        'budget_limit': self.weekly_budget.budget_amount
                    }
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error checking risk budget rule: {e}")
            return None
    
    def check_portfolio(self, portfolio: Portfolio, 
                       config: RiskConfig) -> List[RiskViolation]:
        """Check portfolio risk budget violations."""
        violations = []
        
        if not self.enabled:
            return violations
        
        try:
            self._initialize_budgets(config, portfolio.total_value)
            
            # Check if budgets are near limits
            if self.daily_budget and self.daily_budget.is_near_limit(0.9):  # 90% threshold
                violations.append(RiskViolation(
                    rule_name=self.name,
                    rule_type=self.rule_type,
                    violation_type="daily_risk_budget_warning",
                    current_value=self.daily_budget.get_utilization_pct(),
                    limit_value=90.0,
                    severity=RiskLevel.MEDIUM,
                    timestamp=datetime.now(),
                    details={
                        'budget_used': self.daily_budget.used_amount,
                        'budget_limit': self.daily_budget.budget_amount,
                        'utilization_pct': self.daily_budget.get_utilization_pct()
                    }
                ))
            
        except Exception as e:
            self.logger.error(f"Error checking risk budget portfolio: {e}")
        
        return violations
    
    def record_risk_usage(self, amount: float, symbol: str, strategy_id: Optional[str] = None) -> None:
        """Record risk usage for budget tracking."""
        try:
            # Record in history
            self.risk_usage_history.append({
                'timestamp': datetime.now(),
                'amount': amount,
                'symbol': symbol,
                'strategy_id': strategy_id
            })
            
            # Update budget periods
            if self.daily_budget:
                self.daily_budget.add_risk_usage(amount)
            
            if self.weekly_budget:
                self.weekly_budget.add_risk_usage(amount)
            
            if self.monthly_budget:
                self.monthly_budget.add_risk_usage(amount)
            
        except Exception as e:
            self.logger.error(f"Error recording risk usage: {e}")
    
    def _initialize_budgets(self, config: RiskConfig, portfolio_value: float) -> None:
        """Initialize risk budget periods."""
        now = datetime.now()
        
        # Daily budget
        if self.daily_budget is None or self.daily_budget.end_date.date() < now.date():
            start_date = datetime.combine(now.date(), datetime.min.time())
            end_date = start_date + timedelta(days=1)
            
            self.daily_budget = RiskBudgetPeriod(
                period_type='daily',
                start_date=start_date,
                end_date=end_date,
                budget_amount=config.daily_risk_budget
            )
        
        # Weekly budget
        if self.weekly_budget is None or self.weekly_budget.end_date < now:
            # Start of week (Monday)
            days_since_monday = now.weekday()
            start_date = now - timedelta(days=days_since_monday)
            start_date = datetime.combine(start_date.date(), datetime.min.time())
            end_date = start_date + timedelta(days=7)
            
            self.weekly_budget = RiskBudgetPeriod(
                period_type='weekly',
                start_date=start_date,
                end_date=end_date,
                budget_amount=config.weekly_risk_budget
            )
        
        # Monthly budget
        if self.monthly_budget is None or self.monthly_budget.end_date < now:
            # Start of month
            start_date = datetime.combine(now.replace(day=1).date(), datetime.min.time())
            # End of month
            if now.month == 12:
                end_date = start_date.replace(year=now.year + 1, month=1)
            else:
                end_date = start_date.replace(month=now.month + 1)
            
            self.monthly_budget = RiskBudgetPeriod(
                period_type='monthly',
                start_date=start_date,
                end_date=end_date,
                budget_amount=config.monthly_risk_budget
            )
    
    def get_budget_status(self) -> Dict[str, Any]:
        """Get current budget status."""
        status = {}
        
        if self.daily_budget:
            status['daily'] = {
                'used': self.daily_budget.used_amount,
                'limit': self.daily_budget.budget_amount,
                'remaining': self.daily_budget.remaining_amount,
                'utilization_pct': self.daily_budget.get_utilization_pct(),
                'trades_count': self.daily_budget.trades_count
            }
        
        if self.weekly_budget:
            status['weekly'] = {
                'used': self.weekly_budget.used_amount,
                'limit': self.weekly_budget.budget_amount,
                'remaining': self.weekly_budget.remaining_amount,
                'utilization_pct': self.weekly_budget.get_utilization_pct(),
                'trades_count': self.weekly_budget.trades_count
            }
        
        if self.monthly_budget:
            status['monthly'] = {
                'used': self.monthly_budget.used_amount,
                'limit': self.monthly_budget.budget_amount,
                'remaining': self.monthly_budget.remaining_amount,
                'utilization_pct': self.monthly_budget.get_utilization_pct(),
                'trades_count': self.monthly_budget.trades_count
            }
        
        return status