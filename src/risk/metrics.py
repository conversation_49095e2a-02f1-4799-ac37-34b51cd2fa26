"""
Risk metrics calculation module for comprehensive risk analysis.

This module implements various risk metrics including VaR, volatility analysis,
correlation analysis, stress testing, and scenario analysis.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from scipy import stats
from scipy.optimize import minimize
import logging
from enum import Enum

from .models import RiskMetrics, RiskConfig
from ..models.portfolio import Portfolio, Position


class VaRMethod(Enum):
    """Value at Risk calculation methods."""
    HISTORICAL = "historical"
    PARAMETRIC = "parametric"
    MONTE_CARLO = "monte_carlo"


class StressTestType(Enum):
    """Stress test scenario types."""
    MARKET_CRASH = "market_crash"
    INTEREST_RATE_SHOCK = "interest_rate_shock"
    VOLATILITY_SPIKE = "volatility_spike"
    CORRELATION_BREAKDOWN = "correlation_breakdown"
    LIQUIDITY_CRISIS = "liquidity_crisis"
    CUSTOM = "custom"


@dataclass
class VaRResult:
    """Value at Risk calculation result."""
    
    var_1d: float  # 1-day VaR
    var_5d: float  # 5-day VaR
    var_10d: float  # 10-day VaR
    expected_shortfall_1d: float  # Expected Shortfall (CVaR) 1-day
    expected_shortfall_5d: float  # Expected Shortfall (CVaR) 5-day
    confidence_level: float
    method: VaRMethod
    calculation_date: datetime
    portfolio_value: float
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class VolatilityMetrics:
    """Volatility analysis results."""
    
    daily_volatility: float
    weekly_volatility: float
    monthly_volatility: float
    annualized_volatility: float
    volatility_trend: str  # 'increasing', 'decreasing', 'stable'
    garch_volatility: Optional[float] = None
    ewma_volatility: Optional[float] = None
    realized_volatility: Optional[float] = None
    implied_volatility: Optional[float] = None
    volatility_percentile: Optional[float] = None


@dataclass
class CorrelationAnalysis:
    """Correlation analysis results."""
    
    correlation_matrix: pd.DataFrame
    average_correlation: float
    max_correlation: float
    min_correlation: float
    highly_correlated_pairs: List[Tuple[str, str, float]]
    correlation_clusters: Dict[str, List[str]]
    diversification_ratio: float
    effective_positions: float


@dataclass
class StressTestResult:
    """Stress test scenario result."""
    
    scenario_name: str
    scenario_type: StressTestType
    portfolio_loss: float
    portfolio_loss_pct: float
    position_impacts: Dict[str, float]
    var_breach: bool
    recovery_time_estimate: Optional[int] = None  # days
    scenario_probability: Optional[float] = None
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RiskReport:
    """Comprehensive risk report."""
    
    report_date: datetime
    portfolio_value: float
    var_results: VaRResult
    volatility_metrics: VolatilityMetrics
    correlation_analysis: CorrelationAnalysis
    stress_test_results: List[StressTestResult]
    risk_summary: Dict[str, Any]
    recommendations: List[str]


class RiskMetricsCalculator:
    """
    Comprehensive risk metrics calculator.
    
    Provides various risk calculation methods including VaR, volatility analysis,
    correlation analysis, and stress testing.
    """
    
    def __init__(self, config: Optional[RiskConfig] = None):
        """
        Initialize risk metrics calculator.
        
        Args:
            config: Risk configuration
        """
        self.config = config or RiskConfig()
        self.logger = logging.getLogger(f"{__name__}.RiskMetricsCalculator")
        
        # Cache for calculations
        self._returns_cache: Dict[str, pd.Series] = {}
        self._correlation_cache: Optional[pd.DataFrame] = None
        self._cache_timestamp: Optional[datetime] = None
        
    def calculate_var(self, 
                     portfolio: Portfolio,
                     returns_data: Dict[str, pd.Series],
                     confidence_levels: List[float] = [0.95, 0.99],
                     methods: List[VaRMethod] = [VaRMethod.HISTORICAL, VaRMethod.PARAMETRIC],
                     time_horizons: List[int] = [1, 5, 10]) -> Dict[str, VaRResult]:
        """
        Calculate Value at Risk using multiple methods.
        
        Args:
            portfolio: Portfolio to analyze
            returns_data: Historical returns data for each symbol
            confidence_levels: Confidence levels for VaR calculation
            methods: VaR calculation methods to use
            time_horizons: Time horizons in days
            
        Returns:
            Dictionary of VaR results by method and confidence level
        """
        results = {}
        
        try:
            # Calculate portfolio returns
            portfolio_returns = self._calculate_portfolio_returns(portfolio, returns_data)
            
            if portfolio_returns.empty:
                self.logger.warning("No portfolio returns data available for VaR calculation")
                return results
            
            for method in methods:
                for confidence_level in confidence_levels:
                    key = f"{method.value}_{confidence_level}"
                    
                    if method == VaRMethod.HISTORICAL:
                        result = self._calculate_historical_var(
                            portfolio_returns, portfolio.total_value, 
                            confidence_level, time_horizons
                        )
                    elif method == VaRMethod.PARAMETRIC:
                        result = self._calculate_parametric_var(
                            portfolio_returns, portfolio.total_value,
                            confidence_level, time_horizons
                        )
                    elif method == VaRMethod.MONTE_CARLO:
                        result = self._calculate_monte_carlo_var(
                            portfolio, returns_data, portfolio.total_value,
                            confidence_level, time_horizons
                        )
                    else:
                        continue
                    
                    results[key] = result
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error calculating VaR: {e}")
            return results   
 
    def _calculate_historical_var(self, 
                                 portfolio_returns: pd.Series,
                                 portfolio_value: float,
                                 confidence_level: float,
                                 time_horizons: List[int]) -> VaRResult:
        """Calculate historical VaR."""
        # Sort returns in ascending order
        sorted_returns = portfolio_returns.sort_values()
        
        # Calculate percentile for VaR
        percentile = (1 - confidence_level) * 100
        var_return = np.percentile(sorted_returns, percentile)
        
        # Calculate VaR for different time horizons
        var_1d = abs(var_return * portfolio_value)
        var_5d = var_1d * np.sqrt(5) if 5 in time_horizons else 0
        var_10d = var_1d * np.sqrt(10) if 10 in time_horizons else 0
        
        # Calculate Expected Shortfall (CVaR)
        tail_returns = sorted_returns[sorted_returns <= var_return]
        expected_shortfall_return = tail_returns.mean() if len(tail_returns) > 0 else var_return
        
        expected_shortfall_1d = abs(expected_shortfall_return * portfolio_value)
        expected_shortfall_5d = expected_shortfall_1d * np.sqrt(5)
        
        return VaRResult(
            var_1d=var_1d,
            var_5d=var_5d,
            var_10d=var_10d,
            expected_shortfall_1d=expected_shortfall_1d,
            expected_shortfall_5d=expected_shortfall_5d,
            confidence_level=confidence_level,
            method=VaRMethod.HISTORICAL,
            calculation_date=datetime.now(),
            portfolio_value=portfolio_value,
            details={
                'observations': len(sorted_returns),
                'var_return': var_return,
                'expected_shortfall_return': expected_shortfall_return,
                'tail_observations': len(tail_returns)
            }
        )
    
    def _calculate_parametric_var(self,
                                 portfolio_returns: pd.Series,
                                 portfolio_value: float,
                                 confidence_level: float,
                                 time_horizons: List[int]) -> VaRResult:
        """Calculate parametric (normal distribution) VaR."""
        # Calculate mean and standard deviation
        mean_return = portfolio_returns.mean()
        std_return = portfolio_returns.std()
        
        # Calculate z-score for confidence level
        z_score = stats.norm.ppf(1 - confidence_level)
        
        # Calculate VaR (assuming normal distribution)
        var_return = mean_return + z_score * std_return
        var_1d = abs(var_return * portfolio_value)
        var_5d = var_1d * np.sqrt(5) if 5 in time_horizons else 0
        var_10d = var_1d * np.sqrt(10) if 10 in time_horizons else 0
        
        # Calculate Expected Shortfall for normal distribution
        # ES = μ + σ * φ(Φ^(-1)(α)) / α, where α = 1 - confidence_level
        alpha = 1 - confidence_level
        es_multiplier = stats.norm.pdf(z_score) / alpha
        expected_shortfall_return = mean_return + std_return * es_multiplier
        
        expected_shortfall_1d = abs(expected_shortfall_return * portfolio_value)
        expected_shortfall_5d = expected_shortfall_1d * np.sqrt(5)
        
        return VaRResult(
            var_1d=var_1d,
            var_5d=var_5d,
            var_10d=var_10d,
            expected_shortfall_1d=expected_shortfall_1d,
            expected_shortfall_5d=expected_shortfall_5d,
            confidence_level=confidence_level,
            method=VaRMethod.PARAMETRIC,
            calculation_date=datetime.now(),
            portfolio_value=portfolio_value,
            details={
                'mean_return': mean_return,
                'std_return': std_return,
                'z_score': z_score,
                'var_return': var_return,
                'expected_shortfall_return': expected_shortfall_return
            }
        )    

    def _calculate_monte_carlo_var(self,
                                  portfolio: Portfolio,
                                  returns_data: Dict[str, pd.Series],
                                  portfolio_value: float,
                                  confidence_level: float,
                                  time_horizons: List[int],
                                  num_simulations: int = 10000) -> VaRResult:
        """Calculate Monte Carlo VaR."""
        try:
            # Get portfolio weights
            weights = self._get_portfolio_weights(portfolio)
            
            # Calculate correlation matrix
            correlation_matrix = self._calculate_correlation_matrix(returns_data)
            
            # Calculate mean returns and covariance matrix
            returns_df = pd.DataFrame(returns_data)
            mean_returns = returns_df.mean()
            cov_matrix = returns_df.cov()
            
            # Generate random scenarios
            simulated_returns = []
            
            for _ in range(num_simulations):
                # Generate correlated random returns
                random_returns = np.random.multivariate_normal(
                    mean_returns.values, cov_matrix.values
                )
                
                # Calculate portfolio return for this scenario
                portfolio_return = np.sum(weights * random_returns)
                simulated_returns.append(portfolio_return)
            
            simulated_returns = np.array(simulated_returns)
            
            # Calculate VaR from simulated returns
            percentile = (1 - confidence_level) * 100
            var_return = np.percentile(simulated_returns, percentile)
            
            var_1d = abs(var_return * portfolio_value)
            var_5d = var_1d * np.sqrt(5) if 5 in time_horizons else 0
            var_10d = var_1d * np.sqrt(10) if 10 in time_horizons else 0
            
            # Calculate Expected Shortfall
            tail_returns = simulated_returns[simulated_returns <= var_return]
            expected_shortfall_return = tail_returns.mean() if len(tail_returns) > 0 else var_return
            
            expected_shortfall_1d = abs(expected_shortfall_return * portfolio_value)
            expected_shortfall_5d = expected_shortfall_1d * np.sqrt(5)
            
            return VaRResult(
                var_1d=var_1d,
                var_5d=var_5d,
                var_10d=var_10d,
                expected_shortfall_1d=expected_shortfall_1d,
                expected_shortfall_5d=expected_shortfall_5d,
                confidence_level=confidence_level,
                method=VaRMethod.MONTE_CARLO,
                calculation_date=datetime.now(),
                portfolio_value=portfolio_value,
                details={
                    'num_simulations': num_simulations,
                    'var_return': var_return,
                    'expected_shortfall_return': expected_shortfall_return,
                    'simulation_mean': np.mean(simulated_returns),
                    'simulation_std': np.std(simulated_returns)
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error in Monte Carlo VaR calculation: {e}")
            # Fallback to parametric method
            portfolio_returns = self._calculate_portfolio_returns(portfolio, returns_data)
            return self._calculate_parametric_var(
                portfolio_returns, portfolio_value, confidence_level, time_horizons
            )  
  
    def calculate_volatility_metrics(self,
                                   returns_data: Dict[str, pd.Series],
                                   portfolio: Portfolio) -> VolatilityMetrics:
        """
        Calculate comprehensive volatility metrics.
        
        Args:
            returns_data: Historical returns data
            portfolio: Portfolio to analyze
            
        Returns:
            Volatility metrics
        """
        try:
            # Calculate portfolio returns
            portfolio_returns = self._calculate_portfolio_returns(portfolio, returns_data)
            
            if portfolio_returns.empty:
                self.logger.warning("No returns data for volatility calculation")
                return self._get_default_volatility_metrics()
            
            # Basic volatility calculations
            daily_vol = portfolio_returns.std()
            weekly_vol = daily_vol * np.sqrt(5)
            monthly_vol = daily_vol * np.sqrt(21)
            annual_vol = daily_vol * np.sqrt(252)
            
            # Volatility trend analysis
            recent_vol = portfolio_returns.tail(30).std() * np.sqrt(252)
            older_vol = portfolio_returns.tail(60).head(30).std() * np.sqrt(252)
            
            if recent_vol > older_vol * 1.1:
                vol_trend = "increasing"
            elif recent_vol < older_vol * 0.9:
                vol_trend = "decreasing"
            else:
                vol_trend = "stable"
            
            # EWMA volatility (exponentially weighted moving average)
            ewma_vol = self._calculate_ewma_volatility(portfolio_returns)
            
            # Realized volatility (using high-frequency data if available)
            realized_vol = self._calculate_realized_volatility(portfolio_returns)
            
            # Volatility percentile (current vs historical)
            vol_percentile = self._calculate_volatility_percentile(portfolio_returns, daily_vol)
            
            return VolatilityMetrics(
                daily_volatility=daily_vol,
                weekly_volatility=weekly_vol,
                monthly_volatility=monthly_vol,
                annualized_volatility=annual_vol,
                volatility_trend=vol_trend,
                ewma_volatility=ewma_vol,
                realized_volatility=realized_vol,
                volatility_percentile=vol_percentile
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating volatility metrics: {e}")
            return self._get_default_volatility_metrics()
    
    def calculate_correlation_analysis(self,
                                     returns_data: Dict[str, pd.Series],
                                     portfolio: Portfolio) -> CorrelationAnalysis:
        """
        Calculate correlation analysis for portfolio positions.
        
        Args:
            returns_data: Historical returns data
            portfolio: Portfolio to analyze
            
        Returns:
            Correlation analysis results
        """
        try:
            # Filter returns data to only include portfolio positions
            portfolio_symbols = list(portfolio.positions.keys())
            filtered_returns = {
                symbol: returns for symbol, returns in returns_data.items()
                if symbol in portfolio_symbols
            }
            
            if len(filtered_returns) < 2:
                self.logger.warning("Insufficient positions for correlation analysis")
                return self._get_default_correlation_analysis()
            
            # Create correlation matrix
            returns_df = pd.DataFrame(filtered_returns)
            correlation_matrix = returns_df.corr()
            
            # Calculate correlation statistics
            corr_values = correlation_matrix.values
            np.fill_diagonal(corr_values, np.nan)  # Exclude diagonal (self-correlation)
            
            avg_correlation = np.nanmean(corr_values)
            max_correlation = np.nanmax(corr_values)
            min_correlation = np.nanmin(corr_values)
            
            # Find highly correlated pairs (correlation > 0.7)
            highly_correlated = []
            for i, symbol1 in enumerate(correlation_matrix.columns):
                for j, symbol2 in enumerate(correlation_matrix.columns):
                    if i < j:  # Avoid duplicates
                        corr = correlation_matrix.iloc[i, j]
                        if abs(corr) > 0.7:
                            highly_correlated.append((symbol1, symbol2, corr))
            
            # Identify correlation clusters
            correlation_clusters = self._identify_correlation_clusters(correlation_matrix)
            
            # Calculate diversification ratio
            diversification_ratio = self._calculate_diversification_ratio(
                portfolio, returns_data, correlation_matrix
            )
            
            # Calculate effective number of positions
            effective_positions = self._calculate_effective_positions(
                portfolio, correlation_matrix
            )
            
            return CorrelationAnalysis(
                correlation_matrix=correlation_matrix,
                average_correlation=avg_correlation,
                max_correlation=max_correlation,
                min_correlation=min_correlation,
                highly_correlated_pairs=highly_correlated,
                correlation_clusters=correlation_clusters,
                diversification_ratio=diversification_ratio,
                effective_positions=effective_positions
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating correlation analysis: {e}")
            return self._get_default_correlation_analysis()
    
    def run_stress_tests(self,
                        portfolio: Portfolio,
                        returns_data: Dict[str, pd.Series],
                        scenarios: Optional[List[StressTestType]] = None) -> List[StressTestResult]:
        """
        Run stress tests on portfolio.
        
        Args:
            portfolio: Portfolio to test
            returns_data: Historical returns data
            scenarios: Stress test scenarios to run
            
        Returns:
            List of stress test results
        """
        if scenarios is None:
            scenarios = [
                StressTestType.MARKET_CRASH,
                StressTestType.VOLATILITY_SPIKE,
                StressTestType.CORRELATION_BREAKDOWN
            ]
        
        results = []
        
        for scenario_type in scenarios:
            try:
                if scenario_type == StressTestType.MARKET_CRASH:
                    result = self._stress_test_market_crash(portfolio, returns_data)
                elif scenario_type == StressTestType.VOLATILITY_SPIKE:
                    result = self._stress_test_volatility_spike(portfolio, returns_data)
                elif scenario_type == StressTestType.CORRELATION_BREAKDOWN:
                    result = self._stress_test_correlation_breakdown(portfolio, returns_data)
                elif scenario_type == StressTestType.INTEREST_RATE_SHOCK:
                    result = self._stress_test_interest_rate_shock(portfolio, returns_data)
                elif scenario_type == StressTestType.LIQUIDITY_CRISIS:
                    result = self._stress_test_liquidity_crisis(portfolio, returns_data)
                else:
                    continue
                
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"Error in stress test {scenario_type.value}: {e}")
                continue
        
        return results
    
    def generate_risk_report(self,
                           portfolio: Portfolio,
                           returns_data: Dict[str, pd.Series],
                           include_stress_tests: bool = True) -> RiskReport:
        """
        Generate comprehensive risk report.
        
        Args:
            portfolio: Portfolio to analyze
            returns_data: Historical returns data
            include_stress_tests: Whether to include stress tests
            
        Returns:
            Comprehensive risk report
        """
        try:
            # Calculate VaR
            var_results = self.calculate_var(portfolio, returns_data)
            primary_var = var_results.get('historical_0.95') or var_results.get('parametric_0.95')
            
            if not primary_var:
                primary_var = VaRResult(
                    var_1d=0, var_5d=0, var_10d=0,
                    expected_shortfall_1d=0, expected_shortfall_5d=0,
                    confidence_level=0.95, method=VaRMethod.HISTORICAL,
                    calculation_date=datetime.now(), portfolio_value=portfolio.total_value
                )
            
            # Calculate volatility metrics
            volatility_metrics = self.calculate_volatility_metrics(returns_data, portfolio)
            
            # Calculate correlation analysis
            correlation_analysis = self.calculate_correlation_analysis(returns_data, portfolio)
            
            # Run stress tests
            stress_test_results = []
            if include_stress_tests:
                stress_test_results = self.run_stress_tests(portfolio, returns_data)
            
            # Generate risk summary
            risk_summary = self._generate_risk_summary(
                portfolio, primary_var, volatility_metrics, 
                correlation_analysis, stress_test_results
            )
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                portfolio, primary_var, volatility_metrics,
                correlation_analysis, stress_test_results
            )
            
            return RiskReport(
                report_date=datetime.now(),
                portfolio_value=portfolio.total_value,
                var_results=primary_var,
                volatility_metrics=volatility_metrics,
                correlation_analysis=correlation_analysis,
                stress_test_results=stress_test_results,
                risk_summary=risk_summary,
                recommendations=recommendations
            )
            
        except Exception as e:
            self.logger.error(f"Error generating risk report: {e}")
            raise 
   
    # Helper methods
    
    def _calculate_portfolio_returns(self,
                                   portfolio: Portfolio,
                                   returns_data: Dict[str, pd.Series]) -> pd.Series:
        """Calculate portfolio returns from individual asset returns."""
        try:
            weights = self._get_portfolio_weights(portfolio)
            
            # Filter returns data to portfolio positions
            portfolio_returns_data = {}
            for symbol in portfolio.positions.keys():
                if symbol in returns_data:
                    portfolio_returns_data[symbol] = returns_data[symbol]
            
            if not portfolio_returns_data:
                return pd.Series()
            
            # Create returns DataFrame
            returns_df = pd.DataFrame(portfolio_returns_data)
            
            # Calculate weighted portfolio returns
            portfolio_returns = (returns_df * weights).sum(axis=1)
            
            return portfolio_returns.dropna()
            
        except Exception as e:
            self.logger.error(f"Error calculating portfolio returns: {e}")
            return pd.Series()
    
    def _get_portfolio_weights(self, portfolio: Portfolio) -> np.ndarray:
        """Get portfolio weights as numpy array."""
        total_value = sum(abs(pos.market_value) for pos in portfolio.positions.values())
        
        if total_value == 0:
            return np.array([])
        
        weights = []
        for position in portfolio.positions.values():
            weight = abs(position.market_value) / total_value
            weights.append(weight)
        
        return np.array(weights)
    
    def _calculate_correlation_matrix(self, returns_data: Dict[str, pd.Series]) -> pd.DataFrame:
        """Calculate correlation matrix from returns data."""
        returns_df = pd.DataFrame(returns_data)
        return returns_df.corr()
    
    def _calculate_ewma_volatility(self, returns: pd.Series, lambda_param: float = 0.94) -> float:
        """Calculate EWMA (Exponentially Weighted Moving Average) volatility."""
        try:
            if len(returns) < 2:
                return 0.0
            
            # Calculate squared returns
            squared_returns = returns ** 2
            
            # Calculate EWMA variance
            ewma_var = squared_returns.iloc[0]  # Initialize with first observation
            
            for i in range(1, len(squared_returns)):
                ewma_var = lambda_param * ewma_var + (1 - lambda_param) * squared_returns.iloc[i]
            
            return np.sqrt(ewma_var * 252)  # Annualized
            
        except Exception as e:
            self.logger.error(f"Error calculating EWMA volatility: {e}")
            return 0.0
    
    def _calculate_realized_volatility(self, returns: pd.Series) -> float:
        """Calculate realized volatility."""
        try:
            # For daily returns, realized volatility is just the standard deviation
            return returns.std() * np.sqrt(252)
        except Exception as e:
            self.logger.error(f"Error calculating realized volatility: {e}")
            return 0.0
    
    def _calculate_volatility_percentile(self, returns: pd.Series, current_vol: float) -> float:
        """Calculate current volatility percentile vs historical."""
        try:
            # Calculate rolling 30-day volatilities
            rolling_vols = returns.rolling(window=30).std()
            rolling_vols = rolling_vols.dropna()
            
            if len(rolling_vols) == 0:
                return 50.0  # Default to median
            
            # Calculate percentile of current volatility
            percentile = stats.percentileofscore(rolling_vols, current_vol)
            return percentile
            
        except Exception as e:
            self.logger.error(f"Error calculating volatility percentile: {e}")
            return 50.0 
   
    def _identify_correlation_clusters(self, correlation_matrix: pd.DataFrame) -> Dict[str, List[str]]:
        """Identify correlation clusters using hierarchical clustering."""
        try:
            from scipy.cluster.hierarchy import linkage, fcluster
            from scipy.spatial.distance import squareform
            
            # Convert correlation to distance
            distance_matrix = 1 - correlation_matrix.abs()
            
            # Perform hierarchical clustering
            condensed_distances = squareform(distance_matrix.values)
            linkage_matrix = linkage(condensed_distances, method='ward')
            
            # Get clusters (using distance threshold of 0.5)
            clusters = fcluster(linkage_matrix, t=0.5, criterion='distance')
            
            # Group symbols by cluster
            cluster_dict = {}
            for i, symbol in enumerate(correlation_matrix.columns):
                cluster_id = f"cluster_{clusters[i]}"
                if cluster_id not in cluster_dict:
                    cluster_dict[cluster_id] = []
                cluster_dict[cluster_id].append(symbol)
            
            # Filter out single-symbol clusters
            return {k: v for k, v in cluster_dict.items() if len(v) > 1}
            
        except Exception as e:
            self.logger.error(f"Error identifying correlation clusters: {e}")
            return {}
    
    def _calculate_diversification_ratio(self,
                                       portfolio: Portfolio,
                                       returns_data: Dict[str, pd.Series],
                                       correlation_matrix: pd.DataFrame) -> float:
        """Calculate diversification ratio."""
        try:
            weights = self._get_portfolio_weights(portfolio)
            
            if len(weights) == 0:
                return 1.0
            
            # Calculate individual volatilities
            individual_vols = []
            for symbol in portfolio.positions.keys():
                if symbol in returns_data:
                    vol = returns_data[symbol].std() * np.sqrt(252)
                    individual_vols.append(vol)
                else:
                    individual_vols.append(0.0)
            
            individual_vols = np.array(individual_vols)
            
            # Weighted average of individual volatilities
            weighted_avg_vol = np.sum(weights * individual_vols)
            
            # Portfolio volatility
            portfolio_returns = self._calculate_portfolio_returns(portfolio, returns_data)
            portfolio_vol = portfolio_returns.std() * np.sqrt(252)
            
            if portfolio_vol == 0:
                return 1.0
            
            # Diversification ratio
            diversification_ratio = weighted_avg_vol / portfolio_vol
            
            return diversification_ratio
            
        except Exception as e:
            self.logger.error(f"Error calculating diversification ratio: {e}")
            return 1.0
    
    def _calculate_effective_positions(self,
                                     portfolio: Portfolio,
                                     correlation_matrix: pd.DataFrame) -> float:
        """Calculate effective number of positions."""
        try:
            weights = self._get_portfolio_weights(portfolio)
            
            if len(weights) == 0:
                return 0.0
            
            # Herfindahl index
            herfindahl_index = np.sum(weights ** 2)
            
            # Effective number of positions
            effective_positions = 1.0 / herfindahl_index
            
            return effective_positions
            
        except Exception as e:
            self.logger.error(f"Error calculating effective positions: {e}")
            return len(portfolio.positions) 
   
    # Stress test methods
    
    def _stress_test_market_crash(self,
                                portfolio: Portfolio,
                                returns_data: Dict[str, pd.Series]) -> StressTestResult:
        """Stress test: Market crash scenario (-20% market drop)."""
        scenario_name = "Market Crash (-20%)"
        market_shock = -0.20
        
        # Apply shock to all positions
        total_loss = 0.0
        position_impacts = {}
        
        for symbol, position in portfolio.positions.items():
            position_loss = position.market_value * market_shock
            total_loss += position_loss
            position_impacts[symbol] = position_loss
        
        loss_pct = total_loss / portfolio.total_value if portfolio.total_value > 0 else 0
        
        return StressTestResult(
            scenario_name=scenario_name,
            scenario_type=StressTestType.MARKET_CRASH,
            portfolio_loss=abs(total_loss),
            portfolio_loss_pct=abs(loss_pct),
            position_impacts=position_impacts,
            var_breach=True,  # Market crash typically breaches VaR
            recovery_time_estimate=180,  # Estimated 6 months recovery
            scenario_probability=0.05,  # 5% probability
            details={
                'market_shock': market_shock,
                'affected_positions': len(position_impacts)
            }
        )
    
    def _stress_test_volatility_spike(self,
                                    portfolio: Portfolio,
                                    returns_data: Dict[str, pd.Series]) -> StressTestResult:
        """Stress test: Volatility spike scenario (3x normal volatility)."""
        scenario_name = "Volatility Spike (3x Normal)"
        vol_multiplier = 3.0
        
        # Calculate portfolio returns and volatility
        portfolio_returns = self._calculate_portfolio_returns(portfolio, returns_data)
        
        if portfolio_returns.empty:
            normal_vol = 0.02  # Default 2% daily volatility
        else:
            normal_vol = portfolio_returns.std()
        
        stressed_vol = normal_vol * vol_multiplier
        
        # Estimate potential loss (2 standard deviations)
        potential_loss_pct = 2 * stressed_vol
        total_loss = portfolio.total_value * potential_loss_pct
        
        # Distribute loss across positions proportionally
        position_impacts = {}
        for symbol, position in portfolio.positions.items():
            position_weight = position.market_value / portfolio.total_value if portfolio.total_value > 0 else 0
            position_loss = total_loss * position_weight
            position_impacts[symbol] = position_loss
        
        return StressTestResult(
            scenario_name=scenario_name,
            scenario_type=StressTestType.VOLATILITY_SPIKE,
            portfolio_loss=total_loss,
            portfolio_loss_pct=potential_loss_pct,
            position_impacts=position_impacts,
            var_breach=True,
            recovery_time_estimate=30,  # Estimated 1 month recovery
            scenario_probability=0.10,  # 10% probability
            details={
                'normal_volatility': normal_vol,
                'stressed_volatility': stressed_vol,
                'volatility_multiplier': vol_multiplier
            }
        )
    
    def _stress_test_correlation_breakdown(self,
                                         portfolio: Portfolio,
                                         returns_data: Dict[str, pd.Series]) -> StressTestResult:
        """Stress test: Correlation breakdown scenario (all correlations go to 1)."""
        scenario_name = "Correlation Breakdown (All Correlations → 1)"
        
        # Calculate current diversification benefit
        portfolio_returns = self._calculate_portfolio_returns(portfolio, returns_data)
        
        if portfolio_returns.empty:
            # Estimate based on position weights
            weights = self._get_portfolio_weights(portfolio)
            if len(weights) == 0:
                diversification_loss = 0.0
            else:
                # Assume average individual volatility of 25%
                avg_individual_vol = 0.25
                weighted_avg_vol = avg_individual_vol
                # In perfect correlation scenario, portfolio vol = weighted average vol
                diversification_loss_pct = 0.05  # Estimate 5% additional risk
        else:
            current_vol = portfolio_returns.std()
            # Estimate volatility if all correlations were 1
            individual_vols = []
            weights = self._get_portfolio_weights(portfolio)
            
            for symbol in portfolio.positions.keys():
                if symbol in returns_data:
                    vol = returns_data[symbol].std()
                    individual_vols.append(vol)
                else:
                    individual_vols.append(current_vol)  # Use portfolio vol as fallback
            
            if individual_vols and len(weights) > 0:
                weighted_avg_vol = np.sum(weights * np.array(individual_vols))
                diversification_loss_pct = max(0, weighted_avg_vol - current_vol)
            else:
                diversification_loss_pct = 0.05
        
        # Estimate potential loss from correlation breakdown
        total_loss = portfolio.total_value * diversification_loss_pct
        
        # Distribute impact across positions
        position_impacts = {}
        for symbol, position in portfolio.positions.items():
            position_weight = position.market_value / portfolio.total_value if portfolio.total_value > 0 else 0
            position_impact = total_loss * position_weight
            position_impacts[symbol] = position_impact
        
        return StressTestResult(
            scenario_name=scenario_name,
            scenario_type=StressTestType.CORRELATION_BREAKDOWN,
            portfolio_loss=total_loss,
            portfolio_loss_pct=diversification_loss_pct,
            position_impacts=position_impacts,
            var_breach=False,  # Usually doesn't breach VaR by itself
            recovery_time_estimate=60,  # Estimated 2 months
            scenario_probability=0.15,  # 15% probability during crisis
            details={
                'diversification_loss_pct': diversification_loss_pct,
                'correlation_assumption': 1.0
            }
        ) 
   
    def _stress_test_interest_rate_shock(self,
                                       portfolio: Portfolio,
                                       returns_data: Dict[str, pd.Series]) -> StressTestResult:
        """Stress test: Interest rate shock scenario (+200 bps)."""
        scenario_name = "Interest Rate Shock (+200 bps)"
        rate_shock = 0.02  # 200 basis points
        
        # Estimate impact based on portfolio duration (simplified)
        # Assume average duration of 5 years for equity portfolios
        estimated_duration = 5.0
        
        # Price impact = -Duration × ΔYield
        price_impact_pct = -estimated_duration * rate_shock * 0.5  # Reduced impact for equities
        
        total_loss = portfolio.total_value * abs(price_impact_pct)
        
        # Distribute impact across positions
        position_impacts = {}
        for symbol, position in portfolio.positions.items():
            position_loss = position.market_value * abs(price_impact_pct)
            position_impacts[symbol] = position_loss
        
        return StressTestResult(
            scenario_name=scenario_name,
            scenario_type=StressTestType.INTEREST_RATE_SHOCK,
            portfolio_loss=total_loss,
            portfolio_loss_pct=abs(price_impact_pct),
            position_impacts=position_impacts,
            var_breach=False,
            recovery_time_estimate=90,  # Estimated 3 months
            scenario_probability=0.20,  # 20% probability
            details={
                'rate_shock_bps': rate_shock * 10000,
                'estimated_duration': estimated_duration,
                'price_impact_pct': price_impact_pct
            }
        )
    
    def _stress_test_liquidity_crisis(self,
                                    portfolio: Portfolio,
                                    returns_data: Dict[str, pd.Series]) -> StressTestResult:
        """Stress test: Liquidity crisis scenario (increased bid-ask spreads)."""
        scenario_name = "Liquidity Crisis (High Bid-Ask Spreads)"
        
        # Estimate liquidity impact based on position sizes
        # Larger positions face higher liquidity costs
        total_impact = 0.0
        position_impacts = {}
        
        for symbol, position in portfolio.positions.items():
            position_weight = position.market_value / portfolio.total_value if portfolio.total_value > 0 else 0
            
            # Liquidity cost increases with position size
            base_spread = 0.005  # 0.5% base spread
            size_multiplier = 1 + position_weight * 2  # Larger positions face higher costs
            liquidity_cost_pct = base_spread * size_multiplier
            
            position_impact = position.market_value * liquidity_cost_pct
            total_impact += position_impact
            position_impacts[symbol] = position_impact
        
        loss_pct = total_impact / portfolio.total_value if portfolio.total_value > 0 else 0
        
        return StressTestResult(
            scenario_name=scenario_name,
            scenario_type=StressTestType.LIQUIDITY_CRISIS,
            portfolio_loss=total_impact,
            portfolio_loss_pct=loss_pct,
            position_impacts=position_impacts,
            var_breach=False,
            recovery_time_estimate=45,  # Estimated 1.5 months
            scenario_probability=0.12,  # 12% probability
            details={
                'base_spread_pct': 0.5,
                'liquidity_methodology': 'position_size_weighted'
            }
        )
    
    # Default/fallback methods
    
    def _get_default_volatility_metrics(self) -> VolatilityMetrics:
        """Get default volatility metrics when calculation fails."""
        return VolatilityMetrics(
            daily_volatility=0.0,
            weekly_volatility=0.0,
            monthly_volatility=0.0,
            annualized_volatility=0.0,
            volatility_trend="stable"
        )
    
    def _get_default_correlation_analysis(self) -> CorrelationAnalysis:
        """Get default correlation analysis when calculation fails."""
        return CorrelationAnalysis(
            correlation_matrix=pd.DataFrame(),
            average_correlation=0.0,
            max_correlation=0.0,
            min_correlation=0.0,
            highly_correlated_pairs=[],
            correlation_clusters={},
            diversification_ratio=1.0,
            effective_positions=1.0
        )
    
    # Report generation methods
    
    def _generate_risk_summary(self,
                             portfolio: Portfolio,
                             var_result: VaRResult,
                             volatility_metrics: VolatilityMetrics,
                             correlation_analysis: CorrelationAnalysis,
                             stress_test_results: List[StressTestResult]) -> Dict[str, Any]:
        """Generate risk summary."""
        # Calculate risk level
        var_pct = var_result.var_1d / portfolio.total_value if portfolio.total_value > 0 else 0
        
        if var_pct > 0.05:  # > 5%
            risk_level = "HIGH"
        elif var_pct > 0.02:  # > 2%
            risk_level = "MEDIUM"
        else:
            risk_level = "LOW"
        
        # Count stress test breaches
        stress_breaches = sum(1 for result in stress_test_results if result.var_breach)
        
        return {
            'overall_risk_level': risk_level,
            'var_1d_amount': var_result.var_1d,
            'var_1d_pct': var_pct * 100,
            'expected_shortfall_1d': var_result.expected_shortfall_1d,
            'portfolio_volatility': volatility_metrics.annualized_volatility * 100,
            'volatility_trend': volatility_metrics.volatility_trend,
            'average_correlation': correlation_analysis.average_correlation,
            'diversification_ratio': correlation_analysis.diversification_ratio,
            'effective_positions': correlation_analysis.effective_positions,
            'stress_tests_run': len(stress_test_results),
            'stress_test_breaches': stress_breaches,
            'max_stress_loss_pct': max([r.portfolio_loss_pct for r in stress_test_results], default=0) * 100
        }
    
    def _generate_recommendations(self,
                                portfolio: Portfolio,
                                var_result: VaRResult,
                                volatility_metrics: VolatilityMetrics,
                                correlation_analysis: CorrelationAnalysis,
                                stress_test_results: List[StressTestResult]) -> List[str]:
        """Generate risk management recommendations."""
        recommendations = []
        
        # VaR-based recommendations
        var_pct = var_result.var_1d / portfolio.total_value if portfolio.total_value > 0 else 0
        if var_pct > 0.05:
            recommendations.append("Consider reducing position sizes - daily VaR exceeds 5% of portfolio value")
        
        # Volatility recommendations
        if volatility_metrics.volatility_trend == "increasing":
            recommendations.append("Portfolio volatility is increasing - consider defensive positioning")
        
        if volatility_metrics.annualized_volatility > 0.25:
            recommendations.append("High portfolio volatility (>25%) - consider volatility reduction strategies")
        
        # Correlation recommendations
        if correlation_analysis.average_correlation > 0.7:
            recommendations.append("High average correlation (>0.7) - improve diversification")
        
        if len(correlation_analysis.highly_correlated_pairs) > 3:
            recommendations.append("Multiple highly correlated positions detected - reduce concentration risk")
        
        if correlation_analysis.effective_positions < len(portfolio.positions) * 0.5:
            recommendations.append("Low effective diversification - consider more uncorrelated positions")
        
        # Stress test recommendations
        severe_stress_losses = [r for r in stress_test_results if r.portfolio_loss_pct > 0.15]
        if severe_stress_losses:
            recommendations.append("Portfolio vulnerable to severe stress scenarios - consider hedging strategies")
        
        # Position concentration recommendations
        position_weights = []
        for position in portfolio.positions.values():
            weight = position.market_value / portfolio.total_value if portfolio.total_value > 0 else 0
            position_weights.append(weight)
        
        if position_weights and max(position_weights) > 0.2:
            recommendations.append("Large position concentration detected - consider position size limits")
        
        # Default recommendation if no issues found
        if not recommendations:
            recommendations.append("Portfolio risk profile appears well-managed - continue monitoring")
        
        return recommendations