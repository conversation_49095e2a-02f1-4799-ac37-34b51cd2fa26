"""
Main risk management system that coordinates all risk controls.
"""

import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Callable
import logging
import pandas as pd
import numpy as np

from .models import RiskConfig, RiskViolation, RiskLevel, RiskMetrics, StopLossOrder, TakeProfitOrder
from .rules import RiskRule, PositionSizeRule, StopLossRule, DrawdownRule, RiskBudgetRule
from .monitors import RiskMonitor, DrawdownMonitor, PositionMonitor, RiskBudgetMonitor
from .metrics import RiskMetricsCalculator
from ..models.portfolio import Portfolio
from ..models.trading import Order, Signal, OrderSide, OrderType, OrderStatus


class RiskManager:
    """
    Comprehensive risk management system.
    
    Coordinates all risk rules, monitors, and controls to ensure
    portfolio safety and compliance with risk limits.
    """
    
    def __init__(self, config: Optional[RiskConfig] = None):
        """
        Initialize risk manager.
        
        Args:
            config: Risk configuration, uses defaults if not provided
        """
        self.config = config or RiskConfig()
        self.logger = logging.getLogger(f"{__name__}.RiskManager")
        
        # Risk rules
        self.rules: Dict[str, RiskRule] = {}
        self._initialize_rules()
        
        # Risk monitors
        self.monitors: Dict[str, RiskMonitor] = {}
        self._initialize_monitors()
        
        # State tracking
        self.violations_history: List[RiskViolation] = []
        self.emergency_stop_active = False
        self.last_risk_check = None
        
        # Alert callbacks
        self.alert_callbacks: List[Callable[[RiskViolation], None]] = []
        
        # Performance tracking
        self.risk_metrics_history: List[RiskMetrics] = []
        
        # Advanced risk metrics calculator
        self.metrics_calculator = RiskMetricsCalculator(self.config)
        
        self.logger.info("Risk manager initialized")
    
    def _initialize_rules(self) -> None:
        """Initialize risk rules."""
        self.rules['position_size'] = PositionSizeRule()
        self.rules['stop_loss'] = StopLossRule()
        self.rules['drawdown'] = DrawdownRule()
        self.rules['risk_budget'] = RiskBudgetRule()
        
        self.logger.info(f"Initialized {len(self.rules)} risk rules")
    
    def _initialize_monitors(self) -> None:
        """Initialize risk monitors."""
        self.monitors['drawdown'] = DrawdownMonitor(self.config)
        self.monitors['position'] = PositionMonitor(self.config)
        self.monitors['risk_budget'] = RiskBudgetMonitor(self.config)
        
        # Add alert callbacks to monitors
        for monitor in self.monitors.values():
            monitor.add_alert_callback(self._handle_monitor_alert)
        
        self.logger.info(f"Initialized {len(self.monitors)} risk monitors")
    
    def check_order(self, order: Order, portfolio: Portfolio) -> Tuple[bool, List[RiskViolation]]:
        """
        Check if an order is allowed under current risk rules.
        
        Args:
            order: Order to check
            portfolio: Current portfolio state
            
        Returns:
            Tuple of (is_allowed, violations_list)
        """
        if self.emergency_stop_active:
            violation = RiskViolation(
                rule_name="Emergency Stop",
                rule_type="emergency",
                violation_type="emergency_stop_active",
                current_value=1,
                limit_value=0,
                severity=RiskLevel.CRITICAL,
                timestamp=datetime.now(),
                symbol=order.symbol,
                strategy_id=order.strategy_id,
                details={'reason': 'Emergency stop is active'}
            )
            return False, [violation]
        
        violations = []
        
        try:
            # Check all enabled rules
            for rule_name, rule in self.rules.items():
                if not rule.is_enabled():
                    continue
                
                violation = rule.check_order(order, portfolio, self.config)
                if violation:
                    violations.append(violation)
                    self.violations_history.append(violation)
            
            # Determine if order should be allowed
            critical_violations = [v for v in violations if v.severity == RiskLevel.CRITICAL]
            high_violations = [v for v in violations if v.severity == RiskLevel.HIGH]
            
            # Reject if any critical violations or any high violations
            should_reject = len(critical_violations) > 0 or len(high_violations) > 0
            
            if should_reject:
                self.logger.warning(f"Order rejected: {len(critical_violations)} critical, {len(high_violations)} high violations")
            
            # Trigger alerts for violations
            for violation in violations:
                self._trigger_alert(violation)
            
            self.last_risk_check = datetime.now()
            return not should_reject, violations
            
        except Exception as e:
            self.logger.error(f"Error checking order risk: {e}")
            # Fail safe - reject order on error
            error_violation = RiskViolation(
                rule_name="System Error",
                rule_type="system",
                violation_type="check_error",
                current_value=0,
                limit_value=0,
                severity=RiskLevel.CRITICAL,
                timestamp=datetime.now(),
                symbol=order.symbol,
                strategy_id=order.strategy_id,
                details={'error': str(e)}
            )
            return False, [error_violation]
    
    def check_portfolio(self, portfolio: Portfolio) -> List[RiskViolation]:
        """
        Check portfolio for risk violations.
        
        Args:
            portfolio: Portfolio to check
            
        Returns:
            List of risk violations
        """
        violations = []
        
        try:
            # Check all enabled rules
            for rule_name, rule in self.rules.items():
                if not rule.is_enabled():
                    continue
                
                rule_violations = rule.check_portfolio(portfolio, self.config)
                violations.extend(rule_violations)
            
            # Store violations
            self.violations_history.extend(violations)
            
            # Check for emergency stop conditions
            self._check_emergency_stop(violations, portfolio)
            
            # Trigger alerts
            for violation in violations:
                self._trigger_alert(violation)
            
            self.last_risk_check = datetime.now()
            return violations
            
        except Exception as e:
            self.logger.error(f"Error checking portfolio risk: {e}")
            return []
    
    def update_monitors(self, portfolio: Portfolio, market_data: Dict[str, float]) -> None:
        """
        Update all risk monitors with latest data.
        
        Args:
            portfolio: Current portfolio state
            market_data: Current market prices
        """
        try:
            for monitor_name, monitor in self.monitors.items():
                if monitor.enabled:
                    monitor.update(portfolio, market_data)
            
            # Check stop loss and take profit triggers
            self._check_stop_triggers(market_data)
            
        except Exception as e:
            self.logger.error(f"Error updating risk monitors: {e}")
    
    def calculate_advanced_risk_metrics(self, 
                                      portfolio: Portfolio,
                                      returns_data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """
        Calculate advanced risk metrics using the risk metrics calculator.
        
        Args:
            portfolio: Portfolio to analyze
            returns_data: Historical returns data for each symbol
            
        Returns:
            Dictionary containing advanced risk metrics
        """
        try:
            # Generate comprehensive risk report
            risk_report = self.metrics_calculator.generate_risk_report(
                portfolio, returns_data, include_stress_tests=True
            )
            
            return {
                'var_results': {
                    'var_1d': risk_report.var_results.var_1d,
                    'var_5d': risk_report.var_results.var_5d,
                    'expected_shortfall_1d': risk_report.var_results.expected_shortfall_1d,
                    'confidence_level': risk_report.var_results.confidence_level,
                    'method': risk_report.var_results.method.value
                },
                'volatility_metrics': {
                    'daily_volatility': risk_report.volatility_metrics.daily_volatility,
                    'annualized_volatility': risk_report.volatility_metrics.annualized_volatility,
                    'volatility_trend': risk_report.volatility_metrics.volatility_trend,
                    'ewma_volatility': risk_report.volatility_metrics.ewma_volatility
                },
                'correlation_analysis': {
                    'average_correlation': risk_report.correlation_analysis.average_correlation,
                    'max_correlation': risk_report.correlation_analysis.max_correlation,
                    'diversification_ratio': risk_report.correlation_analysis.diversification_ratio,
                    'effective_positions': risk_report.correlation_analysis.effective_positions
                },
                'stress_test_summary': {
                    'tests_run': len(risk_report.stress_test_results),
                    'max_loss_pct': max([r.portfolio_loss_pct for r in risk_report.stress_test_results], default=0),
                    'var_breaches': sum(1 for r in risk_report.stress_test_results if r.var_breach)
                },
                'risk_summary': risk_report.risk_summary,
                'recommendations': risk_report.recommendations
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating advanced risk metrics: {e}")
            return {}
    
    def calculate_risk_metrics(self, portfolio: Portfolio, 
                             market_data: Optional[Dict[str, float]] = None) -> RiskMetrics:
        """
        Calculate comprehensive risk metrics.
        
        Args:
            portfolio: Portfolio to analyze
            market_data: Current market prices
            
        Returns:
            Risk metrics
        """
        try:
            # Basic portfolio metrics
            total_positions = len(portfolio.positions)
            largest_position_pct = 0.0
            largest_position_symbol = None
            position_values = []  # Initialize outside the if block
            
            if portfolio.positions:
                position_values = [(symbol, pos.market_value) for symbol, pos in portfolio.positions.items()]
                position_values.sort(key=lambda x: abs(x[1]), reverse=True)
                
                if position_values:
                    largest_position_symbol, largest_value = position_values[0]
                    largest_position_pct = abs(largest_value) / portfolio.total_value if portfolio.total_value > 0 else 0
            
            # Leverage calculations
            total_long_value = sum(pos.market_value for pos in portfolio.positions.values() if pos.market_value > 0)
            total_short_value = sum(abs(pos.market_value) for pos in portfolio.positions.values() if pos.market_value < 0)
            total_position_value = total_long_value + total_short_value
            
            gross_leverage = total_position_value / portfolio.total_value if portfolio.total_value > 0 else 0
            net_leverage = abs(total_long_value - total_short_value) / portfolio.total_value if portfolio.total_value > 0 else 0
            
            long_exposure_pct = total_long_value / portfolio.total_value if portfolio.total_value > 0 else 0
            short_exposure_pct = total_short_value / portfolio.total_value if portfolio.total_value > 0 else 0
            
            # Top 5 positions concentration
            top_5_value = sum(abs(value) for _, value in position_values[:5]) if len(position_values) >= 5 else total_position_value
            top_5_positions_pct = top_5_value / portfolio.total_value if portfolio.total_value > 0 else 0
            
            # Drawdown metrics from monitor
            drawdown_monitor = self.monitors.get('drawdown')
            current_drawdown_pct = 0.0
            max_drawdown_pct = 0.0
            drawdown_duration_days = 0
            
            if isinstance(drawdown_monitor, DrawdownMonitor):
                current_drawdown_pct = abs(drawdown_monitor.current_drawdown) * 100
                max_drawdown_pct = abs(drawdown_monitor.max_drawdown) * 100
                drawdown_duration_days = drawdown_monitor.drawdown_duration
            
            # Risk budget utilization
            risk_budget_monitor = self.monitors.get('risk_budget')
            daily_risk_used_pct = 0.0
            weekly_risk_used_pct = 0.0
            monthly_risk_used_pct = 0.0
            
            if isinstance(risk_budget_monitor, RiskBudgetMonitor):
                status = risk_budget_monitor.get_status()
                daily_risk_used_pct = status['daily']['utilization_pct']
                weekly_risk_used_pct = status['weekly']['utilization_pct']
                monthly_risk_used_pct = status['monthly']['utilization_pct']
            
            # Create risk metrics
            metrics = RiskMetrics(
                timestamp=datetime.now(),
                portfolio_value=portfolio.total_value,
                cash=portfolio.cash,
                total_positions=total_positions,
                largest_position_pct=largest_position_pct,
                largest_position_symbol=largest_position_symbol,
                portfolio_beta=1.0,  # Simplified - would need market data for proper calculation
                portfolio_var_1d=0.0,  # Simplified - would need historical returns
                portfolio_var_5d=0.0,  # Simplified - would need historical returns
                expected_shortfall=0.0,  # Simplified - would need historical returns
                current_drawdown_pct=current_drawdown_pct,
                max_drawdown_pct=max_drawdown_pct,
                drawdown_duration_days=drawdown_duration_days,
                gross_leverage=gross_leverage,
                net_leverage=net_leverage,
                long_exposure_pct=long_exposure_pct,
                short_exposure_pct=short_exposure_pct,
                top_5_positions_pct=top_5_positions_pct,
                daily_risk_used_pct=daily_risk_used_pct,
                weekly_risk_used_pct=weekly_risk_used_pct,
                monthly_risk_used_pct=monthly_risk_used_pct
            )
            
            # Store in history
            self.risk_metrics_history.append(metrics)
            
            # Keep only last 1000 records
            if len(self.risk_metrics_history) > 1000:
                self.risk_metrics_history = self.risk_metrics_history[-1000:]
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error calculating risk metrics: {e}")
            # Return basic metrics on error
            return RiskMetrics(
                timestamp=datetime.now(),
                portfolio_value=portfolio.total_value,
                cash=portfolio.cash,
                total_positions=len(portfolio.positions),
                largest_position_pct=0.0,
                largest_position_symbol=None,
                portfolio_beta=1.0,
                portfolio_var_1d=0.0,
                portfolio_var_5d=0.0,
                expected_shortfall=0.0,
                current_drawdown_pct=0.0,
                max_drawdown_pct=0.0,
                drawdown_duration_days=0,
                gross_leverage=0.0,
                net_leverage=0.0,
                long_exposure_pct=0.0,
                short_exposure_pct=0.0,
                top_5_positions_pct=0.0,
                daily_risk_used_pct=0.0,
                weekly_risk_used_pct=0.0,
                monthly_risk_used_pct=0.0
            )
    
    def add_stop_loss(self, symbol: str, stop_price: float, quantity: float,
                     stop_type: str = 'fixed', trail_amount: Optional[float] = None,
                     strategy_id: Optional[str] = None) -> bool:
        """
        Add stop loss order.
        
        Args:
            symbol: Symbol to add stop loss for
            stop_price: Stop loss price
            quantity: Position quantity
            stop_type: Type of stop ('fixed', 'trailing', 'percentage')
            trail_amount: Trailing amount for trailing stops
            strategy_id: Strategy ID
            
        Returns:
            True if added successfully
        """
        try:
            stop_loss_rule = self.rules.get('stop_loss')
            if isinstance(stop_loss_rule, StopLossRule):
                stop_loss_rule.add_stop_loss(
                    symbol=symbol,
                    stop_price=stop_price,
                    quantity=quantity,
                    stop_type=stop_type,
                    trail_amount=trail_amount,
                    strategy_id=strategy_id
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error adding stop loss: {e}")
            return False
    
    def add_take_profit(self, symbol: str, target_price: float, quantity: float,
                       strategy_id: Optional[str] = None) -> bool:
        """
        Add take profit order.
        
        Args:
            symbol: Symbol to add take profit for
            target_price: Take profit price
            quantity: Position quantity
            strategy_id: Strategy ID
            
        Returns:
            True if added successfully
        """
        try:
            stop_loss_rule = self.rules.get('stop_loss')
            if isinstance(stop_loss_rule, StopLossRule):
                stop_loss_rule.add_take_profit(
                    symbol=symbol,
                    target_price=target_price,
                    quantity=quantity,
                    strategy_id=strategy_id
                )
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error adding take profit: {e}")
            return False
    
    def _check_stop_triggers(self, market_data: Dict[str, float]) -> List[Dict[str, Any]]:
        """Check for stop loss and take profit triggers."""
        triggers = []
        
        try:
            stop_loss_rule = self.rules.get('stop_loss')
            if isinstance(stop_loss_rule, StopLossRule):
                triggers = stop_loss_rule.check_triggers(market_data)
                
                # Log triggers
                for trigger in triggers:
                    self.logger.info(f"Stop trigger: {trigger['type']} for {trigger['symbol']} at {trigger['current_price']}")
            
        except Exception as e:
            self.logger.error(f"Error checking stop triggers: {e}")
        
        return triggers
    
    def record_trade_risk(self, symbol: str, trade_value: float, 
                         strategy_id: Optional[str] = None) -> None:
        """
        Record risk usage from a trade.
        
        Args:
            symbol: Symbol traded
            trade_value: Value of the trade
            strategy_id: Strategy ID
        """
        try:
            # Record in risk budget monitor
            risk_budget_monitor = self.monitors.get('risk_budget')
            if isinstance(risk_budget_monitor, RiskBudgetMonitor):
                risk_budget_monitor.record_risk_usage(trade_value, symbol, strategy_id)
            
            # Record in risk budget rule
            risk_budget_rule = self.rules.get('risk_budget')
            if isinstance(risk_budget_rule, RiskBudgetRule):
                risk_budget_rule.record_risk_usage(trade_value, symbol, strategy_id)
            
        except Exception as e:
            self.logger.error(f"Error recording trade risk: {e}")
    
    def _check_emergency_stop(self, violations: List[RiskViolation], portfolio: Portfolio) -> None:
        """Check if emergency stop should be activated."""
        if not self.config.enable_emergency_stop:
            return
        
        # Check for critical violations
        critical_violations = [v for v in violations if v.severity == RiskLevel.CRITICAL]
        
        # Check emergency drawdown threshold
        drawdown_monitor = self.monitors.get('drawdown')
        emergency_drawdown = False
        
        if isinstance(drawdown_monitor, DrawdownMonitor):
            current_drawdown = abs(drawdown_monitor.current_drawdown)
            if current_drawdown > self.config.emergency_stop_drawdown_pct:
                emergency_drawdown = True
        
        # Activate emergency stop if conditions met
        if len(critical_violations) >= 3 or emergency_drawdown:
            if not self.emergency_stop_active:
                self.emergency_stop_active = True
                self.logger.critical("EMERGENCY STOP ACTIVATED")
                
                # Create emergency stop violation
                violation = RiskViolation(
                    rule_name="Emergency Stop",
                    rule_type="emergency",
                    violation_type="emergency_stop_activated",
                    current_value=len(critical_violations),
                    limit_value=2,
                    severity=RiskLevel.CRITICAL,
                    timestamp=datetime.now(),
                    details={
                        'critical_violations': len(critical_violations),
                        'emergency_drawdown': emergency_drawdown,
                        'portfolio_value': portfolio.total_value
                    }
                )
                
                self._trigger_alert(violation)
    
    def deactivate_emergency_stop(self) -> bool:
        """
        Deactivate emergency stop.
        
        Returns:
            True if deactivated successfully
        """
        if self.emergency_stop_active:
            self.emergency_stop_active = False
            self.logger.warning("Emergency stop deactivated")
            return True
        return False
    
    def add_alert_callback(self, callback: Callable[[RiskViolation], None]) -> None:
        """Add alert callback function."""
        self.alert_callbacks.append(callback)
    
    def _trigger_alert(self, violation: RiskViolation) -> None:
        """Trigger alert callbacks."""
        for callback in self.alert_callbacks:
            try:
                callback(violation)
            except Exception as e:
                self.logger.error(f"Error in alert callback: {e}")
    
    def _handle_monitor_alert(self, violation: RiskViolation) -> None:
        """Handle alerts from monitors."""
        self.violations_history.append(violation)
        self._trigger_alert(violation)
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk summary."""
        try:
            # Recent violations
            recent_violations = [
                v for v in self.violations_history
                if (datetime.now() - v.timestamp).total_seconds() < 3600  # Last hour
            ]
            
            # Monitor statuses
            monitor_statuses = {}
            for name, monitor in self.monitors.items():
                if monitor.enabled:
                    monitor_statuses[name] = monitor.get_status()
            
            # Rule statuses
            rule_statuses = {}
            for name, rule in self.rules.items():
                rule_statuses[name] = {
                    'enabled': rule.is_enabled(),
                    'type': rule.rule_type.value
                }
            
            return {
                'timestamp': datetime.now().isoformat(),
                'emergency_stop_active': self.emergency_stop_active,
                'last_risk_check': self.last_risk_check.isoformat() if self.last_risk_check else None,
                'recent_violations': len(recent_violations),
                'total_violations': len(self.violations_history),
                'rules': rule_statuses,
                'monitors': monitor_statuses,
                'config': {
                    'max_position_size_pct': self.config.max_position_size_pct,
                    'max_drawdown_pct': self.config.max_drawdown_pct,
                    'daily_risk_budget': self.config.daily_risk_budget,
                    'max_leverage': self.config.max_leverage
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error generating risk summary: {e}")
            return {'error': str(e)}
    
    def get_recent_violations(self, hours: int = 24) -> List[RiskViolation]:
        """Get recent risk violations."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [v for v in self.violations_history if v.timestamp > cutoff_time]
    
    def clear_old_violations(self, days: int = 7) -> None:
        """Clear old risk violations."""
        cutoff_time = datetime.now() - timedelta(days=days)
        old_count = len(self.violations_history)
        self.violations_history = [v for v in self.violations_history if v.timestamp > cutoff_time]
        new_count = len(self.violations_history)
        
        if old_count > new_count:
            self.logger.info(f"Cleared {old_count - new_count} old risk violations")
    
    def enable_rule(self, rule_name: str) -> bool:
        """Enable a risk rule."""
        if rule_name in self.rules:
            self.rules[rule_name].enable()
            self.logger.info(f"Enabled risk rule: {rule_name}")
            return True
        return False
    
    def disable_rule(self, rule_name: str) -> bool:
        """Disable a risk rule."""
        if rule_name in self.rules:
            self.rules[rule_name].disable()
            self.logger.info(f"Disabled risk rule: {rule_name}")
            return True
        return False
    
    def enable_monitor(self, monitor_name: str) -> bool:
        """Enable a risk monitor."""
        if monitor_name in self.monitors:
            self.monitors[monitor_name].enable()
            self.logger.info(f"Enabled risk monitor: {monitor_name}")
            return True
        return False
    
    def disable_monitor(self, monitor_name: str) -> bool:
        """Disable a risk monitor."""
        if monitor_name in self.monitors:
            self.monitors[monitor_name].disable()
            self.logger.info(f"Disabled risk monitor: {monitor_name}")
            return True
        return False
    
    def update_config(self, new_config: RiskConfig) -> bool:
        """Update risk configuration."""
        try:
            if new_config.validate():
                self.config = new_config
                self.logger.info("Risk configuration updated")
                return True
            else:
                self.logger.error("Invalid risk configuration")
                return False
        except Exception as e:
            self.logger.error(f"Error updating risk configuration: {e}")
            return False