"""
Indicator Calculation Engine

This module provides a high-performance calculation engine for technical indicators
with vectorized processing, parameter validation, caching, and optimization features.
"""

import pandas as pd
import numpy as np
import hashlib
import pickle
import time
from typing import Dict, Any, Optional, Callable, Union, List, Tuple
from dataclasses import dataclass, field
from functools import wraps
import logging

from . import moving_averages, momentum, volatility, volume


@dataclass
class IndicatorConfig:
    """Configuration for an indicator"""
    name: str
    function: Callable
    default_params: Dict[str, Any] = field(default_factory=dict)
    required_columns: List[str] = field(default_factory=list)
    param_validators: Dict[str, Callable] = field(default_factory=dict)
    description: str = ""
    category: str = "general"


@dataclass
class CalculationResult:
    """Result of an indicator calculation"""
    indicator_name: str
    data: Union[pd.Series, pd.DataFrame, Dict[str, pd.Series]]
    params: Dict[str, Any]
    calculation_time: float
    cache_hit: bool = False
    data_hash: str = ""


class IndicatorCache:
    """Simple in-memory cache for indicator results"""
    
    def __init__(self, max_size: int = 1000):
        self.cache: Dict[str, CalculationResult] = {}
        self.access_times: Dict[str, float] = {}
        self.max_size = max_size
    
    def _generate_key(self, data_hash: str, indicator_name: str, params: Dict[str, Any]) -> str:
        """Generate cache key from data hash, indicator name, and parameters"""
        param_str = str(sorted(params.items()))
        key_str = f"{data_hash}_{indicator_name}_{param_str}"
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, data_hash: str, indicator_name: str, params: Dict[str, Any]) -> Optional[CalculationResult]:
        """Get cached result if available"""
        key = self._generate_key(data_hash, indicator_name, params)
        if key in self.cache:
            self.access_times[key] = time.time()
            result = self.cache[key]
            result.cache_hit = True
            return result
        return None
    
    def put(self, result: CalculationResult) -> None:
        """Store result in cache"""
        key = self._generate_key(result.data_hash, result.indicator_name, result.params)
        
        # Remove oldest entries if cache is full
        if len(self.cache) >= self.max_size:
            oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            del self.cache[oldest_key]
            del self.access_times[oldest_key]
        
        self.cache[key] = result
        self.access_times[key] = time.time()
    
    def clear(self) -> None:
        """Clear all cached results"""
        self.cache.clear()
        self.access_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'hit_rate': sum(1 for r in self.cache.values() if r.cache_hit) / max(len(self.cache), 1)
        }


class IndicatorEngine:
    """High-performance indicator calculation engine"""
    
    def __init__(self, enable_cache: bool = True, cache_size: int = 1000):
        self.indicators: Dict[str, IndicatorConfig] = {}
        self.cache = IndicatorCache(cache_size) if enable_cache else None
        self.logger = logging.getLogger(__name__)
        
        # Register built-in indicators
        self._register_builtin_indicators()
    
    def _register_builtin_indicators(self) -> None:
        """Register all built-in indicators"""
        
        # Moving Averages
        self.register_indicator(IndicatorConfig(
            name="sma",
            function=moving_averages.sma,
            default_params={"period": 20},
            required_columns=["close"],
            param_validators={"period": lambda x: isinstance(x, int) and x > 0},
            description="Simple Moving Average",
            category="moving_averages"
        ))
        
        self.register_indicator(IndicatorConfig(
            name="ema",
            function=moving_averages.ema,
            default_params={"period": 20},
            required_columns=["close"],
            param_validators={"period": lambda x: isinstance(x, int) and x > 0},
            description="Exponential Moving Average",
            category="moving_averages"
        ))
        
        self.register_indicator(IndicatorConfig(
            name="wma",
            function=moving_averages.wma,
            default_params={"period": 20},
            required_columns=["close"],
            param_validators={"period": lambda x: isinstance(x, int) and x > 0},
            description="Weighted Moving Average",
            category="moving_averages"
        ))
        
        # Momentum Indicators
        self.register_indicator(IndicatorConfig(
            name="rsi",
            function=momentum.rsi,
            default_params={"period": 14},
            required_columns=["close"],
            param_validators={"period": lambda x: isinstance(x, int) and x > 0},
            description="Relative Strength Index",
            category="momentum"
        ))
        
        self.register_indicator(IndicatorConfig(
            name="macd",
            function=momentum.macd,
            default_params={"fast_period": 12, "slow_period": 26, "signal_period": 9},
            required_columns=["close"],
            param_validators={
                "fast_period": lambda x: isinstance(x, int) and x > 0,
                "slow_period": lambda x: isinstance(x, int) and x > 0,
                "signal_period": lambda x: isinstance(x, int) and x > 0
            },
            description="Moving Average Convergence Divergence",
            category="momentum"
        ))
        
        self.register_indicator(IndicatorConfig(
            name="kdj",
            function=momentum.kdj,
            default_params={"k_period": 9, "d_period": 3, "j_period": 3},
            required_columns=["high", "low", "close"],
            param_validators={
                "k_period": lambda x: isinstance(x, int) and x > 0,
                "d_period": lambda x: isinstance(x, int) and x > 0,
                "j_period": lambda x: isinstance(x, int) and x > 0
            },
            description="KDJ Stochastic Oscillator",
            category="momentum"
        ))
        
        # Volatility Indicators
        self.register_indicator(IndicatorConfig(
            name="bollinger_bands",
            function=volatility.bollinger_bands,
            default_params={"period": 20, "std_dev": 2.0},
            required_columns=["close"],
            param_validators={
                "period": lambda x: isinstance(x, int) and x > 1,
                "std_dev": lambda x: isinstance(x, (int, float)) and x > 0
            },
            description="Bollinger Bands",
            category="volatility"
        ))
        
        self.register_indicator(IndicatorConfig(
            name="atr",
            function=volatility.atr,
            default_params={"period": 14},
            required_columns=["high", "low", "close"],
            param_validators={"period": lambda x: isinstance(x, int) and x > 0},
            description="Average True Range",
            category="volatility"
        ))
        
        # Volume Indicators
        self.register_indicator(IndicatorConfig(
            name="obv",
            function=volume.obv,
            default_params={},
            required_columns=["close", "volume"],
            param_validators={},
            description="On-Balance Volume",
            category="volume"
        ))
        
        self.register_indicator(IndicatorConfig(
            name="vwap",
            function=volume.vwap,
            default_params={"period": None},
            required_columns=["high", "low", "close", "volume"],
            param_validators={"period": lambda x: x is None or (isinstance(x, int) and x > 0)},
            description="Volume Weighted Average Price",
            category="volume"
        ))
    
    def register_indicator(self, config: IndicatorConfig) -> None:
        """Register a new indicator"""
        self.indicators[config.name] = config
        self.logger.info(f"Registered indicator: {config.name}")
    
    def get_available_indicators(self) -> Dict[str, str]:
        """Get dictionary of available indicators with descriptions"""
        return {name: config.description or f"{name} indicator" 
                for name, config in self.indicators.items()}
    
    def get_indicator_names(self) -> List[str]:
        """Get list of available indicator names"""
        return list(self.indicators.keys())
    
    def get_indicator_info(self, name: str) -> Optional[IndicatorConfig]:
        """Get information about a specific indicator"""
        return self.indicators.get(name)
    
    def get_indicators_by_category(self, category: str) -> List[str]:
        """Get indicators by category"""
        return [name for name, config in self.indicators.items() if config.category == category]
    
    def _validate_data(self, data: Union[pd.Series, pd.DataFrame], required_columns: List[str]) -> None:
        """Validate input data"""
        if isinstance(data, pd.Series):
            if required_columns and required_columns != ["close"]:
                raise ValueError(f"Series data provided but indicator requires columns: {required_columns}")
        elif isinstance(data, pd.DataFrame):
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
        else:
            raise ValueError("Data must be pandas Series or DataFrame")
        
        if len(data) == 0:
            raise ValueError("Data cannot be empty")
    
    def _validate_params(self, params: Dict[str, Any], validators: Dict[str, Callable]) -> None:
        """Validate indicator parameters"""
        for param_name, validator in validators.items():
            if param_name in params:
                if not validator(params[param_name]):
                    raise ValueError(f"Invalid value for parameter '{param_name}': {params[param_name]}")
    
    def _generate_data_hash(self, data: Union[pd.Series, pd.DataFrame]) -> str:
        """Generate hash of data for caching"""
        if isinstance(data, pd.Series):
            data_bytes = data.values.tobytes() + data.index.values.tobytes()
        else:
            data_bytes = data.values.tobytes() + data.index.values.tobytes()
        return hashlib.md5(data_bytes).hexdigest()
    
    def calculate(self, indicator_name: str, data: Union[pd.Series, pd.DataFrame], 
                 **params) -> CalculationResult:
        """Calculate a single indicator"""
        if indicator_name not in self.indicators:
            raise ValueError(f"Unknown indicator: {indicator_name}")
        
        config = self.indicators[indicator_name]
        
        # Validate data
        self._validate_data(data, config.required_columns)
        
        # Merge default parameters with provided parameters
        final_params = {**config.default_params, **params}
        
        # Validate parameters
        self._validate_params(final_params, config.param_validators)
        
        # Generate data hash for caching
        data_hash = self._generate_data_hash(data)
        
        # Check cache
        if self.cache:
            cached_result = self.cache.get(data_hash, indicator_name, final_params)
            if cached_result:
                return cached_result
        
        # Calculate indicator
        start_time = time.time()
        
        try:
            if isinstance(data, pd.Series):
                result_data = config.function(data, **final_params)
            else:
                result_data = config.function(data, **final_params)
        except Exception as e:
            raise RuntimeError(f"Error calculating {indicator_name}: {str(e)}")
        
        calculation_time = time.time() - start_time
        
        # Create result
        result = CalculationResult(
            indicator_name=indicator_name,
            data=result_data,
            params=final_params,
            calculation_time=calculation_time,
            data_hash=data_hash
        )
        
        # Cache result
        if self.cache:
            self.cache.put(result)
        
        return result
    
    def calculate_multiple(self, indicators: List[Tuple[str, Dict[str, Any]]], 
                          data: Union[pd.Series, pd.DataFrame]) -> Dict[str, CalculationResult]:
        """Calculate multiple indicators on the same data"""
        results = {}
        
        for indicator_name, params in indicators:
            try:
                result = self.calculate(indicator_name, data, **params)
                results[indicator_name] = result
            except Exception as e:
                self.logger.error(f"Failed to calculate {indicator_name}: {str(e)}")
                continue
        
        return results
    
    def batch_calculate(self, indicator_name: str, datasets: List[Union[pd.Series, pd.DataFrame]], 
                       **params) -> List[CalculationResult]:
        """Calculate the same indicator on multiple datasets"""
        results = []
        
        for i, data in enumerate(datasets):
            try:
                result = self.calculate(indicator_name, data, **params)
                results.append(result)
            except Exception as e:
                self.logger.error(f"Failed to calculate {indicator_name} for dataset {i}: {str(e)}")
                continue
        
        return results
    
    def optimize_parameters(self, indicator_name: str, data: Union[pd.Series, pd.DataFrame],
                           param_ranges: Dict[str, List], optimization_metric: Callable) -> Dict[str, Any]:
        """
        Optimize indicator parameters using grid search
        
        Args:
            indicator_name: Name of the indicator
            data: Input data
            param_ranges: Dictionary of parameter names to lists of values to try
            optimization_metric: Function that takes indicator result and returns a score
            
        Returns:
            Dictionary with best parameters and score
        """
        if indicator_name not in self.indicators:
            raise ValueError(f"Unknown indicator: {indicator_name}")
        
        best_score = float('-inf')
        best_params = {}
        
        # Generate all parameter combinations
        import itertools
        param_names = list(param_ranges.keys())
        param_values = list(param_ranges.values())
        
        for param_combination in itertools.product(*param_values):
            params = dict(zip(param_names, param_combination))
            
            try:
                result = self.calculate(indicator_name, data, **params)
                score = optimization_metric(result.data)
                
                if score > best_score:
                    best_score = score
                    best_params = params
                    
            except Exception as e:
                self.logger.warning(f"Failed to calculate with params {params}: {str(e)}")
                continue
        
        return {
            'best_params': best_params,
            'best_score': best_score
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        stats = {
            'registered_indicators': len(self.indicators),
            'cache_enabled': self.cache is not None
        }
        
        if self.cache:
            stats.update(self.cache.get_stats())
        
        return stats
    
    def clear_cache(self) -> None:
        """Clear the indicator cache"""
        if self.cache:
            self.cache.clear()


# Global engine instance
_engine = None


def get_engine() -> IndicatorEngine:
    """Get the global indicator engine instance"""
    global _engine
    if _engine is None:
        _engine = IndicatorEngine()
    return _engine


def calculate_indicator(indicator_name: str, data: Union[pd.Series, pd.DataFrame], 
                       **params) -> CalculationResult:
    """Convenience function to calculate an indicator using the global engine"""
    return get_engine().calculate(indicator_name, data, **params)


def performance_timer(func):
    """Decorator to time function execution"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        if hasattr(result, 'calculation_time'):
            result.calculation_time = end_time - start_time
        
        return result
    return wrapper


class VectorizedCalculator:
    """Utility class for vectorized calculations"""
    
    @staticmethod
    def rolling_apply_parallel(data: pd.Series, window: int, func: Callable, 
                              n_jobs: int = -1) -> pd.Series:
        """Apply function to rolling windows in parallel"""
        try:
            from joblib import Parallel, delayed
            
            def apply_window(i):
                if i < window - 1:
                    return np.nan
                window_data = data.iloc[i-window+1:i+1]
                return func(window_data)
            
            results = Parallel(n_jobs=n_jobs)(
                delayed(apply_window)(i) for i in range(len(data))
            )
            
            return pd.Series(results, index=data.index)
            
        except ImportError:
            # Fallback to regular rolling apply if joblib not available
            return data.rolling(window=window).apply(func, raw=False)
    
    @staticmethod
    def vectorized_conditions(data: pd.DataFrame, conditions: List[Tuple[str, str, float]]) -> pd.Series:
        """Apply multiple conditions vectorized"""
        result = pd.Series(True, index=data.index)
        
        for column, operator, value in conditions:
            if column not in data.columns:
                continue
                
            if operator == '>':
                result &= data[column] > value
            elif operator == '<':
                result &= data[column] < value
            elif operator == '>=':
                result &= data[column] >= value
            elif operator == '<=':
                result &= data[column] <= value
            elif operator == '==':
                result &= data[column] == value
            elif operator == '!=':
                result &= data[column] != value
        
        return result