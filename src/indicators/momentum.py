"""
Momentum Indicators

This module implements momentum-based technical indicators for
identifying trend strength and potential reversal points.
"""

import pandas as pd
import numpy as np
from typing import Union, Optional, Tuple, Dict
from .moving_averages import ema


def rsi(data: Union[pd.Series, pd.DataFrame], period: int = 14, 
        column: Optional[str] = None) -> pd.Series:
    """
    Relative Strength Index (RSI)
    
    Args:
        data: Price data (Series or DataFrame)
        period: Number of periods for RSI calculation (default: 14)
        column: Column name if data is DataFrame (default: 'close')
        
    Returns:
        pd.Series: RSI values (0-100)
        
    Raises:
        ValueError: If period is less than 1
    """
    if period < 1:
        raise ValueError("Period must be at least 1")
    
    if isinstance(data, pd.DataFrame):
        if column is None:
            column = 'close'
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in DataFrame")
        series = data[column]
    else:
        series = data
    
    if len(series) < period + 1:
        raise ValueError(f"Data length ({len(series)}) is insufficient for RSI calculation")
    
    # Calculate price changes
    delta = series.diff()
    
    # Separate gains and losses
    gains = delta.where(delta > 0, 0)
    losses = -delta.where(delta < 0, 0)
    
    # Calculate average gains and losses using <PERSON>'s smoothing
    avg_gains = gains.ewm(alpha=1/period, adjust=False).mean()
    avg_losses = losses.ewm(alpha=1/period, adjust=False).mean()
    
    # Calculate RS and RSI
    rs = avg_gains / avg_losses
    rsi_values = 100 - (100 / (1 + rs))
    
    return rsi_values


def macd(data: Union[pd.Series, pd.DataFrame], fast_period: int = 12, slow_period: int = 26,
         signal_period: int = 9, column: Optional[str] = None) -> Dict[str, pd.Series]:
    """
    Moving Average Convergence Divergence (MACD)
    
    Args:
        data: Price data (Series or DataFrame)
        fast_period: Fast EMA period (default: 12)
        slow_period: Slow EMA period (default: 26)
        signal_period: Signal line EMA period (default: 9)
        column: Column name if data is DataFrame (default: 'close')
        
    Returns:
        Dict containing:
            - 'macd': MACD line
            - 'signal': Signal line
            - 'histogram': MACD histogram
            
    Raises:
        ValueError: If fast_period >= slow_period
    """
    if fast_period >= slow_period:
        raise ValueError("Fast period must be less than slow period")
    
    if isinstance(data, pd.DataFrame):
        if column is None:
            column = 'close'
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in DataFrame")
        series = data[column]
    else:
        series = data
    
    # Calculate EMAs
    ema_fast = ema(series, fast_period)
    ema_slow = ema(series, slow_period)
    
    # Calculate MACD line
    macd_line = ema_fast - ema_slow
    
    # Calculate signal line
    signal_line = ema(macd_line, signal_period)
    
    # Calculate histogram
    histogram = macd_line - signal_line
    
    return {
        'macd': macd_line,
        'signal': signal_line,
        'histogram': histogram
    }


def kdj(data: pd.DataFrame, k_period: int = 9, d_period: int = 3, j_period: int = 3) -> Dict[str, pd.Series]:
    """
    KDJ Stochastic Oscillator
    
    Args:
        data: OHLC DataFrame with 'high', 'low', 'close' columns
        k_period: Period for %K calculation (default: 9)
        d_period: Period for %D smoothing (default: 3)
        j_period: Period for %J calculation (default: 3)
        
    Returns:
        Dict containing:
            - 'k': %K values
            - 'd': %D values
            - 'j': %J values
            
    Raises:
        ValueError: If required columns are missing
    """
    required_columns = ['high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Calculate lowest low and highest high over k_period
    lowest_low = data['low'].rolling(window=k_period, min_periods=k_period).min()
    highest_high = data['high'].rolling(window=k_period, min_periods=k_period).max()
    
    # Calculate raw %K (RSV - Raw Stochastic Value)
    rsv = 100 * (data['close'] - lowest_low) / (highest_high - lowest_low)
    
    # Calculate %K using exponential smoothing
    k_values = rsv.ewm(alpha=1/d_period, adjust=False).mean()
    
    # Calculate %D using exponential smoothing of %K
    d_values = k_values.ewm(alpha=1/d_period, adjust=False).mean()
    
    # Calculate %J
    j_values = 3 * k_values - 2 * d_values
    
    return {
        'k': k_values,
        'd': d_values,
        'j': j_values
    }


def stochastic(data: pd.DataFrame, k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
    """
    Stochastic Oscillator
    
    Args:
        data: OHLC DataFrame with 'high', 'low', 'close' columns
        k_period: Period for %K calculation (default: 14)
        d_period: Period for %D smoothing (default: 3)
        
    Returns:
        Dict containing:
            - 'k': %K values
            - 'd': %D values
    """
    required_columns = ['high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Calculate lowest low and highest high over k_period
    lowest_low = data['low'].rolling(window=k_period, min_periods=k_period).min()
    highest_high = data['high'].rolling(window=k_period, min_periods=k_period).max()
    
    # Calculate %K
    k_values = 100 * (data['close'] - lowest_low) / (highest_high - lowest_low)
    
    # Calculate %D (SMA of %K)
    d_values = k_values.rolling(window=d_period, min_periods=d_period).mean()
    
    return {
        'k': k_values,
        'd': d_values
    }


def williams_r(data: pd.DataFrame, period: int = 14) -> pd.Series:
    """
    Williams %R
    
    Args:
        data: OHLC DataFrame with 'high', 'low', 'close' columns
        period: Lookback period (default: 14)
        
    Returns:
        pd.Series: Williams %R values (-100 to 0)
    """
    required_columns = ['high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Calculate highest high and lowest low over period
    highest_high = data['high'].rolling(window=period, min_periods=period).max()
    lowest_low = data['low'].rolling(window=period, min_periods=period).min()
    
    # Calculate Williams %R
    williams_r_values = -100 * (highest_high - data['close']) / (highest_high - lowest_low)
    
    return williams_r_values


def roc(data: Union[pd.Series, pd.DataFrame], period: int = 12, 
        column: Optional[str] = None) -> pd.Series:
    """
    Rate of Change (ROC)
    
    Args:
        data: Price data (Series or DataFrame)
        period: Number of periods to look back (default: 12)
        column: Column name if data is DataFrame (default: 'close')
        
    Returns:
        pd.Series: ROC values as percentages
    """
    if isinstance(data, pd.DataFrame):
        if column is None:
            column = 'close'
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in DataFrame")
        series = data[column]
    else:
        series = data
    
    # Calculate ROC
    roc_values = ((series - series.shift(period)) / series.shift(period)) * 100
    
    return roc_values