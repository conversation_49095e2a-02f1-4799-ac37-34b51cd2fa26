"""
Volume Indicators

This module implements volume-based technical indicators for
analyzing trading volume patterns and price-volume relationships.
"""

import pandas as pd
import numpy as np
from typing import Union, Optional, Dict
from .moving_averages import sma


def obv(data: pd.DataFrame) -> pd.Series:
    """
    On-Balance Volume (OBV)
    
    Args:
        data: DataFrame with 'close' and 'volume' columns
        
    Returns:
        pd.Series: OBV values
        
    Raises:
        ValueError: If required columns are missing
    """
    required_columns = ['close', 'volume']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Calculate price changes
    price_change = data['close'].diff()
    
    # Determine volume direction
    volume_direction = np.where(price_change > 0, data['volume'],
                               np.where(price_change < 0, -data['volume'], 0))
    
    # Calculate cumulative OBV
    obv_values = pd.Series(volume_direction, index=data.index).cumsum()
    
    return obv_values


def vwap(data: pd.DataFrame, period: Optional[int] = None) -> pd.Series:
    """
    Volume Weighted Average Price (VWAP)
    
    Args:
        data: DataFrame with 'high', 'low', 'close', 'volume' columns
        period: Period for rolling VWAP (None for cumulative VWAP)
        
    Returns:
        pd.Series: VWAP values
        
    Raises:
        ValueError: If required columns are missing
    """
    required_columns = ['high', 'low', 'close', 'volume']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Calculate typical price
    typical_price = (data['high'] + data['low'] + data['close']) / 3
    
    # Calculate price * volume
    pv = typical_price * data['volume']
    
    if period is None:
        # Cumulative VWAP
        vwap_values = pv.cumsum() / data['volume'].cumsum()
    else:
        # Rolling VWAP
        pv_sum = pv.rolling(window=period, min_periods=period).sum()
        volume_sum = data['volume'].rolling(window=period, min_periods=period).sum()
        vwap_values = pv_sum / volume_sum
    
    return vwap_values


def volume_sma(data: pd.DataFrame, period: int = 20) -> pd.Series:
    """
    Volume Simple Moving Average
    
    Args:
        data: DataFrame with 'volume' column
        period: Period for moving average (default: 20)
        
    Returns:
        pd.Series: Volume SMA values
    """
    if 'volume' not in data.columns:
        raise ValueError("Missing required column: volume")
    
    return sma(data['volume'], period)


def volume_rate(data: pd.DataFrame, period: int = 20) -> pd.Series:
    """
    Volume Rate (Current Volume / Average Volume)
    
    Args:
        data: DataFrame with 'volume' column
        period: Period for average volume calculation (default: 20)
        
    Returns:
        pd.Series: Volume rate values
    """
    if 'volume' not in data.columns:
        raise ValueError("Missing required column: volume")
    
    avg_volume = volume_sma(data, period)
    volume_rate_values = data['volume'] / avg_volume
    
    return volume_rate_values


def accumulation_distribution(data: pd.DataFrame) -> pd.Series:
    """
    Accumulation/Distribution Line (A/D Line)
    
    Args:
        data: DataFrame with 'high', 'low', 'close', 'volume' columns
        
    Returns:
        pd.Series: A/D Line values
        
    Raises:
        ValueError: If required columns are missing
    """
    required_columns = ['high', 'low', 'close', 'volume']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Calculate Money Flow Multiplier
    clv = ((data['close'] - data['low']) - (data['high'] - data['close'])) / (data['high'] - data['low'])
    
    # Handle division by zero (when high == low)
    clv = clv.fillna(0)
    
    # Calculate Money Flow Volume
    mfv = clv * data['volume']
    
    # Calculate cumulative A/D Line
    ad_line = mfv.cumsum()
    
    return ad_line


def chaikin_money_flow(data: pd.DataFrame, period: int = 20) -> pd.Series:
    """
    Chaikin Money Flow (CMF)
    
    Args:
        data: DataFrame with 'high', 'low', 'close', 'volume' columns
        period: Period for CMF calculation (default: 20)
        
    Returns:
        pd.Series: CMF values
    """
    required_columns = ['high', 'low', 'close', 'volume']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Calculate Money Flow Multiplier
    clv = ((data['close'] - data['low']) - (data['high'] - data['close'])) / (data['high'] - data['low'])
    clv = clv.fillna(0)
    
    # Calculate Money Flow Volume
    mfv = clv * data['volume']
    
    # Calculate CMF
    mfv_sum = mfv.rolling(window=period, min_periods=period).sum()
    volume_sum = data['volume'].rolling(window=period, min_periods=period).sum()
    
    cmf = mfv_sum / volume_sum
    
    return cmf


def force_index(data: pd.DataFrame, period: int = 13) -> pd.Series:
    """
    Force Index
    
    Args:
        data: DataFrame with 'close' and 'volume' columns
        period: Period for EMA smoothing (default: 13)
        
    Returns:
        pd.Series: Force Index values
    """
    required_columns = ['close', 'volume']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Calculate raw Force Index
    price_change = data['close'].diff()
    raw_force_index = price_change * data['volume']
    
    # Apply EMA smoothing
    from .moving_averages import ema
    force_index_values = ema(raw_force_index, period)
    
    return force_index_values


def ease_of_movement(data: pd.DataFrame, period: int = 14) -> pd.Series:
    """
    Ease of Movement (EOM)
    
    Args:
        data: DataFrame with 'high', 'low', 'volume' columns
        period: Period for SMA smoothing (default: 14)
        
    Returns:
        pd.Series: EOM values
    """
    required_columns = ['high', 'low', 'volume']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Calculate distance moved
    high_low_avg = (data['high'] + data['low']) / 2
    distance_moved = high_low_avg.diff()
    
    # Calculate box height
    box_height = data['volume'] / (data['high'] - data['low'])
    
    # Handle division by zero
    box_height = box_height.replace([np.inf, -np.inf], 0).fillna(0)
    
    # Calculate raw EOM
    raw_eom = distance_moved / box_height
    raw_eom = raw_eom.replace([np.inf, -np.inf], 0).fillna(0)
    
    # Apply SMA smoothing
    eom_values = sma(raw_eom, period)
    
    return eom_values


def negative_volume_index(data: pd.DataFrame) -> pd.Series:
    """
    Negative Volume Index (NVI)
    
    Args:
        data: DataFrame with 'close' and 'volume' columns
        
    Returns:
        pd.Series: NVI values
    """
    required_columns = ['close', 'volume']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Initialize NVI with starting value of 1000
    nvi = pd.Series(index=data.index, dtype=float)
    nvi.iloc[0] = 1000
    
    # Calculate price change percentage
    price_change_pct = data['close'].pct_change()
    
    # Calculate volume change
    volume_decreased = data['volume'] < data['volume'].shift(1)
    
    # Calculate NVI
    for i in range(1, len(data)):
        if volume_decreased.iloc[i]:
            nvi.iloc[i] = nvi.iloc[i-1] * (1 + price_change_pct.iloc[i])
        else:
            nvi.iloc[i] = nvi.iloc[i-1]
    
    return nvi


def positive_volume_index(data: pd.DataFrame) -> pd.Series:
    """
    Positive Volume Index (PVI)
    
    Args:
        data: DataFrame with 'close' and 'volume' columns
        
    Returns:
        pd.Series: PVI values
    """
    required_columns = ['close', 'volume']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Initialize PVI with starting value of 1000
    pvi = pd.Series(index=data.index, dtype=float)
    pvi.iloc[0] = 1000
    
    # Calculate price change percentage
    price_change_pct = data['close'].pct_change()
    
    # Calculate volume change
    volume_increased = data['volume'] > data['volume'].shift(1)
    
    # Calculate PVI
    for i in range(1, len(data)):
        if volume_increased.iloc[i]:
            pvi.iloc[i] = pvi.iloc[i-1] * (1 + price_change_pct.iloc[i])
        else:
            pvi.iloc[i] = pvi.iloc[i-1]
    
    return pvi