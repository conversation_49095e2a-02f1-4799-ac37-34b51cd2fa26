"""
Moving Average Indicators

This module implements various types of moving averages commonly used
in technical analysis.
"""

import pandas as pd
import numpy as np
from typing import Union, Optional


def sma(data: Union[pd.Series, pd.DataFrame], period: int, column: Optional[str] = None) -> pd.Series:
    """
    Simple Moving Average (SMA)
    
    Args:
        data: Price data (Series or DataFrame)
        period: Number of periods for the moving average
        column: Column name if data is DataFrame (default: 'close')
        
    Returns:
        pd.Series: Simple moving average values
        
    Raises:
        ValueError: If period is less than 1 or greater than data length
    """
    if period < 1:
        raise ValueError("Period must be at least 1")
    
    if isinstance(data, pd.DataFrame):
        if column is None:
            column = 'close'
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in DataFrame")
        series = data[column]
    else:
        series = data
    
    if len(series) < period:
        raise ValueError(f"Data length ({len(series)}) is less than period ({period})")
    
    return series.rolling(window=period, min_periods=period).mean()


def ema(data: Union[pd.Series, pd.DataFrame], period: int, column: Optional[str] = None, 
        alpha: Optional[float] = None) -> pd.Series:
    """
    Exponential Moving Average (EMA)
    
    Args:
        data: Price data (Series or DataFrame)
        period: Number of periods for the moving average
        column: Column name if data is DataFrame (default: 'close')
        alpha: Smoothing factor (default: 2/(period+1))
        
    Returns:
        pd.Series: Exponential moving average values
        
    Raises:
        ValueError: If period is less than 1 or alpha is not between 0 and 1
    """
    if period < 1:
        raise ValueError("Period must be at least 1")
    
    if alpha is None:
        alpha = 2.0 / (period + 1)
    elif not 0 < alpha <= 1:
        raise ValueError("Alpha must be between 0 and 1")
    
    if isinstance(data, pd.DataFrame):
        if column is None:
            column = 'close'
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in DataFrame")
        series = data[column]
    else:
        series = data
    
    if len(series) < period:
        raise ValueError(f"Data length ({len(series)}) is less than period ({period})")
    
    return series.ewm(alpha=alpha, adjust=False).mean()


def wma(data: Union[pd.Series, pd.DataFrame], period: int, column: Optional[str] = None) -> pd.Series:
    """
    Weighted Moving Average (WMA)
    
    Args:
        data: Price data (Series or DataFrame)
        period: Number of periods for the moving average
        column: Column name if data is DataFrame (default: 'close')
        
    Returns:
        pd.Series: Weighted moving average values
        
    Raises:
        ValueError: If period is less than 1 or greater than data length
    """
    if period < 1:
        raise ValueError("Period must be at least 1")
    
    if isinstance(data, pd.DataFrame):
        if column is None:
            column = 'close'
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in DataFrame")
        series = data[column]
    else:
        series = data
    
    if len(series) < period:
        raise ValueError(f"Data length ({len(series)}) is less than period ({period})")
    
    # Create weights (linear weighting: 1, 2, 3, ..., period)
    weights = np.arange(1, period + 1)
    weights_sum = weights.sum()
    
    def weighted_mean(x):
        return np.dot(x, weights) / weights_sum
    
    return series.rolling(window=period, min_periods=period).apply(weighted_mean, raw=True)


def tema(data: Union[pd.Series, pd.DataFrame], period: int, column: Optional[str] = None) -> pd.Series:
    """
    Triple Exponential Moving Average (TEMA)
    
    Args:
        data: Price data (Series or DataFrame)
        period: Number of periods for the moving average
        column: Column name if data is DataFrame (default: 'close')
        
    Returns:
        pd.Series: Triple exponential moving average values
    """
    if isinstance(data, pd.DataFrame):
        if column is None:
            column = 'close'
        series = data[column]
    else:
        series = data
    
    ema1 = ema(series, period)
    ema2 = ema(ema1, period)
    ema3 = ema(ema2, period)
    
    return 3 * ema1 - 3 * ema2 + ema3


def dema(data: Union[pd.Series, pd.DataFrame], period: int, column: Optional[str] = None) -> pd.Series:
    """
    Double Exponential Moving Average (DEMA)
    
    Args:
        data: Price data (Series or DataFrame)
        period: Number of periods for the moving average
        column: Column name if data is DataFrame (default: 'close')
        
    Returns:
        pd.Series: Double exponential moving average values
    """
    if isinstance(data, pd.DataFrame):
        if column is None:
            column = 'close'
        series = data[column]
    else:
        series = data
    
    ema1 = ema(series, period)
    ema2 = ema(ema1, period)
    
    return 2 * ema1 - ema2