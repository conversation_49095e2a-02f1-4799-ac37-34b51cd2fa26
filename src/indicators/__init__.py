"""
Technical Indicators Module

This module provides a comprehensive collection of technical indicators
for quantitative trading analysis, including built-in indicators,
calculation engine, and custom indicator framework.
"""

from .moving_averages import sma, ema, wma
from .momentum import rsi, macd, kdj
from .volatility import bollinger_bands, atr
from .volume import obv, vwap
from .engine import IndicatorEngine, get_engine, calculate_indicator
from .custom import (
    BaseCustomIndicator, FormulaIndicator, CompositeIndicator,
    IndicatorMetadata, CustomIndicatorManager, IndicatorTestFramework
)

__all__ = [
    # Moving Averages
    'sma', 'ema', 'wma',
    # Momentum Indicators
    'rsi', 'macd', 'kdj',
    # Volatility Indicators
    'bollinger_bands', 'atr',
    # Volume Indicators
    'obv', 'vwap',
    # Engine
    'IndicatorEngine', 'get_engine', 'calculate_indicator',
    # Custom Framework
    'BaseCustomIndicator', 'FormulaIndicator', 'CompositeIndicator',
    'IndicatorMetadata', 'CustomIndicatorManager', 'IndicatorTestFramework'
]