"""
Volatility Indicators

This module implements volatility-based technical indicators for
measuring market volatility and identifying potential breakouts.
"""

import pandas as pd
import numpy as np
from typing import Union, Optional, Dict
from .moving_averages import sma


def bollinger_bands(data: Union[pd.Series, pd.DataFrame], period: int = 20, std_dev: float = 2.0,
                   column: Optional[str] = None) -> Dict[str, pd.Series]:
    """
    Bollinger Bands
    
    Args:
        data: Price data (Series or DataFrame)
        period: Period for moving average and standard deviation (default: 20)
        std_dev: Number of standard deviations (default: 2.0)
        column: Column name if data is DataFrame (default: 'close')
        
    Returns:
        Dict containing:
            - 'upper': Upper Bollinger Band
            - 'middle': Middle line (SMA)
            - 'lower': Lower Bollinger Band
            - 'bandwidth': Band width
            - 'percent_b': %B indicator
            
    Raises:
        ValueError: If period is less than 2 or std_dev is negative
    """
    if period < 2:
        raise ValueError("Period must be at least 2")
    if std_dev < 0:
        raise ValueError("Standard deviation multiplier must be non-negative")
    
    if isinstance(data, pd.DataFrame):
        if column is None:
            column = 'close'
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in DataFrame")
        series = data[column]
    else:
        series = data
    
    # Calculate middle line (SMA)
    middle = sma(series, period)
    
    # Calculate standard deviation
    std = series.rolling(window=period, min_periods=period).std()
    
    # Calculate upper and lower bands
    upper = middle + (std * std_dev)
    lower = middle - (std * std_dev)
    
    # Calculate bandwidth (normalized band width)
    bandwidth = (upper - lower) / middle
    
    # Calculate %B (position within bands)
    percent_b = (series - lower) / (upper - lower)
    
    return {
        'upper': upper,
        'middle': middle,
        'lower': lower,
        'bandwidth': bandwidth,
        'percent_b': percent_b
    }


def atr(data: pd.DataFrame, period: int = 14) -> pd.Series:
    """
    Average True Range (ATR)
    
    Args:
        data: OHLC DataFrame with 'high', 'low', 'close' columns
        period: Period for ATR calculation (default: 14)
        
    Returns:
        pd.Series: ATR values
        
    Raises:
        ValueError: If required columns are missing or period is less than 1
    """
    if period < 1:
        raise ValueError("Period must be at least 1")
    
    required_columns = ['high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Calculate True Range components
    high_low = data['high'] - data['low']
    high_close_prev = abs(data['high'] - data['close'].shift(1))
    low_close_prev = abs(data['low'] - data['close'].shift(1))
    
    # True Range is the maximum of the three components
    true_range = pd.concat([high_low, high_close_prev, low_close_prev], axis=1).max(axis=1)
    
    # Calculate ATR using Wilder's smoothing (similar to EMA with alpha = 1/period)
    atr_values = true_range.ewm(alpha=1/period, adjust=False).mean()
    
    return atr_values


def keltner_channels(data: pd.DataFrame, period: int = 20, multiplier: float = 2.0) -> Dict[str, pd.Series]:
    """
    Keltner Channels
    
    Args:
        data: OHLC DataFrame with 'high', 'low', 'close' columns
        period: Period for EMA and ATR calculation (default: 20)
        multiplier: ATR multiplier (default: 2.0)
        
    Returns:
        Dict containing:
            - 'upper': Upper Keltner Channel
            - 'middle': Middle line (EMA of typical price)
            - 'lower': Lower Keltner Channel
    """
    required_columns = ['high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Calculate typical price
    typical_price = (data['high'] + data['low'] + data['close']) / 3
    
    # Calculate middle line (EMA of typical price)
    from .moving_averages import ema
    middle = ema(typical_price, period)
    
    # Calculate ATR
    atr_values = atr(data, period)
    
    # Calculate upper and lower channels
    upper = middle + (multiplier * atr_values)
    lower = middle - (multiplier * atr_values)
    
    return {
        'upper': upper,
        'middle': middle,
        'lower': lower
    }


def donchian_channels(data: pd.DataFrame, period: int = 20) -> Dict[str, pd.Series]:
    """
    Donchian Channels
    
    Args:
        data: OHLC DataFrame with 'high', 'low' columns
        period: Lookback period (default: 20)
        
    Returns:
        Dict containing:
            - 'upper': Upper Donchian Channel (highest high)
            - 'lower': Lower Donchian Channel (lowest low)
            - 'middle': Middle line (average of upper and lower)
    """
    required_columns = ['high', 'low']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Calculate highest high and lowest low over period
    upper = data['high'].rolling(window=period, min_periods=period).max()
    lower = data['low'].rolling(window=period, min_periods=period).min()
    
    # Calculate middle line
    middle = (upper + lower) / 2
    
    return {
        'upper': upper,
        'middle': middle,
        'lower': lower
    }


def standard_deviation(data: Union[pd.Series, pd.DataFrame], period: int = 20,
                      column: Optional[str] = None) -> pd.Series:
    """
    Rolling Standard Deviation
    
    Args:
        data: Price data (Series or DataFrame)
        period: Period for standard deviation calculation (default: 20)
        column: Column name if data is DataFrame (default: 'close')
        
    Returns:
        pd.Series: Rolling standard deviation values
    """
    if isinstance(data, pd.DataFrame):
        if column is None:
            column = 'close'
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in DataFrame")
        series = data[column]
    else:
        series = data
    
    return series.rolling(window=period, min_periods=period).std()


def historical_volatility(data: Union[pd.Series, pd.DataFrame], period: int = 20,
                         annualize: bool = True, column: Optional[str] = None) -> pd.Series:
    """
    Historical Volatility
    
    Args:
        data: Price data (Series or DataFrame)
        period: Period for volatility calculation (default: 20)
        annualize: Whether to annualize the volatility (default: True)
        column: Column name if data is DataFrame (default: 'close')
        
    Returns:
        pd.Series: Historical volatility values
    """
    if isinstance(data, pd.DataFrame):
        if column is None:
            column = 'close'
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in DataFrame")
        series = data[column]
    else:
        series = data
    
    # Calculate log returns
    log_returns = np.log(series / series.shift(1))
    
    # Calculate rolling standard deviation of log returns
    volatility = log_returns.rolling(window=period, min_periods=period).std()
    
    # Annualize if requested (assuming 252 trading days per year)
    if annualize:
        volatility = volatility * np.sqrt(252)
    
    return volatility


def ulcer_index(data: Union[pd.Series, pd.DataFrame], period: int = 14,
                column: Optional[str] = None) -> pd.Series:
    """
    Ulcer Index - measures downside volatility
    
    Args:
        data: Price data (Series or DataFrame)
        period: Period for calculation (default: 14)
        column: Column name if data is DataFrame (default: 'close')
        
    Returns:
        pd.Series: Ulcer Index values
    """
    if isinstance(data, pd.DataFrame):
        if column is None:
            column = 'close'
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in DataFrame")
        series = data[column]
    else:
        series = data
    
    # Calculate running maximum
    running_max = series.rolling(window=period, min_periods=1).max()
    
    # Calculate percentage drawdown
    drawdown_pct = 100 * (series - running_max) / running_max
    
    # Calculate Ulcer Index (RMS of drawdowns)
    ulcer_index = np.sqrt((drawdown_pct ** 2).rolling(window=period, min_periods=period).mean())
    
    return ulcer_index