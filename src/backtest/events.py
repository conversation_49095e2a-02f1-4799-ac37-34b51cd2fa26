"""
Event system for event-driven backtesting engine.

This module defines the event types used in the backtesting system to
simulate real-time trading conditions.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum

from ..models.market_data import MarketData
from ..models.trading import Signal, Order, OrderFill


class EventType(Enum):
    """Event type enumeration."""
    MARKET = "MARKET"
    SIGNAL = "SIGNAL"
    ORDER = "ORDER"
    FILL = "FILL"


@dataclass
class Event(ABC):
    """Base event class for the event-driven system."""
    
    event_type: EventType
    timestamp: datetime
    
    @abstractmethod
    def __str__(self) -> str:
        pass


@dataclass
class MarketEvent(Event):
    """Market data event containing new market data."""
    
    market_data: MarketData
    
    def __init__(self, market_data: MarketData):
        super().__init__(EventType.MARKET, market_data.timestamp)
        self.market_data = market_data
    
    def __str__(self) -> str:
        return f"MarketEvent({self.market_data.symbol}, {self.timestamp}, {self.market_data.close})"


@dataclass
class SignalEvent(Event):
    """Signal event generated by trading strategies."""
    
    signal: Signal
    strategy_id: str
    
    def __init__(self, signal: Signal, strategy_id: str):
        super().__init__(EventType.SIGNAL, signal.timestamp or datetime.now())
        self.signal = signal
        self.strategy_id = strategy_id
    
    def __str__(self) -> str:
        return f"SignalEvent({self.signal.symbol}, {self.signal.action}, {self.signal.quantity})"


@dataclass
class OrderEvent(Event):
    """Order event for order placement."""
    
    order: Order
    
    def __init__(self, order: Order):
        super().__init__(EventType.ORDER, order.timestamp or datetime.now())
        self.order = order
    
    def __str__(self) -> str:
        return f"OrderEvent({self.order.symbol}, {self.order.side}, {self.order.quantity})"


@dataclass
class FillEvent(Event):
    """Fill event for order execution."""
    
    fill: OrderFill
    
    def __init__(self, fill: OrderFill):
        super().__init__(EventType.FILL, fill.timestamp)
        self.fill = fill
    
    def __str__(self) -> str:
        return f"FillEvent({self.fill.symbol}, {self.fill.side}, {self.fill.quantity}, {self.fill.price})"