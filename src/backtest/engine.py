"""
Event-driven backtesting engine.

This module provides the core backtesting engine that processes historical
data in chronological order and simulates trading strategies.
"""

import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Callable
from queue import Queue
import logging
import pandas as pd
from copy import deepcopy

from .events import Event, EventType, MarketEvent, SignalEvent, OrderEvent, FillEvent
from .executor import OrderExecutor, ExecutionConfig
from .result import BacktestResult

from ..models.market_data import MarketData
from ..models.trading import Order, OrderFill, Trade, OrderSide, OrderType, OrderStatus
from ..strategies.signals import Signal, SignalType
from ..models.portfolio import Portfolio, Position
from ..strategies.base import BaseStrategy, StrategyContext


class BacktestProgress:
    """Progress tracking for backtesting."""
    
    def __init__(self, total_steps: int):
        self.total_steps = total_steps
        self.current_step = 0
        self.start_time = datetime.now()
        self.last_update = self.start_time
        
    def update(self, step: int) -> None:
        """Update progress."""
        self.current_step = step
        self.last_update = datetime.now()
    
    def get_progress_pct(self) -> float:
        """Get progress percentage."""
        if self.total_steps == 0:
            return 100.0
        return (self.current_step / self.total_steps) * 100.0
    
    def get_elapsed_time(self) -> timedelta:
        """Get elapsed time."""
        return self.last_update - self.start_time
    
    def get_eta(self) -> Optional[timedelta]:
        """Get estimated time to completion."""
        if self.current_step == 0:
            return None
        
        elapsed = self.get_elapsed_time()
        rate = self.current_step / elapsed.total_seconds()
        remaining_steps = self.total_steps - self.current_step
        
        if rate > 0:
            eta_seconds = remaining_steps / rate
            return timedelta(seconds=eta_seconds)
        
        return None


class BacktestEngine:
    """
    Event-driven backtesting engine.
    
    Processes historical market data chronologically and simulates
    strategy execution with realistic order handling.
    """
    
    def __init__(self, initial_capital: float = 100000.0,
                 execution_config: Optional[ExecutionConfig] = None):
        """
        Initialize backtesting engine.
        
        Args:
            initial_capital: Starting capital for backtesting
            execution_config: Order execution configuration
        """
        self.initial_capital = initial_capital
        self.execution_config = execution_config or ExecutionConfig()
        
        # Core components
        self.event_queue = Queue()
        self.executor = OrderExecutor(self.execution_config)
        self.portfolio = Portfolio(initial_capital=initial_capital, cash=initial_capital)
        
        # Import here to avoid circular imports
        from ..models.portfolio_manager import PortfolioManager
        self.portfolio_manager = PortfolioManager(self.portfolio)
        
        # Strategy management
        self.strategies: Dict[str, BaseStrategy] = {}
        self.strategy_contexts: Dict[str, StrategyContext] = {}
        
        # Data and state
        self.market_data_feed: Optional[pd.DataFrame] = None
        self.current_data: Dict[str, MarketData] = {}
        self.current_timestamp: Optional[datetime] = None
        
        # Results tracking
        self.trades: List[Trade] = []
        self.fills: List[OrderFill] = []
        self.portfolio_history: List[Portfolio] = []
        self.pending_orders: Dict[str, Order] = {}
        
        # Progress tracking
        self.progress: Optional[BacktestProgress] = None
        self.progress_callback: Optional[Callable[[BacktestProgress], None]] = None
        
        # Logging
        self.logger = logging.getLogger(f"{__name__}.BacktestEngine")
        
        # State management
        self.is_running = False
        self.is_paused = False
        self.should_stop = False
    
    def add_strategy(self, strategy: BaseStrategy, strategy_id: Optional[str] = None) -> str:
        """
        Add a strategy to the backtesting engine.
        
        Args:
            strategy: Strategy instance
            strategy_id: Optional strategy ID, auto-generated if not provided
            
        Returns:
            Strategy ID
        """
        if strategy_id is None:
            strategy_id = f"{strategy.__class__.__name__}_{uuid.uuid4().hex[:8]}"
        
        # Create strategy context
        context = StrategyContext(
            data_manager=self,  # Engine acts as data manager for backtesting
            portfolio=self.portfolio
        )
        
        # Initialize strategy
        strategy.set_context(context)
        strategy.initialize(context)
        strategy.is_initialized = True
        strategy.is_active = True
        
        # Store strategy and context
        self.strategies[strategy_id] = strategy
        self.strategy_contexts[strategy_id] = context
        
        self.logger.info(f"Added strategy: {strategy_id}")
        return strategy_id
    
    def set_data_feed(self, data: pd.DataFrame) -> None:
        """
        Set market data feed for backtesting.
        
        Args:
            data: DataFrame with OHLCV data, indexed by timestamp
        """
        # Validate data format
        required_columns = ['symbol', 'open', 'high', 'low', 'close', 'volume']
        if not all(col in data.columns for col in required_columns):
            raise ValueError(f"Data must contain columns: {required_columns}")
        
        # Sort by timestamp
        self.market_data_feed = data.sort_index()
        self.logger.info(f"Set data feed with {len(data)} records")
    
    def set_progress_callback(self, callback: Callable[[BacktestProgress], None]) -> None:
        """Set progress callback function."""
        self.progress_callback = callback
    
    def run_backtest(self, start_date: Optional[datetime] = None,
                    end_date: Optional[datetime] = None) -> BacktestResult:
        """
        Run the backtesting simulation.
        
        Args:
            start_date: Start date for backtesting
            end_date: End date for backtesting
            
        Returns:
            BacktestResult with performance metrics and trade history
        """
        if self.market_data_feed is None:
            raise ValueError("Market data feed not set")
        
        if not self.strategies:
            raise ValueError("No strategies added")
        
        # Filter data by date range
        data = self.market_data_feed
        if start_date:
            data = data[data.index >= start_date]
        if end_date:
            data = data[data.index <= end_date]
        
        if data.empty:
            raise ValueError("No data available for specified date range")
        
        # Initialize progress tracking
        self.progress = BacktestProgress(len(data))
        
        # Reset state
        self._reset_state()
        
        # Set running state
        self.is_running = True
        self.should_stop = False
        
        actual_start_date = data.index[0]
        actual_end_date = data.index[-1]
        
        self.logger.info(f"Starting backtest from {actual_start_date} to {actual_end_date}")
        
        try:
            # Process each timestamp
            for i, (timestamp, row) in enumerate(data.iterrows()):
                if self.should_stop:
                    break
                
                # Update progress
                self.progress.update(i + 1)
                if self.progress_callback:
                    self.progress_callback(self.progress)
                
                # Create market data
                market_data = MarketData(
                    symbol=row['symbol'],
                    timestamp=timestamp,
                    open=row['open'],
                    high=row['high'],
                    low=row['low'],
                    close=row['close'],
                    volume=row['volume'],
                    adj_close=row.get('adj_close')
                )
                
                # Process this timestamp
                self._process_timestamp(market_data)
            
            # Finalize backtest
            self._finalize_backtest()
            
        except Exception as e:
            self.logger.error(f"Backtest failed: {e}")
            raise
        finally:
            self.is_running = False
        
        # Create and return results
        result = BacktestResult(
            id=str(uuid.uuid4()),
            strategy_id=list(self.strategies.keys())[0] if self.strategies else 'unknown',
            start_date=actual_start_date,
            end_date=actual_end_date,
            initial_capital=self.initial_capital,
            final_capital=self.portfolio.total_value,
            total_return=(self.portfolio.total_value - self.initial_capital) / self.initial_capital,
            total_trades=len(self.trades),
            status='completed',
            created_at=datetime.now()
        )
        
        # Store additional data as attributes
        result.trades = self.trades.copy()
        result.fills = self.fills.copy()
        result.portfolio_history = self.portfolio_history.copy()
        
        # Calculate basic performance metrics
        if len(self.trades) > 0:
            winning_trades = [t for t in self.trades if hasattr(t, 'pnl') and t.pnl > 0]
            result.winning_trades = len(winning_trades)
            result.losing_trades = len(self.trades) - len(winning_trades)
            result.win_rate = len(winning_trades) / len(self.trades) if len(self.trades) > 0 else 0
        
        self.logger.info(f"Backtest completed. Total return: {result.total_return:.2%}")
        return result
    
    def stop_backtest(self) -> None:
        """Stop the running backtest."""
        self.should_stop = True
        self.logger.info("Backtest stop requested")
    
    def _reset_state(self) -> None:
        """Reset engine state for new backtest."""
        self.portfolio = Portfolio(initial_capital=self.initial_capital, cash=self.initial_capital)
        self.current_data.clear()
        self.current_timestamp = None
        self.trades.clear()
        self.fills.clear()
        self.portfolio_history.clear()
        self.pending_orders.clear()
        
        # Clear event queue
        while not self.event_queue.empty():
            self.event_queue.get()
    
    def _process_timestamp(self, market_data: MarketData) -> None:
        """Process a single timestamp in the backtest."""
        self.current_timestamp = market_data.timestamp
        self.current_data[market_data.symbol] = market_data
        
        # Add market event to queue
        market_event = MarketEvent(market_data)
        self.event_queue.put(market_event)
        
        # Process all events in queue
        while not self.event_queue.empty():
            event = self.event_queue.get()
            self._handle_event(event)
        
        # Update portfolio with current market prices
        market_prices = {symbol: data.close for symbol, data in self.current_data.items()}
        self.portfolio.update_positions_market_value(market_prices)
        
        # Save portfolio snapshot
        self.portfolio_history.append(deepcopy(self.portfolio))
    
    def _handle_event(self, event: Event) -> None:
        """Handle different types of events."""
        if event.event_type == EventType.MARKET:
            self._handle_market_event(event)
        elif event.event_type == EventType.SIGNAL:
            self._handle_signal_event(event)
        elif event.event_type == EventType.ORDER:
            self._handle_order_event(event)
        elif event.event_type == EventType.FILL:
            self._handle_fill_event(event)
    
    def _handle_market_event(self, event: MarketEvent) -> None:
        """Handle market data events."""
        # Send market data to all active strategies
        for strategy_id, strategy in self.strategies.items():
            if not strategy.is_active:
                continue
            
            try:
                # Generate signals from strategy
                signals = strategy.on_data(event.market_data)
                
                # Create signal events
                for signal in signals:
                    if signal.validate():
                        signal_event = SignalEvent(signal, strategy_id)
                        self.event_queue.put(signal_event)
                    else:
                        self.logger.warning(f"Invalid signal from {strategy_id}: {signal}")
                        
            except Exception as e:
                self.logger.error(f"Strategy {strategy_id} error: {e}")
    
    def _handle_signal_event(self, event: SignalEvent) -> None:
        """Handle trading signal events."""
        signal = event.signal
        
        # Skip HOLD signals
        if signal.signal_type == SignalType.HOLD:
            return
        
        # Convert signal to order
        order = self._signal_to_order(signal, event.strategy_id)
        if order:
            order_event = OrderEvent(order)
            self.event_queue.put(order_event)
    
    def _handle_order_event(self, event: OrderEvent) -> None:
        """Handle order placement events."""
        order = event.order
        
        # Get current market data for the symbol
        market_data = self.current_data.get(order.symbol)
        if not market_data:
            self.logger.warning(f"No market data for symbol {order.symbol}")
            return
        
        # Execute order
        fill = self.executor.execute_order(order, market_data, self.portfolio)
        
        if fill:
            # Store pending order
            self.pending_orders[order.id] = order
            
            # Create fill event
            fill_event = FillEvent(fill)
            self.event_queue.put(fill_event)
        else:
            self.logger.info(f"Order rejected: {order}")
    
    def _handle_fill_event(self, event: FillEvent) -> None:
        """Handle order fill events."""
        fill = event.fill
        
        # Create trade record
        trade = Trade(
            id=str(uuid.uuid4()),
            symbol=fill.symbol,
            side=fill.side,
            quantity=fill.quantity,
            price=fill.price,
            timestamp=fill.timestamp,
            commission=fill.commission,
            strategy_id=fill.strategy_id,
            order_id=fill.order_id
        )
        
        # Add trade to portfolio
        self.portfolio.add_trade(trade)
        
        # Store records
        self.trades.append(trade)
        self.fills.append(fill)
        
        # Remove from pending orders
        if fill.order_id in self.pending_orders:
            del self.pending_orders[fill.order_id]
        
        # Notify strategy of fill
        if fill.strategy_id in self.strategies:
            strategy = self.strategies[fill.strategy_id]
            try:
                strategy.on_order_fill({
                    'fill': fill,
                    'trade': trade
                })
            except Exception as e:
                self.logger.error(f"Strategy {fill.strategy_id} fill notification error: {e}")
    
    def _signal_to_order(self, signal: Signal, strategy_id: str) -> Optional[Order]:
        """Convert trading signal to order."""
        try:
            order = Order(
                id=str(uuid.uuid4()),
                symbol=signal.symbol,
                side=OrderSide.BUY if signal.signal_type == SignalType.BUY else OrderSide.SELL,
                order_type=OrderType.LIMIT if signal.price else OrderType.MARKET,
                quantity=signal.quantity,
                price=signal.price,
                timestamp=signal.timestamp or self.current_timestamp,
                strategy_id=strategy_id,
                metadata=signal.metadata
            )
            
            if order.validate():
                return order
            else:
                self.logger.warning(f"Invalid order created from signal: {signal}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error converting signal to order: {e}")
            return None
    
    def _finalize_backtest(self) -> None:
        """Finalize backtest and cleanup."""
        # Cancel any pending orders
        for order in self.pending_orders.values():
            order.status = OrderStatus.CANCELLED
        
        # Cleanup strategies
        for strategy in self.strategies.values():
            try:
                strategy.cleanup()
            except Exception as e:
                self.logger.error(f"Strategy cleanup error: {e}")
        
        self.logger.info("Backtest finalized")
    
    # Data manager interface for strategies
    def get_historical_data(self, symbol: str, lookback: int, 
                           end_date: Optional[datetime] = None) -> pd.DataFrame:
        """Get historical data for strategy context."""
        if self.market_data_feed is None:
            return pd.DataFrame()
        
        # Use current timestamp as end date if not specified
        if end_date is None:
            end_date = self.current_timestamp
        
        if end_date is None:
            return pd.DataFrame()
        
        # Filter data
        data = self.market_data_feed[
            (self.market_data_feed['symbol'] == symbol) &
            (self.market_data_feed.index <= end_date)
        ].tail(lookback)
        
        return data
    
    def get_current_data(self, symbol: str) -> Optional[MarketData]:
        """Get current market data for strategy context."""
        return self.current_data.get(symbol)