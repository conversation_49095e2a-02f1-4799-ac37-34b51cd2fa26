"""
Order execution simulator for backtesting.

This module simulates order execution with realistic slippage, commission,
and market impact modeling. Supports multiple order types including
market orders, limit orders, and stop orders with partial fill simulation.
"""

import uuid
import random
from datetime import datetime
from typing import Dict, Optional, Callable, List, Any
from dataclasses import dataclass
from enum import Enum
import logging

from ..models.trading import Order, OrderFill, OrderSide, OrderType, OrderStatus
from ..models.market_data import MarketData
from ..models.portfolio import Portfolio


class RejectionReason(Enum):
    """Order rejection reasons."""
    INSUFFICIENT_FUNDS = "INSUFFICIENT_FUNDS"
    INSUFFICIENT_SHARES = "INSUFFICIENT_SHARES"
    PRICE_OUT_OF_RANGE = "PRICE_OUT_OF_RANGE"
    MARKET_CLOSED = "MARKET_CLOSED"
    INVALID_ORDER = "INVALID_ORDER"
    RANDOM_REJECTION = "RANDOM_REJECTION"


@dataclass
class ExecutionConfig:
    """Configuration for order execution simulation."""
    
    # Commission settings
    commission_rate: float = 0.001  # 0.1% commission
    min_commission: float = 1.0     # Minimum commission per trade
    
    # Slippage settings
    slippage_rate: float = 0.0005   # 0.05% base slippage
    slippage_volatility: float = 0.0002  # Additional slippage based on volatility
    
    # Market impact settings
    market_impact_rate: float = 0.0001  # 0.01% market impact per 1000 shares
    max_market_impact: float = 0.01     # Maximum market impact (1%)
    
    # Partial fill settings
    partial_fill_probability: float = 0.1  # 10% chance of partial fills
    min_fill_ratio: float = 0.3            # Minimum 30% fill on partial fills
    max_fill_ratio: float = 0.8            # Maximum 80% fill on partial fills
    
    # Order rejection settings
    rejection_probability: float = 0.02    # 2% random rejection rate
    price_tolerance: float = 0.05          # 5% price tolerance for limit orders
    
    # Stop order settings
    stop_slippage_multiplier: float = 2.0  # Stop orders have higher slippage
    
    def validate(self) -> bool:
        """Validate execution configuration."""
        return all([
            self.commission_rate >= 0,
            self.min_commission >= 0,
            self.slippage_rate >= 0,
            self.slippage_volatility >= 0,
            self.market_impact_rate >= 0,
            self.max_market_impact >= 0,
            0 <= self.partial_fill_probability <= 1,
            0 < self.min_fill_ratio <= self.max_fill_ratio <= 1,
            0 <= self.rejection_probability <= 1,
            self.price_tolerance >= 0,
            self.stop_slippage_multiplier >= 1.0
        ])


class OrderExecutor:
    """
    Order execution simulator for backtesting.
    
    Simulates realistic order execution with multiple order types,
    slippage, commission, market impact, partial fills, and rejections.
    """
    
    def __init__(self, config: Optional[ExecutionConfig] = None):
        self.config = config or ExecutionConfig()
        self.logger = logging.getLogger(f"{__name__}.OrderExecutor")
        
        # Validate configuration
        if not self.config.validate():
            raise ValueError("Invalid execution configuration")
        
        # Track order history for market impact calculation
        self.recent_orders: Dict[str, List[Order]] = {}
        
        # Random seed for consistent testing
        self._random_seed = None
    
    def execute_order(self, order: Order, market_data: MarketData, 
                     portfolio: Portfolio) -> Optional[OrderFill]:
        """
        Execute an order against current market data.
        
        Args:
            order: Order to execute
            market_data: Current market data
            portfolio: Current portfolio state
            
        Returns:
            OrderFill if execution successful, None if rejected
        """
        # Check if order can be executed
        rejection_reason = self._check_order_rejection(order, market_data, portfolio)
        if rejection_reason:
            self.logger.warning(f"Order rejected ({rejection_reason.value}): {order}")
            order.status = OrderStatus.REJECTED
            return None
        
        # Check if order should be triggered (for stop orders)
        if not self._should_trigger_order(order, market_data):
            # Order not triggered yet, keep it pending
            return None
        
        # Calculate execution details
        execution_price = self._calculate_execution_price(order, market_data)
        fill_quantity = self._calculate_fill_quantity(order, market_data)
        commission = self._calculate_commission(order, execution_price, fill_quantity)
        
        # Final validation for available funds/shares
        if not self._validate_execution_resources(order, portfolio, execution_price, 
                                                fill_quantity, commission):
            self.logger.warning(f"Insufficient resources for order: {order}")
            order.status = OrderStatus.REJECTED
            return None
        
        # Create order fill
        fill = OrderFill(
            id=str(uuid.uuid4()),
            order_id=order.id,
            symbol=order.symbol,
            side=order.side,
            quantity=fill_quantity,
            price=execution_price,
            timestamp=market_data.timestamp,
            commission=commission,
            strategy_id=order.strategy_id
        )
        
        # Update order status
        if fill_quantity < order.quantity:
            order.status = OrderStatus.PARTIALLY_FILLED
            # Update remaining quantity
            order.quantity -= fill_quantity
        else:
            order.status = OrderStatus.FILLED
        
        # Track order for market impact
        self._track_order(order, market_data)
        
        self.logger.info(f"Order executed: {fill}")
        return fill
    
    def _check_order_rejection(self, order: Order, market_data: MarketData, 
                              portfolio: Portfolio) -> Optional[RejectionReason]:
        """Check if order should be rejected and return reason."""
        # Basic validation
        if not order.validate():
            return RejectionReason.INVALID_ORDER
        
        # Symbol must match
        if order.symbol != market_data.symbol:
            return RejectionReason.INVALID_ORDER
        
        # Random rejection simulation
        if random.random() < self.config.rejection_probability:
            return RejectionReason.RANDOM_REJECTION
        
        # Check position for sell orders
        if order.side == OrderSide.SELL:
            position = portfolio.get_position(order.symbol)
            current_quantity = position.quantity if position else 0
            if current_quantity < order.quantity:
                return RejectionReason.INSUFFICIENT_SHARES
        
        # Check price limits for limit orders
        if order.order_type == OrderType.LIMIT and order.price:
            price_diff = abs(order.price - market_data.close) / market_data.close
            if price_diff > self.config.price_tolerance:
                return RejectionReason.PRICE_OUT_OF_RANGE
        
        return None
    
    def _should_trigger_order(self, order: Order, market_data: MarketData) -> bool:
        """Check if order should be triggered (mainly for stop orders)."""
        if order.order_type in [OrderType.MARKET, OrderType.LIMIT]:
            return True
        
        if order.order_type == OrderType.STOP and order.stop_price:
            if order.side == OrderSide.BUY:
                # Buy stop: trigger when price goes above stop price
                return market_data.high >= order.stop_price
            else:
                # Sell stop: trigger when price goes below stop price
                return market_data.low <= order.stop_price
        
        if order.order_type == OrderType.STOP_LIMIT and order.stop_price:
            # Same trigger logic as stop orders
            if order.side == OrderSide.BUY:
                return market_data.high >= order.stop_price
            else:
                return market_data.low <= order.stop_price
        
        return True
    
    def _calculate_fill_quantity(self, order: Order, market_data: MarketData) -> float:
        """Calculate the quantity to fill (for partial fills)."""
        # Check for partial fill
        if random.random() < self.config.partial_fill_probability:
            fill_ratio = random.uniform(self.config.min_fill_ratio, 
                                      self.config.max_fill_ratio)
            return round(order.quantity * fill_ratio, 8)
        
        return order.quantity
    
    def _validate_execution_resources(self, order: Order, portfolio: Portfolio,
                                    execution_price: float, fill_quantity: float,
                                    commission: float) -> bool:
        """Validate that we have sufficient resources for execution."""
        if order.side == OrderSide.BUY:
            total_cost = fill_quantity * execution_price + commission
            return portfolio.cash >= total_cost
        else:
            position = portfolio.get_position(order.symbol)
            current_quantity = position.quantity if position else 0
            return current_quantity >= fill_quantity
    
    def _track_order(self, order: Order, market_data: MarketData) -> None:
        """Track order for market impact calculation."""
        if order.symbol not in self.recent_orders:
            self.recent_orders[order.symbol] = []
        
        self.recent_orders[order.symbol].append(order)
        
        # Keep only recent orders (last 10)
        if len(self.recent_orders[order.symbol]) > 10:
            self.recent_orders[order.symbol] = self.recent_orders[order.symbol][-10:]
    
    def _calculate_execution_price(self, order: Order, market_data: MarketData) -> float:
        """Calculate the execution price including slippage and market impact."""
        # Determine base price based on order type
        base_price = self._get_base_price(order, market_data)
        
        # Calculate slippage
        slippage = self._calculate_slippage(order, market_data)
        
        # Calculate market impact
        market_impact = self._calculate_market_impact(order, market_data)
        
        # Apply adjustments
        if order.side == OrderSide.BUY:
            execution_price = base_price * (1 + slippage + market_impact)
        else:
            execution_price = base_price * (1 - slippage - market_impact)
        
        # Ensure price is within reasonable bounds
        execution_price = max(execution_price, 0.01)
        
        return round(execution_price, 4)
    
    def _get_base_price(self, order: Order, market_data: MarketData) -> float:
        """Get base price for order execution."""
        if order.order_type == OrderType.MARKET:
            return market_data.close
        elif order.order_type == OrderType.LIMIT and order.price:
            return order.price
        elif order.order_type == OrderType.STOP:
            # Stop orders execute at market price after trigger
            return market_data.close
        elif order.order_type == OrderType.STOP_LIMIT and order.price:
            # Stop-limit orders execute at limit price after trigger
            return order.price
        else:
            return market_data.close
    
    def _calculate_slippage(self, order: Order, market_data: MarketData) -> float:
        """Calculate slippage based on order type and market conditions."""
        # No slippage for limit orders (they execute at specified price)
        if order.order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT]:
            return 0.0
        
        base_slippage = self.config.slippage_rate
        
        # Higher slippage for stop orders
        if order.order_type == OrderType.STOP:
            base_slippage *= self.config.stop_slippage_multiplier
        
        # Add volatility-based slippage
        if hasattr(market_data, 'volatility') and market_data.volatility:
            volatility_slippage = self.config.slippage_volatility * market_data.volatility
        else:
            # Estimate volatility from high-low range
            daily_range = (market_data.high - market_data.low) / market_data.close
            volatility_slippage = self.config.slippage_volatility * daily_range
        
        total_slippage = base_slippage + volatility_slippage
        
        # Add some randomness
        random_factor = random.uniform(0.5, 1.5)
        return total_slippage * random_factor
    
    def _calculate_market_impact(self, order: Order, market_data: MarketData) -> float:
        """Calculate market impact based on order size and recent activity."""
        # No market impact for limit orders (they execute at specified price)
        if order.order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT]:
            return 0.0
        
        # Base impact from order size
        size_impact = self.config.market_impact_rate * (order.quantity / 1000)
        
        # Additional impact from recent orders on same symbol
        recent_volume = sum(o.quantity for o in self.recent_orders.get(order.symbol, []))
        volume_impact = self.config.market_impact_rate * (recent_volume / 5000)
        
        total_impact = min(size_impact + volume_impact, self.config.max_market_impact)
        
        return total_impact
    
    def _calculate_commission(self, order: Order, execution_price: float, 
                             fill_quantity: float) -> float:
        """Calculate commission for the order."""
        trade_value = fill_quantity * execution_price
        commission = trade_value * self.config.commission_rate
        
        # Apply minimum commission
        commission = max(commission, self.config.min_commission)
        
        return round(commission, 2)
    
    def update_config(self, config: ExecutionConfig) -> None:
        """Update execution configuration."""
        if not config.validate():
            raise ValueError("Invalid execution configuration")
        self.config = config
        self.logger.info("Execution configuration updated")
    
    def set_random_seed(self, seed: int) -> None:
        """Set random seed for consistent testing."""
        self._random_seed = seed
        random.seed(seed)
        self.logger.info(f"Random seed set to {seed}")
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics."""
        total_orders = sum(len(orders) for orders in self.recent_orders.values())
        
        return {
            'total_recent_orders': total_orders,
            'symbols_traded': list(self.recent_orders.keys()),
            'config': {
                'commission_rate': self.config.commission_rate,
                'slippage_rate': self.config.slippage_rate,
                'partial_fill_probability': self.config.partial_fill_probability,
                'rejection_probability': self.config.rejection_probability
            }
        }
    
    def reset_order_history(self) -> None:
        """Reset order history (useful for testing)."""
        self.recent_orders.clear()
        self.logger.info("Order history reset")