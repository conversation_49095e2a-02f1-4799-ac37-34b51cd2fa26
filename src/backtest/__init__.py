"""
Backtesting engine module for quantitative trading system.

This module provides event-driven backtesting capabilities with support for
multiple strategies, order simulation, and performance tracking.
"""

from .engine import BacktestEngine
from .events import Event, MarketEvent, SignalEvent, OrderEvent, FillEvent
from .executor import OrderExecutor
from .result import BacktestResult

__all__ = [
    'BacktestEngine',
    'Event',
    'MarketEvent', 
    'SignalEvent',
    'OrderEvent',
    'FillEvent',
    'OrderExecutor',
    'BacktestResult'
]