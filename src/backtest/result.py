"""
回测结果存储和查询模块
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import uuid
import json
import logging
import pandas as pd
from dataclasses import dataclass, field

from ..data.database import DatabaseManager
from ..exceptions import TradingSystemException

logger = logging.getLogger(__name__)


@dataclass
class BacktestResult:
    """回测结果数据类"""
    
    # 基本信息
    id: str
    strategy_id: str
    start_date: datetime
    end_date: datetime
    initial_capital: float
    
    # 性能指标
    final_capital: float = 0.0
    total_return: float = 0.0
    annual_return: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0
    
    # 交易统计
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    
    # 状态信息
    status: str = 'pending'
    created_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    
    # 其他数据
    symbols: List[str] = field(default_factory=list)
    parameters: Dict[str, Any] = field(default_factory=dict)
    benchmark: Optional[str] = None
    user_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'strategy_id': self.strategy_id,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'initial_capital': self.initial_capital,
            'final_capital': self.final_capital,
            'total_return': self.total_return,
            'annual_return': self.annual_return,
            'max_drawdown': self.max_drawdown,
            'sharpe_ratio': self.sharpe_ratio,
            'sortino_ratio': self.sortino_ratio,
            'calmar_ratio': self.calmar_ratio,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': self.win_rate,
            'profit_factor': self.profit_factor,
            'avg_win': self.avg_win,
            'avg_loss': self.avg_loss,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'error_message': self.error_message,
            'symbols': self.symbols,
            'parameters': self.parameters,
            'benchmark': self.benchmark,
            'user_id': self.user_id
        }


class BacktestResultRepository:
    """回测结果数据访问层"""
    
    def __init__(self):
        self.db = DatabaseManager()
    
    def create_backtest_task(
        self,
        strategy_id: str,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        initial_capital: float,
        parameters: Optional[Dict[str, Any]] = None,
        benchmark: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> str:
        """创建回测任务"""
        try:
            backtest_id = str(uuid.uuid4())
            
            query = """
            INSERT INTO backtest_results (
                id, strategy_id, user_id, symbols, start_date, end_date,
                initial_capital, parameters, benchmark, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                backtest_id,
                strategy_id,
                user_id,
                json.dumps(symbols),
                start_date,
                end_date,
                initial_capital,
                json.dumps(parameters) if parameters else None,
                benchmark,
                'running',
                datetime.now()
            )
            
            self.db.execute(query, params)
            logger.info(f"创建回测任务: {backtest_id}")
            
            return backtest_id
        
        except Exception as e:
            logger.error(f"创建回测任务失败: {str(e)}")
            raise TradingSystemException(f"创建回测任务失败: {str(e)}")
    
    def update_backtest_status(
        self,
        backtest_id: str,
        status: str,
        result_data: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """更新回测状态"""
        try:
            if status == 'completed' and result_data:
                query = """
                UPDATE backtest_results SET
                    status = ?, completed_at = ?, final_capital = ?,
                    total_return = ?, annual_return = ?, max_drawdown = ?,
                    sharpe_ratio = ?, sortino_ratio = ?, calmar_ratio = ?,
                    win_rate = ?, profit_factor = ?, total_trades = ?,
                    winning_trades = ?, losing_trades = ?, avg_win = ?, avg_loss = ?
                WHERE id = ?
                """
                
                params = (
                    status,
                    datetime.now(),
                    result_data.get('final_capital'),
                    result_data.get('total_return'),
                    result_data.get('annual_return'),
                    result_data.get('max_drawdown'),
                    result_data.get('sharpe_ratio'),
                    result_data.get('sortino_ratio'),
                    result_data.get('calmar_ratio'),
                    result_data.get('win_rate'),
                    result_data.get('profit_factor'),
                    result_data.get('total_trades'),
                    result_data.get('winning_trades'),
                    result_data.get('losing_trades'),
                    result_data.get('avg_win'),
                    result_data.get('avg_loss'),
                    backtest_id
                )
            
            elif status == 'failed':
                query = """
                UPDATE backtest_results SET
                    status = ?, completed_at = ?, error_message = ?
                WHERE id = ?
                """
                
                params = (status, datetime.now(), error_message, backtest_id)
            
            else:
                query = "UPDATE backtest_results SET status = ? WHERE id = ?"
                params = (status, backtest_id)
            
            self.db.execute(query, params)
            logger.info(f"更新回测状态: {backtest_id} -> {status}")
            
            return True
        
        except Exception as e:
            logger.error(f"更新回测状态失败: {str(e)}")
            return False
    
    def get_backtest_results(
        self,
        user_id: Optional[str] = None,
        strategy_id: Optional[str] = None,
        symbol: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """获取回测结果列表"""
        try:
            query = """
            SELECT br.*, s.name as strategy_name
            FROM backtest_results br
            LEFT JOIN strategies s ON br.strategy_id = s.id
            WHERE 1=1
            """
            params = []
            
            if user_id:
                query += " AND br.user_id = ?"
                params.append(user_id)
            
            if strategy_id:
                query += " AND br.strategy_id = ?"
                params.append(strategy_id)
            
            if symbol:
                query += " AND br.symbols LIKE ?"
                params.append(f'%"{symbol}"%')
            
            if status:
                query += " AND br.status = ?"
                params.append(status)
            
            if start_date:
                query += " AND br.created_at >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND br.created_at <= ?"
                params.append(end_date)
            
            query += " ORDER BY br.created_at DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])
            
            results = self.db.fetch_all(query, params)
            
            # 处理JSON字段
            for result in results:
                if result.get('symbols'):
                    result['symbols'] = json.loads(result['symbols'])
                if result.get('parameters'):
                    result['parameters'] = json.loads(result['parameters'])
            
            return results
        
        except Exception as e:
            logger.error(f"获取回测结果列表失败: {str(e)}")
            raise TradingSystemException(f"获取回测结果列表失败: {str(e)}")
    
    def get_backtest_result(self, backtest_id: str, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """获取单个回测结果"""
        try:
            query = """
            SELECT br.*, s.name as strategy_name
            FROM backtest_results br
            LEFT JOIN strategies s ON br.strategy_id = s.id
            WHERE br.id = ?
            """
            params = [backtest_id]
            
            if user_id:
                query += " AND br.user_id = ?"
                params.append(user_id)
            
            result = self.db.fetch_one(query, params)
            
            if result:
                # 处理JSON字段
                if result.get('symbols'):
                    result['symbols'] = json.loads(result['symbols'])
                if result.get('parameters'):
                    result['parameters'] = json.loads(result['parameters'])
            
            return result
        
        except Exception as e:
            logger.error(f"获取回测结果失败: {str(e)}")
            raise TradingSystemException(f"获取回测结果失败: {str(e)}")
    
    def get_backtest_trades(
        self,
        backtest_id: str,
        symbol: Optional[str] = None,
        side: Optional[str] = None,
        limit: int = 1000,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """获取回测交易记录"""
        try:
            query = """
            SELECT * FROM backtest_trades
            WHERE backtest_id = ?
            """
            params = [backtest_id]
            
            if symbol:
                query += " AND symbol = ?"
                params.append(symbol)
            
            if side:
                query += " AND side = ?"
                params.append(side)
            
            query += " ORDER BY timestamp LIMIT ? OFFSET ?"
            params.extend([limit, offset])
            
            return self.db.fetch_all(query, params)
        
        except Exception as e:
            logger.error(f"获取交易记录失败: {str(e)}")
            raise TradingSystemException(f"获取交易记录失败: {str(e)}")
    
    def get_portfolio_history(
        self,
        backtest_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        frequency: str = "daily"
    ) -> List[Dict[str, Any]]:
        """获取投资组合历史"""
        try:
            query = """
            SELECT * FROM backtest_portfolio_history
            WHERE backtest_id = ?
            """
            params = [backtest_id]
            
            if start_date:
                query += " AND timestamp >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND timestamp <= ?"
                params.append(end_date)
            
            query += " ORDER BY timestamp"
            
            results = self.db.fetch_all(query, params)
            
            # 根据频率过滤数据
            if frequency != "daily" and results:
                df = pd.DataFrame(results)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
                
                if frequency == "weekly":
                    df = df.resample('W').last()
                elif frequency == "monthly":
                    df = df.resample('M').last()
                
                df.reset_index(inplace=True)
                results = df.to_dict('records')
            
            return results
        
        except Exception as e:
            logger.error(f"获取投资组合历史失败: {str(e)}")
            raise TradingSystemException(f"获取投资组合历史失败: {str(e)}")
    
    def delete_backtest_result(self, backtest_id: str) -> bool:
        """删除回测结果"""
        try:
            # 删除相关的交易记录和投资组合历史
            self.db.execute("DELETE FROM backtest_trades WHERE backtest_id = ?", (backtest_id,))
            self.db.execute("DELETE FROM backtest_portfolio_history WHERE backtest_id = ?", (backtest_id,))
            
            # 删除主记录
            self.db.execute("DELETE FROM backtest_results WHERE id = ?", (backtest_id,))
            
            logger.info(f"删除回测结果: {backtest_id}")
            return True
        
        except Exception as e:
            logger.error(f"删除回测结果失败: {str(e)}")
            return False
    
    def compare_backtest_results(
        self,
        backtest_results: List[Dict[str, Any]],
        metrics: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """比较回测结果"""
        try:
            if not metrics:
                metrics = [
                    'total_return', 'annual_return', 'sharpe_ratio', 'max_drawdown',
                    'win_rate', 'profit_factor', 'total_trades'
                ]
            
            comparison = {
                'metrics': metrics,
                'results': [],
                'ranking': {},
                'summary': {}
            }
            
            # 提取各个回测的指标
            for result in backtest_results:
                result_metrics = {}
                for metric in metrics:
                    result_metrics[metric] = result.get(metric, 0.0)
                
                comparison['results'].append({
                    'backtest_id': result['id'],
                    'strategy_name': result.get('strategy_name', ''),
                    'metrics': result_metrics
                })
            
            # 计算排名
            for metric in metrics:
                values = [(r['backtest_id'], r['metrics'][metric]) for r in comparison['results']]
                # 大部分指标越大越好，除了max_drawdown
                reverse = metric != 'max_drawdown'
                sorted_values = sorted(values, key=lambda x: x[1], reverse=reverse)
                
                comparison['ranking'][metric] = [
                    {'backtest_id': bt_id, 'value': value, 'rank': i + 1}
                    for i, (bt_id, value) in enumerate(sorted_values)
                ]
            
            # 计算汇总统计
            for metric in metrics:
                values = [r['metrics'][metric] for r in comparison['results']]
                comparison['summary'][metric] = {
                    'min': min(values),
                    'max': max(values),
                    'mean': sum(values) / len(values),
                    'std': pd.Series(values).std()
                }
            
            return comparison
        
        except Exception as e:
            logger.error(f"比较回测结果失败: {str(e)}")
            raise TradingSystemException(f"比较回测结果失败: {str(e)}")
    
    def get_user_backtest_statistics(
        self,
        user_id: str,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """获取用户回测统计信息"""
        try:
            query = """
            SELECT 
                COUNT(*) as total_backtests,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_backtests,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_backtests,
                COUNT(CASE WHEN status = 'running' THEN 1 END) as running_backtests,
                AVG(CASE WHEN status = 'completed' THEN total_return END) as avg_return,
                AVG(CASE WHEN status = 'completed' THEN sharpe_ratio END) as avg_sharpe,
                AVG(CASE WHEN status = 'completed' THEN max_drawdown END) as avg_drawdown,
                SUM(CASE WHEN status = 'completed' THEN total_trades ELSE 0 END) as total_trades
            FROM backtest_results
            WHERE user_id = ? AND created_at BETWEEN ? AND ?
            """
            
            result = self.db.fetch_one(query, (user_id, start_date, end_date))
            
            # 获取最佳表现的回测
            best_return_query = """
            SELECT id, total_return, strategy_id
            FROM backtest_results
            WHERE user_id = ? AND status = 'completed' AND created_at BETWEEN ? AND ?
            ORDER BY total_return DESC LIMIT 1
            """
            
            best_return = self.db.fetch_one(best_return_query, (user_id, start_date, end_date))
            
            best_sharpe_query = """
            SELECT id, sharpe_ratio, strategy_id
            FROM backtest_results
            WHERE user_id = ? AND status = 'completed' AND created_at BETWEEN ? AND ?
            ORDER BY sharpe_ratio DESC LIMIT 1
            """
            
            best_sharpe = self.db.fetch_one(best_sharpe_query, (user_id, start_date, end_date))
            
            statistics = dict(result) if result else {}
            statistics['best_return_backtest'] = best_return
            statistics['best_sharpe_backtest'] = best_sharpe
            
            return statistics
        
        except Exception as e:
            logger.error(f"获取用户回测统计失败: {str(e)}")
            raise TradingSystemException(f"获取用户回测统计失败: {str(e)}")
    
    def store_trade_record(
        self,
        backtest_id: str,
        symbol: str,
        side: str,
        quantity: float,
        price: float,
        timestamp: datetime,
        commission: float,
        pnl: Optional[float] = None,
        cumulative_pnl: Optional[float] = None
    ) -> str:
        """存储交易记录"""
        try:
            trade_id = str(uuid.uuid4())
            
            query = """
            INSERT INTO backtest_trades (
                id, backtest_id, symbol, side, quantity, price,
                timestamp, commission, pnl, cumulative_pnl
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                trade_id, backtest_id, symbol, side, quantity, price,
                timestamp, commission, pnl, cumulative_pnl
            )
            
            self.db.execute(query, params)
            return trade_id
        
        except Exception as e:
            logger.error(f"存储交易记录失败: {str(e)}")
            raise TradingSystemException(f"存储交易记录失败: {str(e)}")
    
    def store_portfolio_snapshot(
        self,
        backtest_id: str,
        timestamp: datetime,
        total_value: float,
        cash: float,
        positions_value: float,
        unrealized_pnl: float,
        realized_pnl: float,
        drawdown: float
    ) -> None:
        """存储投资组合快照"""
        try:
            query = """
            INSERT INTO backtest_portfolio_history (
                backtest_id, timestamp, total_value, cash, positions_value,
                unrealized_pnl, realized_pnl, drawdown
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                backtest_id, timestamp, total_value, cash, positions_value,
                unrealized_pnl, realized_pnl, drawdown
            )
            
            self.db.execute(query, params)
        
        except Exception as e:
            logger.error(f"存储投资组合快照失败: {str(e)}")
            raise TradingSystemException(f"存储投资组合快照失败: {str(e)}")