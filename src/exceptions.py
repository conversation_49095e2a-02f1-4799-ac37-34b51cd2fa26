"""
Exception classes for the quantitative trading system.
"""

from typing import Op<PERSON>, Dict, Any


class TradingSystemException(Exception):
    """Base exception for all trading system errors."""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for logging/API responses."""
        return {
            'error_type': self.__class__.__name__,
            'message': self.message,
            'error_code': self.error_code,
            'details': self.details
        }


class DataException(TradingSystemException):
    """Exception for data-related errors."""
    pass


class DataValidationException(DataException):
    """Exception for data validation errors."""
    pass


class DataSourceException(DataException):
    """Exception for data source connection/retrieval errors."""
    pass


class DataFormatException(DataException):
    """Exception for data format errors."""
    pass


class StrategyException(TradingSystemException):
    """Exception for strategy-related errors."""
    pass


class StrategyConfigurationException(StrategyException):
    """Exception for strategy configuration errors."""
    pass


class StrategyExecutionException(StrategyException):
    """Exception for strategy execution errors."""
    pass


class RiskException(TradingSystemException):
    """Exception for risk management errors."""
    pass


class RiskLimitException(RiskException):
    """Exception when risk limits are exceeded."""
    pass


class InsufficientFundsException(RiskException):
    """Exception when there are insufficient funds for a trade."""
    pass


class PositionLimitException(RiskException):
    """Exception when position limits are exceeded."""
    pass


class BacktestException(TradingSystemException):
    """Exception for backtest-related errors."""
    pass


class BacktestConfigurationException(BacktestException):
    """Exception for backtest configuration errors."""
    pass


class BacktestExecutionException(BacktestException):
    """Exception for backtest execution errors."""
    pass


class PortfolioException(TradingSystemException):
    """Exception for portfolio management errors."""
    pass


class OrderException(TradingSystemException):
    """Exception for order-related errors."""
    pass


class OrderValidationException(OrderException):
    """Exception for order validation errors."""
    pass


class OrderExecutionException(OrderException):
    """Exception for order execution errors."""
    pass


class ConfigurationException(TradingSystemException):
    """Exception for system configuration errors."""
    pass


class APIException(TradingSystemException):
    """Exception for API-related errors."""
    pass


class DatabaseException(TradingSystemException):
    """Exception for database-related errors."""
    pass


# Error handling utilities
class ErrorHandler:
    """Centralized error handling utilities."""
    
    @staticmethod
    def handle_data_error(error: Exception, context: str = "") -> DataException:
        """Convert generic errors to data exceptions with context."""
        if isinstance(error, DataException):
            return error
        
        details = {'original_error': str(error), 'context': context}
        return DataException(f"Data error in {context}: {str(error)}", details=details)
    
    @staticmethod
    def handle_strategy_error(error: Exception, strategy_name: str = "") -> StrategyException:
        """Convert generic errors to strategy exceptions with context."""
        if isinstance(error, StrategyException):
            return error
        
        details = {'original_error': str(error), 'strategy_name': strategy_name}
        return StrategyException(f"Strategy error in {strategy_name}: {str(error)}", details=details)
    
    @staticmethod
    def handle_risk_error(error: Exception, context: str = "") -> RiskException:
        """Convert generic errors to risk exceptions with context."""
        if isinstance(error, RiskException):
            return error
        
        details = {'original_error': str(error), 'context': context}
        return RiskException(f"Risk management error in {context}: {str(error)}", details=details)
    
    @staticmethod
    def log_error(error: TradingSystemException, logger=None) -> None:
        """Log error with structured information."""
        error_info = error.to_dict()
        if logger:
            logger.error(f"Trading System Error: {error_info}")
        else:
            print(f"ERROR: {error_info}")
    
    @staticmethod
    def create_error_response(error: TradingSystemException, include_details: bool = False) -> Dict[str, Any]:
        """Create standardized error response for APIs."""
        response = {
            'success': False,
            'error': {
                'type': error.__class__.__name__,
                'message': error.message,
                'code': error.error_code
            }
        }
        
        if include_details and error.details:
            response['error']['details'] = error.details
        
        return response