"""
系统验证模块 - 验证量化交易系统的核心组件是否正常工作
"""

import sys
import traceback
from datetime import datetime, timedelta
from typing import Dict, Any, List
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入核心组件
from src.models.market_data import MarketData, UnifiedMarketData, EconomicData, MarketInfo
from src.models.trading import Order, OrderFill, Trade, Signal, OrderSide, OrderType, OrderStatus, SignalAction
from src.models.portfolio import Portfolio, Position
from src.exceptions import *
from config.settings import ConfigManager


class SystemValidator:
    """系统验证器"""
    
    def __init__(self):
        self.results = []
        self.config_manager = None
    
    def run_all_validations(self) -> Dict[str, Any]:
        """运行所有验证测试"""
        print("开始系统验证...")
        
        # 验证配置系统
        self._validate_configuration()
        
        # 验证数据模型
        self._validate_data_models()
        
        # 验证交易模型
        self._validate_trading_models()
        
        # 验证投资组合模型
        self._validate_portfolio_models()
        
        # 验证异常处理
        self._validate_exception_handling()
        
        # 生成验证报告
        return self._generate_report()
    
    def _validate_configuration(self):
        """验证配置管理系统"""
        try:
            print("验证配置管理系统...")
            
            # 测试配置管理器初始化
            self.config_manager = ConfigManager()
            
            # 验证数据库配置
            db_url = self.config_manager.get_database_url()
            assert "postgresql://" in db_url, "数据库URL格式错误"
            
            # 验证风险配置
            assert 0 < self.config_manager.risk.max_position_size <= 1, "最大仓位配置错误"
            assert self.config_manager.risk.stop_loss_pct > 0, "止损配置错误"
            
            # 验证回测配置
            assert self.config_manager.backtest.initial_capital > 0, "初始资金配置错误"
            assert self.config_manager.backtest.commission_rate >= 0, "手续费配置错误"
            
            self.results.append({
                'component': 'Configuration',
                'status': 'PASS',
                'message': '配置管理系统验证通过'
            })
            
        except Exception as e:
            self.results.append({
                'component': 'Configuration',
                'status': 'FAIL',
                'message': f'配置管理系统验证失败: {str(e)}',
                'error': traceback.format_exc()
            })
    
    def _validate_data_models(self):
        """验证数据模型"""
        try:
            print("验证数据模型...")
            
            # 测试MarketData
            market_data = MarketData(
                symbol="AAPL",
                timestamp=datetime.now(),
                open=150.0,
                high=155.0,
                low=148.0,
                close=152.0,
                volume=1000000,
                adj_close=152.0
            )
            assert market_data.validate(), "MarketData验证失败"
            
            # 测试数据转换
            data_dict = market_data.to_dict()
            assert 'symbol' in data_dict, "数据字典转换失败"
            
            json_str = market_data.to_json()
            assert '"symbol": "AAPL"' in json_str, "JSON转换失败"
            
            # 测试UnifiedMarketData
            unified_data = UnifiedMarketData(
                symbol="AAPL",
                timestamp=datetime.now(),
                open=150.0,
                high=155.0,
                low=148.0,
                close=152.0,
                volume=1000000,
                market="US",
                exchange="NASDAQ",
                currency="USD"
            )
            assert unified_data.validate(), "UnifiedMarketData验证失败"
            
            # 测试EconomicData
            econ_data = EconomicData(
                series_id="GDP",
                timestamp=datetime.now(),
                value=21000000000000.0,
                unit="Billions of Dollars",
                frequency="Quarterly"
            )
            assert econ_data.validate(), "EconomicData验证失败"
            
            # 测试MarketInfo
            market_info = MarketInfo(
                symbol="AAPL",
                name="Apple Inc.",
                market="US",
                exchange="NASDAQ",
                currency="USD",
                lot_size=1.0,
                tick_size=0.01,
                trading_hours={"open": "09:30", "close": "16:00"},
                timezone="America/New_York"
            )
            assert market_info.validate(), "MarketInfo验证失败"
            
            self.results.append({
                'component': 'Data Models',
                'status': 'PASS',
                'message': '数据模型验证通过'
            })
            
        except Exception as e:
            self.results.append({
                'component': 'Data Models',
                'status': 'FAIL',
                'message': f'数据模型验证失败: {str(e)}',
                'error': traceback.format_exc()
            })
    
    def _validate_trading_models(self):
        """验证交易模型"""
        try:
            print("验证交易模型...")
            
            # 测试Order
            order = Order(
                id="order_001",
                symbol="AAPL",
                side=OrderSide.BUY,
                order_type=OrderType.LIMIT,
                quantity=100,
                price=150.0,
                timestamp=datetime.now()
            )
            assert order.validate(), "Order验证失败"
            
            # 测试OrderFill
            fill = OrderFill(
                id="fill_001",
                order_id="order_001",
                symbol="AAPL",
                side=OrderSide.BUY,
                quantity=100,
                price=150.0,
                timestamp=datetime.now(),
                commission=1.0
            )
            assert fill.validate(), "OrderFill验证失败"
            
            # 测试Trade
            trade = Trade(
                id="trade_001",
                symbol="AAPL",
                side=OrderSide.BUY,
                quantity=100,
                price=150.0,
                timestamp=datetime.now(),
                commission=1.0,
                strategy_id="test_strategy"
            )
            assert trade.validate(), "Trade验证失败"
            
            # 测试Signal
            signal = Signal(
                symbol="AAPL",
                action=SignalAction.BUY,
                quantity=100,
                price=150.0,
                timestamp=datetime.now(),
                confidence=0.8,
                strategy_id="test_strategy"
            )
            assert signal.validate(), "Signal验证失败"
            
            self.results.append({
                'component': 'Trading Models',
                'status': 'PASS',
                'message': '交易模型验证通过'
            })
            
        except Exception as e:
            self.results.append({
                'component': 'Trading Models',
                'status': 'FAIL',
                'message': f'交易模型验证失败: {str(e)}',
                'error': traceback.format_exc()
            })
    
    def _validate_portfolio_models(self):
        """验证投资组合模型"""
        try:
            print("验证投资组合模型...")
            
            # 测试Position
            position = Position(
                symbol="AAPL",
                quantity=100,
                avg_price=150.0
            )
            position.update_market_value(155.0)
            assert position.validate(), "Position验证失败"
            assert position.unrealized_pnl == 500.0, "未实现盈亏计算错误"
            
            # 测试Portfolio
            portfolio = Portfolio(
                initial_capital=100000.0,
                cash=100000.0
            )
            assert portfolio.validate(), "Portfolio验证失败"
            
            # 测试添加交易
            trade1 = Trade(
                id="trade_001",
                symbol="AAPL",
                side=OrderSide.BUY,
                quantity=100,
                price=150.0,
                timestamp=datetime.now(),
                commission=1.0
            )
            
            portfolio.add_trade(trade1)
            assert "AAPL" in portfolio.positions, "交易添加失败"
            assert portfolio.cash == 100000.0 - 15000.0 - 1.0, "现金计算错误"
            
            # 测试卖出交易
            trade2 = Trade(
                id="trade_002",
                symbol="AAPL",
                side=OrderSide.SELL,
                quantity=50,
                price=155.0,
                timestamp=datetime.now(),
                commission=1.0
            )
            
            portfolio.add_trade(trade2)
            aapl_position = portfolio.get_position("AAPL")
            assert aapl_position.quantity == 50, "仓位数量计算错误"
            
            # 测试市值更新
            portfolio.update_positions_market_value({"AAPL": 160.0})
            assert aapl_position.market_value == 8000.0, "市值计算错误"
            
            self.results.append({
                'component': 'Portfolio Models',
                'status': 'PASS',
                'message': '投资组合模型验证通过'
            })
            
        except Exception as e:
            self.results.append({
                'component': 'Portfolio Models',
                'status': 'FAIL',
                'message': f'投资组合模型验证失败: {str(e)}',
                'error': traceback.format_exc()
            })
    
    def _validate_exception_handling(self):
        """验证异常处理"""
        try:
            print("验证异常处理...")
            
            # 测试基础异常
            try:
                raise DataException("测试数据异常", error_code="TEST_001")
            except DataException as e:
                assert e.error_code == "TEST_001", "异常代码设置失败"
                error_dict = e.to_dict()
                assert error_dict['error_type'] == 'DataException', "异常字典转换失败"
            
            # 测试错误处理器
            handler = ErrorHandler()
            
            # 测试数据错误处理
            try:
                raise ValueError("测试错误")
            except Exception as e:
                data_error = handler.handle_data_error(e, "测试上下文")
                assert isinstance(data_error, DataException), "数据错误处理失败"
            
            # 测试错误响应生成
            test_error = RiskException("风险限制超出", error_code="RISK_001")
            response = handler.create_error_response(test_error)
            assert response['success'] == False, "错误响应生成失败"
            assert response['error']['type'] == 'RiskException', "错误类型设置失败"
            
            self.results.append({
                'component': 'Exception Handling',
                'status': 'PASS',
                'message': '异常处理验证通过'
            })
            
        except Exception as e:
            self.results.append({
                'component': 'Exception Handling',
                'status': 'FAIL',
                'message': f'异常处理验证失败: {str(e)}',
                'error': traceback.format_exc()
            })
    
    def _generate_report(self) -> Dict[str, Any]:
        """生成验证报告"""
        passed = sum(1 for r in self.results if r['status'] == 'PASS')
        failed = sum(1 for r in self.results if r['status'] == 'FAIL')
        total = len(self.results)
        
        report = {
            'summary': {
                'total_tests': total,
                'passed': passed,
                'failed': failed,
                'success_rate': f"{(passed/total*100):.1f}%" if total > 0 else "0%"
            },
            'details': self.results,
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'PASS' if failed == 0 else 'FAIL'
        }
        
        return report
    
    def print_report(self, report: Dict[str, Any]):
        """打印验证报告"""
        print("\n" + "="*60)
        print("系统验证报告")
        print("="*60)
        
        summary = report['summary']
        print(f"总测试数: {summary['total_tests']}")
        print(f"通过: {summary['passed']}")
        print(f"失败: {summary['failed']}")
        print(f"成功率: {summary['success_rate']}")
        print(f"整体状态: {report['overall_status']}")
        
        print("\n详细结果:")
        print("-"*60)
        
        for result in report['details']:
            status_symbol = "✓" if result['status'] == 'PASS' else "✗"
            print(f"{status_symbol} {result['component']}: {result['message']}")
            
            if result['status'] == 'FAIL' and 'error' in result:
                print(f"  错误详情: {result['error']}")
        
        print("="*60)


def run_system_validation():
    """运行系统验证"""
    validator = SystemValidator()
    report = validator.run_all_validations()
    validator.print_report(report)
    return report


if __name__ == "__main__":
    run_system_validation()