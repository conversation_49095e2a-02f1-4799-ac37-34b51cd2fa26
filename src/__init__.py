"""
量化交易系统 - 核心模块初始化

这个模块提供了量化交易系统的核心功能，包括：
- 数据管理和多数据源支持
- 策略框架和回测引擎
- 风险管理和绩效分析
- Web API和图形界面

主要组件：
- models: 核心数据模型（MarketData, Trade, Portfolio, Position）
- exceptions: 异常处理框架
- config: 配置管理系统
"""

from .models.market_data import MarketData, UnifiedMarketData, EconomicData, MarketInfo
from .models.trading import Order, OrderFill, Trade, Signal, OrderSide, OrderType, OrderStatus, SignalAction
from .models.portfolio import Portfolio, Position
from .models.base import BaseModel
from .exceptions import (
    TradingSystemException,
    DataException,
    DataValidationException,
    DataSourceException,
    DataFormatException,
    StrategyException,
    StrategyConfigurationException,
    StrategyExecutionException,
    RiskException,
    RiskLimitException,
    InsufficientFundsException,
    PositionLimitException,
    BacktestException,
    BacktestConfigurationException,
    BacktestExecutionException,
    PortfolioException,
    OrderException,
    OrderValidationException,
    OrderExecutionException,
    ConfigurationException,
    APIException,
    DatabaseException,
    ErrorHandler
)

# 版本信息
__version__ = "1.0.0"
__author__ = "Quantitative Trading System Team"
__description__ = "A comprehensive quantitative trading system with multi-market support"

# 导出的主要类和函数
__all__ = [
    # 数据模型
    'MarketData',
    'UnifiedMarketData', 
    'EconomicData',
    'MarketInfo',
    'Order',
    'OrderFill',
    'Trade',
    'Signal',
    'Portfolio',
    'Position',
    'BaseModel',
    
    # 枚举类型
    'OrderSide',
    'OrderType', 
    'OrderStatus',
    'SignalAction',
    
    # 异常类
    'TradingSystemException',
    'DataException',
    'DataValidationException',
    'DataSourceException',
    'DataFormatException',
    'StrategyException',
    'StrategyConfigurationException',
    'StrategyExecutionException',
    'RiskException',
    'RiskLimitException',
    'InsufficientFundsException',
    'PositionLimitException',
    'BacktestException',
    'BacktestConfigurationException',
    'BacktestExecutionException',
    'PortfolioException',
    'OrderException',
    'OrderValidationException',
    'OrderExecutionException',
    'ConfigurationException',
    'APIException',
    'DatabaseException',
    'ErrorHandler',
    
    # 版本信息
    '__version__',
    '__author__',
    '__description__'
]


def get_system_info():
    """获取系统信息"""
    return {
        'version': __version__,
        'author': __author__,
        'description': __description__,
        'components': {
            'models': 'Core data models for market data, trading, and portfolio management',
            'exceptions': 'Comprehensive error handling framework',
            'config': 'Configuration management system'
        }
    }


def validate_system_setup():
    """验证系统设置是否正确"""
    try:
        # 验证配置管理
        from config.settings import config
        
        # 验证核心模型
        from datetime import datetime
        
        # 测试MarketData
        test_data = MarketData(
            symbol="TEST",
            timestamp=datetime.now(),
            open=100.0,
            high=105.0,
            low=95.0,
            close=102.0,
            volume=1000
        )
        assert test_data.validate(), "MarketData validation failed"
        
        # 测试Portfolio
        test_portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        assert test_portfolio.validate(), "Portfolio validation failed"
        
        # 测试Trade
        test_trade = Trade(
            id="test_trade_1",
            symbol="TEST",
            side=OrderSide.BUY,
            quantity=100,
            price=102.0,
            timestamp=datetime.now()
        )
        assert test_trade.validate(), "Trade validation failed"
        
        return {
            'status': 'success',
            'message': 'System setup validation passed',
            'config_loaded': True,
            'models_working': True
        }
        
    except Exception as e:
        return {
            'status': 'error',
            'message': f'System setup validation failed: {str(e)}',
            'error': str(e)
        }


# 系统初始化时的自动检查
def _initialize_system():
    """系统初始化检查"""
    try:
        # 确保日志目录存在
        import os
        os.makedirs('logs', exist_ok=True)
        
        # 验证配置
        validation_result = validate_system_setup()
        if validation_result['status'] == 'error':
            print(f"Warning: {validation_result['message']}")
        
    except Exception as e:
        print(f"System initialization warning: {str(e)}")


# 执行初始化
_initialize_system()