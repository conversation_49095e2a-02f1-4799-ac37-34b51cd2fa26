"""
Tests for data storage and access layer.
"""

import pytest
import tempfile
import os
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path

from src.data.database import DatabaseManager
from src.data.manager import DataManager
from src.data.migrations import MigrationManager
from src.models.market_data import UnifiedMarketData, EconomicData, MarketInfo


class TestDatabaseManager:
    """Test database manager functionality."""
    
    def setup_method(self):
        """Setup test database."""
        import tempfile
        import os
        
        # Create a temporary database file for each test
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_db.close()
        
        # Patch the database path
        original_init = DatabaseManager._init_sqlite_connection
        
        def patched_init(db_self):
            import sqlite3
            db_self._sqlite_connection = sqlite3.connect(
                self.temp_db.name, 
                check_same_thread=False,
                timeout=30.0
            )
            db_self._sqlite_connection.row_factory = sqlite3.Row
            db_self._sqlite_connection.execute("PRAGMA journal_mode=WAL")
            db_self._sqlite_connection.execute("PRAGMA synchronous=NORMAL")
            db_self._sqlite_connection.execute("PRAGMA cache_size=10000")
            db_self._sqlite_connection.execute("PRAGMA temp_store=memory")
        
        DatabaseManager._init_sqlite_connection = patched_init
        self.db = DatabaseManager(use_sqlite=True)
        DatabaseManager._init_sqlite_connection = original_init
    
    def teardown_method(self):
        """Cleanup test database."""
        self.db.close()
        import os
        try:
            os.unlink(self.temp_db.name)
        except:
            pass
    
    def test_database_connection(self):
        """Test database connection."""
        with self.db.get_connection() as conn:
            assert conn is not None
    
    def test_execute_query(self):
        """Test query execution."""
        # Create a simple test table
        self.db.execute_command("""
            CREATE TABLE IF NOT EXISTS test_table (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL
            )
        """)
        
        # Insert test data
        self.db.execute_command(
            "INSERT INTO test_table (name) VALUES (?)",
            ("test_name",)
        )
        
        # Query data
        results = self.db.execute_query("SELECT * FROM test_table")
        assert len(results) == 1
        assert results[0]['name'] == 'test_name'
    
    def test_table_operations(self):
        """Test table operations."""
        table_name = "test_operations"
        
        # Table should not exist initially
        assert not self.db.table_exists(table_name)
        
        # Create table
        self.db.execute_command(f"""
            CREATE TABLE {table_name} (
                id INTEGER PRIMARY KEY,
                value TEXT
            )
        """)
        
        # Table should now exist
        assert self.db.table_exists(table_name)
        
        # Get table info
        info = self.db.get_table_info(table_name)
        assert len(info) > 0


class TestMigrationManager:
    """Test migration manager functionality."""
    
    def setup_method(self):
        """Setup test database."""
        import tempfile
        import os
        
        # Create a temporary database file for each test
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_db.close()
        
        # Patch the database path
        original_init = DatabaseManager._init_sqlite_connection
        
        def patched_init(db_self):
            import sqlite3
            db_self._sqlite_connection = sqlite3.connect(
                self.temp_db.name, 
                check_same_thread=False,
                timeout=30.0
            )
            db_self._sqlite_connection.row_factory = sqlite3.Row
            db_self._sqlite_connection.execute("PRAGMA journal_mode=WAL")
            db_self._sqlite_connection.execute("PRAGMA synchronous=NORMAL")
            db_self._sqlite_connection.execute("PRAGMA cache_size=10000")
            db_self._sqlite_connection.execute("PRAGMA temp_store=memory")
        
        DatabaseManager._init_sqlite_connection = patched_init
        self.db = DatabaseManager(use_sqlite=True)
        self.migration_manager = MigrationManager(self.db)
        DatabaseManager._init_sqlite_connection = original_init
    
    def teardown_method(self):
        """Cleanup test database."""
        self.db.close()
        import os
        try:
            os.unlink(self.temp_db.name)
        except:
            pass
    
    def test_migration_application(self):
        """Test migration application."""
        # Apply migrations
        self.migration_manager.migrate()
        
        # Check that tables were created
        expected_tables = ['market_data', 'economic_data', 'market_info', 'data_sources', 'migration_history']
        for table in expected_tables:
            assert self.db.table_exists(table)
        
        # Check migration history
        applied_migrations = self.migration_manager.get_applied_migrations()
        assert len(applied_migrations) > 0
    
    def test_migration_status(self):
        """Test migration status reporting."""
        # Apply migrations first
        self.migration_manager.migrate()
        
        # Get status
        status = self.migration_manager.get_migration_status()
        assert 'total_migrations' in status
        assert 'applied_migrations' in status
        assert 'pending_migrations' in status
        assert status['applied_migrations'] == status['total_migrations']


class TestDataManager:
    """Test data manager functionality."""
    
    def setup_method(self):
        """Setup test data manager."""
        import tempfile
        import os
        
        # Create a temporary database file for each test
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_db.close()
        
        # Create a fresh database manager for each test
        from src.data.database import DatabaseManager
        from src.data.repository import MarketDataRepository, EconomicDataRepository, MarketInfoRepository
        from src.data.migrations import MigrationManager
        
        # Patch the database path
        original_init = DatabaseManager._init_sqlite_connection
        
        def patched_init(db_self):
            import sqlite3
            db_self._sqlite_connection = sqlite3.connect(
                self.temp_db.name, 
                check_same_thread=False,
                timeout=30.0
            )
            db_self._sqlite_connection.row_factory = sqlite3.Row
            db_self._sqlite_connection.execute("PRAGMA journal_mode=WAL")
            db_self._sqlite_connection.execute("PRAGMA synchronous=NORMAL")
            db_self._sqlite_connection.execute("PRAGMA cache_size=10000")
            db_self._sqlite_connection.execute("PRAGMA temp_store=memory")
        
        DatabaseManager._init_sqlite_connection = patched_init
        self.db = DatabaseManager(use_sqlite=True)
        DatabaseManager._init_sqlite_connection = original_init
        
        self.market_data_repo = MarketDataRepository(self.db)
        self.economic_data_repo = EconomicDataRepository(self.db)
        self.market_info_repo = MarketInfoRepository(self.db)
        self.migration_manager = MigrationManager(self.db)
        
        # Initialize database schema
        self.migration_manager.migrate()
        
        # Create data manager with existing components
        self.data_manager = DataManager.__new__(DataManager)
        self.data_manager.db = self.db
        self.data_manager.market_data_repo = self.market_data_repo
        self.data_manager.economic_data_repo = self.economic_data_repo
        self.data_manager.market_info_repo = self.market_info_repo
        self.data_manager.migration_manager = self.migration_manager
    
    def teardown_method(self):
        """Cleanup test data manager."""
        self.db.close()
        import os
        try:
            os.unlink(self.temp_db.name)
        except:
            pass
    
    def test_market_data_operations(self):
        """Test market data CRUD operations."""
        # Use a specific timestamp to avoid conflicts
        test_timestamp = datetime(2023, 1, 1, 10, 0, 0)
        
        # Create test market data
        market_data = UnifiedMarketData(
            symbol="AAPL_TEST",
            timestamp=test_timestamp,
            open=150.0,
            high=155.0,
            low=149.0,
            close=154.0,
            volume=1000000,
            market="US",
            exchange="NASDAQ",
            currency="USD",
            adj_close=154.0
        )
        
        # Save market data
        assert self.data_manager.save_market_data(market_data)
        
        # Retrieve market data
        start_date = test_timestamp - timedelta(days=1)
        end_date = test_timestamp + timedelta(days=1)
        
        df = self.data_manager.get_market_data("AAPL_TEST", start_date, end_date, "US")
        assert not df.empty
        assert len(df) == 1
        assert df.iloc[0]['close'] == 154.0
        
        # Get latest data
        latest = self.data_manager.get_latest_market_data("AAPL_TEST", "US")
        assert latest is not None
        assert latest.close == 154.0
    
    def test_market_data_batch_operations(self):
        """Test batch market data operations."""
        # Create test data batch
        market_data_list = []
        base_time = datetime(2023, 1, 1, 10, 0, 0)
        
        for i in range(5):
            data = UnifiedMarketData(
                symbol="MSFT_TEST",
                timestamp=base_time + timedelta(days=i),
                open=100.0 + i,
                high=105.0 + i,
                low=99.0 + i,
                close=104.0 + i,
                volume=1000000 + i * 10000,
                market="US",
                exchange="NASDAQ",
                currency="USD"
            )
            market_data_list.append(data)
        
        # Save batch
        saved_count = self.data_manager.save_market_data_batch(market_data_list)
        assert saved_count == 5
        
        # Retrieve batch
        start_date = base_time - timedelta(days=1)
        end_date = base_time + timedelta(days=6)
        
        df = self.data_manager.get_market_data("MSFT_TEST", start_date, end_date, "US")
        assert len(df) == 5
        assert df.iloc[0]['close'] == 104.0
        assert df.iloc[-1]['close'] == 108.0
    
    def test_economic_data_operations(self):
        """Test economic data operations."""
        # Use a specific timestamp to avoid conflicts
        test_timestamp = datetime(2023, 1, 1, 10, 0, 0)
        
        # Create test economic data
        economic_data = EconomicData(
            series_id="GDP_TEST",
            timestamp=test_timestamp,
            value=25000.0,
            unit="Billions of Dollars",
            frequency="Quarterly",
            seasonal_adjustment="Seasonally Adjusted"
        )
        
        # Save economic data
        assert self.data_manager.save_economic_data(economic_data)
        
        # Retrieve economic data
        start_date = test_timestamp - timedelta(days=1)
        end_date = test_timestamp + timedelta(days=1)
        
        df = self.data_manager.get_economic_data("GDP_TEST", start_date, end_date)
        assert not df.empty
        assert len(df) == 1
        assert df.iloc[0]['value'] == 25000.0
        
        # Get latest data
        latest = self.data_manager.get_latest_economic_data("GDP_TEST")
        assert latest is not None
        assert latest.value == 25000.0
    
    def test_market_info_operations(self):
        """Test market info operations."""
        # Create test market info
        market_info = MarketInfo(
            symbol="AAPL_TEST",
            name="Apple Inc. Test",
            market="US",
            exchange="NASDAQ",
            currency="USD",
            lot_size=1.0,
            tick_size=0.01,
            trading_hours={"open": "09:30", "close": "16:00"},
            timezone="America/New_York"
        )
        
        # Save market info
        assert self.data_manager.save_market_info(market_info)
        
        # Retrieve market info
        retrieved = self.data_manager.get_market_info("AAPL_TEST", "US")
        assert retrieved is not None
        assert retrieved.name == "Apple Inc. Test"
        assert retrieved.exchange == "NASDAQ"
        
        # Get market symbols
        symbols = self.data_manager.get_market_symbols("US")
        assert len(symbols) >= 1
        # Find our test symbol
        test_symbol = next((s for s in symbols if s.symbol == "AAPL_TEST"), None)
        assert test_symbol is not None
        assert test_symbol.symbol == "AAPL_TEST"
    
    def test_data_coverage_report(self):
        """Test data coverage reporting."""
        # Add some test data first
        test_timestamp = datetime(2023, 1, 1, 10, 0, 0)
        market_data = UnifiedMarketData(
            symbol="TEST_COVERAGE",
            timestamp=test_timestamp,
            open=100.0,
            high=105.0,
            low=99.0,
            close=104.0,
            volume=1000000,
            market="US",
            exchange="NYSE",
            currency="USD"
        )
        self.data_manager.save_market_data(market_data)
        
        # Get coverage report
        report = self.data_manager.get_data_coverage_report()
        assert 'market_data' in report
        assert 'economic_data' in report
        assert 'markets' in report
        assert 'generated_at' in report
    
    def test_data_quality_report(self):
        """Test data quality reporting."""
        # Add test data with some quality issues
        base_time = datetime(2023, 1, 1, 10, 0, 0)
        
        # Good data
        good_data = UnifiedMarketData(
            symbol="QUALITY_TEST_UNIQUE",
            timestamp=base_time,
            open=100.0,
            high=105.0,
            low=99.0,
            close=104.0,
            volume=1000000,
            market="US",
            exchange="NYSE",
            currency="USD"
        )
        self.data_manager.save_market_data(good_data)
        
        # Data with zero volume
        zero_volume_data = UnifiedMarketData(
            symbol="QUALITY_TEST_UNIQUE",
            timestamp=base_time + timedelta(days=1),
            open=104.0,
            high=106.0,
            low=103.0,
            close=105.0,
            volume=0,  # Zero volume
            market="US",
            exchange="NYSE",
            currency="USD"
        )
        self.data_manager.save_market_data(zero_volume_data)
        
        # Get quality report
        report = self.data_manager.get_data_quality_report("QUALITY_TEST_UNIQUE", "US")
        assert 'symbol' in report
        assert 'date_range' in report
        assert 'records' in report
        assert 'missing_values' in report
        assert 'price_anomalies' in report
        assert report['price_anomalies']['zero_volume'] == 1


if __name__ == "__main__":
    pytest.main([__file__])