"""
Tests for the risk monitoring system.
"""

import pytest
import tempfile
import os
import time
import threading
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.risk.monitoring_system import (
    RiskMonitoringSystem, RiskAlert, RiskEvent, RiskThreshold,
    AlertSeverity, AlertStatus, MonitoringStatus
)
from src.risk.manager import RiskManager
from src.risk.models import RiskConfig, RiskViolation, RiskLevel, RiskMetrics
from src.models.portfolio import Portfolio, Position


class TestRiskMonitoringSystem:
    """Test cases for RiskMonitoringSystem."""
    
    @pytest.fixture
    def temp_db(self):
        """Create temporary database for testing."""
        fd, path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        yield path
        try:
            os.unlink(path)
        except OSError:
            pass
    
    @pytest.fixture
    def risk_config(self):
        """Create test risk configuration."""
        return RiskConfig(
            max_position_size_pct=0.1,
            max_drawdown_pct=0.15,
            daily_risk_budget=0.02,
            max_leverage=2.0
        )
    
    @pytest.fixture
    def risk_manager(self, risk_config):
        """Create test risk manager."""
        return RiskManager(risk_config)
    
    @pytest.fixture
    def monitoring_system(self, risk_manager, temp_db):
        """Create test monitoring system."""
        return RiskMonitoringSystem(risk_manager, db_path=temp_db)
    
    @pytest.fixture
    def sample_portfolio(self):
        """Create sample portfolio for testing."""
        portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        
        # Add some positions
        position1 = Position(
            symbol="AAPL",
            quantity=100,
            avg_price=150.0,
            last_price=155.0
        )
        position2 = Position(
            symbol="GOOGL",
            quantity=50,
            avg_price=2800.0,
            last_price=2850.0
        )
        
        portfolio.positions["AAPL"] = position1
        portfolio.positions["GOOGL"] = position2
        portfolio.update_total_value()
        
        return portfolio
    
    def test_initialization(self, monitoring_system):
        """Test monitoring system initialization."""
        assert monitoring_system.status == MonitoringStatus.STOPPED
        assert len(monitoring_system.thresholds) > 0
        assert monitoring_system.start_time is None
        assert len(monitoring_system.active_alerts) == 0
    
    def test_database_initialization(self, temp_db, risk_manager):
        """Test database initialization."""
        monitoring_system = RiskMonitoringSystem(risk_manager, db_path=temp_db)
        
        # Check if database file exists
        assert os.path.exists(temp_db)
        
        # Check if tables were created
        import sqlite3
        conn = sqlite3.connect(temp_db)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = ['risk_alerts', 'risk_events', 'risk_metrics_log']
        for table in expected_tables:
            assert table in tables
        
        conn.close()
    
    def test_default_thresholds(self, monitoring_system):
        """Test default threshold configuration."""
        expected_thresholds = [
            "Portfolio Drawdown",
            "Daily Loss Limit", 
            "Position Concentration",
            "Leverage Ratio",
            "VaR 1-Day",
            "Risk Budget Utilization",
            "Cash Level"
        ]
        
        for threshold_name in expected_thresholds:
            assert threshold_name in monitoring_system.thresholds
            threshold = monitoring_system.thresholds[threshold_name]
            assert threshold.enabled
            assert threshold.warning_threshold > 0
            assert threshold.critical_threshold > threshold.warning_threshold or threshold.comparison_operator == 'lt'
    
    def test_start_stop_monitoring(self, monitoring_system):
        """Test starting and stopping monitoring."""
        # Test start
        assert monitoring_system.start_monitoring()
        assert monitoring_system.status == MonitoringStatus.RUNNING
        assert monitoring_system.start_time is not None
        assert monitoring_system._monitoring_thread is not None
        
        # Wait a bit for thread to start
        time.sleep(0.1)
        assert monitoring_system._monitoring_thread.is_alive()
        
        # Test stop
        assert monitoring_system.stop_monitoring()
        assert monitoring_system.status == MonitoringStatus.STOPPED
        
        # Wait for thread to stop
        time.sleep(0.1)
        assert not monitoring_system._monitoring_thread.is_alive()
    
    def test_pause_resume_monitoring(self, monitoring_system):
        """Test pausing and resuming monitoring."""
        # Start monitoring first
        monitoring_system.start_monitoring()
        
        # Test pause
        assert monitoring_system.pause_monitoring()
        assert monitoring_system.status == MonitoringStatus.PAUSED
        
        # Test resume
        assert monitoring_system.resume_monitoring()
        assert monitoring_system.status == MonitoringStatus.RUNNING
        
        # Clean up
        monitoring_system.stop_monitoring()
    
    def test_threshold_checking(self, monitoring_system, sample_portfolio):
        """Test risk threshold checking."""
        # Create risk metrics that will breach thresholds
        metrics = RiskMetrics(
            timestamp=datetime.now(),
            portfolio_value=sample_portfolio.total_value,
            cash=sample_portfolio.cash,
            total_positions=len(sample_portfolio.positions),
            largest_position_pct=30.0,  # This should breach position concentration threshold
            largest_position_symbol="AAPL",
            portfolio_beta=1.0,
            portfolio_var_1d=0.0,
            portfolio_var_5d=0.0,
            expected_shortfall=0.0,
            current_drawdown_pct=12.0,  # This should breach drawdown warning threshold
            max_drawdown_pct=12.0,
            drawdown_duration_days=5,
            gross_leverage=1.0,
            net_leverage=1.0,
            long_exposure_pct=100.0,
            short_exposure_pct=0.0,
            top_5_positions_pct=100.0,
            daily_risk_used_pct=85.0,  # This should breach risk budget threshold
            weekly_risk_used_pct=60.0,
            monthly_risk_used_pct=40.0
        )
        
        # Check thresholds
        monitoring_system._check_risk_thresholds(metrics)
        
        # Should have generated alerts
        assert len(monitoring_system.active_alerts) > 0
        
        # Check specific alerts
        alert_titles = [alert.title for alert in monitoring_system.active_alerts.values()]
        assert any("Position Concentration" in title for title in alert_titles)
        assert any("Portfolio Drawdown" in title for title in alert_titles)
        assert any("Risk Budget Utilization" in title for title in alert_titles)
    
    def test_risk_violation_handling(self, monitoring_system):
        """Test handling of risk violations from risk manager."""
        # Create a risk violation
        violation = RiskViolation(
            rule_name="Test Rule",
            rule_type="position_size",
            violation_type="max_position_size",
            current_value=0.15,
            limit_value=0.10,
            severity=RiskLevel.HIGH,
            timestamp=datetime.now(),
            symbol="AAPL",
            strategy_id="test_strategy",
            details={'test': 'data'}
        )
        
        # Handle the violation
        monitoring_system._handle_risk_violation(violation)
        
        # Check that alert was created
        assert len(monitoring_system.active_alerts) == 1
        
        alert = list(monitoring_system.active_alerts.values())[0]
        assert alert.severity == AlertSeverity.ERROR  # HIGH maps to ERROR
        assert alert.symbol == "AAPL"
        assert alert.strategy_id == "test_strategy"
        assert alert.violation == violation
    
    def test_alert_acknowledgment(self, monitoring_system):
        """Test alert acknowledgment functionality."""
        # Create an alert
        alert = RiskAlert(
            id="test-alert-1",
            timestamp=datetime.now(),
            severity=AlertSeverity.WARNING,
            status=AlertStatus.ACTIVE,
            title="Test Alert",
            message="Test message",
            source="test"
        )
        
        monitoring_system.active_alerts[alert.id] = alert
        
        # Acknowledge the alert
        assert monitoring_system.acknowledge_alert(alert.id, "test_user", "Test acknowledgment")
        
        # Check alert status
        assert alert.status == AlertStatus.ACKNOWLEDGED
        assert alert.acknowledged_by == "test_user"
        assert alert.acknowledged_at is not None
        assert alert.metadata.get('acknowledgment_notes') == "Test acknowledgment"
    
    def test_alert_resolution(self, monitoring_system):
        """Test alert resolution functionality."""
        # Create an alert
        alert = RiskAlert(
            id="test-alert-2",
            timestamp=datetime.now(),
            severity=AlertSeverity.WARNING,
            status=AlertStatus.ACTIVE,
            title="Test Alert",
            message="Test message",
            source="test"
        )
        
        monitoring_system.active_alerts[alert.id] = alert
        
        # Resolve the alert
        assert monitoring_system.resolve_alert(alert.id, "test_user", "Test resolution")
        
        # Check alert status
        assert alert.status == AlertStatus.RESOLVED
        assert alert.resolved_at is not None
        assert alert.metadata.get('resolution_notes') == "Test resolution"
        
        # Should be removed from active alerts
        assert alert.id not in monitoring_system.active_alerts
    
    def test_event_logging(self, monitoring_system):
        """Test event logging functionality."""
        # Log an event
        event_id = monitoring_system._log_event(
            event_type="test_event",
            source="test_source",
            description="Test event description",
            severity=AlertSeverity.INFO,
            symbol="AAPL",
            strategy_id="test_strategy",
            user_id="test_user",
            metadata={'test_key': 'test_value'}
        )
        
        assert event_id != ""
        assert len(monitoring_system.event_history) == 1
        
        event = monitoring_system.event_history[0]
        assert event.event_type == "test_event"
        assert event.source == "test_source"
        assert event.symbol == "AAPL"
        assert event.strategy_id == "test_strategy"
        assert event.user_id == "test_user"
        assert event.metadata['test_key'] == 'test_value'
    
    def test_decision_recording(self, monitoring_system):
        """Test risk decision recording."""
        # Record a decision
        event_id = monitoring_system.record_decision(
            decision_type="emergency_stop",
            description="Emergency stop activated due to high drawdown",
            user_id="risk_manager",
            symbol="AAPL",
            metadata={'drawdown_pct': 20.0}
        )
        
        assert event_id != ""
        assert monitoring_system.stats['decisions_recorded'] == 1
        
        # Check event was logged
        events = monitoring_system.get_event_history(1)
        assert len(events) == 1
        
        event = events[0]
        assert event.event_type == "risk_decision"
        assert event.decision_taken == "emergency_stop"
        assert event.user_id == "risk_manager"
    
    def test_alert_suppression(self, monitoring_system):
        """Test alert suppression functionality."""
        threshold_name = "Portfolio Drawdown"
        
        # Suppress alerts for this threshold
        assert monitoring_system.suppress_alert_type(threshold_name, duration_minutes=1)
        assert threshold_name in monitoring_system.suppressed_alerts
        
        # Wait for suppression to be removed (in real test, we'd mock the timer)
        # For now, just verify the suppression was set
        assert threshold_name in monitoring_system.suppressed_alerts
    
    def test_threshold_management(self, monitoring_system):
        """Test adding and removing thresholds."""
        # Add new threshold
        new_threshold = RiskThreshold(
            name="Test Threshold",
            metric_name="test_metric",
            warning_threshold=10.0,
            critical_threshold=20.0,
            comparison_operator="gt",
            description="Test threshold"
        )
        
        assert monitoring_system.add_threshold(new_threshold)
        assert "Test Threshold" in monitoring_system.thresholds
        
        # Remove threshold
        assert monitoring_system.remove_threshold("Test Threshold")
        assert "Test Threshold" not in monitoring_system.thresholds
    
    def test_monitoring_status(self, monitoring_system):
        """Test monitoring status reporting."""
        status = monitoring_system.get_monitoring_status()
        
        assert 'status' in status
        assert 'active_alerts' in status
        assert 'total_alerts_generated' in status
        assert 'total_events_logged' in status
        assert 'thresholds_configured' in status
        assert status['status'] == MonitoringStatus.STOPPED.value
    
    def test_alert_history(self, monitoring_system):
        """Test alert history retrieval."""
        # Create some test alerts
        for i in range(3):
            alert = RiskAlert(
                id=f"test-alert-{i}",
                timestamp=datetime.now() - timedelta(hours=i),
                severity=AlertSeverity.WARNING,
                status=AlertStatus.ACTIVE,
                title=f"Test Alert {i}",
                message=f"Test message {i}",
                source="test"
            )
            monitoring_system.alert_history.append(alert)
        
        # Get recent alerts
        recent_alerts = monitoring_system.get_alert_history(hours=2)
        assert len(recent_alerts) == 2  # Only alerts from last 2 hours
        
        # Check ordering (newest first)
        assert recent_alerts[0].title == "Test Alert 0"
        assert recent_alerts[1].title == "Test Alert 1"
    
    def test_event_history(self, monitoring_system):
        """Test event history retrieval."""
        # Create some test events
        for i in range(3):
            event = RiskEvent(
                id=f"test-event-{i}",
                timestamp=datetime.now() - timedelta(hours=i),
                event_type="test_event",
                source="test",
                description=f"Test event {i}",
                severity=AlertSeverity.INFO
            )
            monitoring_system.event_history.append(event)
        
        # Get recent events
        recent_events = monitoring_system.get_event_history(hours=2)
        assert len(recent_events) == 2  # Only events from last 2 hours
        
        # Test filtering by event type
        filtered_events = monitoring_system.get_event_history(hours=24, event_type="test_event")
        assert len(filtered_events) == 3
        assert all(event.event_type == "test_event" for event in filtered_events)
    
    def test_monitoring_report(self, monitoring_system):
        """Test monitoring report generation."""
        # Add some test data
        alert = RiskAlert(
            id="test-alert",
            timestamp=datetime.now(),
            severity=AlertSeverity.CRITICAL,
            status=AlertStatus.ACTIVE,
            title="Test Alert",
            message="Test message",
            source="threshold_monitor",
            metadata={'threshold_name': 'Test Threshold'}
        )
        monitoring_system.alert_history.append(alert)
        
        event = RiskEvent(
            id="test-event",
            timestamp=datetime.now(),
            event_type="alert_generated",
            source="test",
            description="Test event",
            severity=AlertSeverity.INFO
        )
        monitoring_system.event_history.append(event)
        
        # Generate report
        report = monitoring_system.generate_monitoring_report(hours=1)
        
        assert 'report_period_hours' in report
        assert 'system_status' in report
        assert 'alert_summary' in report
        assert 'event_summary' in report
        assert 'statistics' in report
        assert 'configuration' in report
        
        # Check alert summary
        assert report['alert_summary']['total_alerts'] == 1
        assert report['alert_summary']['alerts_by_severity']['critical'] == 1
        assert report['alert_summary']['threshold_breaches']['Test Threshold'] == 1
        
        # Check event summary
        assert report['event_summary']['total_events'] == 1
        assert report['event_summary']['events_by_type']['alert_generated'] == 1
    
    def test_callbacks(self, monitoring_system):
        """Test alert and event callbacks."""
        alert_callback_called = False
        event_callback_called = False
        
        def alert_callback(alert):
            nonlocal alert_callback_called
            alert_callback_called = True
            assert isinstance(alert, RiskAlert)
        
        def event_callback(event):
            nonlocal event_callback_called
            event_callback_called = True
            assert isinstance(event, RiskEvent)
        
        # Add callbacks
        monitoring_system.add_alert_callback(alert_callback)
        monitoring_system.add_event_callback(event_callback)
        
        # Trigger alert
        alert = RiskAlert(
            id="test-alert",
            timestamp=datetime.now(),
            severity=AlertSeverity.WARNING,
            status=AlertStatus.ACTIVE,
            title="Test Alert",
            message="Test message",
            source="test"
        )
        monitoring_system._process_alert(alert)
        
        # Trigger event
        monitoring_system._log_event(
            event_type="test_event",
            source="test",
            description="Test event",
            severity=AlertSeverity.INFO
        )
        
        assert alert_callback_called
        assert event_callback_called
    
    def test_context_manager(self, monitoring_system):
        """Test context manager functionality."""
        with monitoring_system as ms:
            assert ms.status == MonitoringStatus.RUNNING
        
        assert monitoring_system.status == MonitoringStatus.STOPPED
    
    def test_cooldown_mechanism(self, monitoring_system, sample_portfolio):
        """Test alert cooldown mechanism."""
        # Create metrics that breach threshold
        metrics = RiskMetrics(
            timestamp=datetime.now(),
            portfolio_value=sample_portfolio.total_value,
            cash=sample_portfolio.cash,
            total_positions=len(sample_portfolio.positions),
            largest_position_pct=30.0,  # Breaches threshold
            largest_position_symbol="AAPL",
            portfolio_beta=1.0,
            portfolio_var_1d=0.0,
            portfolio_var_5d=0.0,
            expected_shortfall=0.0,
            current_drawdown_pct=5.0,
            max_drawdown_pct=5.0,
            drawdown_duration_days=0,
            gross_leverage=1.0,
            net_leverage=1.0,
            long_exposure_pct=100.0,
            short_exposure_pct=0.0,
            top_5_positions_pct=100.0,
            daily_risk_used_pct=50.0,
            weekly_risk_used_pct=30.0,
            monthly_risk_used_pct=20.0
        )
        
        # First check should generate alert
        monitoring_system._check_risk_thresholds(metrics)
        initial_alert_count = len(monitoring_system.active_alerts)
        assert initial_alert_count > 0
        
        # Second check should not generate new alert due to cooldown
        monitoring_system._check_risk_thresholds(metrics)
        assert len(monitoring_system.active_alerts) == initial_alert_count
        
        # Check that cooldown was set
        assert len(monitoring_system.alert_cooldowns) > 0


if __name__ == "__main__":
    pytest.main([__file__])