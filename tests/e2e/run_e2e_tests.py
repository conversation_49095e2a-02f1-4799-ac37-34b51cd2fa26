"""
端到端测试运行脚本
提供完整的测试套件执行和报告功能
"""
import os
import sys
import subprocess
import argparse
import json
from datetime import datetime
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)


class E2ETestRunner:
    """端到端测试运行器"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.end_time = None
    
    def run_test_suite(self, test_type='all', verbose=False, generate_report=True):
        """运行测试套件"""
        self.start_time = datetime.now()
        
        print("=" * 80)
        print("量化交易系统 - 端到端测试套件")
        print("=" * 80)
        print(f"开始时间: {self.start_time}")
        print()
        
        # 定义测试模块
        test_modules = {
            'backtest': 'test_complete_backtest_flow.py',
            'multi_strategy': 'test_multi_strategy_portfolio.py',
            'performance': 'test_performance_load_testing.py',
            'api': 'test_api_automation.py',
            'multi_market': 'test_multi_market_integration.py',
            'cross_market': 'test_cross_market_strategies.py',
            'failover': 'test_data_source_failover.py'
        }
        
        # 确定要运行的测试
        if test_type == 'all':
            tests_to_run = list(test_modules.values())
        elif test_type in test_modules:
            tests_to_run = [test_modules[test_type]]
        else:
            print(f"错误: 未知的测试类型 '{test_type}'")
            print(f"可用的测试类型: {', '.join(['all'] + list(test_modules.keys()))}")
            return False
        
        # 运行测试
        overall_success = True
        
        for test_file in tests_to_run:
            success = self._run_single_test(test_file, verbose)
            overall_success = overall_success and success
        
        self.end_time = datetime.now()
        
        # 生成报告
        if generate_report:
            self._generate_test_report()
        
        # 打印总结
        self._print_summary(overall_success)
        
        return overall_success
    
    def _run_single_test(self, test_file, verbose=False):
        """运行单个测试文件"""
        print(f"运行测试: {test_file}")
        print("-" * 60)
        
        test_path = os.path.join(os.path.dirname(__file__), test_file)
        
        # 构建pytest命令
        cmd = [
            sys.executable, '-m', 'pytest',
            test_path,
            '-v' if verbose else '-q',
            '--tb=short',
            '--durations=10',
            '--json-report',
            f'--json-report-file=test_results_{test_file.replace(".py", "")}.json'
        ]
        
        # 添加覆盖率报告（如果安装了pytest-cov）
        try:
            import pytest_cov
            cmd.extend(['--cov=src', '--cov-report=term-missing'])
        except ImportError:
            pass
        
        start_time = time.time()
        
        try:
            # 运行测试
            result = subprocess.run(
                cmd,
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=1800  # 30分钟超时
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 记录结果
            self.test_results[test_file] = {
                'success': result.returncode == 0,
                'execution_time': execution_time,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'return_code': result.returncode
            }
            
            # 打印结果
            if result.returncode == 0:
                print(f"✅ 测试通过 (耗时: {execution_time:.2f}秒)")
            else:
                print(f"❌ 测试失败 (耗时: {execution_time:.2f}秒)")
                if verbose:
                    print("STDOUT:")
                    print(result.stdout)
                    print("STDERR:")
                    print(result.stderr)
            
            print()
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            print(f"⏰ 测试超时 (超过30分钟)")
            self.test_results[test_file] = {
                'success': False,
                'execution_time': 1800,
                'error': 'timeout',
                'return_code': -1
            }
            print()
            return False
            
        except Exception as e:
            print(f"💥 测试执行错误: {e}")
            self.test_results[test_file] = {
                'success': False,
                'execution_time': 0,
                'error': str(e),
                'return_code': -1
            }
            print()
            return False
    
    def _generate_test_report(self):
        """生成测试报告"""
        report_file = f"e2e_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report_data = {
            'test_suite': '量化交易系统端到端测试',
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'total_duration': (self.end_time - self.start_time).total_seconds(),
            'test_results': self.test_results,
            'summary': self._get_test_summary()
        }
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            print(f"📊 测试报告已生成: {report_file}")
            
        except Exception as e:
            print(f"⚠️  生成测试报告失败: {e}")
    
    def _get_test_summary(self):
        """获取测试摘要"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        
        total_time = sum(result['execution_time'] for result in self.test_results.values())
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': passed_tests / total_tests if total_tests > 0 else 0,
            'total_execution_time': total_time
        }
    
    def _print_summary(self, overall_success):
        """打印测试摘要"""
        print("=" * 80)
        print("测试摘要")
        print("=" * 80)
        
        summary = self._get_test_summary()
        
        print(f"总测试数: {summary['total_tests']}")
        print(f"通过测试: {summary['passed_tests']}")
        print(f"失败测试: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']:.1%}")
        print(f"总执行时间: {summary['total_execution_time']:.2f}秒")
        print()
        
        # 详细结果
        print("详细结果:")
        for test_file, result in self.test_results.items():
            status = "✅ 通过" if result['success'] else "❌ 失败"
            print(f"  {test_file}: {status} ({result['execution_time']:.2f}秒)")
        
        print()
        print(f"结束时间: {self.end_time}")
        
        if overall_success:
            print("🎉 所有测试通过!")
        else:
            print("⚠️  部分测试失败，请检查详细日志")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='运行量化交易系统端到端测试')
    
    parser.add_argument(
        '--type', '-t',
        choices=['all', 'backtest', 'multi_strategy', 'performance', 'api', 
                'multi_market', 'cross_market', 'failover'],
        default='all',
        help='要运行的测试类型 (默认: all)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出模式'
    )
    
    parser.add_argument(
        '--no-report',
        action='store_true',
        help='不生成测试报告'
    )
    
    parser.add_argument(
        '--quick',
        action='store_true',
        help='快速模式，跳过性能测试'
    )
    
    args = parser.parse_args()
    
    # 快速模式跳过性能测试
    if args.quick and args.type == 'all':
        print("⚡ 快速模式: 跳过性能测试")
        # 这里可以设置环境变量来跳过性能测试
        os.environ['SKIP_PERFORMANCE_TESTS'] = '1'
    
    # 创建并运行测试
    runner = E2ETestRunner()
    success = runner.run_test_suite(
        test_type=args.type,
        verbose=args.verbose,
        generate_report=not args.no_report
    )
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()