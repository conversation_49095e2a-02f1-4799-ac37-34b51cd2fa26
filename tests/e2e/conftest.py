"""
端到端测试配置文件
提供共享的测试夹具和配置
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)


@pytest.fixture(scope="session")
def test_config():
    """测试配置"""
    return {
        'test_data_size': {
            'small': 100,    # 100个数据点
            'medium': 1000,  # 1000个数据点
            'large': 10000   # 10000个数据点
        },
        'performance_thresholds': {
            'max_response_time': 5.0,      # 最大响应时间（秒）
            'max_memory_usage': 1000,      # 最大内存使用（MB）
            'min_success_rate': 0.95       # 最小成功率
        },
        'market_symbols': {
            'us_stocks': ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN'],
            'china_stocks': ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH'],
            'crypto': ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT'],
            'etfs': ['SPY', 'QQQ', '510300.SH', '159919.SZ']
        },
        'test_date_ranges': {
            'short_term': ('2023-11-01', '2023-12-31'),
            'medium_term': ('2023-06-01', '2023-12-31'),
            'long_term': ('2023-01-01', '2023-12-31')
        }
    }


@pytest.fixture
def mock_market_data_generator():
    """市场数据生成器"""
    
    def generate_data(symbol, start_date, end_date, freq='D', market_type='stock'):
        """生成模拟市场数据"""
        dates = pd.date_range(start=start_date, end=end_date, freq=freq)
        
        # 根据市场类型调整数据特征
        if market_type == 'stock':
            base_price = 100
            volatility = 0.02
        elif market_type == 'crypto':
            base_price = 30000
            volatility = 0.05
        elif market_type == 'forex':
            base_price = 1.0
            volatility = 0.01
        else:
            base_price = 100
            volatility = 0.02
        
        # 生成价格序列
        np.random.seed(hash(symbol) % 2**32)  # 基于符号的固定种子
        returns = np.random.randn(len(dates)) * volatility
        prices = base_price * np.exp(returns.cumsum())
        
        # 生成OHLCV数据
        data = pd.DataFrame({
            'open': prices * (1 + np.random.randn(len(dates)) * 0.001),
            'high': prices * (1 + np.abs(np.random.randn(len(dates))) * 0.002),
            'low': prices * (1 - np.abs(np.random.randn(len(dates))) * 0.002),
            'close': prices,
            'volume': np.random.randint(1000, 100000, len(dates))
        }, index=dates)
        
        # 确保OHLC逻辑正确
        data['high'] = np.maximum.reduce([data['open'], data['high'], data['close']])
        data['low'] = np.minimum.reduce([data['open'], data['low'], data['close']])
        
        return data
    
    return generate_data


@pytest.fixture
def mock_economic_data_generator():
    """经济数据生成器"""
    
    def generate_economic_data(indicators, start_date, end_date, freq='M'):
        """生成模拟经济数据"""
        dates = pd.date_range(start=start_date, end=end_date, freq=freq)
        
        data = pd.DataFrame(index=dates)
        
        # 预定义的经济指标基准值和波动性
        indicator_config = {
            'gdp_growth': {'base': 2.5, 'volatility': 0.5},
            'inflation_rate': {'base': 3.0, 'volatility': 0.3},
            'unemployment_rate': {'base': 4.0, 'volatility': 0.2},
            'interest_rate': {'base': 2.0, 'volatility': 0.1},
            'consumer_confidence': {'base': 100, 'volatility': 5},
            'industrial_production': {'base': 0, 'volatility': 1}
        }
        
        for indicator in indicators:
            if indicator in indicator_config:
                config = indicator_config[indicator]
                base_value = config['base']
                volatility = config['volatility']
                
                # 生成带趋势的随机序列
                trend = np.linspace(0, 0.1, len(dates))  # 轻微上升趋势
                noise = np.random.randn(len(dates)) * volatility
                
                data[indicator] = base_value + trend + noise
            else:
                # 默认生成标准正态分布数据
                data[indicator] = np.random.randn(len(dates))
        
        return data
    
    return generate_economic_data


@pytest.fixture
def performance_monitor():
    """性能监控器"""
    
    class PerformanceMonitor:
        def __init__(self):
            self.metrics = {
                'execution_times': [],
                'memory_usage': [],
                'success_count': 0,
                'failure_count': 0
            }
        
        def start_timing(self):
            """开始计时"""
            import time
            self.start_time = time.time()
        
        def end_timing(self):
            """结束计时"""
            import time
            if hasattr(self, 'start_time'):
                execution_time = time.time() - self.start_time
                self.metrics['execution_times'].append(execution_time)
                return execution_time
            return None
        
        def record_memory_usage(self):
            """记录内存使用"""
            try:
                import psutil
                process = psutil.Process()
                memory_mb = process.memory_info().rss / 1024 / 1024
                self.metrics['memory_usage'].append(memory_mb)
                return memory_mb
            except ImportError:
                return None
        
        def record_success(self):
            """记录成功"""
            self.metrics['success_count'] += 1
        
        def record_failure(self):
            """记录失败"""
            self.metrics['failure_count'] += 1
        
        def get_summary(self):
            """获取性能摘要"""
            total_operations = self.metrics['success_count'] + self.metrics['failure_count']
            
            summary = {
                'total_operations': total_operations,
                'success_rate': self.metrics['success_count'] / total_operations if total_operations > 0 else 0,
                'avg_execution_time': np.mean(self.metrics['execution_times']) if self.metrics['execution_times'] else 0,
                'max_execution_time': np.max(self.metrics['execution_times']) if self.metrics['execution_times'] else 0,
                'avg_memory_usage': np.mean(self.metrics['memory_usage']) if self.metrics['memory_usage'] else 0,
                'max_memory_usage': np.max(self.metrics['memory_usage']) if self.metrics['memory_usage'] else 0
            }
            
            return summary
    
    return PerformanceMonitor()


@pytest.fixture
def test_data_cleanup():
    """测试数据清理"""
    created_files = []
    
    def register_file(filepath):
        """注册需要清理的文件"""
        created_files.append(filepath)
    
    yield register_file
    
    # 测试结束后清理文件
    for filepath in created_files:
        try:
            if os.path.exists(filepath):
                os.remove(filepath)
        except Exception:
            pass  # 忽略清理错误


@pytest.fixture
def mock_external_apis():
    """模拟外部API"""
    
    class MockAPIManager:
        def __init__(self):
            self.api_calls = []
            self.response_delays = {}
            self.failure_rates = {}
        
        def set_response_delay(self, api_name, delay):
            """设置API响应延迟"""
            self.response_delays[api_name] = delay
        
        def set_failure_rate(self, api_name, rate):
            """设置API失败率"""
            self.failure_rates[api_name] = rate
        
        def mock_api_call(self, api_name, *args, **kwargs):
            """模拟API调用"""
            import time
            import random
            
            # 记录调用
            self.api_calls.append({
                'api_name': api_name,
                'timestamp': datetime.now(),
                'args': args,
                'kwargs': kwargs
            })
            
            # 模拟延迟
            if api_name in self.response_delays:
                time.sleep(self.response_delays[api_name])
            
            # 模拟失败
            if api_name in self.failure_rates:
                if random.random() < self.failure_rates[api_name]:
                    raise ConnectionError(f"模拟的{api_name} API失败")
            
            # 返回模拟响应
            return {'status': 'success', 'data': 'mock_data'}
        
        def get_call_history(self):
            """获取调用历史"""
            return self.api_calls
        
        def reset(self):
            """重置状态"""
            self.api_calls = []
            self.response_delays = {}
            self.failure_rates = {}
    
    return MockAPIManager()


# 测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "slow: 标记测试为慢速测试"
    )
    config.addinivalue_line(
        "markers", "integration: 标记测试为集成测试"
    )
    config.addinivalue_line(
        "markers", "performance: 标记测试为性能测试"
    )
    config.addinivalue_line(
        "markers", "e2e: 标记测试为端到端测试"
    )


# 跳过条件
def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    # 如果没有安装某些依赖，跳过相关测试
    try:
        import psutil
    except ImportError:
        skip_psutil = pytest.mark.skip(reason="需要安装psutil")
        for item in items:
            if "memory" in item.nodeid or "performance" in item.nodeid:
                item.add_marker(skip_psutil)
    
    # 标记慢速测试
    for item in items:
        if "load_testing" in item.nodeid or "performance" in item.nodeid:
            item.add_marker(pytest.mark.slow)