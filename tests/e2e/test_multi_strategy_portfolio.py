"""
多策略组合测试场景
测试多个策略同时运行的组合效果
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.strategies.multi_strategy_manager import MultiStrategyManager
from src.strategies.strategy_framework import BaseStrategy
from src.portfolio.portfolio_manager import PortfolioManager
from src.backtest.backtest_engine import BacktestEngine
from src.data.data_source_manager import DataSourceManager


class TestMultiStrategyPortfolio:
    """多策略组合测试"""
    
    @pytest.fixture
    def setup_strategies(self):
        """设置测试策略"""
        
        class MomentumStrategy(BaseStrategy):
            def __init__(self):
                super().__init__("momentum_strategy")
                
            def generate_signals(self, data):
                signals = pd.DataFrame(index=data.index)
                signals['signal'] = 0
                
                # 动量策略：价格上涨趋势买入
                returns = data['close'].pct_change()
                momentum = returns.rolling(window=10).mean()
                
                signals.loc[momentum > 0.01, 'signal'] = 1
                signals.loc[momentum < -0.01, 'signal'] = -1
                
                return signals
        
        class MeanReversionStrategy(BaseStrategy):
            def __init__(self):
                super().__init__("mean_reversion_strategy")
                
            def generate_signals(self, data):
                signals = pd.DataFrame(index=data.index)
                signals['signal'] = 0
                
                # 均值回归策略：价格偏离均值时反向操作
                price = data['close']
                ma = price.rolling(window=20).mean()
                std = price.rolling(window=20).std()
                
                z_score = (price - ma) / std
                
                signals.loc[z_score > 2, 'signal'] = -1  # 超买，卖出
                signals.loc[z_score < -2, 'signal'] = 1   # 超卖，买入
                
                return signals
        
        class BreakoutStrategy(BaseStrategy):
            def __init__(self):
                super().__init__("breakout_strategy")
                
            def generate_signals(self, data):
                signals = pd.DataFrame(index=data.index)
                signals['signal'] = 0
                
                # 突破策略：价格突破阻力位买入
                high_20 = data['high'].rolling(window=20).max()
                low_20 = data['low'].rolling(window=20).min()
                
                signals.loc[data['close'] > high_20.shift(1), 'signal'] = 1
                signals.loc[data['close'] < low_20.shift(1), 'signal'] = -1
                
                return signals
        
        return {
            'momentum': MomentumStrategy(),
            'mean_reversion': MeanReversionStrategy(),
            'breakout': BreakoutStrategy()
        }
    
    @pytest.fixture
    def setup_test_data(self):
        """设置测试数据"""
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        
        # 创建不同特征的测试数据
        np.random.seed(42)
        
        # 趋势性数据（适合动量策略）
        trend_data = pd.DataFrame({
            'open': np.random.randn(len(dates)).cumsum() + 100,
            'high': np.random.randn(len(dates)).cumsum() + 105,
            'low': np.random.randn(len(dates)).cumsum() + 95,
            'close': np.random.randn(len(dates)).cumsum() + 100,
            'volume': np.random.randint(1000, 10000, len(dates))
        }, index=dates)
        
        # 震荡性数据（适合均值回归策略）
        oscillating_data = pd.DataFrame({
            'open': 100 + 10 * np.sin(np.arange(len(dates)) * 0.1) + np.random.randn(len(dates)) * 2,
            'high': 105 + 10 * np.sin(np.arange(len(dates)) * 0.1) + np.random.randn(len(dates)) * 2,
            'low': 95 + 10 * np.sin(np.arange(len(dates)) * 0.1) + np.random.randn(len(dates)) * 2,
            'close': 100 + 10 * np.sin(np.arange(len(dates)) * 0.1) + np.random.randn(len(dates)) * 2,
            'volume': np.random.randint(1000, 10000, len(dates))
        }, index=dates)
        
        return {
            'AAPL': trend_data,
            'GOOGL': oscillating_data,
            'MSFT': trend_data * 1.1  # 相关但不完全相同的趋势
        }
    
    def test_multi_strategy_initialization(self, setup_strategies):
        """测试多策略管理器初始化"""
        strategies = setup_strategies
        
        # 创建多策略管理器
        manager = MultiStrategyManager()
        
        # 添加策略
        for name, strategy in strategies.items():
            manager.add_strategy(strategy, weight=1.0/len(strategies))
        
        # 验证策略添加成功
        assert len(manager.strategies) == 3
        assert manager.get_strategy('momentum_strategy') is not None
        assert manager.get_strategy('mean_reversion_strategy') is not None
        assert manager.get_strategy('breakout_strategy') is not None
    
    def test_multi_strategy_signal_generation(self, setup_strategies, setup_test_data):
        """测试多策略信号生成"""
        strategies = setup_strategies
        test_data = setup_test_data
        
        manager = MultiStrategyManager()
        
        # 添加策略并设置权重
        manager.add_strategy(strategies['momentum'], weight=0.4)
        manager.add_strategy(strategies['mean_reversion'], weight=0.3)
        manager.add_strategy(strategies['breakout'], weight=0.3)
        
        # 为每个资产生成组合信号
        for symbol, data in test_data.items():
            combined_signals = manager.generate_combined_signals(symbol, data)
            
            # 验证信号格式
            assert isinstance(combined_signals, pd.DataFrame)
            assert 'signal' in combined_signals.columns
            assert len(combined_signals) == len(data)
            
            # 验证信号值在合理范围内
            assert combined_signals['signal'].min() >= -1
            assert combined_signals['signal'].max() <= 1
    
    def test_multi_strategy_backtest(self, setup_strategies, setup_test_data):
        """测试多策略组合回测"""
        strategies = setup_strategies
        test_data = setup_test_data
        
        # 创建多策略管理器
        manager = MultiStrategyManager()
        manager.add_strategy(strategies['momentum'], weight=0.4)
        manager.add_strategy(strategies['mean_reversion'], weight=0.3)
        manager.add_strategy(strategies['breakout'], weight=0.3)
        
        # 创建回测引擎
        backtest_engine = BacktestEngine(
            initial_capital=100000,
            commission=0.001
        )
        
        # Mock数据源
        with patch.object(DataSourceManager, 'get_historical_data') as mock_data:
            def get_data_side_effect(symbol, start_date, end_date):
                return test_data.get(symbol, test_data['AAPL'])
            
            mock_data.side_effect = get_data_side_effect
            
            # 运行多策略回测
            results = backtest_engine.run_multi_strategy_backtest(
                strategy_manager=manager,
                symbols=list(test_data.keys()),
                start_date='2023-01-01',
                end_date='2023-12-31'
            )
        
        # 验证回测结果
        assert results is not None
        assert 'portfolio_value' in results
        assert 'strategy_performance' in results
        assert 'combined_performance' in results
        
        # 验证每个策略的表现
        strategy_perf = results['strategy_performance']
        assert 'momentum_strategy' in strategy_perf
        assert 'mean_reversion_strategy' in strategy_perf
        assert 'breakout_strategy' in strategy_perf
    
    def test_strategy_weight_optimization(self, setup_strategies, setup_test_data):
        """测试策略权重优化"""
        strategies = setup_strategies
        test_data = setup_test_data
        
        manager = MultiStrategyManager()
        
        # 添加策略
        for strategy in strategies.values():
            manager.add_strategy(strategy, weight=1.0/len(strategies))
        
        # 模拟历史表现数据
        historical_returns = {
            'momentum_strategy': np.random.randn(252) * 0.02 + 0.001,
            'mean_reversion_strategy': np.random.randn(252) * 0.015 + 0.0005,
            'breakout_strategy': np.random.randn(252) * 0.025 + 0.0008
        }
        
        # 优化权重
        optimal_weights = manager.optimize_weights(
            historical_returns=historical_returns,
            target_return=0.12,
            risk_tolerance=0.15
        )
        
        # 验证权重优化结果
        assert isinstance(optimal_weights, dict)
        assert len(optimal_weights) == 3
        assert abs(sum(optimal_weights.values()) - 1.0) < 0.01  # 权重和为1
        
        # 验证所有权重为正数
        for weight in optimal_weights.values():
            assert weight >= 0
    
    def test_strategy_correlation_analysis(self, setup_strategies, setup_test_data):
        """测试策略相关性分析"""
        strategies = setup_strategies
        test_data = setup_test_data
        
        manager = MultiStrategyManager()
        
        # 添加策略
        for strategy in strategies.values():
            manager.add_strategy(strategy, weight=1.0/len(strategies))
        
        # 生成策略信号
        strategy_signals = {}
        for symbol, data in test_data.items():
            strategy_signals[symbol] = {}
            for name, strategy in strategies.items():
                signals = strategy.generate_signals(data)
                strategy_signals[symbol][name] = signals['signal']
        
        # 分析策略相关性
        correlation_matrix = manager.analyze_strategy_correlation(strategy_signals)
        
        # 验证相关性矩阵
        assert isinstance(correlation_matrix, pd.DataFrame)
        assert correlation_matrix.shape == (3, 3)
        
        # 验证对角线为1
        for i in range(3):
            assert abs(correlation_matrix.iloc[i, i] - 1.0) < 0.01
    
    def test_dynamic_weight_adjustment(self, setup_strategies, setup_test_data):
        """测试动态权重调整"""
        strategies = setup_strategies
        test_data = setup_test_data
        
        manager = MultiStrategyManager()
        
        # 添加策略
        for strategy in strategies.values():
            manager.add_strategy(strategy, weight=1.0/len(strategies))
        
        # 模拟策略表现数据
        performance_data = {
            'momentum_strategy': {'return': 0.15, 'sharpe': 1.2, 'max_dd': 0.08},
            'mean_reversion_strategy': {'return': 0.08, 'sharpe': 0.9, 'max_dd': 0.05},
            'breakout_strategy': {'return': 0.12, 'sharpe': 1.0, 'max_dd': 0.10}
        }
        
        # 动态调整权重
        new_weights = manager.adjust_weights_dynamically(
            performance_data=performance_data,
            lookback_period=60,
            adjustment_factor=0.1
        )
        
        # 验证权重调整
        assert isinstance(new_weights, dict)
        assert len(new_weights) == 3
        assert abs(sum(new_weights.values()) - 1.0) < 0.01
        
        # 验证表现好的策略权重增加
        # 动量策略表现最好，权重应该相对较高
        assert new_weights['momentum_strategy'] >= new_weights['mean_reversion_strategy']