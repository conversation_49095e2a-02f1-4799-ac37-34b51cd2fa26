"""
API接口自动化测试
测试所有API端点的功能和性能
"""
import pytest
import asyncio
import json
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import pandas as pd
import numpy as np

from fastapi.testclient import TestClient
from src.api.main import app
from src.api.models import *
from src.data.data_source_manager import DataSourceManager
from src.backtest.backtest_engine import BacktestEngine


class TestAPIAutomation:
    """API自动化测试"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    @pytest.fixture
    def sample_data(self):
        """创建测试数据"""
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        return pd.DataFrame({
            'open': np.random.randn(len(dates)).cumsum() + 100,
            'high': np.random.randn(len(dates)).cumsum() + 105,
            'low': np.random.randn(len(dates)).cumsum() + 95,
            'close': np.random.randn(len(dates)).cumsum() + 100,
            'volume': np.random.randint(1000, 10000, len(dates))
        }, index=dates)
    
    def test_health_check_endpoint(self, client):
        """测试健康检查端点"""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data
    
    def test_market_data_endpoints(self, client, sample_data):
        """测试市场数据相关端点"""
        
        with patch.object(DataSourceManager, 'get_historical_data') as mock_data:
            mock_data.return_value = sample_data
            
            # 测试获取历史数据
            response = client.get(
                "/api/v1/market-data/historical",
                params={
                    "symbol": "AAPL",
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31",
                    "interval": "1d"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "data" in data
            assert len(data["data"]) > 0
            
            # 验证数据格式
            first_record = data["data"][0]
            required_fields = ["timestamp", "open", "high", "low", "close", "volume"]
            assert all(field in first_record for field in required_fields)
    
    def test_strategy_management_endpoints(self, client):
        """测试策略管理端点"""
        
        # 测试获取策略列表
        response = client.get("/api/v1/strategies")
        assert response.status_code == 200
        
        strategies = response.json()
        assert isinstance(strategies, list)
        
        # 测试创建策略
        strategy_config = {
            "name": "test_strategy",
            "type": "momentum",
            "parameters": {
                "short_window": 10,
                "long_window": 30
            },
            "description": "测试策略"
        }
        
        response = client.post("/api/v1/strategies", json=strategy_config)
        assert response.status_code == 201
        
        created_strategy = response.json()
        assert created_strategy["name"] == strategy_config["name"]
        assert created_strategy["type"] == strategy_config["type"]
        
        # 测试获取单个策略
        strategy_id = created_strategy["id"]
        response = client.get(f"/api/v1/strategies/{strategy_id}")
        assert response.status_code == 200
        
        # 测试更新策略
        updated_config = strategy_config.copy()
        updated_config["parameters"]["short_window"] = 15
        
        response = client.put(f"/api/v1/strategies/{strategy_id}", json=updated_config)
        assert response.status_code == 200
        
        # 测试删除策略
        response = client.delete(f"/api/v1/strategies/{strategy_id}")
        assert response.status_code == 204
    
    def test_backtest_endpoints(self, client, sample_data):
        """测试回测相关端点"""
        
        with patch.object(DataSourceManager, 'get_historical_data') as mock_data:
            mock_data.return_value = sample_data
            
            with patch.object(BacktestEngine, 'run_backtest') as mock_backtest:
                # Mock回测结果
                mock_results = {
                    "portfolio_value": sample_data['close'].tolist(),
                    "trades": [],
                    "performance_metrics": {
                        "total_return": 0.15,
                        "sharpe_ratio": 1.2,
                        "max_drawdown": 0.08,
                        "win_rate": 0.6
                    }
                }
                mock_backtest.return_value = mock_results
                
                # 测试创建回测任务
                backtest_config = {
                    "strategy_id": "test_strategy_id",
                    "symbols": ["AAPL", "GOOGL"],
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31",
                    "initial_capital": 100000,
                    "commission": 0.001
                }
                
                response = client.post("/api/v1/backtests", json=backtest_config)
                assert response.status_code == 202  # Accepted
                
                backtest_task = response.json()
                assert "task_id" in backtest_task
                assert backtest_task["status"] == "pending"
                
                # 测试获取回测状态
                task_id = backtest_task["task_id"]
                response = client.get(f"/api/v1/backtests/{task_id}")
                assert response.status_code == 200
                
                # 测试获取回测结果
                response = client.get(f"/api/v1/backtests/{task_id}/results")
                assert response.status_code == 200
                
                results = response.json()
                assert "performance_metrics" in results
                assert "portfolio_value" in results
    
    def test_portfolio_endpoints(self, client):
        """测试投资组合端点"""
        
        # 测试创建投资组合
        portfolio_config = {
            "name": "test_portfolio",
            "initial_capital": 100000,
            "description": "测试投资组合"
        }
        
        response = client.post("/api/v1/portfolios", json=portfolio_config)
        assert response.status_code == 201
        
        portfolio = response.json()
        portfolio_id = portfolio["id"]
        
        # 测试获取投资组合列表
        response = client.get("/api/v1/portfolios")
        assert response.status_code == 200
        
        portfolios = response.json()
        assert isinstance(portfolios, list)
        assert len(portfolios) > 0
        
        # 测试获取投资组合详情
        response = client.get(f"/api/v1/portfolios/{portfolio_id}")
        assert response.status_code == 200
        
        # 测试添加持仓
        position_data = {
            "symbol": "AAPL",
            "quantity": 100,
            "price": 150.0
        }
        
        response = client.post(f"/api/v1/portfolios/{portfolio_id}/positions", json=position_data)
        assert response.status_code == 201
        
        # 测试获取持仓列表
        response = client.get(f"/api/v1/portfolios/{portfolio_id}/positions")
        assert response.status_code == 200
        
        positions = response.json()
        assert isinstance(positions, list)
    
    def test_risk_management_endpoints(self, client):
        """测试风险管理端点"""
        
        # 测试风险指标计算
        risk_request = {
            "portfolio_id": "test_portfolio_id",
            "confidence_level": 0.95,
            "time_horizon": 1
        }
        
        with patch('src.risk.risk_management.RiskManager.calculate_var') as mock_var:
            mock_var.return_value = 0.05
            
            response = client.post("/api/v1/risk/var", json=risk_request)
            assert response.status_code == 200
            
            risk_data = response.json()
            assert "var" in risk_data
            assert "confidence_level" in risk_data
        
        # 测试风险限额检查
        limit_check_request = {
            "portfolio_id": "test_portfolio_id",
            "proposed_trade": {
                "symbol": "AAPL",
                "quantity": 1000,
                "side": "buy"
            }
        }
        
        response = client.post("/api/v1/risk/check-limits", json=limit_check_request)
        assert response.status_code == 200
        
        check_result = response.json()
        assert "approved" in check_result
        assert "risk_metrics" in check_result
    
    def test_performance_analytics_endpoints(self, client, sample_data):
        """测试性能分析端点"""
        
        # 测试性能指标计算
        performance_request = {
            "portfolio_id": "test_portfolio_id",
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "benchmark": "SPY"
        }
        
        with patch('src.performance.performance_analyzer.PerformanceAnalyzer.calculate_metrics') as mock_metrics:
            mock_metrics.return_value = {
                "total_return": 0.15,
                "annual_return": 0.12,
                "volatility": 0.18,
                "sharpe_ratio": 1.2,
                "max_drawdown": 0.08,
                "beta": 1.1,
                "alpha": 0.02
            }
            
            response = client.post("/api/v1/analytics/performance", json=performance_request)
            assert response.status_code == 200
            
            metrics = response.json()
            assert "total_return" in metrics
            assert "sharpe_ratio" in metrics
            assert "max_drawdown" in metrics
        
        # 测试归因分析
        attribution_request = {
            "portfolio_id": "test_portfolio_id",
            "start_date": "2023-01-01",
            "end_date": "2023-12-31"
        }
        
        response = client.post("/api/v1/analytics/attribution", json=attribution_request)
        assert response.status_code == 200
        
        attribution = response.json()
        assert "sector_attribution" in attribution
        assert "security_attribution" in attribution
    
    def test_api_rate_limiting(self, client):
        """测试API限流"""
        
        # 快速发送多个请求
        responses = []
        for i in range(100):
            response = client.get("/health")
            responses.append(response)
        
        # 检查是否有限流响应
        status_codes = [r.status_code for r in responses]
        
        # 大部分请求应该成功
        success_count = sum(1 for code in status_codes if code == 200)
        assert success_count >= 90  # 至少90%成功
        
        # 可能有一些限流响应
        rate_limited_count = sum(1 for code in status_codes if code == 429)
        assert rate_limited_count <= 10  # 限流响应不超过10%
    
    def test_api_error_handling(self, client):
        """测试API错误处理"""
        
        # 测试无效的策略ID
        response = client.get("/api/v1/strategies/invalid_id")
        assert response.status_code == 404
        
        error_data = response.json()
        assert "detail" in error_data
        
        # 测试无效的请求数据
        invalid_strategy = {
            "name": "",  # 空名称
            "type": "invalid_type"  # 无效类型
        }
        
        response = client.post("/api/v1/strategies", json=invalid_strategy)
        assert response.status_code == 422  # Validation error
        
        # 测试服务器错误
        with patch.object(DataSourceManager, 'get_historical_data') as mock_data:
            mock_data.side_effect = Exception("数据源错误")
            
            response = client.get(
                "/api/v1/market-data/historical",
                params={"symbol": "AAPL", "start_date": "2023-01-01", "end_date": "2023-12-31"}
            )
            assert response.status_code == 500
    
    def test_api_performance_benchmarks(self, client, sample_data):
        """测试API性能基准"""
        
        with patch.object(DataSourceManager, 'get_historical_data') as mock_data:
            mock_data.return_value = sample_data
            
            # 测试响应时间
            start_time = time.time()
            
            response = client.get(
                "/api/v1/market-data/historical",
                params={
                    "symbol": "AAPL",
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31"
                }
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            assert response.status_code == 200
            assert response_time < 2.0  # 响应时间应小于2秒
        
        # 测试并发请求性能
        import concurrent.futures
        
        def make_request():
            return client.get("/health")
        
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(50)]
            responses = [future.result() for future in futures]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 验证所有请求成功
        assert all(r.status_code == 200 for r in responses)
        
        # 并发性能应该合理
        assert total_time < 10.0  # 50个并发请求应在10秒内完成
        
        print(f"API性能测试:")
        print(f"单个请求响应时间: {response_time:.3f}秒")
        print(f"50个并发请求总时间: {total_time:.3f}秒")
        print(f"平均每个请求时间: {total_time/50:.3f}秒")
    
    def test_websocket_endpoints(self, client):
        """测试WebSocket端点"""
        
        # 测试实时数据推送
        with client.websocket_connect("/ws/market-data") as websocket:
            # 订阅数据
            subscribe_message = {
                "action": "subscribe",
                "symbols": ["AAPL", "GOOGL"]
            }
            websocket.send_json(subscribe_message)
            
            # 接收确认消息
            response = websocket.receive_json()
            assert response["status"] == "subscribed"
            
            # 模拟数据推送
            with patch('src.api.websocket.market_data_stream') as mock_stream:
                mock_data = {
                    "symbol": "AAPL",
                    "price": 150.0,
                    "timestamp": datetime.now().isoformat()
                }
                
                # 这里应该接收到推送的数据
                # 实际实现中需要配置数据推送机制
                
        # 测试策略信号推送
        with client.websocket_connect("/ws/signals") as websocket:
            subscribe_message = {
                "action": "subscribe",
                "strategy_id": "test_strategy"
            }
            websocket.send_json(subscribe_message)
            
            response = websocket.receive_json()
            assert response["status"] == "subscribed"