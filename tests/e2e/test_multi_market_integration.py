"""
多数据源集成测试
测试美股、A股、加密货币、经济数据的集成
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import asyncio

from src.data.data_source_manager import DataSourceManager
from src.data.adapters.yahoo_finance_adapter import YahooFinanceAdapter
from src.data.adapters.tushare_adapter import TushareAdapter
from src.data.adapters.binance_adapter import BinanceAdapter
from src.data.adapters.fred_adapter import FredAdapter
from src.strategies.multi_market_strategy import MultiMarketStrategy
from src.backtest.backtest_engine import BacktestEngine


class TestMultiMarketIntegration:
    """多市场数据源集成测试"""
    
    @pytest.fixture
    def mock_us_stock_data(self):
        """模拟美股数据"""
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        return pd.DataFrame({
            'open': np.random.randn(len(dates)).cumsum() + 150,
            'high': np.random.randn(len(dates)).cumsum() + 155,
            'low': np.random.randn(len(dates)).cumsum() + 145,
            'close': np.random.randn(len(dates)).cumsum() + 150,
            'volume': np.random.randint(1000000, 10000000, len(dates))
        }, index=dates)
    
    @pytest.fixture
    def mock_china_stock_data(self):
        """模拟A股数据"""
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        # 过滤掉周末
        dates = dates[dates.dayofweek < 5]
        return pd.DataFrame({
            'open': np.random.randn(len(dates)).cumsum() + 20,
            'high': np.random.randn(len(dates)).cumsum() + 22,
            'low': np.random.randn(len(dates)).cumsum() + 18,
            'close': np.random.randn(len(dates)).cumsum() + 20,
            'volume': np.random.randint(100000, 1000000, len(dates))
        }, index=dates)
    
    @pytest.fixture
    def mock_crypto_data(self):
        """模拟加密货币数据"""
        # 加密货币24/7交易
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='H')
        return pd.DataFrame({
            'open': np.random.randn(len(dates)).cumsum() + 30000,
            'high': np.random.randn(len(dates)).cumsum() + 31000,
            'low': np.random.randn(len(dates)).cumsum() + 29000,
            'close': np.random.randn(len(dates)).cumsum() + 30000,
            'volume': np.random.randint(100, 10000, len(dates))
        }, index=dates)
    
    @pytest.fixture
    def mock_economic_data(self):
        """模拟经济数据"""
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='M')
        return pd.DataFrame({
            'gdp_growth': np.random.randn(len(dates)) * 0.5 + 2.5,
            'inflation_rate': np.random.randn(len(dates)) * 0.3 + 3.0,
            'unemployment_rate': np.random.randn(len(dates)) * 0.2 + 4.0,
            'interest_rate': np.random.randn(len(dates)) * 0.1 + 2.0
        }, index=dates)
    
    def test_data_source_manager_initialization(self):
        """测试数据源管理器初始化"""
        manager = DataSourceManager()
        
        # 验证所有数据源适配器已注册
        assert 'yahoo_finance' in manager.adapters
        assert 'tushare' in manager.adapters
        assert 'binance' in manager.adapters
        assert 'fred' in manager.adapters
        
        # 验证适配器类型
        assert isinstance(manager.adapters['yahoo_finance'], YahooFinanceAdapter)
        assert isinstance(manager.adapters['tushare'], TushareAdapter)
        assert isinstance(manager.adapters['binance'], BinanceAdapter)
        assert isinstance(manager.adapters['fred'], FredAdapter)
    
    def test_us_stock_data_integration(self, mock_us_stock_data):
        """测试美股数据集成"""
        manager = DataSourceManager()
        
        with patch.object(YahooFinanceAdapter, 'get_historical_data') as mock_yahoo:
            mock_yahoo.return_value = mock_us_stock_data
            
            # 获取美股数据
            data = manager.get_historical_data(
                symbol='AAPL',
                start_date='2023-01-01',
                end_date='2023-12-31',
                source='yahoo_finance'
            )
            
            assert data is not None
            assert len(data) > 0
            assert all(col in data.columns for col in ['open', 'high', 'low', 'close', 'volume'])
            
            # 验证数据质量
            assert not data.isnull().any().any()
            assert (data['high'] >= data['low']).all()
            assert (data['high'] >= data['open']).all()
            assert (data['high'] >= data['close']).all()
    
    def test_china_stock_data_integration(self, mock_china_stock_data):
        """测试A股数据集成"""
        manager = DataSourceManager()
        
        with patch.object(TushareAdapter, 'get_historical_data') as mock_tushare:
            mock_tushare.return_value = mock_china_stock_data
            
            # 获取A股数据
            data = manager.get_historical_data(
                symbol='000001.SZ',
                start_date='2023-01-01',
                end_date='2023-12-31',
                source='tushare'
            )
            
            assert data is not None
            assert len(data) > 0
            
            # 验证A股特有的数据格式
            assert all(col in data.columns for col in ['open', 'high', 'low', 'close', 'volume'])
            
            # 验证交易日历（A股不包含周末）
            weekdays = data.index.dayofweek
            assert all(day < 5 for day in weekdays)  # 0-4 代表周一到周五
    
    def test_crypto_data_integration(self, mock_crypto_data):
        """测试加密货币数据集成"""
        manager = DataSourceManager()
        
        with patch.object(BinanceAdapter, 'get_historical_data') as mock_binance:
            mock_binance.return_value = mock_crypto_data
            
            # 获取加密货币数据
            data = manager.get_historical_data(
                symbol='BTCUSDT',
                start_date='2023-01-01',
                end_date='2023-12-31',
                source='binance'
            )
            
            assert data is not None
            assert len(data) > 0
            
            # 验证24/7交易特征
            unique_hours = data.index.hour.unique()
            assert len(unique_hours) > 20  # 应该包含大部分小时
            
            # 验证数据连续性（加密货币市场不休市）
            time_diff = data.index.to_series().diff().dropna()
            expected_freq = pd.Timedelta(hours=1)
            assert (time_diff == expected_freq).sum() > len(time_diff) * 0.9  # 90%以上应该是连续的
    
    def test_economic_data_integration(self, mock_economic_data):
        """测试经济数据集成"""
        manager = DataSourceManager()
        
        with patch.object(FredAdapter, 'get_economic_data') as mock_fred:
            mock_fred.return_value = mock_economic_data
            
            # 获取经济数据
            data = manager.get_economic_data(
                indicators=['GDP', 'CPI', 'UNEMPLOYMENT', 'INTEREST_RATE'],
                start_date='2023-01-01',
                end_date='2023-12-31'
            )
            
            assert data is not None
            assert len(data) > 0
            
            # 验证经济指标
            expected_columns = ['gdp_growth', 'inflation_rate', 'unemployment_rate', 'interest_rate']
            assert all(col in data.columns for col in expected_columns)
            
            # 验证月度频率
            assert data.index.freq == 'M' or len(data) <= 12  # 月度数据
    
    def test_multi_source_data_synchronization(self, mock_us_stock_data, mock_china_stock_data, mock_crypto_data):
        """测试多数据源同步"""
        manager = DataSourceManager()
        
        # Mock所有数据源
        with patch.object(YahooFinanceAdapter, 'get_historical_data') as mock_yahoo, \
             patch.object(TushareAdapter, 'get_historical_data') as mock_tushare, \
             patch.object(BinanceAdapter, 'get_historical_data') as mock_binance:
            
            mock_yahoo.return_value = mock_us_stock_data
            mock_tushare.return_value = mock_china_stock_data
            mock_binance.return_value = mock_crypto_data
            
            # 同时获取多个市场数据
            symbols_sources = [
                ('AAPL', 'yahoo_finance'),
                ('000001.SZ', 'tushare'),
                ('BTCUSDT', 'binance')
            ]
            
            all_data = {}
            for symbol, source in symbols_sources:
                data = manager.get_historical_data(
                    symbol=symbol,
                    start_date='2023-01-01',
                    end_date='2023-12-31',
                    source=source
                )
                all_data[symbol] = data
            
            # 验证所有数据都获取成功
            assert len(all_data) == 3
            assert all(data is not None for data in all_data.values())
            
            # 验证数据时间范围
            for symbol, data in all_data.items():
                assert data.index.min().year == 2023
                assert data.index.max().year == 2023
    
    def test_cross_market_strategy_integration(self, mock_us_stock_data, mock_china_stock_data, mock_economic_data):
        """测试跨市场策略集成"""
        
        class CrossMarketStrategy(MultiMarketStrategy):
            def generate_signals(self, market_data, economic_data=None):
                signals = {}
                
                for symbol, data in market_data.items():
                    signal_df = pd.DataFrame(index=data.index)
                    signal_df['signal'] = 0
                    
                    # 基于经济数据调整信号
                    if economic_data is not None:
                        # 当GDP增长率高时，增加股票权重
                        latest_gdp = economic_data['gdp_growth'].iloc[-1]
                        if latest_gdp > 3.0:
                            multiplier = 1.2
                        else:
                            multiplier = 0.8
                    else:
                        multiplier = 1.0
                    
                    # 简单移动平均策略
                    short_ma = data['close'].rolling(window=10).mean()
                    long_ma = data['close'].rolling(window=30).mean()
                    
                    signal_df.loc[short_ma > long_ma, 'signal'] = 1 * multiplier
                    signal_df.loc[short_ma < long_ma, 'signal'] = -1 * multiplier
                    
                    signals[symbol] = signal_df
                
                return signals
        
        strategy = CrossMarketStrategy()
        manager = DataSourceManager()
        
        # Mock数据源
        with patch.object(YahooFinanceAdapter, 'get_historical_data') as mock_yahoo, \
             patch.object(TushareAdapter, 'get_historical_data') as mock_tushare, \
             patch.object(FredAdapter, 'get_economic_data') as mock_fred:
            
            mock_yahoo.return_value = mock_us_stock_data
            mock_tushare.return_value = mock_china_stock_data
            mock_fred.return_value = mock_economic_data
            
            # 准备市场数据
            market_data = {
                'AAPL': manager.get_historical_data('AAPL', '2023-01-01', '2023-12-31', 'yahoo_finance'),
                '000001.SZ': manager.get_historical_data('000001.SZ', '2023-01-01', '2023-12-31', 'tushare')
            }
            
            # 获取经济数据
            economic_data = manager.get_economic_data(
                ['GDP', 'CPI'],
                '2023-01-01',
                '2023-12-31'
            )
            
            # 生成跨市场信号
            signals = strategy.generate_signals(market_data, economic_data)
            
            # 验证信号生成
            assert len(signals) == 2
            assert 'AAPL' in signals
            assert '000001.SZ' in signals
            
            for symbol, signal_df in signals.items():
                assert isinstance(signal_df, pd.DataFrame)
                assert 'signal' in signal_df.columns
                assert len(signal_df) > 0
    
    def test_data_quality_validation(self, mock_us_stock_data, mock_china_stock_data, mock_crypto_data):
        """测试数据质量验证"""
        manager = DataSourceManager()
        
        # 创建包含异常值的数据
        corrupted_data = mock_us_stock_data.copy()
        corrupted_data.iloc[10, corrupted_data.columns.get_loc('high')] = -100  # 异常高价
        corrupted_data.iloc[20, corrupted_data.columns.get_loc('volume')] = -1000  # 异常成交量
        
        with patch.object(YahooFinanceAdapter, 'get_historical_data') as mock_yahoo:
            mock_yahoo.return_value = corrupted_data
            
            # 获取数据并验证
            data = manager.get_historical_data('AAPL', '2023-01-01', '2023-12-31', 'yahoo_finance')
            
            # 数据质量检查
            quality_report = manager.validate_data_quality(data)
            
            assert 'missing_values' in quality_report
            assert 'outliers' in quality_report
            assert 'data_consistency' in quality_report
            
            # 应该检测到异常值
            assert quality_report['outliers']['count'] > 0
            assert not quality_report['data_consistency']['price_logic']  # 价格逻辑不一致
    
    def test_real_time_data_streaming(self):
        """测试实时数据流"""
        manager = DataSourceManager()
        
        # Mock实时数据流
        mock_stream_data = [
            {'symbol': 'AAPL', 'price': 150.0, 'volume': 1000, 'timestamp': datetime.now()},
            {'symbol': 'BTCUSDT', 'price': 30000.0, 'volume': 0.5, 'timestamp': datetime.now()},
        ]
        
        with patch.object(manager, 'start_real_time_stream') as mock_stream:
            mock_stream.return_value = iter(mock_stream_data)
            
            # 启动实时数据流
            stream = manager.start_real_time_stream(['AAPL', 'BTCUSDT'])
            
            # 验证数据流
            received_data = list(stream)
            assert len(received_data) == 2
            
            for data_point in received_data:
                assert 'symbol' in data_point
                assert 'price' in data_point
                assert 'volume' in data_point
                assert 'timestamp' in data_point
    
    def test_data_source_failover(self, mock_us_stock_data):
        """测试数据源故障切换"""
        manager = DataSourceManager()
        
        # 配置备用数据源
        manager.configure_failover({
            'primary': 'yahoo_finance',
            'backup': ['alpha_vantage', 'quandl']
        })
        
        with patch.object(YahooFinanceAdapter, 'get_historical_data') as mock_yahoo, \
             patch.object(manager, '_get_backup_data') as mock_backup:
            
            # 主数据源失败
            mock_yahoo.side_effect = Exception("Yahoo Finance API错误")
            mock_backup.return_value = mock_us_stock_data
            
            # 获取数据应该自动切换到备用源
            data = manager.get_historical_data_with_failover(
                symbol='AAPL',
                start_date='2023-01-01',
                end_date='2023-12-31'
            )
            
            assert data is not None
            assert len(data) > 0
            
            # 验证使用了备用数据源
            mock_backup.assert_called_once()
    
    def test_multi_market_backtest_integration(self, mock_us_stock_data, mock_china_stock_data, mock_crypto_data):
        """测试多市场回测集成"""
        
        class GlobalStrategy(MultiMarketStrategy):
            def generate_signals(self, market_data, economic_data=None):
                signals = {}
                
                for symbol, data in market_data.items():
                    signal_df = pd.DataFrame(index=data.index)
                    signal_df['signal'] = 0
                    
                    # 不同市场使用不同策略
                    if 'USDT' in symbol:  # 加密货币
                        # 高频策略
                        returns = data['close'].pct_change()
                        signal_df.loc[returns > 0.02, 'signal'] = 1
                        signal_df.loc[returns < -0.02, 'signal'] = -1
                    elif '.SZ' in symbol or '.SH' in symbol:  # A股
                        # 均值回归策略
                        ma = data['close'].rolling(window=20).mean()
                        std = data['close'].rolling(window=20).std()
                        z_score = (data['close'] - ma) / std
                        signal_df.loc[z_score > 2, 'signal'] = -1
                        signal_df.loc[z_score < -2, 'signal'] = 1
                    else:  # 美股
                        # 动量策略
                        momentum = data['close'].rolling(window=10).mean() / data['close'].rolling(window=30).mean() - 1
                        signal_df.loc[momentum > 0.05, 'signal'] = 1
                        signal_df.loc[momentum < -0.05, 'signal'] = -1
                    
                    signals[symbol] = signal_df
                
                return signals
        
        strategy = GlobalStrategy()
        backtest_engine = BacktestEngine(initial_capital=1000000)
        
        # Mock数据源
        with patch.object(DataSourceManager, 'get_historical_data') as mock_data:
            def get_data_side_effect(symbol, start_date, end_date, source=None):
                if 'USDT' in symbol:
                    return mock_crypto_data
                elif '.SZ' in symbol:
                    return mock_china_stock_data
                else:
                    return mock_us_stock_data
            
            mock_data.side_effect = get_data_side_effect
            
            # 运行多市场回测
            results = backtest_engine.run_multi_market_backtest(
                strategy=strategy,
                symbols=['AAPL', '000001.SZ', 'BTCUSDT'],
                start_date='2023-01-01',
                end_date='2023-12-31'
            )
            
            # 验证回测结果
            assert results is not None
            assert 'portfolio_value' in results
            assert 'market_performance' in results
            assert 'currency_effects' in results
            
            # 验证多市场表现
            market_perf = results['market_performance']
            assert 'US' in market_perf
            assert 'CN' in market_perf
            assert 'CRYPTO' in market_perf