"""
端到端回测流程集成测试
测试完整的回测流程，从数据获取到结果分析
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import asyncio
from unittest.mock import Mock, patch, MagicMock


class TestCompleteBacktestFlow:
    """完整回测流程测试"""
    
    @pytest.fixture
    def setup_test_environment(self):
        """设置测试环境"""
        # 创建测试用的策略
        class TestStrategy:
            def __init__(self, name="test_strategy"):
                self.name = name
                
            def generate_signals(self, data):
                """简单的移动平均策略"""
                signals = pd.DataFrame(index=data.index)
                signals['signal'] = 0
                
                # 计算移动平均
                short_ma = data['close'].rolling(window=5).mean()
                long_ma = data['close'].rolling(window=20).mean()
                
                # 生成信号
                signals.loc[short_ma > long_ma, 'signal'] = 1
                signals.loc[short_ma < long_ma, 'signal'] = -1
                
                return signals
        
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        test_data = pd.DataFrame({
            'open': np.random.randn(len(dates)).cumsum() + 100,
            'high': np.random.randn(len(dates)).cumsum() + 105,
            'low': np.random.randn(len(dates)).cumsum() + 95,
            'close': np.random.randn(len(dates)).cumsum() + 100,
            'volume': np.random.randint(1000, 10000, len(dates))
        }, index=dates)
        
        return {
            'strategy': TestStrategy(),
            'test_data': test_data,
            'symbols': ['AAPL', 'GOOGL', 'MSFT']
        }
    
    def test_complete_backtest_workflow(self, setup_test_environment):
        """测试完整的回测工作流程"""
        strategy = setup_test_environment['strategy']
        test_data = setup_test_environment['test_data']
        symbols = setup_test_environment['symbols']
        
        # 1. 模拟回测引擎
        mock_backtest_engine = MagicMock()
        mock_backtest_engine.initial_capital = 100000
        mock_backtest_engine.commission = 0.001
        mock_backtest_engine.slippage = 0.0005
        
        # 2. 模拟回测结果
        mock_results = {
            'portfolio_value': test_data['close'] * 1000,  # 模拟投资组合价值
            'trades': pd.DataFrame({
                'symbol': ['AAPL', 'GOOGL', 'MSFT'],
                'side': ['buy', 'sell', 'buy'],
                'quantity': [100, 50, 75],
                'price': [150.0, 2500.0, 300.0],
                'timestamp': pd.date_range('2023-01-01', periods=3, freq='D')
            }),
            'performance_metrics': {
                'total_return': 0.15,
                'sharpe_ratio': 1.2,
                'max_drawdown': 0.08,
                'win_rate': 0.6
            }
        }
        
        mock_backtest_engine.run_backtest.return_value = mock_results
        
        # 3. 运行回测
        results = mock_backtest_engine.run_backtest(
            strategy=strategy,
            symbols=symbols,
            start_date='2023-01-01',
            end_date='2023-12-31'
        )
        
        # 4. 验证回测结果
        assert results is not None
        assert 'portfolio_value' in results
        assert 'trades' in results
        assert 'performance_metrics' in results
        
        # 验证性能指标
        metrics = results['performance_metrics']
        assert 'total_return' in metrics
        assert 'sharpe_ratio' in metrics
        assert 'max_drawdown' in metrics
        assert 'win_rate' in metrics
        
        # 验证交易记录
        trades = results['trades']
        assert isinstance(trades, pd.DataFrame)
        if not trades.empty:
            required_columns = ['symbol', 'side', 'quantity', 'price', 'timestamp']
            assert all(col in trades.columns for col in required_columns)
    
    def test_backtest_with_risk_management(self, setup_test_environment):
        """测试带风险管理的回测流程"""
        strategy = setup_test_environment['strategy']
        test_data = setup_test_environment['test_data']
        symbols = setup_test_environment['symbols']
        
        # 模拟风险管理器
        mock_risk_manager = MagicMock()
        mock_risk_manager.max_position_size = 0.1
        mock_risk_manager.max_portfolio_risk = 0.02
        mock_risk_manager.stop_loss_pct = 0.05
        
        # 模拟回测引擎
        mock_backtest_engine = MagicMock()
        mock_backtest_engine.initial_capital = 100000
        mock_backtest_engine.risk_manager = mock_risk_manager
        
        # 模拟带风险控制的回测结果
        mock_results = {
            'portfolio_value': test_data['close'] * 1000,
            'trades': pd.DataFrame(),
            'performance_metrics': {
                'total_return': 0.12,
                'sharpe_ratio': 1.1,
                'max_drawdown': 0.06,  # 风险控制后的较小回撤
                'win_rate': 0.65
            }
        }
        
        mock_backtest_engine.run_backtest.return_value = mock_results
        
        results = mock_backtest_engine.run_backtest(
            strategy=strategy,
            symbols=symbols,
            start_date='2023-01-01',
            end_date='2023-12-31'
        )
        
        # 验证风险控制生效
        assert results is not None
        
        # 检查最大回撤是否在合理范围内
        max_drawdown = results['performance_metrics']['max_drawdown']
        assert max_drawdown <= 0.15  # 最大回撤不超过15%
    
    def test_backtest_performance_analysis(self, setup_test_environment):
        """测试回测性能分析功能"""
        strategy = setup_test_environment['strategy']
        test_data = setup_test_environment['test_data']
        symbols = setup_test_environment['symbols']
        
        # 模拟回测结果
        mock_results = {
            'portfolio_value': test_data['close'] * 1000,
            'trades': pd.DataFrame({
                'symbol': ['AAPL'] * 10,
                'side': ['buy', 'sell'] * 5,
                'quantity': [100] * 10,
                'price': np.random.uniform(140, 160, 10),
                'timestamp': pd.date_range('2023-01-01', periods=10, freq='D')
            }),
            'performance_metrics': {
                'total_return': 0.15,
                'sharpe_ratio': 1.2,
                'max_drawdown': 0.08,
                'win_rate': 0.6
            }
        }
        
        # 模拟性能分析器
        mock_analyzer = MagicMock()
        
        # 模拟分析结果
        mock_analysis = {
            'risk_metrics': {
                'var_95': 0.025,
                'cvar_95': 0.035,
                'beta': 1.1
            },
            'return_metrics': {
                'annual_return': 0.15,
                'volatility': 0.18,
                'information_ratio': 0.8
            },
            'trade_analysis': {
                'total_trades': 10,
                'winning_trades': 6,
                'avg_trade_return': 0.015
            }
        }
        
        mock_analyzer.analyze_performance.return_value = mock_analysis
        
        # 分析结果
        analysis = mock_analyzer.analyze_performance(
            portfolio_values=mock_results['portfolio_value'],
            trades=mock_results['trades'],
            benchmark_data=test_data['close']
        )
        
        # 验证分析结果
        assert 'risk_metrics' in analysis
        assert 'return_metrics' in analysis
        assert 'trade_analysis' in analysis
        
        # 验证具体指标
        risk_metrics = analysis['risk_metrics']
        assert 'var_95' in risk_metrics
        assert 'cvar_95' in risk_metrics
        assert 'beta' in risk_metrics
        
        return_metrics = analysis['return_metrics']
        assert 'annual_return' in return_metrics
        assert 'volatility' in return_metrics
        assert 'information_ratio' in return_metrics
    
    def test_backtest_error_handling(self, setup_test_environment):
        """测试回测过程中的错误处理"""
        strategy = setup_test_environment['strategy']
        symbols = setup_test_environment['symbols']
        
        # 模拟回测引擎
        mock_backtest_engine = MagicMock()
        
        # 模拟数据获取失败
        mock_backtest_engine.run_backtest.side_effect = Exception("数据获取失败")
        
        with pytest.raises(Exception, match="数据获取失败"):
            mock_backtest_engine.run_backtest(
                strategy=strategy,
                symbols=symbols,
                start_date='2023-01-01',
                end_date='2023-12-31'
            )
    
    def test_backtest_with_custom_parameters(self, setup_test_environment):
        """测试自定义参数的回测"""
        strategy = setup_test_environment['strategy']
        test_data = setup_test_environment['test_data']
        symbols = setup_test_environment['symbols']
        
        # 模拟自定义参数的回测引擎
        mock_backtest_engine = MagicMock()
        mock_backtest_engine.initial_capital = 50000
        mock_backtest_engine.commission = 0.002
        mock_backtest_engine.slippage = 0.001
        mock_backtest_engine.margin_rate = 0.5
        
        # 模拟回测结果，反映自定义参数
        portfolio_values = pd.Series(
            data=np.linspace(50000, 55000, len(test_data)),
            index=test_data.index
        )
        
        mock_results = {
            'portfolio_value': portfolio_values,
            'trades': pd.DataFrame(),
            'performance_metrics': {
                'total_return': 0.10,
                'sharpe_ratio': 1.0,
                'max_drawdown': 0.05,
                'win_rate': 0.55
            }
        }
        
        mock_backtest_engine.run_backtest.return_value = mock_results
        
        results = mock_backtest_engine.run_backtest(
            strategy=strategy,
            symbols=symbols,
            start_date='2023-01-01',
            end_date='2023-12-31'
        )
        
        # 验证自定义参数生效
        assert results is not None
        
        # 验证初始资金
        initial_value = results['portfolio_value'].iloc[0]
        assert abs(initial_value - 50000) < 1000  # 允许小幅偏差