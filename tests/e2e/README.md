# 端到端测试套件

本目录包含量化交易系统的完整端到端测试套件，涵盖了系统的所有主要功能和集成场景。

## 测试模块概述

### 1. 完整回测流程测试 (`test_complete_backtest_flow.py`)
- 测试完整的回测工作流程
- 验证数据获取、策略执行、风险管理和性能分析
- 包含错误处理和自定义参数测试

### 2. 多策略组合测试 (`test_multi_strategy_portfolio.py`)
- 测试多个策略同时运行的组合效果
- 验证策略权重优化和动态调整
- 包含策略相关性分析

### 3. 性能和负载测试 (`test_performance_load_testing.py`)
- 大数据量处理性能测试
- 高频数据处理测试
- 并发执行性能测试
- 内存泄漏检测

### 4. API接口自动化测试 (`test_api_automation.py`)
- 所有REST API端点测试
- WebSocket实时数据测试
- API性能基准测试
- 错误处理和限流测试

### 5. 多市场数据集成测试 (`test_multi_market_integration.py`)
- 美股、A股、加密货币数据集成
- 经济数据集成测试
- 数据质量验证
- 实时数据流测试

### 6. 跨市场策略测试 (`test_cross_market_strategies.py`)
- 时区套利策略测试
- 货币对冲策略测试
- 跨资产动量策略测试
- 波动率制度策略测试

### 7. 数据源故障切换测试 (`test_data_source_failover.py`)
- 主数据源故障检测
- 自动切换到备用数据源
- 重试机制和指数退避
- 并发故障处理

## 运行测试

### 基本用法

```bash
# 运行所有端到端测试
python tests/e2e/run_e2e_tests.py

# 运行特定类型的测试
python tests/e2e/run_e2e_tests.py --type backtest

# 详细输出模式
python tests/e2e/run_e2e_tests.py --verbose

# 快速模式（跳过性能测试）
python tests/e2e/run_e2e_tests.py --quick
```

### 可用的测试类型

- `all`: 运行所有测试（默认）
- `backtest`: 回测流程测试
- `multi_strategy`: 多策略组合测试
- `performance`: 性能和负载测试
- `api`: API接口测试
- `multi_market`: 多市场集成测试
- `cross_market`: 跨市场策略测试
- `failover`: 数据源故障切换测试

### 使用pytest直接运行

```bash
# 运行单个测试文件
pytest tests/e2e/test_complete_backtest_flow.py -v

# 运行所有端到端测试
pytest tests/e2e/ -v

# 运行特定测试并生成覆盖率报告
pytest tests/e2e/test_api_automation.py --cov=src --cov-report=html

# 运行性能测试（标记为slow）
pytest tests/e2e/ -m "slow" -v

# 跳过慢速测试
pytest tests/e2e/ -m "not slow" -v
```

## 测试配置

### 环境变量

- `SKIP_PERFORMANCE_TESTS`: 设置为'1'跳过性能测试
- `TEST_DATA_SIZE`: 设置测试数据大小（small/medium/large）
- `API_BASE_URL`: API测试的基础URL
- `TEST_TIMEOUT`: 测试超时时间（秒）

### 配置文件

测试配置在 `conftest.py` 中定义，包括：

- 测试数据大小配置
- 性能阈值设置
- 市场符号列表
- 测试日期范围

## 测试数据

### 模拟数据生成

测试使用模拟数据生成器创建测试数据：

- **市场数据**: 基于符号生成一致的OHLCV数据
- **经济数据**: 生成各种经济指标的时间序列
- **汇率数据**: 生成主要货币对的汇率数据

### 数据特征

- 股票数据：基准价格100，波动率2%
- 加密货币：基准价格30000，波动率5%
- 外汇数据：基准价格1.0，波动率1%

## 性能基准

### 响应时间要求

- API响应时间：< 2秒
- 回测执行时间：< 5分钟（大数据集）
- 数据获取时间：< 1秒

### 内存使用要求

- 大数据集处理：< 2GB内存增长
- 高频数据处理：< 1GB内存增长
- 并发处理：合理的内存使用

### 成功率要求

- 整体测试成功率：> 95%
- API可用性：> 99%
- 数据源可靠性：> 95%

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ModuleNotFoundError: No module named 'src'
   ```
   确保从项目根目录运行测试，或设置PYTHONPATH。

2. **数据源连接失败**
   ```
   ConnectionError: 无法连接到数据源
   ```
   检查网络连接和API密钥配置。

3. **内存不足**
   ```
   MemoryError: 内存不足
   ```
   减少测试数据大小或增加系统内存。

4. **测试超时**
   ```
   TimeoutError: 测试执行超时
   ```
   增加超时时间或优化测试代码。

### 调试技巧

1. **使用详细模式**
   ```bash
   python tests/e2e/run_e2e_tests.py --verbose
   ```

2. **运行单个测试**
   ```bash
   pytest tests/e2e/test_specific.py::TestClass::test_method -v -s
   ```

3. **查看测试报告**
   测试完成后会生成JSON格式的详细报告。

4. **使用调试器**
   ```bash
   pytest tests/e2e/test_specific.py --pdb
   ```

## 持续集成

### GitHub Actions配置示例

```yaml
name: E2E Tests

on: [push, pull_request]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Run E2E tests
      run: |
        python tests/e2e/run_e2e_tests.py --quick
    
    - name: Upload test results
      uses: actions/upload-artifact@v2
      if: always()
      with:
        name: test-results
        path: e2e_test_report_*.json
```

## 贡献指南

### 添加新测试

1. 在相应的测试文件中添加测试方法
2. 使用适当的pytest标记（@pytest.mark.slow等）
3. 添加详细的文档字符串
4. 确保测试是独立的和可重复的

### 测试最佳实践

1. **独立性**: 每个测试应该独立运行
2. **可重复性**: 测试结果应该一致
3. **清晰性**: 测试意图应该明确
4. **效率性**: 避免不必要的重复操作
5. **覆盖性**: 覆盖正常和异常情况

### 代码风格

- 遵循PEP 8代码风格
- 使用有意义的测试和变量名
- 添加适当的注释和文档
- 使用类型提示（如果适用）

## 许可证

本测试套件遵循与主项目相同的许可证。