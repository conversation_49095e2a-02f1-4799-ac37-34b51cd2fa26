"""
Pydantic模型验证测试 - 独立测试
"""

import pytest
from datetime import datetime, timedelta
from pydantic import BaseModel, ValidationError, validator
from typing import List, Optional, Dict, Any


# 复制关键的Pydantic模型用于测试
class MarketDataQuery(BaseModel):
    """市场数据查询参数"""
    symbols: List[str]
    start_date: datetime
    end_date: Optional[datetime] = None
    interval: str = "1d"
    market: Optional[str] = None
    
    @validator('symbols')
    def validate_symbols(cls, v):
        if not v:
            raise ValueError("至少需要一个交易品种")
        if len(v) > 100:
            raise ValueError("单次查询最多支持100个品种")
        return v
    
    @validator('interval')
    def validate_interval(cls, v):
        valid_intervals = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M']
        if v not in valid_intervals:
            raise ValueError(f"无效的时间间隔，必须是: {', '.join(valid_intervals)}")
        return v


class EconomicDataQuery(BaseModel):
    """经济数据查询参数"""
    series_ids: List[str]
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    limit: int = 1000
    
    @validator('series_ids')
    def validate_series_ids(cls, v):
        if not v:
            raise ValueError("至少需要一个序列ID")
        if len(v) > 50:
            raise ValueError("单次查询最多支持50个序列")
        return v


class BacktestRequest(BaseModel):
    """回测请求参数"""
    strategy_id: str
    symbols: List[str]
    start_date: datetime
    end_date: datetime
    initial_capital: float = 100000.0
    parameters: Optional[Dict[str, Any]] = None
    benchmark: Optional[str] = "SPY"
    
    @validator('symbols')
    def validate_symbols(cls, v):
        if not v:
            raise ValueError("至少需要一个交易品种")
        if len(v) > 20:
            raise ValueError("单次回测最多支持20个品种")
        return v


class TestMarketDataQueryValidation:
    """市场数据查询参数验证测试"""
    
    def test_valid_market_data_query(self):
        """测试有效的市场数据查询"""
        query = MarketDataQuery(
            symbols=["AAPL", "GOOGL"],
            start_date=datetime.now() - timedelta(days=30),
            end_date=datetime.now(),
            interval="1d",
            market="US"
        )
        
        assert len(query.symbols) == 2
        assert query.symbols == ["AAPL", "GOOGL"]
        assert query.interval == "1d"
        assert query.market == "US"
    
    def test_empty_symbols_validation(self):
        """测试空符号列表验证"""
        with pytest.raises(ValidationError) as exc_info:
            MarketDataQuery(
                symbols=[],
                start_date=datetime.now() - timedelta(days=30),
                end_date=datetime.now()
            )
        
        assert "至少需要一个交易品种" in str(exc_info.value)
    
    def test_too_many_symbols_validation(self):
        """测试过多符号验证"""
        with pytest.raises(ValidationError) as exc_info:
            MarketDataQuery(
                symbols=[f"SYMBOL{i}" for i in range(101)],
                start_date=datetime.now() - timedelta(days=30),
                end_date=datetime.now()
            )
        
        assert "单次查询最多支持100个品种" in str(exc_info.value)
    
    def test_invalid_interval_validation(self):
        """测试无效时间间隔验证"""
        with pytest.raises(ValidationError) as exc_info:
            MarketDataQuery(
                symbols=["AAPL"],
                start_date=datetime.now() - timedelta(days=30),
                end_date=datetime.now(),
                interval="invalid_interval"
            )
        
        assert "无效的时间间隔" in str(exc_info.value)
    
    def test_valid_intervals(self):
        """测试所有有效的时间间隔"""
        valid_intervals = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M']
        
        for interval in valid_intervals:
            query = MarketDataQuery(
                symbols=["AAPL"],
                start_date=datetime.now() - timedelta(days=30),
                interval=interval
            )
            assert query.interval == interval
    
    def test_default_values(self):
        """测试默认值"""
        query = MarketDataQuery(
            symbols=["AAPL"],
            start_date=datetime.now() - timedelta(days=30)
        )
        
        assert query.interval == "1d"
        assert query.end_date is None
        assert query.market is None


class TestEconomicDataQueryValidation:
    """经济数据查询参数验证测试"""
    
    def test_valid_economic_data_query(self):
        """测试有效的经济数据查询"""
        query = EconomicDataQuery(
            series_ids=["GDP", "UNRATE", "CPIAUCSL"],
            start_date=datetime.now() - timedelta(days=365),
            end_date=datetime.now(),
            limit=500
        )
        
        assert len(query.series_ids) == 3
        assert query.limit == 500
    
    def test_empty_series_ids_validation(self):
        """测试空序列ID列表验证"""
        with pytest.raises(ValidationError) as exc_info:
            EconomicDataQuery(series_ids=[])
        
        assert "至少需要一个序列ID" in str(exc_info.value)
    
    def test_too_many_series_ids_validation(self):
        """测试过多序列ID验证"""
        with pytest.raises(ValidationError) as exc_info:
            EconomicDataQuery(
                series_ids=[f"SERIES{i}" for i in range(51)]
            )
        
        assert "单次查询最多支持50个序列" in str(exc_info.value)
    
    def test_default_limit(self):
        """测试默认限制值"""
        query = EconomicDataQuery(series_ids=["GDP"])
        assert query.limit == 1000
    
    def test_optional_dates(self):
        """测试可选日期参数"""
        query = EconomicDataQuery(series_ids=["GDP"])
        assert query.start_date is None
        assert query.end_date is None


class TestBacktestRequestValidation:
    """回测请求参数验证测试"""
    
    def test_valid_backtest_request(self):
        """测试有效的回测请求"""
        request = BacktestRequest(
            strategy_id="moving_average_strategy",
            symbols=["AAPL", "GOOGL"],
            start_date=datetime.now() - timedelta(days=365),
            end_date=datetime.now() - timedelta(days=1),
            initial_capital=100000.0,
            parameters={"fast_period": 10, "slow_period": 20},
            benchmark="SPY"
        )
        
        assert request.strategy_id == "moving_average_strategy"
        assert len(request.symbols) == 2
        assert request.initial_capital == 100000.0
        assert request.parameters["fast_period"] == 10
        assert request.benchmark == "SPY"
    
    def test_empty_symbols_in_backtest(self):
        """测试回测中的空符号列表"""
        with pytest.raises(ValidationError) as exc_info:
            BacktestRequest(
                strategy_id="test_strategy",
                symbols=[],
                start_date=datetime.now() - timedelta(days=365),
                end_date=datetime.now() - timedelta(days=1)
            )
        
        assert "至少需要一个交易品种" in str(exc_info.value)
    
    def test_too_many_symbols_in_backtest(self):
        """测试回测中的过多符号"""
        with pytest.raises(ValidationError) as exc_info:
            BacktestRequest(
                strategy_id="test_strategy",
                symbols=[f"SYMBOL{i}" for i in range(21)],
                start_date=datetime.now() - timedelta(days=365),
                end_date=datetime.now() - timedelta(days=1)
            )
        
        assert "单次回测最多支持20个品种" in str(exc_info.value)
    
    def test_default_values_in_backtest(self):
        """测试回测请求的默认值"""
        request = BacktestRequest(
            strategy_id="test_strategy",
            symbols=["AAPL"],
            start_date=datetime.now() - timedelta(days=365),
            end_date=datetime.now() - timedelta(days=1)
        )
        
        assert request.initial_capital == 100000.0
        assert request.benchmark == "SPY"
        assert request.parameters is None
    
    def test_custom_parameters(self):
        """测试自定义参数"""
        custom_params = {
            "fast_period": 5,
            "slow_period": 15,
            "stop_loss": 0.05,
            "take_profit": 0.10
        }
        
        request = BacktestRequest(
            strategy_id="test_strategy",
            symbols=["AAPL"],
            start_date=datetime.now() - timedelta(days=365),
            end_date=datetime.now() - timedelta(days=1),
            parameters=custom_params
        )
        
        assert request.parameters == custom_params
        assert request.parameters["fast_period"] == 5
        assert request.parameters["stop_loss"] == 0.05


class TestDateValidation:
    """日期验证测试"""
    
    def test_date_order_validation(self):
        """测试日期顺序（这个需要在实际API中实现）"""
        # 这里只是演示如何测试日期逻辑
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now() - timedelta(days=60)  # 结束日期早于开始日期
        
        # 在实际实现中，应该在API层面验证日期顺序
        assert start_date > end_date  # 这会失败，说明日期顺序有问题
    
    def test_future_date_validation(self):
        """测试未来日期验证"""
        future_date = datetime.now() + timedelta(days=30)
        
        # 可以创建查询，但在实际API中应该验证不允许未来日期
        query = MarketDataQuery(
            symbols=["AAPL"],
            start_date=future_date
        )
        
        assert query.start_date == future_date


class TestComplexValidationScenarios:
    """复杂验证场景测试"""
    
    def test_large_date_range(self):
        """测试大日期范围"""
        start_date = datetime(2020, 1, 1)
        end_date = datetime(2024, 1, 1)
        
        query = MarketDataQuery(
            symbols=["AAPL"],
            start_date=start_date,
            end_date=end_date
        )
        
        date_diff = (end_date - start_date).days
        assert date_diff > 1000  # 超过1000天的数据
    
    def test_maximum_symbols_boundary(self):
        """测试符号数量边界值"""
        # 测试99个符号（应该通过）
        symbols_99 = [f"SYMBOL{i:02d}" for i in range(99)]
        query = MarketDataQuery(
            symbols=symbols_99,
            start_date=datetime.now() - timedelta(days=30)
        )
        assert len(query.symbols) == 99
        
        # 测试100个符号（应该通过）
        symbols_100 = [f"SYMBOL{i:03d}" for i in range(100)]
        query = MarketDataQuery(
            symbols=symbols_100,
            start_date=datetime.now() - timedelta(days=30)
        )
        assert len(query.symbols) == 100
        
        # 测试101个符号（应该失败）
        symbols_101 = [f"SYMBOL{i:03d}" for i in range(101)]
        with pytest.raises(ValidationError):
            MarketDataQuery(
                symbols=symbols_101,
                start_date=datetime.now() - timedelta(days=30)
            )
    
    def test_special_characters_in_symbols(self):
        """测试符号中的特殊字符"""
        special_symbols = ["AAPL", "BRK.A", "BRK.B", "GOOGL"]
        
        query = MarketDataQuery(
            symbols=special_symbols,
            start_date=datetime.now() - timedelta(days=30)
        )
        
        assert "BRK.A" in query.symbols
        assert "BRK.B" in query.symbols


if __name__ == "__main__":
    pytest.main([__file__, "-v"])