#!/usr/bin/env python3
"""
Comprehensive tests for multi-market strategy framework.

Tests all components of the multi-market strategy system including:
- Multi-market context and data handling
- Currency conversion and timezone management
- Cross-market arbitrage strategies
- Multi-market risk management
- Strategy coordination and management
"""

import unittest
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timezone, timedelta
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock

# Import components to test
from src.strategies.multi_market import (
    MultiMarketContext, CurrencyConverter, MarketSession, 
    MarketFees, SlippageModel, CurrencyRate, BaseMultiMarketStrategy
)
from src.strategies.multi_market_manager import (
    MultiMarketStrategyManager, StrategyAllocation, MarketAllocation, StrategyPriority
)
from src.strategies.market_config import MarketConfigManager
from src.strategies.multi_market_risk import MultiMarketRiskManager, RiskLimit
from src.strategies.examples.cross_market_arbitrage import CrossMarketArbitrageStrategy
from src.models.market_data import UnifiedMarketData, MarketInfo
from src.models.portfolio import Portfolio, Position
from src.models.trading import Order


class TestCurrencyConverter(unittest.TestCase):
    """Test currency conversion functionality."""
    
    def setUp(self):
        self.converter = CurrencyConverter()
    
    def test_same_currency_conversion(self):
        """Test conversion between same currencies."""
        result = self.converter.convert(100.0, 'USD', 'USD')
        self.assertEqual(result, 100.0)
    
    def test_basic_conversion(self):
        """Test basic currency conversion."""
        # Add a test rate
        rate = CurrencyRate('EUR', 'USD', 1.1, datetime.now(timezone.utc))
        self.converter.add_rate(rate)
        
        result = self.converter.convert(100.0, 'EUR', 'USD')
        self.assertAlmostEqual(result, 110.0, places=2)
    
    def test_inverse_rate_creation(self):
        """Test that inverse rates are automatically created."""
        rate = CurrencyRate('GBP', 'USD', 1.25, datetime.now(timezone.utc))
        self.converter.add_rate(rate)
        
        # Test inverse conversion
        result = self.converter.convert(125.0, 'USD', 'GBP')
        self.assertAlmostEqual(result, 100.0, places=2)
    
    def test_cross_rate_calculation(self):
        """Test cross-rate calculation through base currency."""
        # Add rates: EUR->USD and GBP->USD
        eur_rate = CurrencyRate('EUR', 'USD', 1.1, datetime.now(timezone.utc))
        gbp_rate = CurrencyRate('GBP', 'USD', 1.25, datetime.now(timezone.utc))
        
        self.converter.add_rate(eur_rate)
        self.converter.add_rate(gbp_rate)
        
        # Convert EUR to GBP through USD
        result = self.converter.convert(100.0, 'EUR', 'GBP')
        expected = 100.0 * 1.1 / 1.25  # EUR->USD->GBP
        self.assertAlmostEqual(result, expected, places=2)
    
    def test_conversion_cost_estimation(self):
        """Test currency conversion cost estimation."""
        # Major currency pair
        cost = self.converter.get_conversion_cost('USD', 'EUR')
        self.assertEqual(cost, 0.001)  # 0.1%
        
        # Exotic pair
        cost = self.converter.get_conversion_cost('THB', 'ZAR')
        self.assertEqual(cost, 0.005)  # 0.5%
    
    def test_rate_cleanup(self):
        """Test cleanup of old exchange rates."""
        old_time = datetime.now(timezone.utc) - timedelta(hours=50)
        old_rate = CurrencyRate('TEST', 'USD', 1.0, old_time)
        
        self.converter.add_rate(old_rate)
        self.assertIn('TEST_USD', self.converter.rates)
        
        self.converter.cleanup_old_rates(max_age_hours=48)
        self.assertNotIn('TEST_USD', self.converter.rates)


class TestMultiMarketContext(unittest.TestCase):
    """Test multi-market context functionality."""
    
    def setUp(self):
        self.converter = CurrencyConverter()
        self.sessions = {
            'US_NYSE': MarketSession('US', 'NYSE', 'US/Eastern', '09:30', '16:00'),
            'CRYPTO_BINANCE': MarketSession('CRYPTO', 'BINANCE', 'UTC', '00:00', '23:59', is_24h=True)
        }
        self.fees = {
            'US_NYSE': MarketFees('US', 'NYSE', 0.0025, 0.0035, 1.0, currency='USD'),
            'CRYPTO_BINANCE': MarketFees('CRYPTO', 'BINANCE', 0.001, 0.001, 0.0, currency='USDT')
        }
        self.slippage = {
            'US_NYSE': SlippageModel('US', 'NYSE', 0.0005, 0.000001, 1.2, 0.01),
            'CRYPTO_BINANCE': SlippageModel('CRYPTO', 'BINANCE', 0.0002, 0.0000005, 2.0, 0.05)
        }
        
        self.context = MultiMarketContext(
            currency_converter=self.converter,
            market_sessions=self.sessions,
            fee_models=self.fees,
            slippage_models=self.slippage
        )
    
    def test_market_open_check(self):
        """Test market open/close checking."""
        # Crypto market should always be open
        is_open = self.context.is_market_open('CRYPTO')
        self.assertTrue(is_open)
    
    def test_order_cost_calculation(self):
        """Test order cost calculation with fees and slippage."""
        cost_info = self.context.calculate_order_cost(
            'AAPL', 'US', 'NYSE', 100, 150.0, 'BUY'
        )
        
        self.assertIn('base_cost', cost_info)
        self.assertIn('fees', cost_info)
        self.assertIn('slippage', cost_info)
        self.assertIn('total_cost', cost_info)
        
        self.assertEqual(cost_info['base_cost'], 15000.0)  # 100 * 150
        self.assertGreater(cost_info['total_cost'], cost_info['base_cost'])
    
    def test_market_synchronization_check(self):
        """Test market synchronization checking."""
        # Mock some market data
        current_time = datetime.now(timezone.utc)
        
        # Add some test data to cache
        test_data = UnifiedMarketData(
            symbol='TEST', timestamp=current_time, open=100, high=101,
            low=99, close=100.5, volume=1000, market='US', exchange='NYSE', currency='USD'
        )
        
        self.context.market_data_cache['US'] = {'TEST': test_data}
        
        sync_status = self.context.check_market_synchronization(['US'], max_time_diff_seconds=60)
        self.assertIn('US', sync_status)
    
    def test_cross_market_correlation_calculation(self):
        """Test cross-market correlation calculation."""
        # Mock data manager
        mock_data_manager = Mock()
        
        # Create sample data for correlation calculation
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        prices1 = np.random.randn(100).cumsum() + 100
        prices2 = prices1 + np.random.randn(100) * 0.1  # Highly correlated
        
        data1 = pd.DataFrame({
            'timestamp': dates,
            'close': prices1
        })
        data2 = pd.DataFrame({
            'timestamp': dates,
            'close': prices2
        })
        
        mock_data_manager.get_historical_data.side_effect = [data1, data2]
        self.context.data_manager = mock_data_manager
        
        correlation = self.context.calculate_cross_market_correlation(
            'AAPL', 'US', 'AAPL', 'EU', lookback_periods=100
        )
        
        self.assertIsNotNone(correlation)
        self.assertGreater(correlation, 0.8)  # Should be highly correlated


class TestCrossMarketArbitrageStrategy(unittest.TestCase):
    """Test cross-market arbitrage strategy."""
    
    def setUp(self):
        self.strategy = CrossMarketArbitrageStrategy(
            parameters={
                'min_profit_threshold': 0.01,
                'max_position_size': 10000.0,
                'price_staleness_threshold': 30,
                'correlation_threshold': 0.9
            }
        )
        
        # Create mock context
        self.mock_context = Mock(spec=MultiMarketContext)
        self.mock_context.convert_currency.return_value = 100.0
        self.mock_context.is_market_open.return_value = True
        self.mock_context.get_portfolio.return_value = Mock(total_value=100000.0)
        
        self.strategy.initialize(self.mock_context)
    
    def test_strategy_initialization(self):
        """Test strategy initialization."""
        self.assertTrue(self.strategy.is_initialized)
        self.assertEqual(len(self.strategy.supported_markets), 3)
        self.assertIn('US', self.strategy.supported_markets)
        self.assertIn('CN', self.strategy.supported_markets)
        self.assertIn('CRYPTO', self.strategy.supported_markets)
    
    def test_parameter_definitions(self):
        """Test strategy parameter definitions."""
        params = self.strategy.get_parameter_definitions()
        
        required_params = [
            'min_profit_threshold', 'max_position_size', 'price_staleness_threshold',
            'correlation_threshold', 'max_holding_time', 'risk_limit'
        ]
        
        for param in required_params:
            self.assertIn(param, params)
    
    def test_arbitrage_opportunity_detection(self):
        """Test arbitrage opportunity detection."""
        current_time = datetime.now(timezone.utc)
        
        # Create market data with price difference
        market_data = {
            'US': [UnifiedMarketData(
                symbol='AAPL', timestamp=current_time, open=150, high=152,
                low=149, close=151, volume=1000, market='US', exchange='NASDAQ', currency='USD'
            )],
            'EU': [UnifiedMarketData(
                symbol='AAPL', timestamp=current_time, open=140, high=142,
                low=139, close=141, volume=500, market='EU', exchange='XETRA', currency='EUR'
            )]
        }
        
        # Mock currency conversion to show price difference
        def mock_convert(amount, from_curr, to_curr):
            if from_curr == 'EUR' and to_curr == 'USD':
                return amount * 1.1  # EUR to USD
            return amount
        
        self.mock_context.convert_currency.side_effect = mock_convert
        
        # Mock validation to return True
        self.strategy.validate_cross_market_opportunity = Mock(return_value=True)
        
        signals = self.strategy.on_multi_market_data(market_data)
        
        # Should generate arbitrage signals due to price difference
        self.assertGreater(len(signals), 0)
        
        # Check signal properties
        signal = signals[0]
        self.assertEqual(signal.symbol, 'AAPL')
        self.assertIn(signal.action, ['BUY', 'SELL'])
        self.assertGreater(signal.quantity, 0)
        self.assertIn('arbitrage_type', signal.metadata)
    
    def test_risk_limit_checking(self):
        """Test risk limit checking for arbitrage opportunities."""
        # Create opportunity that should pass risk checks
        opportunity = {
            'symbol': 'TEST',
            'market1': 'US',
            'market2': 'EU',
            'price1_usd': 100.0,
            'price2_usd': 102.0,
            'profit_percentage': 0.02
        }
        
        # Test with empty active positions
        self.strategy.active_positions = {}
        result = self.strategy._check_risk_limits(opportunity, 0.1)
        self.assertTrue(result)
        
        # Test with too many active positions
        self.strategy.active_positions = {f'pos_{i}': {} for i in range(6)}
        result = self.strategy._check_risk_limits(opportunity, 0.1)
        self.assertFalse(result)
    
    def test_position_exit_conditions(self):
        """Test position exit condition checking."""
        # Create active position that should be closed
        old_time = datetime.now(timezone.utc) - timedelta(minutes=70)  # Older than max holding time
        
        self.strategy.active_positions['test_pos'] = {
            'signal': Mock(action='BUY', quantity=100, symbol='TEST'),
            'opportunity': {'symbol': 'TEST'},
            'entry_time': old_time,
            'status': 'filled'
        }
        
        exit_signals = self.strategy._check_position_exits()
        
        self.assertEqual(len(exit_signals), 1)
        self.assertEqual(exit_signals[0].action, 'SELL')  # Opposite of original BUY
        self.assertEqual(exit_signals[0].symbol, 'TEST')


class TestMultiMarketRiskManager(unittest.TestCase):
    """Test multi-market risk management."""
    
    def setUp(self):
        self.converter = CurrencyConverter()
        self.risk_manager = MultiMarketRiskManager(self.converter)
        
        # Create test portfolio
        positions = {
            'AAPL': Position('AAPL', 100, 150.0, 15000.0, 0.0),
            'BTCUSDT': Position('BTCUSDT', 0.5, 45000.0, 22500.0, 0.0)
        }
        self.portfolio = Portfolio(
            cash=50000.0,
            positions=positions,
            total_value=87500.0,
            unrealized_pnl=0.0,
            realized_pnl=0.0
        )
    
    def test_market_exposure_calculation(self):
        """Test market exposure calculation."""
        exposures = self.risk_manager.calculate_market_exposures(self.portfolio)
        
        self.assertIn('US', exposures)  # AAPL should be classified as US
        self.assertIn('CRYPTO', exposures)  # BTCUSDT should be classified as CRYPTO
        
        # Check exposure values
        us_exposure = exposures['US']
        self.assertEqual(us_exposure.total_value, 15000.0)
        self.assertAlmostEqual(us_exposure.percentage_of_portfolio, 15000.0 / 87500.0, places=3)
    
    def test_currency_exposure_calculation(self):
        """Test currency exposure calculation."""
        exposures = self.risk_manager.calculate_currency_exposures(self.portfolio)
        
        self.assertIn('USD', exposures)
        self.assertIn('USDT', exposures)
        
        # Check USD exposure (should include AAPL)
        usd_exposure = exposures['USD']
        self.assertEqual(usd_exposure.total_value, 15000.0)
    
    def test_order_risk_checking(self):
        """Test order risk checking."""
        # Create test order
        order = Order(
            symbol='AAPL',
            side='BUY',
            quantity=50,
            price=150.0,
            order_type='MARKET',
            timestamp=datetime.now(timezone.utc)
        )
        
        is_allowed, violations = self.risk_manager.check_order_risk(order, self.portfolio)
        
        # Should be allowed for reasonable order size
        self.assertTrue(is_allowed)
        self.assertEqual(len(violations), 0)
        
        # Test with very large order
        large_order = Order(
            symbol='AAPL',
            side='BUY',
            quantity=10000,  # Very large
            price=150.0,
            order_type='MARKET',
            timestamp=datetime.now(timezone.utc)
        )
        
        is_allowed, violations = self.risk_manager.check_order_risk(large_order, self.portfolio)
        
        # Should have violations for large order
        self.assertGreater(len(violations), 0)
    
    def test_risk_limit_management(self):
        """Test risk limit management."""
        # Add custom risk limit
        custom_limit = RiskLimit(
            name='test_limit',
            limit_type='percentage',
            value=0.05,
            scope='portfolio'
        )
        
        self.risk_manager.add_risk_limit(custom_limit)
        self.assertIn('test_limit', self.risk_manager.risk_limits)
        
        # Remove risk limit
        removed = self.risk_manager.remove_risk_limit('test_limit')
        self.assertTrue(removed)
        self.assertNotIn('test_limit', self.risk_manager.risk_limits)
    
    def test_risk_summary_generation(self):
        """Test risk summary generation."""
        summary = self.risk_manager.get_risk_summary(self.portfolio)
        
        required_fields = [
            'timestamp', 'portfolio_value', 'cash', 'leverage',
            'total_positions', 'market_exposures', 'currency_exposures'
        ]
        
        for field in required_fields:
            self.assertIn(field, summary)
        
        self.assertEqual(summary['portfolio_value'], 87500.0)
        self.assertEqual(summary['cash'], 50000.0)
        self.assertEqual(summary['total_positions'], 2)


class TestMultiMarketStrategyManager(unittest.TestCase):
    """Test multi-market strategy manager."""
    
    def setUp(self):
        self.market_config = MarketConfigManager()
        self.risk_manager = MultiMarketRiskManager()
        self.manager = MultiMarketStrategyManager(self.market_config, self.risk_manager)
    
    def test_strategy_registration(self):
        """Test strategy registration and management."""
        # Create test strategy
        strategy = CrossMarketArbitrageStrategy("Test_Strategy")
        
        # Register strategy
        success = self.manager.register_strategy(strategy)
        self.assertTrue(success)
        self.assertEqual(len(self.manager.strategies), 1)
        
        # Check strategy was properly initialized
        strategy_id = list(self.manager.strategies.keys())[0]
        registered_strategy = self.manager.strategies[strategy_id]
        self.assertTrue(registered_strategy.is_initialized)
        
        # Unregister strategy
        success = self.manager.unregister_strategy(strategy_id)
        self.assertTrue(success)
        self.assertEqual(len(self.manager.strategies), 0)
    
    def test_market_data_processing(self):
        """Test market data processing and signal coordination."""
        # Register test strategies
        strategy1 = CrossMarketArbitrageStrategy("Strategy_1")
        strategy2 = CrossMarketArbitrageStrategy("Strategy_2")
        
        self.manager.register_strategy(strategy1)
        self.manager.register_strategy(strategy2)
        
        # Create test market data
        current_time = datetime.now(timezone.utc)
        market_data = {
            'AAPL': UnifiedMarketData(
                symbol='AAPL', timestamp=current_time, open=150, high=152,
                low=149, close=151, volume=1000, market='US', exchange='NASDAQ', currency='USD'
            ),
            'BTCUSDT': UnifiedMarketData(
                symbol='BTCUSDT', timestamp=current_time, open=45000, high=45500,
                low=44800, close=45200, volume=100, market='CRYPTO', exchange='BINANCE', currency='USDT'
            )
        }
        
        # Process market data
        signals = self.manager.process_market_data(market_data)
        
        # Should return list of coordinated signals
        self.assertIsInstance(signals, list)
    
    def test_allocation_management(self):
        """Test strategy allocation management."""
        # First register a strategy to have a valid strategy_id
        strategy = Mock()
        strategy.name = "test_strategy"
        strategy.supported_markets = ['US']
        strategy.initialize = Mock()
        strategy.set_multi_market_context = Mock()
        
        success = self.manager.register_strategy(strategy)
        self.assertTrue(success)
        
        # Get the actual strategy_id that was created
        strategy_id = list(self.manager.strategies.keys())[0]
        
        # Create test allocation
        allocation = StrategyAllocation(
            strategy_id=strategy_id,
            capital_allocation=0.3,
            risk_allocation=0.2,
            priority=StrategyPriority.HIGH,
            max_positions=5
        )
        
        # Update allocation
        success = self.manager.update_strategy_allocation(strategy_id, allocation)
        self.assertTrue(success)
        
        # Check allocation was stored
        stored_allocation = self.manager.strategy_allocations.get(strategy_id)
        self.assertIsNotNone(stored_allocation)
        self.assertEqual(stored_allocation.capital_allocation, 0.3)
    
    def test_manager_status(self):
        """Test manager status reporting."""
        status = self.manager.get_manager_status()
        
        required_fields = [
            'timestamp', 'total_strategies', 'active_strategies',
            'supported_markets', 'total_capital_allocated', 'market_allocations'
        ]
        
        for field in required_fields:
            self.assertIn(field, status)


class TestMarketConfigManager(unittest.TestCase):
    """Test market configuration manager."""
    
    def setUp(self):
        # Use a temporary config path for testing
        self.config_manager = MarketConfigManager(config_path='test_market_config.yaml')
    
    def test_default_configuration_loading(self):
        """Test loading of default market configurations."""
        # Should have default sessions
        self.assertGreater(len(self.config_manager.sessions), 0)
        self.assertGreater(len(self.config_manager.fees), 0)
        self.assertGreater(len(self.config_manager.slippage_models), 0)
        
        # Check specific default sessions
        self.assertIn('US_NYSE', self.config_manager.sessions)
        self.assertIn('CRYPTO_BINANCE', self.config_manager.sessions)
    
    def test_market_session_management(self):
        """Test market session management."""
        # Add new session
        test_session = MarketSession(
            market='TEST',
            exchange='TEST_EXCHANGE',
            timezone='UTC',
            open_time='09:00',
            close_time='17:00'
        )
        
        self.config_manager.add_market_session('TEST_EXCHANGE', test_session)
        
        # Retrieve session
        retrieved_session = self.config_manager.get_market_session('TEST', 'TEST_EXCHANGE')
        self.assertIsNotNone(retrieved_session)
        self.assertEqual(retrieved_session.market, 'TEST')
    
    def test_fee_structure_management(self):
        """Test fee structure management."""
        # Add new fee structure
        test_fees = MarketFees(
            market='TEST',
            exchange='TEST_EXCHANGE',
            maker_fee=0.001,
            taker_fee=0.002,
            min_fee=1.0,
            currency='USD'
        )
        
        self.config_manager.add_market_fees('TEST_EXCHANGE', test_fees)
        
        # Retrieve fees
        retrieved_fees = self.config_manager.get_market_fees('TEST', 'TEST_EXCHANGE')
        self.assertIsNotNone(retrieved_fees)
        self.assertEqual(retrieved_fees.maker_fee, 0.001)
    
    def test_configuration_summary(self):
        """Test configuration summary generation."""
        summary = self.config_manager.get_configuration_summary()
        
        required_fields = [
            'total_sessions', 'total_fee_structures', 'total_slippage_models',
            'markets', 'sessions_by_market'
        ]
        
        for field in required_fields:
            self.assertIn(field, summary)
        
        self.assertGreater(summary['total_sessions'], 0)
    
    def tearDown(self):
        """Clean up test files."""
        import os
        if os.path.exists('test_market_config.yaml'):
            os.remove('test_market_config.yaml')


def run_comprehensive_tests():
    """Run all comprehensive tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add all test classes
    test_classes = [
        TestCurrencyConverter,
        TestMultiMarketContext,
        TestCrossMarketArbitrageStrategy,
        TestMultiMarketRiskManager,
        TestMultiMarketStrategyManager,
        TestMarketConfigManager
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    print("Running Comprehensive Multi-Market Strategy Tests")
    print("=" * 60)
    
    success = run_comprehensive_tests()
    
    print("=" * 60)
    if success:
        print("✓ All multi-market strategy tests passed!")
    else:
        print("✗ Some tests failed!")
        sys.exit(1)