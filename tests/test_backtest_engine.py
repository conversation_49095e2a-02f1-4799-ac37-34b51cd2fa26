"""
Tests for the backtesting engine.
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import uuid

from src.backtest.engine import BacktestEngine, BacktestProgress
from src.backtest.events import MarketEvent, SignalEvent, OrderEvent, FillEvent
from src.backtest.executor import ExecutionConfig
from src.strategies.base import BaseStrategy, StrategyContext
from src.models.market_data import MarketData
from src.models.trading import Signal, SignalAction, Order, OrderSide, OrderType
from src.strategies.parameters import StrategyParameter, ParameterType


class TestStrategy(BaseStrategy):
    """Simple test strategy for backtesting."""
    
    def get_parameter_definitions(self):
        return {
            'buy_threshold': StrategyParameter(
                name='buy_threshold',
                param_type=ParameterType.FLOAT,
                default_value=0.02,
                description='Buy threshold'
            )
        }
    
    def initialize(self, context):
        self.context = context
        self.is_initialized = True
    
    def on_data(self, data):
        # Simple momentum strategy
        if isinstance(data, MarketData):
            if data.close > data.open * 1.01:  # 1% up
                return [Signal(
                    symbol=data.symbol,
                    action=SignalAction.BUY,
                    quantity=100,
                    timestamp=data.timestamp
                )]
            elif data.close < data.open * 0.99:  # 1% down
                return [Signal(
                    symbol=data.symbol,
                    action=SignalAction.SELL,
                    quantity=100,
                    timestamp=data.timestamp
                )]
        return []


class TestBacktestProgress:
    """Test BacktestProgress class."""
    
    def test_progress_initialization(self):
        progress = BacktestProgress(100)
        assert progress.total_steps == 100
        assert progress.current_step == 0
        assert progress.get_progress_pct() == 0.0
    
    def test_progress_update(self):
        progress = BacktestProgress(100)
        progress.update(50)
        assert progress.current_step == 50
        assert progress.get_progress_pct() == 50.0
    
    def test_progress_completion(self):
        progress = BacktestProgress(100)
        progress.update(100)
        assert progress.get_progress_pct() == 100.0


class TestBacktestEngine:
    """Test BacktestEngine class."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample market data for testing."""
        dates = pd.date_range('2023-01-01', periods=10, freq='D')
        data = []
        
        for i, date in enumerate(dates):
            data.append({
                'symbol': 'TEST',
                'open': 100 + i,
                'high': 105 + i,
                'low': 95 + i,
                'close': 102 + i,
                'volume': 1000
            })
        
        df = pd.DataFrame(data, index=dates)
        return df
    
    @pytest.fixture
    def engine(self):
        """Create backtesting engine for testing."""
        return BacktestEngine(initial_capital=100000.0)
    
    @pytest.fixture
    def test_strategy(self):
        """Create test strategy."""
        return TestStrategy(name="TestStrategy")
    
    def test_engine_initialization(self, engine):
        """Test engine initialization."""
        assert engine.initial_capital == 100000.0
        assert engine.portfolio.initial_capital == 100000.0
        assert engine.portfolio.cash == 100000.0
        assert len(engine.strategies) == 0
        assert not engine.is_running
    
    def test_add_strategy(self, engine, test_strategy):
        """Test adding strategy to engine."""
        strategy_id = engine.add_strategy(test_strategy)
        
        assert strategy_id in engine.strategies
        assert engine.strategies[strategy_id] == test_strategy
        assert test_strategy.is_initialized
        assert test_strategy.is_active
        assert test_strategy.context is not None
    
    def test_set_data_feed(self, engine, sample_data):
        """Test setting data feed."""
        engine.set_data_feed(sample_data)
        
        assert engine.market_data_feed is not None
        assert len(engine.market_data_feed) == 10
        assert 'symbol' in engine.market_data_feed.columns
    
    def test_set_data_feed_invalid(self, engine):
        """Test setting invalid data feed."""
        invalid_data = pd.DataFrame({'invalid': [1, 2, 3]})
        
        with pytest.raises(ValueError, match="Data must contain columns"):
            engine.set_data_feed(invalid_data)
    
    def test_run_backtest_no_data(self, engine, test_strategy):
        """Test running backtest without data."""
        engine.add_strategy(test_strategy)
        
        with pytest.raises(ValueError, match="Market data feed not set"):
            engine.run_backtest()
    
    def test_run_backtest_no_strategies(self, engine, sample_data):
        """Test running backtest without strategies."""
        engine.set_data_feed(sample_data)
        
        with pytest.raises(ValueError, match="No strategies added"):
            engine.run_backtest()
    
    def test_run_backtest_success(self, engine, sample_data, test_strategy):
        """Test successful backtest run."""
        engine.add_strategy(test_strategy)
        engine.set_data_feed(sample_data)
        
        result = engine.run_backtest()
        
        assert result is not None
        assert result.initial_capital == 100000.0
        assert result.start_date == sample_data.index[0]
        assert result.end_date == sample_data.index[-1]
        assert len(result.portfolio_history) == len(sample_data)
    
    def test_run_backtest_with_date_range(self, engine, sample_data, test_strategy):
        """Test backtest with date range filtering."""
        engine.add_strategy(test_strategy)
        engine.set_data_feed(sample_data)
        
        start_date = sample_data.index[2]
        end_date = sample_data.index[7]
        
        result = engine.run_backtest(start_date=start_date, end_date=end_date)
        
        assert result.start_date == start_date
        assert result.end_date == end_date
        assert len(result.portfolio_history) == 6  # 7-2+1
    
    def test_progress_callback(self, engine, sample_data, test_strategy):
        """Test progress callback functionality."""
        progress_updates = []
        
        def progress_callback(progress):
            progress_updates.append(progress.get_progress_pct())
        
        engine.add_strategy(test_strategy)
        engine.set_data_feed(sample_data)
        engine.set_progress_callback(progress_callback)
        
        engine.run_backtest()
        
        assert len(progress_updates) == len(sample_data)
        assert progress_updates[-1] == 100.0
    
    def test_stop_backtest(self, engine, sample_data, test_strategy):
        """Test stopping backtest."""
        engine.add_strategy(test_strategy)
        engine.set_data_feed(sample_data)
        
        # Mock the process to stop after first iteration
        original_process = engine._process_timestamp
        call_count = 0
        
        def mock_process(market_data):
            nonlocal call_count
            call_count += 1
            if call_count == 2:
                engine.stop_backtest()
            return original_process(market_data)
        
        engine._process_timestamp = mock_process
        
        result = engine.run_backtest()
        
        # Should have stopped early
        assert len(result.portfolio_history) < len(sample_data)
    
    def test_signal_to_order_conversion(self, engine):
        """Test signal to order conversion."""
        signal = Signal(
            symbol='TEST',
            action=SignalAction.BUY,
            quantity=100,
            price=105.0,
            timestamp=datetime.now()
        )
        
        order = engine._signal_to_order(signal, 'test_strategy')
        
        assert order is not None
        assert order.symbol == 'TEST'
        assert order.side == OrderSide.BUY
        assert order.quantity == 100
        assert order.price == 105.0
        assert order.order_type == OrderType.LIMIT
        assert order.strategy_id == 'test_strategy'
    
    def test_signal_to_order_market_order(self, engine):
        """Test signal to market order conversion."""
        signal = Signal(
            symbol='TEST',
            action=SignalAction.SELL,
            quantity=50,
            timestamp=datetime.now()
        )
        
        order = engine._signal_to_order(signal, 'test_strategy')
        
        assert order is not None
        assert order.side == OrderSide.SELL
        assert order.order_type == OrderType.MARKET
        assert order.price is None
    
    def test_get_historical_data(self, engine, sample_data):
        """Test getting historical data for strategies."""
        engine.set_data_feed(sample_data)
        engine.current_timestamp = sample_data.index[5]
        
        historical_data = engine.get_historical_data('TEST', lookback=3)
        
        assert len(historical_data) == 3
        assert historical_data.index[-1] <= sample_data.index[5]
    
    def test_get_current_data(self, engine):
        """Test getting current market data."""
        market_data = MarketData(
            symbol='TEST',
            timestamp=datetime.now(),
            open=100,
            high=105,
            low=95,
            close=102,
            volume=1000
        )
        
        engine.current_data['TEST'] = market_data
        
        current_data = engine.get_current_data('TEST')
        assert current_data == market_data
        
        # Test non-existent symbol
        assert engine.get_current_data('NONEXISTENT') is None


class TestBacktestIntegration:
    """Integration tests for backtesting system."""
    
    def test_full_backtest_workflow(self):
        """Test complete backtesting workflow."""
        # Create sample data with clear buy/sell signals
        dates = pd.date_range('2023-01-01', periods=20, freq='D')
        data = []
        
        for i, date in enumerate(dates):
            # Create alternating up/down pattern
            if i % 4 < 2:  # Up days
                open_price = 100 + i
                close_price = open_price * 1.02  # 2% up
            else:  # Down days
                open_price = 100 + i
                close_price = open_price * 0.98  # 2% down
            
            data.append({
                'symbol': 'TEST',
                'open': open_price,
                'high': close_price + 1,
                'low': open_price - 1,
                'close': close_price,
                'volume': 1000
            })
        
        df = pd.DataFrame(data, index=dates)
        
        # Create engine and strategy
        engine = BacktestEngine(initial_capital=100000.0)
        strategy = TestStrategy(name="TestStrategy")
        
        # Run backtest
        engine.add_strategy(strategy)
        engine.set_data_feed(df)
        result = engine.run_backtest()
        
        # Verify results
        assert result is not None
        assert result.initial_capital == 100000.0
        assert len(result.trades) > 0  # Should have some trades
        assert len(result.portfolio_history) == 20
        
        # Check that portfolio value changed
        assert result.final_capital != result.initial_capital
        
        # Verify metrics calculation
        result.calculate_metrics()
        assert result.total_return != 0
        assert result.total_trades > 0
    
    def test_backtest_with_execution_config(self):
        """Test backtesting with custom execution configuration."""
        # Create sample data
        dates = pd.date_range('2023-01-01', periods=5, freq='D')
        data = []
        
        for i, date in enumerate(dates):
            data.append({
                'symbol': 'TEST',
                'open': 100,
                'high': 105,
                'low': 95,
                'close': 102,  # Always 2% up to trigger buys
                'volume': 1000
            })
        
        df = pd.DataFrame(data, index=dates)
        
        # Create engine with custom execution config
        execution_config = ExecutionConfig(
            commission_rate=0.002,  # 0.2% commission
            slippage_rate=0.001     # 0.1% slippage
        )
        
        engine = BacktestEngine(
            initial_capital=100000.0,
            execution_config=execution_config
        )
        
        strategy = TestStrategy(name="TestStrategy")
        
        # Run backtest
        engine.add_strategy(strategy)
        engine.set_data_feed(df)
        result = engine.run_backtest()
        
        # Verify that commission and slippage were applied
        if result.trades:
            total_commission = sum(trade.commission for trade in result.trades)
            assert total_commission > 0
            
            # Check that execution prices include slippage
            for trade in result.trades:
                assert trade.price != 102  # Should be different due to slippage