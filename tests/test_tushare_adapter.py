"""
Tests for Tushare adapter.
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

from src.data.adapters.base import DataSourceConfig
from src.data.adapters.tushare_adapter import TushareAdapter
from src.models.market_data import UnifiedMarketData, MarketInfo
from src.exceptions import DataException


@pytest.fixture
def tushare_config():
    """Create test configuration for Tushare adapter."""
    return DataSourceConfig(
        name='tushare_test',
        adapter_class='src.data.adapters.tushare_adapter.TushareAdapter',
        enabled=True,
        priority=1,
        rate_limit={'requests_per_minute': 200},
        credentials={'token': 'test_token'},
        default_params={'adj': 'qfq'}
    )


@pytest.fixture
def mock_tushare_pro():
    """Mock Tushare Pro API."""
    with patch('tushare.pro_api') as mock_pro:
        mock_api = Mock()
        mock_pro.return_value = mock_api
        yield mock_api


@pytest.fixture
def sample_stock_data():
    """Sample stock data from Tushare."""
    return pd.DataFrame({
        'ts_code': ['000001.SZ', '000001.SZ', '000001.SZ'],
        'trade_date': ['20240101', '20240102', '20240103'],
        'open': [10.0, 10.5, 11.0],
        'high': [10.5, 11.0, 11.5],
        'low': [9.8, 10.2, 10.8],
        'close': [10.2, 10.8, 11.2],
        'vol': [1000, 1200, 1500],  # 成交量（手）
        'amount': [10200, 12960, 16800]  # 成交额（千元）
    })


@pytest.fixture
def sample_index_data():
    """Sample index data from Tushare."""
    return pd.DataFrame({
        'ts_code': ['000001.SH', '000001.SH', '000001.SH'],
        'trade_date': ['20240101', '20240102', '20240103'],
        'open': [3000.0, 3050.0, 3100.0],
        'high': [3050.0, 3100.0, 3150.0],
        'low': [2980.0, 3020.0, 3080.0],
        'close': [3020.0, 3080.0, 3120.0],
        'vol': [100000, 120000, 150000],
        'amount': [500000, 600000, 750000]
    })


@pytest.fixture
def sample_stock_basic():
    """Sample stock basic info from Tushare."""
    return pd.DataFrame({
        'ts_code': ['000001.SZ', '000002.SZ', '600000.SH'],
        'symbol': ['000001', '000002', '600000'],
        'name': ['平安银行', '万科A', '浦发银行'],
        'area': ['深圳', '深圳', '上海'],
        'industry': ['银行', '房地产', '银行'],
        'market': ['主板', '主板', '主板'],
        'list_status': ['L', 'L', 'L'],
        'list_date': ['19910403', '19910129', '19991110']
    })


class TestTushareAdapter:
    """Test cases for TushareAdapter."""
    
    @patch('tushare.set_token')
    def test_init_success(self, mock_set_token, tushare_config, mock_tushare_pro):
        """Test successful initialization."""
        adapter = TushareAdapter(tushare_config)
        
        mock_set_token.assert_called_once_with('test_token')
        assert adapter.config == tushare_config
        assert adapter.pro is not None
    
    @patch('tushare.set_token')
    def test_init_no_token(self, mock_set_token, tushare_config):
        """Test initialization without token."""
        tushare_config.credentials = {}
        
        with pytest.raises(DataException, match="Tushare token is required"):
            TushareAdapter(tushare_config)
    
    @patch('tushare.set_token')
    def test_init_api_failure(self, mock_set_token, tushare_config):
        """Test initialization with API failure."""
        mock_set_token.side_effect = Exception("API initialization failed")
        
        with pytest.raises(DataException, match="Tushare initialization failed"):
            TushareAdapter(tushare_config)
    
    @patch('tushare.set_token')
    def test_fetch_stock_data(self, mock_set_token, tushare_config, mock_tushare_pro, sample_stock_data):
        """Test fetching stock historical data."""
        mock_tushare_pro.daily.return_value = sample_stock_data
        
        adapter = TushareAdapter(tushare_config)
        adapter.validate_symbol = Mock(return_value=True)
        
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 3)
        
        result = adapter.fetch_historical_data('000001.SZ', start_date, end_date)
        
        assert len(result) == 3
        assert all(isinstance(item, UnifiedMarketData) for item in result)
        assert result[0].symbol == '000001.SZ'
        assert result[0].market == 'CN'
        assert result[0].exchange == 'SZSE'
        assert result[0].currency == 'CNY'
        assert result[0].volume == 100000  # 1000手 * 100
        
        mock_tushare_pro.daily.assert_called_once_with(
            ts_code='000001.SZ',
            start_date='20240101',
            end_date='20240103'
        )
    
    @patch('tushare.set_token')
    def test_fetch_index_data(self, mock_set_token, tushare_config, mock_tushare_pro, sample_index_data):
        """Test fetching index historical data."""
        mock_tushare_pro.index_daily.return_value = sample_index_data
        
        adapter = TushareAdapter(tushare_config)
        adapter.validate_symbol = Mock(return_value=True)
        
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 3)
        
        result = adapter.fetch_historical_data('000001.SH', start_date, end_date)
        
        assert len(result) == 3
        assert all(isinstance(item, UnifiedMarketData) for item in result)
        assert result[0].symbol == '000001.SH'
        assert result[0].market == 'CN'
        assert result[0].exchange == 'SSE'
        
        mock_tushare_pro.index_daily.assert_called_once_with(
            ts_code='000001.SH',
            start_date='20240101',
            end_date='20240103'
        )
    
    @patch('tushare.set_token')
    def test_get_available_symbols(self, mock_set_token, tushare_config, mock_tushare_pro, sample_stock_basic):
        """Test getting available symbols."""
        mock_tushare_pro.stock_basic.return_value = sample_stock_basic
        
        adapter = TushareAdapter(tushare_config)
        
        symbols = adapter.get_available_symbols()
        
        assert len(symbols) > 3  # Should include stocks + indices
        assert '000001.SZ' in symbols
        assert '000002.SZ' in symbols
        assert '600000.SH' in symbols
        # Should also include major indices
        assert '000001.SH' in symbols  # 上证指数
        assert '399001.SZ' in symbols  # 深证成指
        
        mock_tushare_pro.stock_basic.assert_called_once()
    
    @patch('tushare.set_token')
    def test_validate_symbol_stock(self, mock_set_token, tushare_config, mock_tushare_pro):
        """Test symbol validation for stocks."""
        mock_df = pd.DataFrame({
            'ts_code': ['000001.SZ'],
            'name': ['平安银行'],
            'list_status': ['L']
        })
        mock_tushare_pro.stock_basic.return_value = mock_df
        
        adapter = TushareAdapter(tushare_config)
        
        assert adapter.validate_symbol('000001.SZ') is True
        
        mock_tushare_pro.stock_basic.assert_called_once_with(
            ts_code='000001.SZ',
            fields='ts_code,name,list_status'
        )
    
    @patch('tushare.set_token')
    def test_validate_symbol_index(self, mock_set_token, tushare_config, mock_tushare_pro):
        """Test symbol validation for indices."""
        adapter = TushareAdapter(tushare_config)
        
        # Major indices should be valid without API call
        assert adapter.validate_symbol('000001.SH') is True
        assert adapter.validate_symbol('399001.SZ') is True
        
        # Should not call API for major indices
        mock_tushare_pro.stock_basic.assert_not_called()
    
    @patch('tushare.set_token')
    def test_get_market_info_stock(self, mock_set_token, tushare_config, mock_tushare_pro):
        """Test getting market info for stocks."""
        mock_df = pd.DataFrame({
            'ts_code': ['000001.SZ'],
            'symbol': ['000001'],
            'name': ['平安银行'],
            'area': ['深圳'],
            'industry': ['银行'],
            'market': ['主板'],
            'list_date': ['19910403'],
            'list_status': ['L']
        })
        mock_tushare_pro.stock_basic.return_value = mock_df
        
        adapter = TushareAdapter(tushare_config)
        adapter.validate_symbol = Mock(return_value=True)
        
        market_info = adapter.get_market_info('000001.SZ')
        
        assert isinstance(market_info, MarketInfo)
        assert market_info.symbol == '000001.SZ'
        assert market_info.name == '平安银行'
        assert market_info.market == 'CN'
        assert market_info.exchange == 'SZSE'
        assert market_info.currency == 'CNY'
        assert market_info.lot_size == 100.0  # A股最小交易单位
        assert market_info.tick_size == 0.01
        assert market_info.timezone == 'Asia/Shanghai'
    
    @patch('tushare.set_token')
    def test_get_market_info_index(self, mock_set_token, tushare_config, mock_tushare_pro):
        """Test getting market info for indices."""
        adapter = TushareAdapter(tushare_config)
        adapter.validate_symbol = Mock(return_value=True)
        
        market_info = adapter.get_market_info('000001.SH')
        
        assert isinstance(market_info, MarketInfo)
        assert market_info.symbol == '000001.SH'
        assert market_info.name == '上证指数'
        assert market_info.market == 'CN'
        assert market_info.exchange == 'SSE'
        assert market_info.lot_size == 1.0  # 指数没有最小交易单位
    
    @patch('tushare.set_token')
    def test_get_trading_calendar(self, mock_set_token, tushare_config, mock_tushare_pro):
        """Test getting trading calendar."""
        mock_df = pd.DataFrame({
            'exchange': ['SSE', 'SSE', 'SSE', 'SSE'],
            'cal_date': ['20240101', '20240102', '20240103', '20240104'],
            'is_open': [0, 1, 1, 0]  # 1月1日和4日不开市
        })
        mock_tushare_pro.trade_cal.return_value = mock_df
        
        adapter = TushareAdapter(tushare_config)
        
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 4)
        
        trading_days = adapter.get_trading_calendar(start_date, end_date)
        
        assert len(trading_days) == 2  # 只有1月2日和3日开市
        assert trading_days[0].day == 2
        assert trading_days[1].day == 3
        
        mock_tushare_pro.trade_cal.assert_called_once_with(
            exchange='SSE',
            start_date='20240101',
            end_date='20240104'
        )
    
    @patch('tushare.set_token')
    def test_fetch_financial_data(self, mock_set_token, tushare_config, mock_tushare_pro):
        """Test fetching financial data."""
        mock_df = pd.DataFrame({
            'ts_code': ['000001.SZ'],
            'end_date': ['20231231'],
            'roe': [12.5],
            'roa': [1.2],
            'eps': [1.85]
        })
        mock_tushare_pro.fina_indicator.return_value = mock_df
        
        adapter = TushareAdapter(tushare_config)
        adapter.validate_symbol = Mock(return_value=True)
        
        financial_data = adapter.fetch_financial_data('000001.SZ')
        
        assert isinstance(financial_data, pd.DataFrame)
        assert len(financial_data) == 1
        assert financial_data.iloc[0]['ts_code'] == '000001.SZ'
        
        mock_tushare_pro.fina_indicator.assert_called_once_with(
            ts_code='000001.SZ',
            period='20231231'
        )
    
    @patch('tushare.set_token')
    def test_search_stocks(self, mock_set_token, tushare_config, mock_tushare_pro, sample_stock_basic):
        """Test searching stocks."""
        mock_tushare_pro.stock_basic.return_value = sample_stock_basic
        
        adapter = TushareAdapter(tushare_config)
        
        # Search by name
        results = adapter.search_stocks('银行', limit=5)
        
        assert len(results) == 2  # 平安银行 and 浦发银行
        assert any(result['name'] == '平安银行' for result in results)
        assert any(result['name'] == '浦发银行' for result in results)
        
        # Search by code
        results = adapter.search_stocks('000001', limit=5)
        
        assert len(results) == 1
        assert results[0]['symbol'] == '000001.SZ'
        assert results[0]['name'] == '平安银行'
    
    @patch('tushare.set_token')
    def test_get_stock_list_by_exchange(self, mock_set_token, tushare_config, mock_tushare_pro):
        """Test getting stock list by exchange."""
        mock_df = pd.DataFrame({
            'ts_code': ['000001.SZ', '000002.SZ'],
            'symbol': ['000001', '000002'],
            'name': ['平安银行', '万科A']
        })
        mock_tushare_pro.stock_basic.return_value = mock_df
        
        adapter = TushareAdapter(tushare_config)
        
        stocks = adapter.get_stock_list_by_exchange('SZSE')
        
        assert len(stocks) == 2
        assert '000001.SZ' in stocks
        assert '000002.SZ' in stocks
        
        mock_tushare_pro.stock_basic.assert_called_once_with(
            exchange='SZSE',
            list_status='L',
            fields='ts_code,symbol,name'
        )
    
    @patch('tushare.set_token')
    def test_market_properties(self, mock_set_token, tushare_config, mock_tushare_pro):
        """Test market-related properties."""
        adapter = TushareAdapter(tushare_config)
        
        assert adapter.get_market_type() == 'CN'
        assert adapter.get_exchange_name() == 'SSE'
        assert adapter.get_default_currency() == 'CNY'
        
        market_hours = adapter.get_market_hours()
        assert market_hours['morning_open'] == '09:30'
        assert market_hours['morning_close'] == '11:30'
        assert market_hours['afternoon_open'] == '13:00'
        assert market_hours['afternoon_close'] == '15:00'
        assert market_hours['timezone'] == 'Asia/Shanghai'
        
        intervals = adapter.get_supported_intervals()
        assert '1d' in intervals
        assert '1w' in intervals
        assert '1M' in intervals
    
    @patch('tushare.set_token')
    def test_health_check_success(self, mock_set_token, tushare_config, mock_tushare_pro, sample_index_data):
        """Test successful health check."""
        mock_tushare_pro.index_daily.return_value = sample_index_data
        
        adapter = TushareAdapter(tushare_config)
        
        is_healthy, message = adapter.health_check()
        
        assert is_healthy is True
        assert "Health check passed" in message
        assert adapter.is_healthy is True
        assert adapter.last_error is None
    
    @patch('tushare.set_token')
    def test_health_check_failure(self, mock_set_token, tushare_config, mock_tushare_pro):
        """Test failed health check."""
        mock_tushare_pro.index_daily.side_effect = Exception("API error")
        
        adapter = TushareAdapter(tushare_config)
        
        is_healthy, message = adapter.health_check()
        
        assert is_healthy is False
        assert "Health check failed" in message
        assert adapter.is_healthy is False
        assert adapter.last_error is not None
    
    @patch('tushare.set_token')
    def test_exchange_detection(self, mock_set_token, tushare_config, mock_tushare_pro):
        """Test exchange detection from symbol."""
        adapter = TushareAdapter(tushare_config)
        
        assert adapter._get_exchange_from_symbol('000001.SZ') == 'SZSE'
        assert adapter._get_exchange_from_symbol('600000.SH') == 'SSE'
        assert adapter._get_exchange_from_symbol('000001.SH') == 'SSE'
        assert adapter._get_exchange_from_symbol('invalid') == 'UNKNOWN'
    
    @patch('tushare.set_token')
    def test_is_index_symbol(self, mock_set_token, tushare_config, mock_tushare_pro):
        """Test index symbol detection."""
        adapter = TushareAdapter(tushare_config)
        
        assert adapter._is_index_symbol('000001.SH') is True  # 上证指数
        assert adapter._is_index_symbol('399001.SZ') is True  # 深证成指
        assert adapter._is_index_symbol('000001.SZ') is False  # 这是股票代码，不是指数
        assert adapter._is_index_symbol('AAPL') is False
    
    @patch('tushare.set_token')
    def test_empty_data_handling(self, mock_set_token, tushare_config, mock_tushare_pro):
        """Test handling of empty data responses."""
        mock_tushare_pro.daily.return_value = pd.DataFrame()
        
        adapter = TushareAdapter(tushare_config)
        adapter.validate_symbol = Mock(return_value=True)
        
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 3)
        
        result = adapter.fetch_historical_data('000001.SZ', start_date, end_date)
        
        assert result == []
    
    @patch('tushare.set_token')
    def test_invalid_symbol_handling(self, mock_set_token, tushare_config, mock_tushare_pro):
        """Test handling of invalid symbols."""
        adapter = TushareAdapter(tushare_config)
        adapter.validate_symbol = Mock(return_value=False)
        
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 3)
        
        with pytest.raises(DataException, match="Invalid symbol"):
            adapter.fetch_historical_data('INVALID', start_date, end_date)
    
    @patch('tushare.set_token')
    def test_caching_behavior(self, mock_set_token, tushare_config, mock_tushare_pro, sample_stock_basic):
        """Test caching behavior for symbol validation and market info."""
        mock_tushare_pro.stock_basic.return_value = sample_stock_basic
        
        adapter = TushareAdapter(tushare_config)
        
        # First call should hit the API
        symbols1 = adapter.get_available_symbols()
        assert mock_tushare_pro.stock_basic.call_count == 1
        
        # Second call should use cache
        symbols2 = adapter.get_available_symbols()
        assert mock_tushare_pro.stock_basic.call_count == 1  # No additional calls
        
        assert symbols1 == symbols2


if __name__ == '__main__':
    pytest.main([__file__])