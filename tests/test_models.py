"""
Unit tests for core data models.
"""

import pytest
from datetime import datetime
from src.models.market_data import MarketData, UnifiedMarketData, EconomicData, MarketInfo
from src.models.trading import Order, OrderFill, Trade, Signal, OrderSide, OrderType, SignalAction
from src.models.portfolio import <PERSON><PERSON>lio, Position
from src.exceptions import TradingSystemException


class TestMarketData:
    """Test MarketData model."""
    
    def test_market_data_creation(self):
        """Test creating a MarketData instance."""
        data = MarketData(
            symbol="AAPL",
            timestamp=datetime(2023, 1, 1),
            open=150.0,
            high=155.0,
            low=149.0,
            close=154.0,
            volume=1000000
        )
        
        assert data.symbol == "AAPL"
        assert data.open == 150.0
        assert data.high == 155.0
        assert data.low == 149.0
        assert data.close == 154.0
        assert data.volume == 1000000
    
    def test_market_data_validation(self):
        """Test MarketData validation."""
        # Valid data
        valid_data = MarketData(
            symbol="AAPL",
            timestamp=datetime(2023, 1, 1),
            open=150.0,
            high=155.0,
            low=149.0,
            close=154.0,
            volume=1000000
        )
        assert valid_data.validate() == True
        
        # Invalid data - high < close
        invalid_data = MarketData(
            symbol="AAPL",
            timestamp=datetime(2023, 1, 1),
            open=150.0,
            high=150.0,  # High should be >= close
            low=149.0,
            close=154.0,
            volume=1000000
        )
        assert invalid_data.validate() == False


class TestTradingModels:
    """Test trading-related models."""
    
    def test_order_creation(self):
        """Test creating an Order instance."""
        order = Order(
            id="order_1",
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100
        )
        
        assert order.id == "order_1"
        assert order.symbol == "AAPL"
        assert order.side == OrderSide.BUY
        assert order.order_type == OrderType.MARKET
        assert order.quantity == 100
    
    def test_trade_creation(self):
        """Test creating a Trade instance."""
        trade = Trade(
            id="trade_1",
            symbol="AAPL",
            side=OrderSide.BUY,
            quantity=100,
            price=150.0,
            timestamp=datetime(2023, 1, 1),
            commission=1.0
        )
        
        assert trade.id == "trade_1"
        assert trade.symbol == "AAPL"
        assert trade.side == OrderSide.BUY
        assert trade.quantity == 100
        assert trade.price == 150.0
        assert trade.commission == 1.0
    
    def test_signal_creation(self):
        """Test creating a Signal instance."""
        signal = Signal(
            symbol="AAPL",
            action=SignalAction.BUY,
            quantity=100,
            confidence=0.8
        )
        
        assert signal.symbol == "AAPL"
        assert signal.action == SignalAction.BUY
        assert signal.quantity == 100
        assert signal.confidence == 0.8


class TestPortfolioModels:
    """Test portfolio and position models."""
    
    def test_position_creation(self):
        """Test creating a Position instance."""
        position = Position(
            symbol="AAPL",
            quantity=100,
            avg_price=150.0
        )
        
        assert position.symbol == "AAPL"
        assert position.quantity == 100
        assert position.avg_price == 150.0
    
    def test_position_market_value_update(self):
        """Test updating position market value."""
        position = Position(
            symbol="AAPL",
            quantity=100,
            avg_price=150.0
        )
        
        position.update_market_value(160.0)
        
        assert position.last_price == 160.0
        assert position.market_value == 16000.0  # 100 * 160
        assert position.unrealized_pnl == 1000.0  # (160 - 150) * 100
    
    def test_portfolio_creation(self):
        """Test creating a Portfolio instance."""
        portfolio = Portfolio(
            initial_capital=100000.0,
            cash=100000.0
        )
        
        assert portfolio.initial_capital == 100000.0
        assert portfolio.cash == 100000.0
        assert portfolio.total_value == 100000.0
        assert len(portfolio.positions) == 0
    
    def test_portfolio_add_trade(self):
        """Test adding a trade to portfolio."""
        portfolio = Portfolio(
            initial_capital=100000.0,
            cash=100000.0
        )
        
        # Add a buy trade
        buy_trade = Trade(
            id="trade_1",
            symbol="AAPL",
            side=OrderSide.BUY,
            quantity=100,
            price=150.0,
            timestamp=datetime(2023, 1, 1),
            commission=1.0
        )
        
        portfolio.add_trade(buy_trade)
        
        # Check cash reduction
        expected_cash = 100000.0 - (100 * 150.0 + 1.0)  # 100000 - 15001
        assert portfolio.cash == expected_cash
        
        # Check position creation
        assert "AAPL" in portfolio.positions
        position = portfolio.positions["AAPL"]
        assert position.quantity == 100
        assert position.avg_price == 150.0
    
    def test_portfolio_total_return(self):
        """Test portfolio total return calculation."""
        portfolio = Portfolio(
            initial_capital=100000.0,
            cash=90000.0
        )
        portfolio.total_value = 110000.0
        
        total_return = portfolio.get_total_return()
        assert total_return == 0.1  # 10% return


class TestExceptions:
    """Test exception classes."""
    
    def test_trading_system_exception(self):
        """Test base TradingSystemException."""
        error = TradingSystemException(
            message="Test error",
            error_code="TEST_001",
            details={"context": "unit_test"}
        )
        
        assert error.message == "Test error"
        assert error.error_code == "TEST_001"
        assert error.details["context"] == "unit_test"
        
        error_dict = error.to_dict()
        assert error_dict["error_type"] == "TradingSystemException"
        assert error_dict["message"] == "Test error"
        assert error_dict["error_code"] == "TEST_001"


if __name__ == "__main__":
    pytest.main([__file__])