"""
示例策略测试
"""

import unittest
from datetime import datetime
from unittest.mock import Mock, MagicMock
import pandas as pd
import numpy as np

from src.strategies.base import StrategyContext
from src.strategies.examples import (
    MovingAverageCrossoverStrategy,
    RSIMeanReversionStrategy,
    MACDTrendFollowingStrategy,
    MultiFactorStrategy
)
from src.strategies.signals import SignalType
from src.models.market_data import MarketData


class TestExampleStrategies(unittest.TestCase):
    """示例策略测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.mock_data_manager = Mock()
        self.mock_indicator_engine = Mock()
        self.mock_portfolio = Mock()
        
        self.context = StrategyContext(
            data_manager=self.mock_data_manager,
            indicator_engine=self.mock_indicator_engine,
            portfolio=self.mock_portfolio
        )
        
        # 模拟历史数据
        self.mock_historical_data = pd.DataFrame({
            'open': np.random.uniform(100, 110, 100),
            'high': np.random.uniform(105, 115, 100),
            'low': np.random.uniform(95, 105, 100),
            'close': np.random.uniform(100, 110, 100),
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        self.mock_data_manager.get_historical_data.return_value = self.mock_historical_data
        
        # 模拟当前市场数据
        self.current_data = MarketData(
            symbol='AAPL',
            timestamp=datetime.now(),
            open=105.0,
            high=107.0,
            low=104.0,
            close=106.0,
            volume=5000
        )
    
    def test_moving_average_crossover_strategy(self):
        """测试移动平均交叉策略"""
        strategy = MovingAverageCrossoverStrategy('MA_Test')
        
        # 测试参数定义
        param_defs = strategy.get_parameter_definitions()
        self.assertIn('fast_period', param_defs)
        self.assertIn('slow_period', param_defs)
        self.assertIn('ma_type', param_defs)
        
        # 测试初始化
        strategy.initialize(self.context)
        self.assertTrue(strategy.is_initialized)
        
        # 模拟移动平均指标
        self.mock_indicator_engine.calculate.side_effect = [
            pd.Series([105.5] * 10),  # 快速MA
            pd.Series([104.0] * 10)   # 慢速MA
        ]
        
        # 设置历史MA数据以检测交叉
        strategy.last_fast_ma = 103.5
        strategy.last_slow_ma = 104.0
        
        # 处理数据
        signals = strategy.on_data(self.current_data)
        
        # 应该生成买入信号（快线上穿慢线）
        self.assertEqual(len(signals), 1)
        self.assertEqual(signals[0].signal_type, SignalType.BUY)
        self.assertEqual(signals[0].symbol, 'AAPL')
        self.assertIsNotNone(signals[0].stop_loss)
        self.assertIsNotNone(signals[0].take_profit)
    
    def test_rsi_mean_reversion_strategy(self):
        """测试RSI均值回归策略"""
        strategy = RSIMeanReversionStrategy('RSI_Test')
        
        # 测试参数定义
        param_defs = strategy.get_parameter_definitions()
        self.assertIn('rsi_period', param_defs)
        self.assertIn('oversold_threshold', param_defs)
        self.assertIn('overbought_threshold', param_defs)
        
        # 测试初始化
        strategy.initialize(self.context)
        self.assertTrue(strategy.is_initialized)
        
        # 模拟RSI指标 - 超卖状态
        self.mock_indicator_engine.calculate.return_value = pd.Series([25.0])  # 超卖
        
        # 模拟无持仓
        self.context.get_position = Mock(return_value=0)
        
        # 处理数据
        signals = strategy.on_data(self.current_data)
        
        # 应该生成买入信号
        self.assertEqual(len(signals), 1)
        self.assertEqual(signals[0].signal_type, SignalType.BUY)
        
        # 测试超买情况
        self.mock_indicator_engine.calculate.return_value = pd.Series([75.0])  # 超买
        signals = strategy.on_data(self.current_data)
        
        # 应该生成卖出信号
        self.assertEqual(len(signals), 1)
        self.assertEqual(signals[0].signal_type, SignalType.SELL)
    
    def test_macd_trend_following_strategy(self):
        """测试MACD趋势跟踪策略"""
        strategy = MACDTrendFollowingStrategy('MACD_Test')
        
        # 测试参数定义
        param_defs = strategy.get_parameter_definitions()
        self.assertIn('fast_period', param_defs)
        self.assertIn('slow_period', param_defs)
        self.assertIn('signal_period', param_defs)
        
        # 测试初始化
        strategy.initialize(self.context)
        self.assertTrue(strategy.is_initialized)
        
        # 模拟MACD指标
        mock_macd_data = pd.DataFrame({
            'MACD': [0.5],
            'Signal': [0.3],
            'Histogram': [0.2]
        })
        self.mock_indicator_engine.calculate.return_value = mock_macd_data
        
        # 设置历史MACD数据以检测交叉
        strategy.last_macd = -0.1
        strategy.last_signal_line = 0.3
        strategy.last_histogram = -0.4
        
        # 处理数据
        signals = strategy.on_data(self.current_data)
        
        # 应该生成买入信号（MACD上穿信号线）
        self.assertEqual(len(signals), 1)
        self.assertEqual(signals[0].signal_type, SignalType.BUY)
    
    def test_multi_factor_strategy(self):
        """测试多因子组合策略"""
        strategy = MultiFactorStrategy('MultiFactor_Test')
        
        # 测试参数定义
        param_defs = strategy.get_parameter_definitions()
        self.assertIn('ma_weight', param_defs)
        self.assertIn('rsi_weight', param_defs)
        self.assertIn('macd_weight', param_defs)
        self.assertIn('bb_weight', param_defs)
        
        # 测试初始化
        strategy.initialize(self.context)
        self.assertTrue(strategy.is_initialized)
        
        # 模拟各种指标
        def mock_indicator_side_effect(name, data, **kwargs):
            if name == 'SMA':
                if kwargs.get('period') == 10:  # 快速MA
                    return pd.Series([106.0])
                else:  # 慢速MA
                    return pd.Series([104.0])
            elif name == 'RSI':
                return pd.Series([25.0])  # 超卖
            elif name == 'MACD':
                return pd.DataFrame({
                    'MACD': [0.5],
                    'Signal': [0.3],
                    'Histogram': [0.2]
                })
            elif name == 'BOLLINGER_BANDS':
                return pd.DataFrame({
                    'Upper': [108.0],
                    'Lower': [102.0],
                    'Middle': [105.0]
                })
            return pd.Series([0])
        
        self.mock_indicator_engine.calculate.side_effect = mock_indicator_side_effect
        
        # 处理数据
        signals = strategy.on_data(self.current_data)
        
        # 应该生成信号（多个指标都偏向买入）
        self.assertEqual(len(signals), 1)
        self.assertEqual(signals[0].signal_type, SignalType.BUY)
        
        # 验证元数据
        metadata = signals[0].metadata
        self.assertIn('signal_strength', metadata)
        self.assertIn('indicators', metadata)
        self.assertIn('weights', metadata)
    
    def test_strategy_parameter_validation(self):
        """测试策略参数验证"""
        # 测试移动平均策略的参数验证
        strategy = MovingAverageCrossoverStrategy('MA_Test', {
            'fast_period': 20,
            'slow_period': 10  # 错误：快速周期大于慢速周期
        })
        
        with self.assertRaises(ValueError):
            strategy.initialize(self.context)
        
        # 测试RSI策略的参数验证 - 使用有效的参数值但逻辑错误
        strategy = RSIMeanReversionStrategy('RSI_Test', {
            'oversold_threshold': 35,
            'overbought_threshold': 65,  # 有效值
            'neutral_exit_lower': 50,    # 错误：中性区域下限大于上限
            'neutral_exit_upper': 45
        })
        
        with self.assertRaises(ValueError):
            strategy.initialize(self.context)
    
    def test_strategy_info_methods(self):
        """测试策略信息方法"""
        strategies = [
            MovingAverageCrossoverStrategy('MA_Test'),
            RSIMeanReversionStrategy('RSI_Test'),
            MACDTrendFollowingStrategy('MACD_Test'),
            MultiFactorStrategy('MultiFactor_Test')
        ]
        
        for strategy in strategies:
            strategy.initialize(self.context)
            
            # 测试策略状态
            status = strategy.get_status()
            self.assertIn('name', status)
            self.assertIn('is_initialized', status)
            self.assertTrue(status['is_initialized'])
            
            # 测试策略信息
            info = strategy.get_strategy_info()
            self.assertIn('name', info)
            self.assertIn('type', info)
            self.assertIn('description', info)
    
    def test_signal_metadata(self):
        """测试信号元数据"""
        strategy = MovingAverageCrossoverStrategy('MA_Test')
        strategy.initialize(self.context)
        
        # 模拟指标数据
        self.mock_indicator_engine.calculate.side_effect = [
            pd.Series([106.0]),  # 快速MA
            pd.Series([104.0])   # 慢速MA
        ]
        
        # 设置历史数据
        strategy.last_fast_ma = 103.0
        strategy.last_slow_ma = 104.0
        
        signals = strategy.on_data(self.current_data)
        
        if signals:
            signal = signals[0]
            metadata = signal.metadata
            
            # 验证元数据包含必要信息
            self.assertIn('fast_ma', metadata)
            self.assertIn('slow_ma', metadata)
            self.assertIn('crossover_strength', metadata)
            self.assertIn('ma_type', metadata)
    
    def test_error_handling(self):
        """测试错误处理"""
        strategy = MovingAverageCrossoverStrategy('MA_Test')
        strategy.initialize(self.context)
        
        # 模拟数据管理器异常
        self.mock_data_manager.get_historical_data.side_effect = Exception("数据获取失败")
        
        # 处理数据不应该抛出异常
        signals = strategy.on_data(self.current_data)
        self.assertEqual(len(signals), 0)  # 应该返回空列表
        
        # 模拟指标计算异常
        self.mock_data_manager.get_historical_data.side_effect = None
        self.mock_data_manager.get_historical_data.return_value = self.mock_historical_data
        self.mock_indicator_engine.calculate.side_effect = Exception("指标计算失败")
        
        signals = strategy.on_data(self.current_data)
        self.assertEqual(len(signals), 0)  # 应该返回空列表


if __name__ == '__main__':
    unittest.main()