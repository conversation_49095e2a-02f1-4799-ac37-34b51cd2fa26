"""
Integration tests for the complete backtesting system.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.backtest.engine import BacktestEngine
from src.backtest.executor import ExecutionConfig
from src.strategies.base import BaseStrategy
from src.strategies.parameters import StrategyParameter, ParameterType
from src.models.trading import Signal, SignalAction
from src.models.market_data import MarketData


class SimpleMovingAverageStrategy(BaseStrategy):
    """Simple moving average crossover strategy for testing."""
    
    def get_parameter_definitions(self):
        return {
            'short_window': StrategyParameter(
                name='short_window',
                param_type=ParameterType.INTEGER,
                default_value=5,
                description='Short MA window'
            ),
            'long_window': StrategyParameter(
                name='long_window',
                param_type=ParameterType.INTEGER,
                default_value=20,
                description='Long MA window'
            ),
            'position_size': StrategyParameter(
                name='position_size',
                param_type=ParameterType.FLOAT,
                default_value=1000.0,
                description='Position size in dollars'
            )
        }
    
    def initialize(self, context):
        self.context = context
        self.is_initialized = True
        self.position = 0
    
    def on_data(self, data):
        if not isinstance(data, MarketData):
            return []
        
        short_window = self.get_parameter('short_window', 5)
        long_window = self.get_parameter('long_window', 20)
        position_size = self.get_parameter('position_size', 1000.0)
        
        # Get historical data
        historical_data = self.context.get_historical_data(
            data.symbol, 
            long_window + 1,
            data.timestamp
        )
        
        if len(historical_data) < long_window + 1:
            return []
        
        # Calculate moving averages
        prices = historical_data['close']
        short_ma = prices.tail(short_window).mean()
        long_ma = prices.tail(long_window).mean()
        
        # Previous MAs for crossover detection
        if len(prices) >= long_window + 1:
            prev_short_ma = prices.iloc[-(short_window+1):-1].mean()
            prev_long_ma = prices.iloc[-(long_window+1):-1].mean()
        else:
            return []
        
        signals = []
        current_position = self.context.get_position(data.symbol)
        quantity = int(position_size / data.close)
        
        # Golden cross - buy signal
        if (short_ma > long_ma and prev_short_ma <= prev_long_ma and 
            current_position <= 0 and quantity > 0):
            signals.append(Signal(
                symbol=data.symbol,
                action=SignalAction.BUY,
                quantity=quantity,
                timestamp=data.timestamp,
                metadata={'short_ma': short_ma, 'long_ma': long_ma}
            ))
        
        # Death cross - sell signal
        elif (short_ma < long_ma and prev_short_ma >= prev_long_ma and 
              current_position > 0):
            signals.append(Signal(
                symbol=data.symbol,
                action=SignalAction.SELL,
                quantity=min(quantity, int(current_position)),
                timestamp=data.timestamp,
                metadata={'short_ma': short_ma, 'long_ma': long_ma}
            ))
        
        return signals


def create_trending_data(symbol='TEST', days=100, trend=0.001):
    """Create trending market data for testing."""
    dates = pd.date_range('2023-01-01', periods=days, freq='D')
    
    # Create trending price with some noise
    np.random.seed(42)
    base_price = 100.0
    prices = [base_price]
    
    for i in range(1, days):
        # Add trend and random noise
        daily_return = trend + np.random.normal(0, 0.02)
        new_price = prices[-1] * (1 + daily_return)
        prices.append(new_price)
    
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        # Generate OHLC from close price
        daily_vol = 0.01
        high = price * (1 + np.random.uniform(0, daily_vol))
        low = price * (1 - np.random.uniform(0, daily_vol))
        open_price = prices[i-1] if i > 0 else price
        
        # Ensure OHLC relationships
        high = max(high, price, open_price)
        low = min(low, price, open_price)
        
        data.append({
            'symbol': symbol,
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(price, 2),
            'volume': int(np.random.uniform(100000, 1000000))
        })
    
    return pd.DataFrame(data, index=dates)


class TestBacktestIntegration:
    """Integration tests for the complete backtesting system."""
    
    def test_complete_backtest_workflow(self):
        """Test complete backtesting workflow with realistic data."""
        # Create trending data that should generate MA crossovers
        data = create_trending_data(symbol='TEST', days=60, trend=0.002)
        
        # Create backtesting engine
        execution_config = ExecutionConfig(
            commission_rate=0.001,
            slippage_rate=0.0005
        )
        
        engine = BacktestEngine(
            initial_capital=50000.0,
            execution_config=execution_config
        )
        
        # Create and add strategy
        strategy = SimpleMovingAverageStrategy(
            name="MA_Strategy",
            parameters={
                'short_window': 5,
                'long_window': 15,
                'position_size': 2000.0
            }
        )
        
        strategy_id = engine.add_strategy(strategy)
        
        # Set data and run backtest
        engine.set_data_feed(data)
        result = engine.run_backtest()
        
        # Verify basic results
        assert result is not None
        assert result.initial_capital == 50000.0
        assert result.start_date == data.index[0]
        assert result.end_date == data.index[-1]
        
        # Should have some trades due to MA crossovers
        assert len(result.trades) > 0
        assert len(result.portfolio_history) == len(data)
        
        # Verify metrics calculation
        result.calculate_metrics()
        assert result.total_return != 0
        assert result.total_trades > 0
        
        # With upward trending data, should be profitable
        assert result.final_capital != result.initial_capital
        
        # Verify portfolio progression
        assert len(result.portfolio_history) == len(data)
        
        # Check that trades have proper commission and slippage
        if result.trades:
            for trade in result.trades:
                assert trade.commission > 0  # Should have commission
                assert trade.price > 0
                assert trade.quantity > 0
    
    def test_multiple_strategies(self):
        """Test backtesting with multiple strategies."""
        data = create_trending_data(symbol='TEST', days=40, trend=0.001)
        
        engine = BacktestEngine(initial_capital=100000.0)
        
        # Add two strategies with different parameters
        strategy1 = SimpleMovingAverageStrategy(
            name="MA_Fast",
            parameters={
                'short_window': 3,
                'long_window': 10,
                'position_size': 1500.0
            }
        )
        
        strategy2 = SimpleMovingAverageStrategy(
            name="MA_Slow",
            parameters={
                'short_window': 8,
                'long_window': 20,
                'position_size': 2000.0
            }
        )
        
        engine.add_strategy(strategy1)
        engine.add_strategy(strategy2)
        
        engine.set_data_feed(data)
        result = engine.run_backtest()
        
        # Should have trades from both strategies
        assert len(result.trades) > 0
        
        # Check that trades come from different strategies
        strategy_ids = set(trade.strategy_id for trade in result.trades)
        assert len(strategy_ids) >= 1  # At least one strategy should trade
    
    def test_backtest_with_date_range(self):
        """Test backtesting with specific date range."""
        data = create_trending_data(symbol='TEST', days=50, trend=0.001)
        
        engine = BacktestEngine(initial_capital=50000.0)
        strategy = SimpleMovingAverageStrategy(name="MA_Strategy")
        engine.add_strategy(strategy)
        engine.set_data_feed(data)
        
        # Test with date range
        start_date = data.index[10]
        end_date = data.index[40]
        
        result = engine.run_backtest(start_date=start_date, end_date=end_date)
        
        assert result.start_date == start_date
        assert result.end_date == end_date
        assert len(result.portfolio_history) == 31  # 40-10+1
    
    def test_backtest_performance_metrics(self):
        """Test that performance metrics are calculated correctly."""
        # Create data with known characteristics
        data = create_trending_data(symbol='TEST', days=50, trend=0.005)  # Strong uptrend
        
        engine = BacktestEngine(initial_capital=100000.0)
        strategy = SimpleMovingAverageStrategy(
            name="MA_Strategy",
            parameters={'position_size': 5000.0}
        )
        engine.add_strategy(strategy)
        engine.set_data_feed(data)
        
        result = engine.run_backtest()
        result.calculate_metrics()
        
        # With strong uptrend and MA strategy, should be profitable
        if result.total_trades > 0:
            # Basic sanity checks
            assert -1 <= result.total_return <= 10  # Reasonable return range
            assert result.volatility >= 0
            assert -1 <= result.max_drawdown <= 0  # Drawdown should be negative
            
            # If profitable, should have positive Sharpe ratio
            if result.total_return > 0:
                assert result.sharpe_ratio > 0
    
    def test_backtest_error_handling(self):
        """Test error handling in backtesting."""
        engine = BacktestEngine(initial_capital=50000.0)
        
        # Test with no data
        with pytest.raises(ValueError, match="Market data feed not set"):
            engine.run_backtest()
        
        # Test with no strategies
        data = create_trending_data(symbol='TEST', days=10)
        engine.set_data_feed(data)
        
        with pytest.raises(ValueError, match="No strategies added"):
            engine.run_backtest()
        
        # Test with invalid data
        invalid_data = pd.DataFrame({'invalid': [1, 2, 3]})
        
        with pytest.raises(ValueError, match="Data must contain columns"):
            engine.set_data_feed(invalid_data)
    
    def test_backtest_progress_tracking(self):
        """Test progress tracking functionality."""
        data = create_trending_data(symbol='TEST', days=20)
        
        engine = BacktestEngine(initial_capital=50000.0)
        strategy = SimpleMovingAverageStrategy(name="MA_Strategy")
        engine.add_strategy(strategy)
        engine.set_data_feed(data)
        
        progress_updates = []
        
        def progress_callback(progress):
            progress_updates.append({
                'step': progress.current_step,
                'total': progress.total_steps,
                'pct': progress.get_progress_pct()
            })
        
        engine.set_progress_callback(progress_callback)
        result = engine.run_backtest()
        
        # Should have received progress updates
        assert len(progress_updates) == len(data)
        assert progress_updates[0]['pct'] == 5.0  # First step: 1/20 * 100
        assert progress_updates[-1]['pct'] == 100.0  # Last step: 20/20 * 100