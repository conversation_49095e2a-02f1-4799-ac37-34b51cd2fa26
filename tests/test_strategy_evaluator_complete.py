"""
策略评估和选择完整测试

测试8.3的所有功能要求：
- 编写策略表现评估算法
- 实现策略替换建议系统
- 创建策略生命周期管理
- 编写策略优化和参数调整工具
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.strategies.strategy_evaluator import (
    StrategyEvaluator, StrategySelector, ReplacementAdvisor, ParameterOptimizer, LifecycleManager,
    StrategyEvaluation, ReplacementSuggestion, ParameterOptimizationResult,
    EvaluationPeriod, LifecycleStage, ReplacementReason
)
from src.strategies.multi_strategy_manager import StrategyPortfolioManager, WeightingMethod
from src.strategies.base import BaseStrategy
from src.analysis.metrics import PerformanceMetrics

class MockStrategy(BaseStrategy):
    """模拟策略类"""
    
    def __init__(self, name: str):
        parameters = {
            'param1': 10,
            'param2': 0.5,
            'param3': 0.05
        }
        super().__init__(name, parameters)
    
    def get_parameter_definitions(self):
        from src.strategies.parameters import StrategyParameter, ParameterType
        return {
            'param1': StrategyParameter(
                name='param1',
                param_type=ParameterType.INTEGER,
                default_value=10,
                min_value=5,
                max_value=50
            ),
            'param2': StrategyParameter(
                name='param2',
                param_type=ParameterType.FLOAT,
                default_value=0.5,
                min_value=0.1,
                max_value=1.0
            ),
            'param3': StrategyParameter(
                name='param3',
                param_type=ParameterType.FLOAT,
                default_value=0.05,
                min_value=0.01,
                max_value=0.2
            )
        }
    
    def initialize(self, context):
        self.context = context
        self.is_initialized = True
    
    def on_data(self, data):
        return []


class Test8_3_1_PerformanceEvaluation:
    """测试8.3.1: 策略表现评估算法"""
    
    def test_evaluator_initialization(self):
        """测试评估器初始化"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager, EvaluationPeriod.MONTHLY, lookback_days=252)
        
        assert evaluator.portfolio_manager == portfolio_manager
        assert evaluator.evaluation_period == EvaluationPeriod.MONTHLY
        assert evaluator.lookback_days == 252
        assert 'performance' in evaluator.evaluation_weights
        assert 'risk' in evaluator.evaluation_weights
        assert 'consistency' in evaluator.evaluation_weights
        assert 'adaptability' in evaluator.evaluation_weights
        print("✅ 评估器初始化测试通过")
    
    def test_single_strategy_evaluation(self):
        """测试单个策略评估"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        
        # 添加测试策略
        strategy = MockStrategy("Test_Strategy")
        strategy_id = portfolio_manager.add_strategy(strategy, weight=1.0)
        
        # 评估策略
        evaluation = evaluator.evaluate_strategy(strategy_id)
        
        assert isinstance(evaluation, StrategyEvaluation)
        assert evaluation.strategy_id == strategy_id
        assert evaluation.strategy_name == "Test_Strategy"
        assert 0 <= evaluation.overall_score <= 100
        assert 0 <= evaluation.performance_score <= 100
        assert 0 <= evaluation.risk_score <= 100
        assert evaluation.recommendation in ["BUY", "HOLD", "REDUCE", "SELL"]
        print("✅ 单个策略评估测试通过")
    
    def test_all_strategies_evaluation(self):
        """测试所有策略评估"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        
        # 添加多个测试策略
        strategies = [
            MockStrategy("High_Performer"),
            MockStrategy("Medium_Performer"),
            MockStrategy("Low_Performer")
        ]
        
        for strategy in strategies:
            portfolio_manager.add_strategy(strategy, weight=1.0/len(strategies))
        
        # 评估所有策略
        evaluations = evaluator.evaluate_all_strategies()
        
        assert len(evaluations) == 3
        assert all(isinstance(eval, StrategyEvaluation) for eval in evaluations)
        
        # 验证排名
        for i, evaluation in enumerate(evaluations):
            assert evaluation.rank == i + 1
            assert 0 < evaluation.percentile <= 100
        
        print("✅ 所有策略评估测试通过")
    
    def test_performance_metrics_calculation(self):
        """测试绩效指标计算"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        
        strategy = MockStrategy("Test_Strategy")
        strategy_id = portfolio_manager.add_strategy(strategy, weight=1.0)
        
        # 获取策略绩效数据（模拟）
        start_date = datetime.now() - timedelta(days=252)
        end_date = datetime.now()
        
        performance_data = evaluator._get_strategy_performance_data(strategy_id, start_date, end_date)
        
        assert isinstance(performance_data, pd.Series)
        assert len(performance_data) > 0
        assert performance_data.iloc[0] > 0  # 起始价值应该为正
        
        # 测试指标计算
        metrics = evaluator._calculate_performance_metrics(performance_data)
        assert isinstance(metrics, PerformanceMetrics)
        
        print("✅ 绩效指标计算测试通过")


class Test8_3_2_ReplacementAdvisor:
    """测试8.3.2: 策略替换建议系统"""
    
    def test_replacement_advisor_initialization(self):
        """测试替换建议器初始化"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        advisor = ReplacementAdvisor(evaluator)
        
        assert advisor.evaluator == evaluator
        print("✅ 替换建议器初始化测试通过")
    
    def test_replacement_suggestions_generation(self):
        """测试替换建议生成"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        advisor = ReplacementAdvisor(evaluator)
        
        # 添加测试策略
        strategies = [
            MockStrategy("Good_Strategy"),
            MockStrategy("Poor_Strategy")
        ]
        
        for strategy in strategies:
            portfolio_manager.add_strategy(strategy, weight=0.5)
        
        # 生成替换建议
        suggestions = advisor.generate_replacement_suggestions(
            performance_threshold=60.0,  # 较高阈值，可能触发替换建议
            risk_threshold=50.0
        )
        
        assert isinstance(suggestions, list)
        # 由于使用模拟数据，可能会有替换建议
        for suggestion in suggestions:
            assert isinstance(suggestion, ReplacementSuggestion)
            assert suggestion.target_strategy_id is not None
            assert suggestion.reason in ReplacementReason
            assert suggestion.urgency in ["low", "medium", "high", "critical"]
            assert 0 <= suggestion.confidence <= 1.0
        
        print("✅ 替换建议生成测试通过")
    
    def test_replacement_analysis(self):
        """测试替换分析逻辑"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        advisor = ReplacementAdvisor(evaluator)
        
        strategy = MockStrategy("Test_Strategy")
        strategy_id = portfolio_manager.add_strategy(strategy, weight=1.0)
        
        # 获取评估结果
        evaluation = evaluator.evaluate_strategy(strategy_id)
        
        # 测试替换分析
        suggestion = advisor._analyze_replacement_need(
            evaluation,
            performance_threshold=80.0,  # 设置高阈值
            risk_threshold=80.0
        )
        
        # 由于模拟数据可能不会达到很高的分数，可能会有替换建议
        if suggestion:
            assert isinstance(suggestion, ReplacementSuggestion)
            assert suggestion.target_strategy_id == strategy_id
        
        print("✅ 替换分析逻辑测试通过")


class Test8_3_3_LifecycleManager:
    """测试8.3.3: 策略生命周期管理"""
    
    def test_lifecycle_manager_initialization(self):
        """测试生命周期管理器初始化"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        lifecycle_manager = LifecycleManager(evaluator)
        
        assert lifecycle_manager.evaluator == evaluator
        assert len(lifecycle_manager.strategy_stages) == 0
        assert len(lifecycle_manager.stage_history) == 0
        print("✅ 生命周期管理器初始化测试通过")
    
    def test_strategy_stage_updates(self):
        """测试策略阶段更新"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        lifecycle_manager = LifecycleManager(evaluator)
        
        strategy_id = "test_strategy"
        
        # 更新策略阶段
        lifecycle_manager.update_strategy_stage(
            strategy_id, 
            LifecycleStage.TESTING, 
            "开始测试阶段"
        )
        
        assert lifecycle_manager.get_strategy_stage(strategy_id) == LifecycleStage.TESTING
        assert strategy_id in lifecycle_manager.stage_history
        assert len(lifecycle_manager.stage_history[strategy_id]) == 1
        print("✅ 策略阶段更新测试通过")
    
    def test_stage_duration_tracking(self):
        """测试阶段持续时间跟踪"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        lifecycle_manager = LifecycleManager(evaluator)
        
        strategy_id = "test_strategy"
        
        # 更新策略阶段
        lifecycle_manager.update_strategy_stage(
            strategy_id, 
            LifecycleStage.DEVELOPMENT
        )
        
        # 获取持续时间
        duration = lifecycle_manager.get_stage_duration(strategy_id)
        
        assert isinstance(duration, timedelta)
        assert duration.total_seconds() >= 0
        print("✅ 阶段持续时间跟踪测试通过")
    
    def test_stage_transition_recommendations(self):
        """测试阶段转换建议"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        lifecycle_manager = LifecycleManager(evaluator)
        
        # 添加测试策略
        strategy = MockStrategy("Test_Strategy")
        strategy_id = portfolio_manager.add_strategy(strategy, weight=1.0)
        
        # 设置开发阶段
        lifecycle_manager.update_strategy_stage(strategy_id, LifecycleStage.DEVELOPMENT)
        
        # 模拟长时间开发（通过修改历史记录）
        past_time = datetime.now() - timedelta(days=35)
        lifecycle_manager.stage_history[strategy_id] = [(LifecycleStage.DEVELOPMENT, past_time)]
        
        # 获取阶段转换建议
        recommendation = lifecycle_manager.recommend_stage_transition(strategy_id)
        
        # 由于模拟了35天的开发时间，应该建议进入测试阶段
        if recommendation:
            assert recommendation == LifecycleStage.TESTING
        
        print("✅ 阶段转换建议测试通过")


class Test8_3_4_ParameterOptimizer:
    """测试8.3.4: 策略优化和参数调整工具"""
    
    def test_parameter_optimizer_initialization(self):
        """测试参数优化器初始化"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        optimizer = ParameterOptimizer(evaluator)
        
        assert optimizer.evaluator == evaluator
        print("✅ 参数优化器初始化测试通过")
    
    def test_grid_search_optimization(self):
        """测试网格搜索优化"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        optimizer = ParameterOptimizer(evaluator)
        
        # 添加测试策略
        strategy = MockStrategy("Test_Strategy")
        strategy_id = portfolio_manager.add_strategy(strategy, weight=1.0)
        
        # 定义参数范围
        parameter_ranges = {
            'param1': (5, 25),
            'param2': (0.2, 0.8)
        }
        
        # 运行网格搜索优化
        result = optimizer.optimize_strategy_parameters(
            strategy_id,
            parameter_ranges,
            optimization_method="grid_search",
            max_iterations=20
        )
        
        assert isinstance(result, ParameterOptimizationResult)
        assert result.strategy_id == strategy_id
        assert 'param1' in result.original_parameters
        assert 'param2' in result.original_parameters
        assert 'param1' in result.optimized_parameters
        assert 'param2' in result.optimized_parameters
        assert result.optimization_method == "grid_search"
        
        # 验证参数在指定范围内
        assert 5 <= result.optimized_parameters['param1'] <= 25
        assert 0.2 <= result.optimized_parameters['param2'] <= 0.8
        
        print("✅ 网格搜索优化测试通过")
    
    def test_random_search_optimization(self):
        """测试随机搜索优化"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        optimizer = ParameterOptimizer(evaluator)
        
        # 添加测试策略
        strategy = MockStrategy("Test_Strategy")
        strategy_id = portfolio_manager.add_strategy(strategy, weight=1.0)
        
        # 定义参数范围
        parameter_ranges = {
            'param2': (0.1, 1.0),
            'param3': (0.01, 0.1)
        }
        
        # 运行随机搜索优化
        result = optimizer.optimize_strategy_parameters(
            strategy_id,
            parameter_ranges,
            optimization_method="random_search",
            max_iterations=15
        )
        
        assert isinstance(result, ParameterOptimizationResult)
        assert result.strategy_id == strategy_id
        assert result.optimization_method == "random_search"
        assert isinstance(result.performance_improvement, float)
        
        # 验证参数在指定范围内
        assert 0.1 <= result.optimized_parameters['param2'] <= 1.0
        assert 0.01 <= result.optimized_parameters['param3'] <= 0.1
        
        print("✅ 随机搜索优化测试通过")
    
    def test_parameter_evaluation(self):
        """测试参数评估"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        optimizer = ParameterOptimizer(evaluator)
        
        # 添加测试策略
        strategy = MockStrategy("Test_Strategy")
        strategy_id = portfolio_manager.add_strategy(strategy, weight=1.0)
        
        # 测试参数评估
        test_parameters = {'param1': 15, 'param2': 0.6}
        score = optimizer._evaluate_parameters(strategy_id, test_parameters)
        
        assert isinstance(score, float)
        assert 0 <= score <= 100
        print("✅ 参数评估测试通过")


class Test8_3_Integration:
    """测试8.3集成功能"""
    
    def test_strategy_selection_workflow(self):
        """测试策略选择工作流"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        evaluator = StrategyEvaluator(portfolio_manager)
        selector = StrategySelector(evaluator, max_strategies=5, min_score_threshold=40.0)
        
        # 添加多个测试策略
        strategies = [
            MockStrategy("Strategy_A"),
            MockStrategy("Strategy_B"),
            MockStrategy("Strategy_C"),
            MockStrategy("Strategy_D")
        ]
        
        for strategy in strategies:
            portfolio_manager.add_strategy(strategy, weight=0.25)
        
        # 选择最优策略
        selected_strategies = selector.select_optimal_strategies(
            diversification_constraint=True
        )
        
        assert isinstance(selected_strategies, list)
        assert len(selected_strategies) <= 5  # 不超过最大策略数
        assert all(sid in portfolio_manager.strategies for sid in selected_strategies)
        
        print("✅ 策略选择工作流测试通过")
    
    def test_comprehensive_evaluation_workflow(self):
        """测试综合评估工作流"""
        print("开始综合评估工作流测试...")
        
        # 1. 设置系统组件
        portfolio_manager = StrategyPortfolioManager(
            initial_capital=1000000.0,
            weighting_method=WeightingMethod.PERFORMANCE_BASED
        )
        evaluator = StrategyEvaluator(portfolio_manager, EvaluationPeriod.MONTHLY)
        advisor = ReplacementAdvisor(evaluator)
        lifecycle_manager = LifecycleManager(evaluator)
        optimizer = ParameterOptimizer(evaluator)
        
        # 2. 添加测试策略
        strategies = [
            MockStrategy("High_Quality_Strategy"),
            MockStrategy("Medium_Quality_Strategy"),
            MockStrategy("Low_Quality_Strategy")
        ]
        
        strategy_ids = []
        for i, strategy in enumerate(strategies):
            strategy_id = portfolio_manager.add_strategy(strategy, weight=1.0/len(strategies))
            strategy_ids.append(strategy_id)
            
            # 设置初始生命周期阶段
            lifecycle_manager.update_strategy_stage(strategy_id, LifecycleStage.LIVE_TRADING)
        
        # 3. 评估所有策略
        evaluations = evaluator.evaluate_all_strategies()
        assert len(evaluations) == 3
        print(f"   - 评估了{len(evaluations)}个策略")
        
        # 4. 生成替换建议
        suggestions = advisor.generate_replacement_suggestions()
        print(f"   - 生成了{len(suggestions)}个替换建议")
        
        # 5. 检查生命周期转换建议
        transition_recommendations = []
        for strategy_id in strategy_ids:
            recommendation = lifecycle_manager.recommend_stage_transition(strategy_id)
            if recommendation:
                transition_recommendations.append((strategy_id, recommendation))
        
        print(f"   - 生成了{len(transition_recommendations)}个生命周期转换建议")
        
        # 6. 参数优化（选择一个策略进行优化）
        if strategy_ids:
            parameter_ranges = {
                'param1': (8, 20),
                'param2': (0.3, 0.7)
            }
            
            optimization_result = optimizer.optimize_strategy_parameters(
                strategy_ids[0],
                parameter_ranges,
                max_iterations=10
            )
            
            assert optimization_result.strategy_id == strategy_ids[0]
            print(f"   - 完成策略{strategy_ids[0]}的参数优化")
        
        # 7. 验证整个流程
        assert all(eval.overall_score >= 0 for eval in evaluations), "所有评估分数应该为非负"
        assert all(0 <= eval.rank <= len(evaluations) for eval in evaluations), "排名应该在合理范围内"
        
        print("✅ 8.3 综合评估工作流测试通过！")


def run_all_8_3_tests():
    """运行所有8.3测试"""
    print("=" * 60)
    print("开始测试8.3: 实现策略评估和选择")
    print("=" * 60)
    
    # 测试8.3.1: 策略表现评估算法
    print("\n📊 测试8.3.1: 策略表现评估算法")
    test_8_3_1 = Test8_3_1_PerformanceEvaluation()
    test_8_3_1.test_evaluator_initialization()
    test_8_3_1.test_single_strategy_evaluation()
    test_8_3_1.test_all_strategies_evaluation()
    test_8_3_1.test_performance_metrics_calculation()
    
    # 测试8.3.2: 策略替换建议系统
    print("\n🔄 测试8.3.2: 策略替换建议系统")
    test_8_3_2 = Test8_3_2_ReplacementAdvisor()
    test_8_3_2.test_replacement_advisor_initialization()
    test_8_3_2.test_replacement_suggestions_generation()
    test_8_3_2.test_replacement_analysis()
    
    # 测试8.3.3: 策略生命周期管理
    print("\n🔄 测试8.3.3: 策略生命周期管理")
    test_8_3_3 = Test8_3_3_LifecycleManager()
    test_8_3_3.test_lifecycle_manager_initialization()
    test_8_3_3.test_strategy_stage_updates()
    test_8_3_3.test_stage_duration_tracking()
    test_8_3_3.test_stage_transition_recommendations()
    
    # 测试8.3.4: 策略优化和参数调整工具
    print("\n⚙️ 测试8.3.4: 策略优化和参数调整工具")
    test_8_3_4 = Test8_3_4_ParameterOptimizer()
    test_8_3_4.test_parameter_optimizer_initialization()
    test_8_3_4.test_grid_search_optimization()
    test_8_3_4.test_random_search_optimization()
    test_8_3_4.test_parameter_evaluation()
    
    # 集成测试
    print("\n🔗 测试8.3集成功能")
    test_integration = Test8_3_Integration()
    test_integration.test_strategy_selection_workflow()
    test_integration.test_comprehensive_evaluation_workflow()
    
    print("\n" + "=" * 60)
    print("🎉 8.3策略评估和选择所有测试完成！")
    print("=" * 60)


if __name__ == "__main__":
    run_all_8_3_tests()