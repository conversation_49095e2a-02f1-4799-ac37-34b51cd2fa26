"""
策略协调机制完整测试

测试8.2的所有功能要求：
- 多策略信号聚合和优先级管理
- 资金分配和仓位协调逻辑  
- 策略间通信和状态同步
- 策略组合风险控制
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.strategies.strategy_coordinator import (
    StrategyCoordinator, SignalAggregator, CapitalAllocator,
    PrioritySignal, AggregatedSignal, PositionAllocation,
    SignalPriority, AggregationMethod, StrategyState
)
from src.strategies.multi_strategy_manager import StrategyPortfolioManager, WeightingMethod
from src.strategies.base import BaseStrategy
from src.strategies.signals import Signal, SignalType
from src.risk.manager import RiskManager

class MockStrategy(BaseStrategy):
    """模拟策略类"""
    
    def __init__(self, name: str):
        super().__init__(name)
        self._signals = []
        self.parameters = {}
    
    def get_parameter_definitions(self):
        return {}
    
    def initialize(self, context):
        self.context = context
        self.is_initialized = True
    
    def on_data(self, data):
        return self._signals.copy()
    
    def add_signal(self, signal: Signal):
        self._signals.append(signal)


class Test8_2_SignalAggregation:
    """测试8.2.1: 多策略信号聚合和优先级管理"""
    
    def test_priority_based_aggregation(self):
        """测试基于优先级的信号聚合"""
        aggregator = SignalAggregator(AggregationMethod.PRIORITY_BASED)
        
        signals = [
            PrioritySignal(
                signal=Signal("AAPL", SignalType.BUY, datetime.now(), "strategy_1", 0.7),
                priority=SignalPriority.MEDIUM,
                strategy_id="strategy_1"
            ),
            PrioritySignal(
                signal=Signal("AAPL", SignalType.SELL, datetime.now(), "strategy_2", 0.8),
                priority=SignalPriority.HIGH,
                strategy_id="strategy_2"
            )
        ]
        
        aggregated = aggregator.aggregate_signals(signals)
        
        assert "AAPL" in aggregated
        # 高优先级的SELL信号应该胜出
        assert aggregated["AAPL"].signal_type == SignalType.SELL
        print("✅ 基于优先级的信号聚合测试通过")
    
    def test_confidence_based_aggregation(self):
        """测试基于置信度的信号聚合"""
        aggregator = SignalAggregator(AggregationMethod.CONFIDENCE_BASED)
        
        signals = [
            PrioritySignal(
                signal=Signal("MSFT", SignalType.BUY, datetime.now(), "strategy_1", 0.6),
                priority=SignalPriority.HIGH,
                strategy_id="strategy_1"
            ),
            PrioritySignal(
                signal=Signal("MSFT", SignalType.SELL, datetime.now(), "strategy_2", 0.9),
                priority=SignalPriority.MEDIUM,
                strategy_id="strategy_2"  
            )
        ]
        
        aggregated = aggregator.aggregate_signals(signals)
        
        assert "MSFT" in aggregated
        # 高置信度的SELL信号应该胜出
        assert aggregated["MSFT"].signal_type == SignalType.SELL
        print("✅ 基于置信度的信号聚合测试通过")
    
    def test_weighted_average_aggregation(self):
        """测试加权平均聚合"""
        aggregator = SignalAggregator(AggregationMethod.WEIGHTED_AVERAGE)
        
        signals = [
            PrioritySignal(
                signal=Signal("TSLA", SignalType.BUY, datetime.now(), "strategy_1", 0.8),
                priority=SignalPriority.HIGH,
                weight=0.6,
                strategy_id="strategy_1"
            ),
            PrioritySignal(
                signal=Signal("TSLA", SignalType.BUY, datetime.now(), "strategy_2", 0.7),
                priority=SignalPriority.MEDIUM,
                weight=0.4,
                strategy_id="strategy_2"
            )
        ]
        
        aggregated = aggregator.aggregate_signals(signals)
        
        assert "TSLA" in aggregated
        assert aggregated["TSLA"].signal_type == SignalType.BUY
        # 加权置信度应该是 0.8*0.6 + 0.7*0.4 = 0.76
        expected_confidence = (0.8 * 0.6 + 0.7 * 0.4) / (0.6 + 0.4)
        assert abs(aggregated["TSLA"].aggregate_confidence - expected_confidence) < 0.001
        print("✅ 加权平均聚合测试通过")


class Test8_2_CapitalAllocation:
    """测试8.2.2: 资金分配和仓位协调逻辑"""
    
    def test_capital_allocator_initialization(self):
        """测试资金分配器初始化"""
        allocator = CapitalAllocator(total_capital=1000000.0)
        
        assert allocator.total_capital == 1000000.0
        assert allocator.allocated_capital == 0.0
        assert len(allocator.allocations) == 0
        print("✅ 资金分配器初始化测试通过")
    
    def test_capital_allocation_logic(self):
        """测试资金分配逻辑"""
        allocator = CapitalAllocator(total_capital=1000000.0)
        
        # 创建聚合信号
        aggregated_signals = {
            "AAPL": AggregatedSignal(
                symbol="AAPL",
                signal_type=SignalType.BUY,
                aggregate_confidence=0.8,
                component_signals=[
                    PrioritySignal(
                        signal=Signal("AAPL", SignalType.BUY, datetime.now(), "strategy_1", 0.8),
                        priority=SignalPriority.HIGH,
                        strategy_id="strategy_1"
                    )
                ],
                aggregation_method=AggregationMethod.CONFIDENCE_BASED,
                timestamp=datetime.now()
            )
        }
        
        strategy_weights = {"strategy_1": 0.5}
        
        allocations = allocator.allocate_capital(aggregated_signals, strategy_weights)
        
        assert "strategy_1" in allocations
        assert len(allocations["strategy_1"]) == 1
        
        allocation = allocations["strategy_1"][0]
        assert allocation.symbol == "AAPL"
        assert allocation.strategy_id == "strategy_1"
        assert allocation.target_quantity > 0
        print("✅ 资金分配逻辑测试通过")
    
    def test_position_coordination(self):
        """测试仓位协调"""
        allocator = CapitalAllocator(total_capital=1000000.0)
        
        # 模拟当前持仓
        current_positions = {
            "AAPL": {"strategy_1": 100.0, "strategy_2": -50.0}  # 策略1多头100股，策略2空头50股
        }
        
        aggregated_signals = {
            "AAPL": AggregatedSignal(
                symbol="AAPL",
                signal_type=SignalType.SELL,
                aggregate_confidence=0.7,
                component_signals=[
                    PrioritySignal(
                        signal=Signal("AAPL", SignalType.SELL, datetime.now(), "strategy_1", 0.7),
                        priority=SignalPriority.MEDIUM,
                        strategy_id="strategy_1"
                    )
                ],
                aggregation_method=AggregationMethod.CONFIDENCE_BASED,
                timestamp=datetime.now()
            )
        }
        
        strategy_weights = {"strategy_1": 0.6}
        
        allocations = allocator.allocate_capital(aggregated_signals, strategy_weights, current_positions)
        
        assert "strategy_1" in allocations
        allocation = allocations["strategy_1"][0]
        assert allocation.current_quantity == 100.0  # 应该反映当前持仓
        print("✅ 仓位协调测试通过")


class Test8_2_StateCommunication:
    """测试8.2.3: 策略间通信和状态同步"""
    
    def test_strategy_state_management(self):
        """测试策略状态管理"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        coordinator = StrategyCoordinator(portfolio_manager)
        
        # 添加策略
        strategy1 = MockStrategy("Test_Strategy_1")
        strategy2 = MockStrategy("Test_Strategy_2")
        
        strategy_id1 = portfolio_manager.add_strategy(strategy1, 0.5)
        strategy_id2 = portfolio_manager.add_strategy(strategy2, 0.5)
        
        # 测试状态更新
        coordinator.update_strategy_state(strategy_id1, StrategyState.ACTIVE, "激活策略")
        coordinator.update_strategy_state(strategy_id2, StrategyState.SUSPENDED, "暂停策略")
        
        assert coordinator.strategy_states[strategy_id1] == StrategyState.ACTIVE
        assert coordinator.strategy_states[strategy_id2] == StrategyState.SUSPENDED
        print("✅ 策略状态管理测试通过")
    
    def test_strategy_synchronization(self):
        """测试策略同步"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        coordinator = StrategyCoordinator(portfolio_manager)
        
        # 添加策略
        strategy = MockStrategy("Test_Strategy")
        strategy_id = portfolio_manager.add_strategy(strategy, 1.0)
        
        # 测试同步
        sync_results = coordinator.synchronize_strategies()
        
        assert strategy_id in sync_results
        assert sync_results[strategy_id] is True
        assert coordinator.strategy_states[strategy_id] == StrategyState.ACTIVE
        print("✅ 策略同步测试通过")
    
    def test_callback_system(self):
        """测试回调系统"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        coordinator = StrategyCoordinator(portfolio_manager)
        
        # 测试回调函数
        signal_callback_called = False
        state_callback_called = False
        
        def signal_callback(signals):
            nonlocal signal_callback_called
            signal_callback_called = True
        
        def state_callback(update):
            nonlocal state_callback_called
            state_callback_called = True
        
        coordinator.add_signal_callback(signal_callback)
        coordinator.add_state_callback(state_callback)
        
        # 触发回调
        strategy = MockStrategy("Test_Strategy")
        strategy_id = portfolio_manager.add_strategy(strategy, 1.0)
        
        coordinator.update_strategy_state(strategy_id, StrategyState.ACTIVE)
        
        assert state_callback_called
        print("✅ 回调系统测试通过")


class Test8_2_RiskControl:
    """测试8.2.4: 策略组合风险控制"""
    
    def test_coordination_with_risk_control(self):
        """测试协调过程中的风险控制"""
        portfolio_manager = StrategyPortfolioManager(initial_capital=1000000.0)
        risk_manager = RiskManager()
        coordinator = StrategyCoordinator(portfolio_manager, risk_manager)
        
        # 添加策略
        strategy = MockStrategy("Test_Strategy")
        strategy_id = portfolio_manager.add_strategy(strategy, 1.0)
        
        # 测试协调状态
        status = coordinator.get_coordination_status()
        
        assert "total_strategies" in status
        assert "active_strategies" in status
        assert "available_capital" in status
        assert status["total_strategies"] == 1
        print("✅ 风险控制集成测试通过")
    
    def test_capital_limits(self):
        """测试资金限制"""
        allocator = CapitalAllocator(total_capital=100000.0)  # 较小的资金池
        
        # 创建大额信号
        aggregated_signals = {
            "AAPL": AggregatedSignal(
                symbol="AAPL",
                signal_type=SignalType.BUY,
                aggregate_confidence=1.0,  # 最高置信度
                component_signals=[
                    PrioritySignal(
                        signal=Signal("AAPL", SignalType.BUY, datetime.now(), "strategy_1", 1.0),
                        priority=SignalPriority.HIGH,
                        strategy_id="strategy_1"
                    )
                ],
                aggregation_method=AggregationMethod.CONFIDENCE_BASED,
                timestamp=datetime.now()
            )
        }
        
        strategy_weights = {"strategy_1": 1.0}
        
        allocations = allocator.allocate_capital(aggregated_signals, strategy_weights)
        
        # 验证分配的资金不超过总资金
        total_allocated = sum(
            alloc.allocated_capital 
            for strategy_allocs in allocations.values() 
            for alloc in strategy_allocs
        )
        
        assert total_allocated <= allocator.total_capital
        print("✅ 资金限制测试通过")


class Test8_2_Integration:
    """测试8.2集成功能"""
    
    def test_full_coordination_workflow(self):
        """测试完整的协调工作流"""
        print("开始完整协调工作流测试...")
        
        # 1. 设置系统
        portfolio_manager = StrategyPortfolioManager(
            initial_capital=1000000.0,
            weighting_method=WeightingMethod.EQUAL_WEIGHT
        )
        coordinator = StrategyCoordinator(portfolio_manager)
        
        # 2. 添加策略
        strategies = [
            MockStrategy("Strategy_A"),
            MockStrategy("Strategy_B"),
            MockStrategy("Strategy_C")
        ]
        
        strategy_ids = []
        for strategy in strategies:
            strategy_id = portfolio_manager.add_strategy(strategy, weight=1.0/len(strategies))
            strategy_ids.append(strategy_id)
            
            # 为每个策略添加一些测试信号
            strategy.add_signal(Signal(
                symbol="AAPL",
                signal_type=SignalType.BUY,
                timestamp=datetime.now(),
                strategy_name=strategy.name,
                confidence=0.8
            ))
        
        # 3. 测试策略状态同步
        sync_results = coordinator.synchronize_strategies()
        assert all(sync_results.values()), "所有策略应该同步成功"
        
        # 4. 测试协调过程
        market_data = {
            'conditions': 'normal',
            'volatility_regime': 'low'
        }
        
        orders = coordinator.coordinate_strategies(market_data)
        
        # 5. 验证结果
        assert isinstance(orders, dict), "应该返回订单字典"
        
        # 6. 测试协调状态
        status = coordinator.get_coordination_status()
        assert status['total_strategies'] == 3
        assert status['active_strategies'] >= 0
        
        print("✅ 8.2 完整协调工作流测试通过！")


def run_all_8_2_tests():
    """运行所有8.2测试"""
    print("=" * 60)
    print("开始测试8.2: 创建策略协调机制")
    print("=" * 60)
    
    # 测试8.2.1: 多策略信号聚合和优先级管理
    print("\n📋 测试8.2.1: 多策略信号聚合和优先级管理")
    test_8_2_1 = Test8_2_SignalAggregation()
    test_8_2_1.test_priority_based_aggregation()
    test_8_2_1.test_confidence_based_aggregation()
    test_8_2_1.test_weighted_average_aggregation()
    
    # 测试8.2.2: 资金分配和仓位协调逻辑
    print("\n💰 测试8.2.2: 资金分配和仓位协调逻辑")
    test_8_2_2 = Test8_2_CapitalAllocation()
    test_8_2_2.test_capital_allocator_initialization()
    test_8_2_2.test_capital_allocation_logic()
    test_8_2_2.test_position_coordination()
    
    # 测试8.2.3: 策略间通信和状态同步
    print("\n🔄 测试8.2.3: 策略间通信和状态同步")
    test_8_2_3 = Test8_2_StateCommunication()
    test_8_2_3.test_strategy_state_management()
    test_8_2_3.test_strategy_synchronization()
    test_8_2_3.test_callback_system()
    
    # 测试8.2.4: 策略组合风险控制
    print("\n🛡️ 测试8.2.4: 策略组合风险控制")
    test_8_2_4 = Test8_2_RiskControl()
    test_8_2_4.test_coordination_with_risk_control()
    test_8_2_4.test_capital_limits()
    
    # 集成测试
    print("\n🔗 测试8.2集成功能")
    test_integration = Test8_2_Integration()
    test_integration.test_full_coordination_workflow()
    
    print("\n" + "=" * 60)
    print("🎉 8.2策略协调机制所有测试完成！")
    print("=" * 60)


if __name__ == "__main__":
    run_all_8_2_tests()