"""
Tests for FRED data adapter.
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

from src.data.adapters.fred_adapter import FREDAdapter
from src.data.adapters.base import DataSourceConfig
from src.models.market_data import EconomicData, MarketInfo
from src.exceptions import DataException


@pytest.fixture
def fred_config():
    """Create test configuration for FRED adapter."""
    return DataSourceConfig(
        name='fred_test',
        adapter_class='src.data.adapters.fred_adapter.FREDAdapter',
        enabled=True,
        priority=1,
        rate_limit={'requests_per_minute': 60},
        credentials={'api_key': 'test_api_key'},
        default_params={}
    )


@pytest.fixture
def mock_fred_api():
    """Create mock FRED API."""
    with patch('src.data.adapters.fred_adapter.CustomFredClient') as mock_fred_class:
        mock_fred = Mock()
        mock_fred_class.return_value = mock_fred
        
        # Mock series data
        mock_series_data = pd.Series(
            [100.0, 101.5, 102.0, 103.2],
            index=pd.date_range('2022-01-01', periods=4, freq='Q'),
            name='GDP'
        )
        mock_fred.get_series.return_value = mock_series_data
        
        # Mock series info
        mock_series_info = pd.Series({
            'id': 'GDP',
            'title': 'Gross Domestic Product',
            'units': 'Billions of Dollars',
            'frequency': 'Quarterly',
            'seasonal_adjustment': 'Seasonally Adjusted Annual Rate',
            'last_updated': '2023-01-01',
            'notes': 'GDP measures the value of goods and services produced.'
        })
        mock_fred.get_series_info.return_value = mock_series_info
        
        # Mock search results
        mock_search_results = pd.DataFrame({
            'id': ['GDP', 'GDPC1', 'UNRATE'],
            'title': [
                'Gross Domestic Product',
                'Real Gross Domestic Product',
                'Unemployment Rate'
            ],
            'units': [
                'Billions of Dollars',
                'Billions of Chained 2012 Dollars',
                'Percent'
            ],
            'frequency': ['Quarterly', 'Quarterly', 'Monthly'],
            'seasonal_adjustment': [
                'Seasonally Adjusted Annual Rate',
                'Seasonally Adjusted Annual Rate',
                'Seasonally Adjusted'
            ],
            'last_updated': ['2023-01-01', '2023-01-01', '2023-01-01'],
            'popularity': [100, 95, 90],
            'notes': [
                'GDP measures the value of goods and services produced.',
                'Real GDP measures inflation-adjusted value.',
                'Unemployment rate as percent of labor force.'
            ]
        })
        mock_fred.search.return_value = mock_search_results
        
        # Mock latest release
        mock_latest_data = pd.Series(
            [103.5],
            index=pd.date_range('2023-01-01', periods=1, freq='Q'),
            name='GDP'
        )
        mock_fred.get_series_latest_release.return_value = mock_latest_data
        
        # Mock all releases (real-time data)
        mock_realtime_data = pd.Series(
            [103.0, 103.2, 103.5],
            index=pd.date_range('2023-01-01', periods=3, freq='D'),
            name='GDP'
        )
        mock_fred.get_series_all_releases.return_value = mock_realtime_data
        
        yield mock_fred


class TestFREDAdapter:
    """Test cases for FRED adapter."""
    
    def test_initialization(self, fred_config, mock_fred_api):
        """Test adapter initialization."""
        adapter = FREDAdapter(fred_config)
        
        assert adapter.config == fred_config
        assert adapter.get_market_type() == 'ECONOMIC'
        assert adapter.get_exchange_name() == 'FRED'
        assert adapter.get_default_currency() == 'USD'
    
    def test_initialization_without_api_key(self):
        """Test adapter initialization failure without API key."""
        config = DataSourceConfig(
            name='fred_test',
            adapter_class='src.data.adapters.fred_adapter.FREDAdapter',
            enabled=True,
            priority=1,
            rate_limit={'requests_per_minute': 60},
            credentials={},  # No API key
            default_params={}
        )
        
        with pytest.raises(DataException):
            FREDAdapter(config)
    
    def test_get_available_symbols(self, fred_config, mock_fred_api):
        """Test getting available symbols."""
        adapter = FREDAdapter(fred_config)
        symbols = adapter.get_available_symbols()
        
        assert isinstance(symbols, list)
        assert 'GDP' in symbols
        assert 'UNRATE' in symbols
        assert 'FEDFUNDS' in symbols
    
    def test_validate_symbol(self, fred_config, mock_fred_api):
        """Test symbol validation."""
        adapter = FREDAdapter(fred_config)
        
        assert adapter.validate_symbol('GDP') is True
        # Mock will return empty series for invalid symbols
        mock_fred_api.get_series_info.return_value = pd.Series()
        assert adapter.validate_symbol('INVALID') is False
    
    def test_get_series_info(self, fred_config, mock_fred_api):
        """Test getting series information."""
        adapter = FREDAdapter(fred_config)
        series_info = adapter.get_series_info('GDP')
        
        assert isinstance(series_info, dict)
        assert series_info['id'] == 'GDP'
        assert series_info['title'] == 'Gross Domestic Product'
        assert series_info['units'] == 'Billions of Dollars'
        assert series_info['frequency'] == 'Quarterly'
    
    def test_get_market_info(self, fred_config, mock_fred_api):
        """Test getting market information."""
        adapter = FREDAdapter(fred_config)
        market_info = adapter.get_market_info('GDP')
        
        assert isinstance(market_info, MarketInfo)
        assert market_info.symbol == 'GDP'
        assert market_info.name == 'Gross Domestic Product'
        assert market_info.market == 'ECONOMIC'
        assert market_info.exchange == 'FRED'
        assert market_info.currency == 'USD'
    
    def test_fetch_historical_data(self, fred_config, mock_fred_api):
        """Test fetching historical data."""
        adapter = FREDAdapter(fred_config)
        
        start_date = datetime(2022, 1, 1)
        end_date = datetime(2022, 12, 31)
        
        data = adapter.fetch_historical_data('GDP', start_date, end_date)
        
        assert isinstance(data, list)
        assert len(data) == 4  # Quarterly data
        
        # Check first record
        first_record = data[0]
        assert isinstance(first_record, EconomicData)
        assert first_record.series_id == 'GDP'
        assert first_record.value == 100.0
        assert first_record.market == 'ECONOMIC'
        assert first_record.source == 'FRED'
        assert first_record.unit == 'Billions of Dollars'
        assert first_record.frequency == 'Quarterly'
    
    def test_search_series(self, fred_config, mock_fred_api):
        """Test searching for series."""
        adapter = FREDAdapter(fred_config)
        results = adapter.search_series('GDP', limit=10)
        
        assert isinstance(results, list)
        assert len(results) == 3
        
        # Check first result
        first_result = results[0]
        assert first_result['id'] == 'GDP'
        assert first_result['title'] == 'Gross Domestic Product'
        assert first_result['units'] == 'Billions of Dollars'
        assert first_result['frequency'] == 'Quarterly'
    
    def test_get_popular_series(self, fred_config, mock_fred_api):
        """Test getting popular series."""
        adapter = FREDAdapter(fred_config)
        popular = adapter.get_popular_series()
        
        assert isinstance(popular, list)
        assert 'GDP' in popular
        assert 'UNRATE' in popular
        assert 'FEDFUNDS' in popular
    
    def test_fetch_multiple_series(self, fred_config, mock_fred_api):
        """Test fetching multiple series."""
        adapter = FREDAdapter(fred_config)
        
        start_date = datetime(2022, 1, 1)
        end_date = datetime(2022, 12, 31)
        series_ids = ['GDP', 'UNRATE']
        
        results = adapter.fetch_multiple_series(series_ids, start_date, end_date)
        
        assert isinstance(results, dict)
        assert 'GDP' in results
        assert 'UNRATE' in results
        assert len(results['GDP']) == 4
        assert len(results['UNRATE']) == 4
    
    def test_get_latest_value(self, fred_config, mock_fred_api):
        """Test getting latest value."""
        adapter = FREDAdapter(fred_config)
        latest = adapter.get_latest_value('GDP')
        
        assert isinstance(latest, dict)
        assert latest['series_id'] == 'GDP'
        assert latest['value'] == 103.5
        assert latest['title'] == 'Gross Domestic Product'
        assert latest['units'] == 'Billions of Dollars'
    
    def test_fetch_real_time_data(self, fred_config, mock_fred_api):
        """Test fetching real-time data."""
        adapter = FREDAdapter(fred_config)
        
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 3)
        
        data = adapter.fetch_real_time_data('GDP', start_date, end_date)
        
        assert isinstance(data, list)
        assert len(data) == 3
        
        # Check that it's economic data
        for record in data:
            assert isinstance(record, EconomicData)
            assert record.series_id == 'GDP'
            assert record.market == 'ECONOMIC'
    
    def test_get_categories(self, fred_config, mock_fred_api):
        """Test getting categories."""
        adapter = FREDAdapter(fred_config)
        categories = adapter.get_categories()
        
        assert isinstance(categories, dict)
        assert 'GDP and Growth' in categories
        assert 'Employment and Labor' in categories
        assert 'Interest Rates' in categories
        
        # Check that GDP is in the right category
        assert 'GDP' in categories['GDP and Growth']
    
    def test_get_series_by_category(self, fred_config, mock_fred_api):
        """Test getting series by category."""
        adapter = FREDAdapter(fred_config)
        gdp_series = adapter.get_series_by_category('GDP and Growth')
        
        assert isinstance(gdp_series, list)
        assert 'GDP' in gdp_series
        assert 'GDPC1' in gdp_series
    
    def test_get_supported_intervals(self, fred_config, mock_fred_api):
        """Test getting supported intervals."""
        adapter = FREDAdapter(fred_config)
        intervals = adapter.get_supported_intervals()
        
        assert isinstance(intervals, list)
        assert 'd' in intervals  # Daily
        assert 'w' in intervals  # Weekly
        assert 'm' in intervals  # Monthly
        assert 'q' in intervals  # Quarterly
        assert 'a' in intervals  # Annual
    
    def test_health_check(self, fred_config, mock_fred_api):
        """Test health check."""
        adapter = FREDAdapter(fred_config)
        
        is_healthy, message = adapter.health_check()
        
        assert is_healthy is True
        assert 'Health check passed' in message
        assert adapter.is_healthy is True
    
    def test_frequency_mapping(self, fred_config, mock_fred_api):
        """Test frequency mapping."""
        adapter = FREDAdapter(fred_config)
        
        assert adapter._map_frequency('Daily') == 'Daily'
        assert adapter._map_frequency('Weekly') == 'Weekly'
        assert adapter._map_frequency('Monthly') == 'Monthly'
        assert adapter._map_frequency('Quarterly') == 'Quarterly'
        assert adapter._map_frequency('Annual') == 'Annual'
    
    def test_invalid_series_handling(self, fred_config, mock_fred_api):
        """Test handling of invalid series."""
        adapter = FREDAdapter(fred_config)
        
        # Mock API exception for invalid series
        mock_fred_api.get_series.side_effect = Exception("Series not found")
        
        with pytest.raises(DataException):
            adapter.fetch_historical_data('INVALID', datetime.now(), datetime.now())
    
    def test_empty_data_handling(self, fred_config, mock_fred_api):
        """Test handling of empty data."""
        adapter = FREDAdapter(fred_config)
        
        # Mock empty series
        mock_fred_api.get_series.return_value = pd.Series(dtype=float)
        
        start_date = datetime(2022, 1, 1)
        end_date = datetime(2022, 12, 31)
        
        data = adapter.fetch_historical_data('GDP', start_date, end_date)
        assert len(data) == 0
    
    def test_data_validation(self, fred_config, mock_fred_api):
        """Test data validation in fetch_historical_data."""
        adapter = FREDAdapter(fred_config)
        
        # Mock series with NaN values
        mock_series_with_nan = pd.Series(
            [100.0, float('nan'), 102.0],
            index=pd.date_range('2022-01-01', periods=3, freq='Q'),
            name='GDP'
        )
        mock_fred_api.get_series.return_value = mock_series_with_nan
        
        start_date = datetime(2022, 1, 1)
        end_date = datetime(2022, 12, 31)
        
        data = adapter.fetch_historical_data('GDP', start_date, end_date)
        
        # Should skip NaN values
        assert len(data) == 2
        assert all(not pd.isna(record.value) for record in data)
    
    def test_retry_mechanism(self, fred_config, mock_fred_api):
        """Test retry mechanism for failed requests."""
        adapter = FREDAdapter(fred_config)
        
        # Mock function that fails first time, succeeds second time
        call_count = 0
        def mock_function():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise Exception("Temporary failure")
            return pd.Series([100.0], index=[datetime.now()])
        
        # Test retry mechanism
        result = adapter._make_request_with_retry(mock_function)
        assert not result.empty
        assert call_count == 2
    
    @patch('src.data.adapters.fred_adapter.Fred')
    def test_initialization_failure(self, mock_fred_class, fred_config):
        """Test adapter initialization failure."""
        # Mock FRED initialization failure
        mock_fred_class.side_effect = Exception("API connection failed")
        
        with pytest.raises(DataException):
            FREDAdapter(fred_config)


if __name__ == '__main__':
    pytest.main([__file__])