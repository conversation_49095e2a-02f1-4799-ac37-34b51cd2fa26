"""
策略框架综合测试

验证策略基础类、上下文管理器、生命周期管理和参数验证系统的完整功能
"""

import unittest
from unittest.mock import Mock, MagicMock
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from src.strategies.base import BaseStrategy, StrategyContext
from src.strategies.parameters import StrategyParameter, ParameterType, ParameterValidator, ParameterManager
from src.strategies.lifecycle import StrategyLifecycleManager, StrategyState
from src.strategies.signals import Signal, SignalType, SignalManager
from src.models.market_data import MarketData
from src.models.portfolio import Portfolio, Position


class ComprehensiveTestStrategy(BaseStrategy):
    """
    综合测试策略类
    
    用于测试策略框架的各种功能
    """
    
    def get_parameter_definitions(self):
        return {
            'period': StrategyParameter(
                name='period',
                param_type=ParameterType.INTEGER,
                default_value=20,
                required=True,
                description='计算周期',
                min_value=1,
                max_value=200,
                validator=ParameterValidator.positive_number
            ),
            'threshold': StrategyParameter(
                name='threshold',
                param_type=ParameterType.FLOAT,
                default_value=0.02,
                required=False,
                description='阈值',
                min_value=0.0,
                max_value=1.0,
                validator=ParameterValidator.probability
            ),
            'symbols': StrategyParameter(
                name='symbols',
                param_type=ParameterType.LIST,
                default_value=['AAPL', 'GOOGL'],
                required=False,
                description='交易标的列表'
            ),
            'enabled': StrategyParameter(
                name='enabled',
                param_type=ParameterType.BOOLEAN,
                default_value=True,
                required=False,
                description='是否启用策略'
            )
        }
    
    def initialize(self, context: StrategyContext):
        """策略初始化"""
        self.context = context
        self.is_initialized = True
        self.last_prices = {}
        self.signal_count = 0
        
        # 验证上下文可用性
        if not context:
            raise ValueError("策略上下文不能为空")
    
    def on_data(self, data):
        """处理市场数据"""
        if not self.is_active:
            return []
        
        signals = []
        
        # 处理单个标的数据
        if isinstance(data, MarketData):
            signal = self._process_single_data(data)
            if signal:
                signals.append(signal)
        
        # 处理多个标的数据
        elif isinstance(data, dict):
            for symbol, market_data in data.items():
                signal = self._process_single_data(market_data)
                if signal:
                    signals.append(signal)
        
        return signals
    
    def _process_single_data(self, data: MarketData):
        """处理单个标的的数据"""
        symbol = data.symbol
        current_price = data.close
        
        # 简单的价格变化策略
        if symbol in self.last_prices:
            price_change = (current_price - self.last_prices[symbol]) / self.last_prices[symbol]
            threshold = self.get_parameter('threshold', 0.02)
            
            if price_change > threshold:
                self.signal_count += 1
                return Signal(
                    symbol=symbol,
                    signal_type=SignalType.BUY,
                    timestamp=data.timestamp,
                    strategy_name=self.name,
                    confidence=min(1.0, abs(price_change) / threshold),
                    quantity=100,
                    price=current_price,
                    metadata={'price_change': price_change}
                )
            elif price_change < -threshold:
                self.signal_count += 1
                return Signal(
                    symbol=symbol,
                    signal_type=SignalType.SELL,
                    timestamp=data.timestamp,
                    strategy_name=self.name,
                    confidence=min(1.0, abs(price_change) / threshold),
                    quantity=100,
                    price=current_price,
                    metadata={'price_change': price_change}
                )
        
        self.last_prices[symbol] = current_price
        return None
    
    def on_signal(self, signal: Signal):
        """信号生成后的处理"""
        self.logger.info(f"策略 {self.name} 生成信号: {signal.signal_type.value} {signal.symbol}")
    
    def on_order_fill(self, fill_info):
        """订单成交处理"""
        self.logger.info(f"策略 {self.name} 订单成交: {fill_info}")
    
    def cleanup(self):
        """策略清理"""
        super().cleanup()
        self.last_prices.clear()
        self.signal_count = 0


class TestStrategyFrameworkComprehensive(unittest.TestCase):
    """策略框架综合测试类"""
    
    def setUp(self):
        """测试设置"""
        # 创建模拟的数据管理器
        self.mock_data_manager = Mock()
        self.mock_data_manager.get_historical_data.return_value = pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=100, freq='D'),
            'open': np.random.randn(100) * 10 + 100,
            'high': np.random.randn(100) * 10 + 105,
            'low': np.random.randn(100) * 10 + 95,
            'close': np.random.randn(100) * 10 + 100,
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        # 创建模拟的指标引擎
        self.mock_indicator_engine = Mock()
        self.mock_indicator_engine.calculate.return_value = pd.Series(
            np.random.randn(100), 
            index=pd.date_range('2023-01-01', periods=100, freq='D')
        )
        
        # 创建模拟的投资组合
        self.mock_portfolio = Portfolio(
            initial_capital=100000.0,
            cash=100000.0,
            positions={
                'AAPL': Position(symbol='AAPL', quantity=100, avg_price=150.0, market_value=15000.0, unrealized_pnl=500.0)
            },
            total_value=115000.0,
            unrealized_pnl=500.0,
            realized_pnl=0.0
        )
        
        # 创建策略上下文
        self.context = StrategyContext(
            data_manager=self.mock_data_manager,
            indicator_engine=self.mock_indicator_engine,
            portfolio=self.mock_portfolio
        )
        
        # 创建生命周期管理器
        self.lifecycle_manager = StrategyLifecycleManager()
    
    def test_strategy_parameter_system(self):
        """测试策略参数系统"""
        # 测试参数定义
        strategy = ComprehensiveTestStrategy('param_test')
        param_defs = strategy.get_parameter_definitions()
        
        self.assertIn('period', param_defs)
        self.assertIn('threshold', param_defs)
        self.assertIn('symbols', param_defs)
        self.assertIn('enabled', param_defs)
        
        # 测试参数验证
        period_param = param_defs['period']
        self.assertTrue(period_param.validate(20))
        
        with self.assertRaises(ValueError):
            period_param.validate(-1)  # 负数应该失败
        
        with self.assertRaises(ValueError):
            period_param.validate(300)  # 超出最大值应该失败
        
        # 测试参数设置和获取
        strategy.set_parameter('period', 30)
        self.assertEqual(strategy.get_parameter('period'), 30)
        
        # 测试默认值
        self.assertEqual(strategy.get_parameter('threshold'), 0.02)
    
    def test_parameter_manager(self):
        """测试参数管理器"""
        manager = ParameterManager()
        
        # 添加参数定义
        param = StrategyParameter(
            name='test_param',
            param_type=ParameterType.FLOAT,
            default_value=1.0,
            required=True,
            min_value=0.0,
            max_value=10.0
        )
        manager.add_parameter(param)
        
        # 验证参数
        values = {'test_param': 5.0}
        validated = manager.validate_parameters(values)
        self.assertEqual(validated['test_param'], 5.0)
        
        # 测试无效参数
        with self.assertRaises(ValueError):
            manager.validate_parameters({'test_param': -1.0})
        
        # 测试缺失必需参数
        with self.assertRaises(ValueError):
            manager.validate_parameters({})
        
        # 测试参数模式
        schema = manager.get_parameter_schema()
        self.assertIn('test_param', schema)
        self.assertEqual(schema['test_param']['type'], 'float')
    
    def test_strategy_context_functionality(self):
        """测试策略上下文功能"""
        # 测试历史数据获取
        historical_data = self.context.get_historical_data('AAPL', 30)
        self.assertIsInstance(historical_data, pd.DataFrame)
        self.mock_data_manager.get_historical_data.assert_called_once()
        
        # 测试指标计算
        mock_data = pd.DataFrame({'close': [100, 101, 102, 103, 104]})
        indicator_result = self.context.get_indicator('SMA', mock_data, period=5)
        self.assertIsInstance(indicator_result, pd.Series)
        self.mock_indicator_engine.calculate.assert_called_once()
        
        # 测试投资组合信息
        portfolio = self.context.get_portfolio()
        self.assertEqual(portfolio.cash, 100000.0)
        
        position = self.context.get_position('AAPL')
        self.assertEqual(position, 100)
        
        cash = self.context.get_cash()
        self.assertEqual(cash, 100000.0)
    
    def test_strategy_lifecycle_complete_workflow(self):
        """测试策略完整生命周期工作流"""
        # 创建策略
        strategy = ComprehensiveTestStrategy(
            name='lifecycle_test',
            parameters={'period': 10, 'threshold': 0.05}
        )
        
        # 注册策略
        self.assertTrue(self.lifecycle_manager.register_strategy(strategy, self.context))
        self.assertEqual(
            self.lifecycle_manager.get_strategy_state('lifecycle_test'),
            StrategyState.CREATED
        )
        
        # 初始化策略
        self.assertTrue(self.lifecycle_manager.initialize_strategy('lifecycle_test'))
        self.assertEqual(
            self.lifecycle_manager.get_strategy_state('lifecycle_test'),
            StrategyState.INITIALIZED
        )
        self.assertTrue(strategy.is_initialized)
        
        # 启动策略
        self.assertTrue(self.lifecycle_manager.start_strategy('lifecycle_test'))
        self.assertEqual(
            self.lifecycle_manager.get_strategy_state('lifecycle_test'),
            StrategyState.RUNNING
        )
        self.assertTrue(strategy.is_active)
        
        # 处理数据并生成信号
        market_data = MarketData(
            symbol='AAPL',
            timestamp=datetime.now(),
            open=100.0,
            high=105.0,
            low=95.0,
            close=102.0,
            volume=10000
        )
        
        # 第一次数据处理（建立基准价格）
        signals = self.lifecycle_manager.process_data('lifecycle_test', market_data)
        self.assertEqual(len(signals), 0)  # 第一次不应该有信号
        
        # 第二次数据处理（价格变化超过阈值）
        market_data.close = 110.0  # 价格上涨约8%，超过5%阈值
        signals = self.lifecycle_manager.process_data('lifecycle_test', market_data)
        self.assertEqual(len(signals), 1)
        self.assertEqual(signals[0].signal_type, SignalType.BUY)
        self.assertEqual(signals[0].symbol, 'AAPL')
        
        # 暂停策略
        self.assertTrue(self.lifecycle_manager.pause_strategy('lifecycle_test'))
        self.assertEqual(
            self.lifecycle_manager.get_strategy_state('lifecycle_test'),
            StrategyState.PAUSED
        )
        self.assertFalse(strategy.is_active)
        
        # 恢复策略
        self.assertTrue(self.lifecycle_manager.start_strategy('lifecycle_test'))
        self.assertEqual(
            self.lifecycle_manager.get_strategy_state('lifecycle_test'),
            StrategyState.RUNNING
        )
        
        # 停止策略
        self.assertTrue(self.lifecycle_manager.stop_strategy('lifecycle_test'))
        self.assertEqual(
            self.lifecycle_manager.get_strategy_state('lifecycle_test'),
            StrategyState.STOPPED
        )
        
        # 注销策略
        self.assertTrue(self.lifecycle_manager.unregister_strategy('lifecycle_test'))
        self.assertIsNone(self.lifecycle_manager.get_strategy_state('lifecycle_test'))
    
    def test_multi_symbol_data_processing(self):
        """测试多标的数据处理"""
        strategy = ComprehensiveTestStrategy('multi_symbol_test')
        self.lifecycle_manager.register_strategy(strategy, self.context)
        self.lifecycle_manager.initialize_strategy('multi_symbol_test')
        self.lifecycle_manager.start_strategy('multi_symbol_test')
        
        # 准备多标的数据
        multi_data = {
            'AAPL': MarketData(
                symbol='AAPL',
                timestamp=datetime.now(),
                open=150.0, high=155.0, low=148.0, close=152.0, volume=10000
            ),
            'GOOGL': MarketData(
                symbol='GOOGL',
                timestamp=datetime.now(),
                open=2800.0, high=2850.0, low=2780.0, close=2820.0, volume=5000
            )
        }
        
        # 第一次处理（建立基准）
        signals = self.lifecycle_manager.process_data('multi_symbol_test', multi_data)
        self.assertEqual(len(signals), 0)
        
        # 第二次处理（价格变化）
        multi_data['AAPL'].close = 160.0  # AAPL上涨约5.3%
        multi_data['GOOGL'].close = 2750.0  # GOOGL下跌约2.5%
        
        signals = self.lifecycle_manager.process_data('multi_symbol_test', multi_data)
        
        # 应该生成两个信号
        self.assertEqual(len(signals), 2)
        
        # 验证信号内容
        aapl_signal = next((s for s in signals if s.symbol == 'AAPL'), None)
        googl_signal = next((s for s in signals if s.symbol == 'GOOGL'), None)
        
        self.assertIsNotNone(aapl_signal)
        self.assertEqual(aapl_signal.signal_type, SignalType.BUY)
        
        self.assertIsNotNone(googl_signal)
        self.assertEqual(googl_signal.signal_type, SignalType.SELL)
    
    def test_error_handling_and_recovery(self):
        """测试错误处理和恢复"""
        # 创建会抛出异常的策略
        class ErrorStrategy(BaseStrategy):
            def get_parameter_definitions(self):
                return {}
            
            def initialize(self, context):
                self.context = context
                self.is_initialized = True
            
            def on_data(self, data):
                if hasattr(self, 'should_error') and self.should_error:
                    raise ValueError("模拟策略错误")
                return []
        
        strategy = ErrorStrategy('error_test')
        self.lifecycle_manager.register_strategy(strategy, self.context)
        self.lifecycle_manager.initialize_strategy('error_test')
        self.lifecycle_manager.start_strategy('error_test')
        
        # 正常处理
        market_data = MarketData(
            symbol='TEST',
            timestamp=datetime.now(),
            open=100.0, high=105.0, low=95.0, close=102.0, volume=1000
        )
        
        signals = self.lifecycle_manager.process_data('error_test', market_data)
        self.assertEqual(len(signals), 0)
        self.assertEqual(
            self.lifecycle_manager.get_strategy_state('error_test'),
            StrategyState.RUNNING
        )
        
        # 触发错误
        strategy.should_error = True
        signals = self.lifecycle_manager.process_data('error_test', market_data)
        self.assertEqual(len(signals), 0)
        self.assertEqual(
            self.lifecycle_manager.get_strategy_state('error_test'),
            StrategyState.ERROR
        )
    
    def test_parameter_validation_edge_cases(self):
        """测试参数验证边界情况"""
        # 测试自定义验证器
        def custom_validator(value):
            return isinstance(value, int) and value % 2 == 0  # 只允许偶数
        
        param = StrategyParameter(
            name='even_number',
            param_type=ParameterType.INTEGER,
            validator=custom_validator
        )
        
        self.assertTrue(param.validate(4))
        
        with self.assertRaises(ValueError):
            param.validate(3)  # 奇数应该失败
        
        # 测试选择值验证
        choice_param = StrategyParameter(
            name='choice_param',
            param_type=ParameterType.STRING,
            choices=['option1', 'option2', 'option3']
        )
        
        self.assertTrue(choice_param.validate('option1'))
        
        with self.assertRaises(ValueError):
            choice_param.validate('invalid_option')
        
        # 测试必需参数
        required_param = StrategyParameter(
            name='required_param',
            param_type=ParameterType.STRING,
            required=True
        )
        
        with self.assertRaises(ValueError):
            required_param.validate(None)
    
    def test_strategy_status_and_info(self):
        """测试策略状态和信息获取"""
        strategy = ComprehensiveTestStrategy(
            name='status_test',
            parameters={'period': 15, 'threshold': 0.03}
        )
        
        # 测试初始状态
        status = strategy.get_status()
        self.assertEqual(status['name'], 'status_test')
        self.assertFalse(status['is_initialized'])
        self.assertFalse(status['is_active'])
        self.assertEqual(status['parameters']['period'], 15)
        self.assertEqual(status['class_name'], 'ComprehensiveTestStrategy')
        
        # 注册并初始化后的状态
        self.lifecycle_manager.register_strategy(strategy, self.context)
        self.lifecycle_manager.initialize_strategy('status_test')
        
        status = strategy.get_status()
        self.assertTrue(status['is_initialized'])
        
        # 启动后的状态
        self.lifecycle_manager.start_strategy('status_test')
        status = strategy.get_status()
        self.assertTrue(status['is_active'])
        
        # 测试生命周期管理器的策略信息
        all_strategies = self.lifecycle_manager.get_all_strategies()
        self.assertIn('status_test', all_strategies)
        
        strategy_info = all_strategies['status_test']
        self.assertEqual(strategy_info['state'], StrategyState.RUNNING)
        self.assertIn('status', strategy_info)
        
        # 测试运行中策略列表
        running_strategies = self.lifecycle_manager.get_running_strategies()
        self.assertIn('status_test', running_strategies)
    
    def test_signal_integration_with_strategy(self):
        """测试信号与策略的集成"""
        strategy = ComprehensiveTestStrategy('signal_integration_test')
        self.lifecycle_manager.register_strategy(strategy, self.context)
        self.lifecycle_manager.initialize_strategy('signal_integration_test')
        self.lifecycle_manager.start_strategy('signal_integration_test')
        
        # 验证信号管理器集成
        signal_manager = self.lifecycle_manager.signal_manager
        initial_signal_count = len(signal_manager.signal_history)
        
        # 生成信号
        market_data = MarketData(
            symbol='TEST',
            timestamp=datetime.now(),
            open=100.0, high=105.0, low=95.0, close=100.0, volume=1000
        )
        
        # 第一次处理
        self.lifecycle_manager.process_data('signal_integration_test', market_data)
        
        # 第二次处理，触发信号
        market_data.close = 110.0
        signals = self.lifecycle_manager.process_data('signal_integration_test', market_data)
        
        # 验证信号被正确记录
        self.assertEqual(len(signals), 1)
        self.assertEqual(len(signal_manager.signal_history), initial_signal_count + 1)
        
        # 验证信号内容
        recorded_signal = signal_manager.signal_history[-1]
        self.assertEqual(recorded_signal.symbol, 'TEST')
        self.assertEqual(recorded_signal.strategy_name, 'signal_integration_test')
        self.assertEqual(recorded_signal.signal_type, SignalType.BUY)
        
        # 验证最新信号获取
        latest_signal = signal_manager.get_latest_signal('TEST')
        self.assertEqual(latest_signal.signal_id, recorded_signal.signal_id)


if __name__ == '__main__':
    unittest.main()