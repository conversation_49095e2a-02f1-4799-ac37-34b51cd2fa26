"""
Tests for Indicator Calculation Engine

This module contains unit tests for the indicator calculation engine
to ensure proper functionality of caching, parameter validation, and performance optimization.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.indicators.engine import (
    IndicatorEngine, IndicatorConfig, CalculationResult, IndicatorCache,
    get_engine, calculate_indicator, VectorizedCalculator
)


class TestIndicatorCache:
    """Test cases for indicator caching"""
    
    def setup_method(self):
        """Setup test cache"""
        self.cache = IndicatorCache(max_size=3)
    
    def test_cache_basic_operations(self):
        """Test basic cache operations"""
        # Create a mock result
        result = CalculationResult(
            indicator_name="sma",
            data=pd.Series([1, 2, 3]),
            params={"period": 10},
            calculation_time=0.001,
            data_hash="test_hash"
        )
        
        # Test put and get
        self.cache.put(result)
        retrieved = self.cache.get("test_hash", "sma", {"period": 10})
        
        assert retrieved is not None
        assert retrieved.indicator_name == "sma"
        assert retrieved.cache_hit == True
    
    def test_cache_miss(self):
        """Test cache miss"""
        result = self.cache.get("nonexistent_hash", "sma", {"period": 10})
        assert result is None
    
    def test_cache_size_limit(self):
        """Test cache size limit"""
        # Add more items than cache size
        for i in range(5):
            result = CalculationResult(
                indicator_name=f"indicator_{i}",
                data=pd.Series([1, 2, 3]),
                params={"period": 10},
                calculation_time=0.001,
                data_hash=f"hash_{i}"
            )
            self.cache.put(result)
        
        # Cache should only contain max_size items
        assert len(self.cache.cache) <= self.cache.max_size
    
    def test_cache_stats(self):
        """Test cache statistics"""
        stats = self.cache.get_stats()
        
        assert 'size' in stats
        assert 'max_size' in stats
        assert 'hit_rate' in stats
        assert stats['max_size'] == 3


class TestIndicatorEngine:
    """Test cases for indicator engine"""
    
    def setup_method(self):
        """Setup test engine and data"""
        self.engine = IndicatorEngine(enable_cache=True, cache_size=10)
        
        # Create test data
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        closes = [100]
        for i in range(99):
            change = np.random.randn() * 0.02
            closes.append(closes[-1] * (1 + change))
        
        highs = [c * (1 + abs(np.random.randn() * 0.01)) for c in closes]
        lows = [c * (1 - abs(np.random.randn() * 0.01)) for c in closes]
        volumes = [np.random.randint(1000, 10000) for _ in range(100)]
        
        self.data = pd.DataFrame({
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes
        }, index=dates)
        
        self.series_data = pd.Series(closes, index=dates)
    
    def test_engine_initialization(self):
        """Test engine initialization"""
        assert len(self.engine.get_available_indicators()) > 0
        assert 'sma' in self.engine.get_available_indicators()
        assert 'rsi' in self.engine.get_available_indicators()
        assert 'bollinger_bands' in self.engine.get_available_indicators()
    
    def test_calculate_single_indicator(self):
        """Test calculating a single indicator"""
        result = self.engine.calculate('sma', self.series_data, period=20)
        
        assert isinstance(result, CalculationResult)
        assert result.indicator_name == 'sma'
        assert isinstance(result.data, pd.Series)
        assert result.params['period'] == 20
        assert result.calculation_time >= 0
    
    def test_calculate_with_dataframe(self):
        """Test calculating indicator with DataFrame"""
        result = self.engine.calculate('atr', self.data, period=14)
        
        assert isinstance(result, CalculationResult)
        assert result.indicator_name == 'atr'
        assert isinstance(result.data, pd.Series)
    
    def test_calculate_complex_indicator(self):
        """Test calculating complex indicator (returns dict)"""
        result = self.engine.calculate('macd', self.series_data)
        
        assert isinstance(result, CalculationResult)
        assert isinstance(result.data, dict)
        assert 'macd' in result.data
        assert 'signal' in result.data
        assert 'histogram' in result.data
    
    def test_parameter_validation(self):
        """Test parameter validation"""
        # Test invalid period
        with pytest.raises(ValueError):
            self.engine.calculate('sma', self.series_data, period=0)
        
        # Test invalid parameter type
        with pytest.raises(ValueError):
            self.engine.calculate('sma', self.series_data, period="invalid")
    
    def test_data_validation(self):
        """Test data validation"""
        # Test empty data
        empty_data = pd.Series([], dtype=float)
        with pytest.raises(ValueError):
            self.engine.calculate('sma', empty_data, period=10)
        
        # Test missing columns
        incomplete_data = self.data[['close']]
        with pytest.raises(ValueError):
            self.engine.calculate('atr', incomplete_data, period=14)
    
    def test_caching(self):
        """Test indicator caching"""
        # First calculation
        result1 = self.engine.calculate('sma', self.series_data, period=20)
        assert not result1.cache_hit
        
        # Second calculation with same parameters should hit cache
        result2 = self.engine.calculate('sma', self.series_data, period=20)
        assert result2.cache_hit
        
        # Different parameters should not hit cache
        result3 = self.engine.calculate('sma', self.series_data, period=10)
        assert not result3.cache_hit
    
    def test_calculate_multiple(self):
        """Test calculating multiple indicators"""
        indicators = [
            ('sma', {'period': 20}),
            ('ema', {'period': 20}),
            ('rsi', {'period': 14})
        ]
        
        results = self.engine.calculate_multiple(indicators, self.series_data)
        
        assert len(results) == 3
        assert 'sma' in results
        assert 'ema' in results
        assert 'rsi' in results
        
        for result in results.values():
            assert isinstance(result, CalculationResult)
    
    def test_batch_calculate(self):
        """Test batch calculation"""
        datasets = [self.series_data[:50], self.series_data[25:75], self.series_data[50:]]
        
        results = self.engine.batch_calculate('sma', datasets, period=10)
        
        assert len(results) == 3
        for result in results:
            assert isinstance(result, CalculationResult)
            assert result.indicator_name == 'sma'
    
    def test_get_indicators_by_category(self):
        """Test getting indicators by category"""
        ma_indicators = self.engine.get_indicators_by_category('moving_averages')
        momentum_indicators = self.engine.get_indicators_by_category('momentum')
        
        assert 'sma' in ma_indicators
        assert 'ema' in ma_indicators
        assert 'rsi' in momentum_indicators
        assert 'macd' in momentum_indicators
    
    def test_indicator_info(self):
        """Test getting indicator information"""
        info = self.engine.get_indicator_info('sma')
        
        assert info is not None
        assert info.name == 'sma'
        assert info.description == 'Simple Moving Average'
        assert info.category == 'moving_averages'
        assert 'period' in info.default_params
    
    def test_performance_stats(self):
        """Test performance statistics"""
        # Calculate some indicators to populate cache
        self.engine.calculate('sma', self.series_data, period=20)
        self.engine.calculate('rsi', self.series_data, period=14)
        
        stats = self.engine.get_performance_stats()
        
        assert 'registered_indicators' in stats
        assert 'cache_enabled' in stats
        assert stats['cache_enabled'] == True
        assert stats['registered_indicators'] > 0
    
    def test_clear_cache(self):
        """Test clearing cache"""
        # Calculate indicator to populate cache
        self.engine.calculate('sma', self.series_data, period=20)
        
        # Clear cache
        self.engine.clear_cache()
        
        # Next calculation should not hit cache
        result = self.engine.calculate('sma', self.series_data, period=20)
        assert not result.cache_hit
    
    def test_unknown_indicator(self):
        """Test calculating unknown indicator"""
        with pytest.raises(ValueError):
            self.engine.calculate('unknown_indicator', self.series_data)
    
    def test_register_custom_indicator(self):
        """Test registering custom indicator"""
        def custom_indicator(data, multiplier=2):
            return data * multiplier
        
        config = IndicatorConfig(
            name="custom_multiply",
            function=custom_indicator,
            default_params={"multiplier": 2},
            required_columns=["close"],
            param_validators={"multiplier": lambda x: isinstance(x, (int, float))},
            description="Custom multiplier indicator",
            category="custom"
        )
        
        self.engine.register_indicator(config)
        
        # Test the custom indicator
        result = self.engine.calculate('custom_multiply', self.series_data, multiplier=3)
        
        assert isinstance(result, CalculationResult)
        assert result.indicator_name == 'custom_multiply'
        # Check that the result is the original data multiplied by 3
        expected = self.series_data * 3
        pd.testing.assert_series_equal(result.data, expected)


class TestGlobalEngine:
    """Test global engine functions"""
    
    def setup_method(self):
        """Setup test data"""
        self.data = pd.Series([100, 101, 102, 103, 104] * 20)
    
    def test_get_engine(self):
        """Test getting global engine"""
        engine = get_engine()
        assert isinstance(engine, IndicatorEngine)
        
        # Should return same instance
        engine2 = get_engine()
        assert engine is engine2
    
    def test_calculate_indicator_convenience(self):
        """Test convenience function"""
        result = calculate_indicator('sma', self.data, period=10)
        
        assert isinstance(result, CalculationResult)
        assert result.indicator_name == 'sma'


class TestVectorizedCalculator:
    """Test vectorized calculator utilities"""
    
    def setup_method(self):
        """Setup test data"""
        self.data = pd.Series(range(100), dtype=float)
        self.df_data = pd.DataFrame({
            'a': range(100),
            'b': range(100, 200),
            'c': range(200, 300)
        })
    
    def test_vectorized_conditions(self):
        """Test vectorized conditions"""
        conditions = [
            ('a', '>', 50),
            ('b', '<', 150),
            ('c', '>=', 250)
        ]
        
        result = VectorizedCalculator.vectorized_conditions(self.df_data, conditions)
        
        assert isinstance(result, pd.Series)
        assert result.dtype == bool
        
        # Check that conditions are applied correctly
        expected = (self.df_data['a'] > 50) & (self.df_data['b'] < 150) & (self.df_data['c'] >= 250)
        pd.testing.assert_series_equal(result, expected)
    
    def test_rolling_apply_parallel_fallback(self):
        """Test rolling apply with fallback (no joblib)"""
        def mean_func(x):
            return x.mean()
        
        result = VectorizedCalculator.rolling_apply_parallel(
            self.data, window=10, func=mean_func, n_jobs=1
        )
        
        assert isinstance(result, pd.Series)
        assert len(result) == len(self.data)
        
        # First 9 values should be NaN
        assert result.iloc[:9].isna().all()
        
        # 10th value should be mean of first 10 values
        expected_10th = self.data.iloc[:10].mean()
        assert abs(result.iloc[9] - expected_10th) < 1e-10


if __name__ == '__main__':
    pytest.main([__file__])