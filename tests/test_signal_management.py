"""
信号管理增强功能测试
"""

import unittest
from datetime import datetime
from src.strategies.signals import (
    Signal, SignalType, SignalQualityAssessor, SignalAggregator, 
    SignalConflictResolver, SignalRepository
)


class TestSignalManagement(unittest.TestCase):
    """信号管理测试类"""
    
    def test_signal_quality_assessment(self):
        """测试信号质量评估"""
        assessor = SignalQualityAssessor()
        
        # 创建一个完整的高质量信号
        high_quality_signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            strategy_name='test_strategy',
            confidence=0.9,
            quantity=100,
            price=150.0,
            stop_loss=145.0,
            take_profit=160.0,
            metadata={'reason': 'strong_momentum'}
        )
        
        quality_metrics = assessor.assess_signal_quality(high_quality_signal)
        
        # 验证质量指标
        self.assertGreater(quality_metrics['confidence_score'], 0.8)
        self.assertEqual(quality_metrics['completeness_score'], 1.0)  # 所有字段都完整
        self.assertGreater(quality_metrics['timing_score'], 0.8)  # 信号很新
        self.assertIn('overall_quality', quality_metrics)
    
    def test_signal_aggregation(self):
        """测试信号聚合"""
        aggregator = SignalAggregator()
        
        # 创建多个同向信号
        signals = [
            Signal(
                symbol='AAPL',
                signal_type=SignalType.BUY,
                timestamp=datetime.now(),
                strategy_name='strategy1',
                confidence=0.8
            ),
            Signal(
                symbol='AAPL',
                signal_type=SignalType.BUY,
                timestamp=datetime.now(),
                strategy_name='strategy2',
                confidence=0.7
            ),
            Signal(
                symbol='AAPL',
                signal_type=SignalType.SELL,
                timestamp=datetime.now(),
                strategy_name='strategy3',
                confidence=0.6
            )
        ]
        
        # 测试加权平均聚合
        aggregated = aggregator.aggregate_signals(signals, 'weighted_average')
        self.assertEqual(len(aggregated), 1)  # 买入信号胜出
        self.assertEqual(aggregated[0].signal_type, SignalType.BUY)
        
        # 测试多数投票聚合
        aggregated = aggregator.aggregate_signals(signals, 'majority_vote')
        self.assertEqual(len(aggregated), 1)
        self.assertEqual(aggregated[0].signal_type, SignalType.BUY)  # 买入信号数量多
        
        # 测试最高置信度聚合
        aggregated = aggregator.aggregate_signals(signals, 'highest_confidence')
        self.assertEqual(len(aggregated), 1)
        # 由于冲突解决器会先处理信号，置信度可能会被调整
        self.assertGreaterEqual(aggregated[0].confidence, 0.7)  # 应该是较高的置信度
    
    def test_signal_conflict_resolution(self):
        """测试信号冲突解决"""
        resolver = SignalConflictResolver()
        
        # 创建冲突信号
        buy_signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            strategy_name='strategy1',
            confidence=0.8
        )
        
        sell_signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.SELL,
            timestamp=datetime.now(),
            strategy_name='strategy2',
            confidence=0.6
        )
        
        conflicting_signals = [buy_signal, sell_signal]
        resolved = resolver.resolve_conflicts(conflicting_signals)
        
        # 应该选择置信度更高的买入信号
        self.assertEqual(len(resolved), 1)
        self.assertEqual(resolved[0].signal_type, SignalType.BUY)
    
    def test_signal_repository(self):
        """测试信号存储库"""
        repo = SignalRepository()
        
        # 创建测试信号
        signal1 = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            strategy_name='strategy1',
            confidence=0.8
        )
        
        signal2 = Signal(
            symbol='GOOGL',
            signal_type=SignalType.SELL,
            timestamp=datetime.now(),
            strategy_name='strategy1',
            confidence=0.7
        )
        
        # 保存信号
        self.assertTrue(repo.save_signal(signal1))
        self.assertTrue(repo.save_signal(signal2))
        
        # 按标的查询
        aapl_signals = repo.get_signals_by_symbol('AAPL')
        self.assertEqual(len(aapl_signals), 1)
        self.assertEqual(aapl_signals[0].symbol, 'AAPL')
        
        # 按策略查询
        strategy1_signals = repo.get_signals_by_strategy('strategy1')
        self.assertEqual(len(strategy1_signals), 2)
    
    def test_signal_completeness_assessment(self):
        """测试信号完整性评估"""
        assessor = SignalQualityAssessor()
        
        # 完整信号
        complete_signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            strategy_name='test',
            confidence=0.8,
            quantity=100,
            price=150.0,
            stop_loss=145.0,
            take_profit=160.0,
            metadata={'reason': 'test'}
        )
        
        completeness = assessor._assess_completeness(complete_signal)
        self.assertEqual(completeness, 1.0)
        
        # 不完整信号
        incomplete_signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            strategy_name='test',
            confidence=0.8
        )
        
        completeness = assessor._assess_completeness(incomplete_signal)
        self.assertEqual(completeness, 0.0)  # 没有可选字段
    
    def test_strategy_performance_tracking(self):
        """测试策略表现跟踪"""
        assessor = SignalQualityAssessor()
        
        # 更新策略表现
        performance_metrics = {
            'win_rate': 0.65,
            'avg_return': 0.08,
            'sharpe_ratio': 1.2
        }
        assessor.update_strategy_performance('test_strategy', performance_metrics)
        
        # 创建信号并评估
        signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            strategy_name='test_strategy',
            confidence=0.8
        )
        
        quality_metrics = assessor.assess_signal_quality(signal)
        
        # 验证策略表现被考虑在内
        self.assertEqual(quality_metrics['strategy_win_rate'], 0.65)
        self.assertEqual(quality_metrics['strategy_avg_return'], 0.08)
        self.assertEqual(quality_metrics['strategy_sharpe_ratio'], 1.2)


if __name__ == '__main__':
    unittest.main()