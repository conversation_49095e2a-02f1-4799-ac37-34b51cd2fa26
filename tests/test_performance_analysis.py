"""
Tests for the performance analysis module.

This test suite covers:
- Core performance metrics calculations
- Performance report generation
- Strategy comparison and analysis
- Edge cases and error handling
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.analysis.metrics import (
    PerformanceMetrics, MetricsCalculator, PerformanceFrequency
)
from src.analysis.reports import (
    PerformanceReporter, ReportConfig, ReportFormat, ChartGenerator
)
from src.analysis.comparison import (
    StrategyComparator, StrategyData, ComparisonResult, 
    ComparisonMethod, RankingMethod
)
from src.models.trading import Trade, OrderSide, OrderType
from src.models.portfolio import Portfolio


class TestPerformanceMetrics:
    """Test PerformanceMetrics class."""
    
    def test_metrics_initialization(self):
        """Test metrics initialization."""
        metrics = PerformanceMetrics()
        
        assert metrics.total_return == 0.0
        assert metrics.sharpe_ratio == 0.0
        assert metrics.max_drawdown == 0.0
        assert metrics.total_trades == 0
    
    def test_metrics_to_dict(self):
        """Test metrics conversion to dictionary."""
        metrics = PerformanceMetrics()
        metrics.total_return = 0.15
        metrics.sharpe_ratio = 1.2
        metrics.max_drawdown = -0.08
        
        result_dict = metrics.to_dict()
        
        assert isinstance(result_dict, dict)
        assert 'basic_returns' in result_dict
        assert 'risk_adjusted' in result_dict
        assert 'drawdowns' in result_dict
        assert result_dict['basic_returns']['total_return'] == 0.15
        assert result_dict['risk_adjusted']['sharpe_ratio'] == 1.2
        assert result_dict['drawdowns']['max_drawdown'] == -0.08


class TestMetricsCalculator:
    """Test MetricsCalculator class."""
    
    @pytest.fixture
    def sample_portfolio_values(self):
        """Create sample portfolio values for testing."""
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        
        # Create a portfolio that goes up with some volatility
        np.random.seed(42)
        returns = np.random.normal(0.0008, 0.02, 252)  # Daily returns ~20% annual vol
        returns[50:60] = -0.05  # Simulate a drawdown period
        
        portfolio_values = pd.Series(100000.0, index=dates)
        for i in range(1, len(portfolio_values)):
            portfolio_values.iloc[i] = portfolio_values.iloc[i-1] * (1 + returns[i])
        
        return portfolio_values
    
    @pytest.fixture
    def sample_trades(self):
        """Create sample trades for testing."""
        trades = []
        base_date = datetime(2023, 1, 1)
        
        for i in range(10):
            trade = Trade(
                id=f"trade_{i}",
                symbol="AAPL",
                side=OrderSide.BUY if i % 2 == 0 else OrderSide.SELL,
                quantity=100,
                price=150.0 + i,
                timestamp=base_date + timedelta(days=i*10)
            )
            # Add some P&L
            trade.pnl = np.random.normal(100, 200)  # Random P&L
            trades.append(trade)
        
        return trades
    
    def test_calculator_initialization(self):
        """Test calculator initialization."""
        calc = MetricsCalculator()
        assert calc.risk_free_rate == 0.02
        
        calc_custom = MetricsCalculator(risk_free_rate=0.03)
        assert calc_custom.risk_free_rate == 0.03
    
    def test_calculate_metrics_basic(self, sample_portfolio_values):
        """Test basic metrics calculation."""
        calc = MetricsCalculator()
        
        metrics = calc.calculate_metrics(sample_portfolio_values)
        
        assert isinstance(metrics, PerformanceMetrics)
        assert metrics.total_return != 0
        assert metrics.annualized_return != 0
        assert metrics.volatility >= 0
        assert metrics.max_drawdown <= 0
        assert metrics.start_date is not None
        assert metrics.end_date is not None
        assert metrics.period_length > 0
    
    def test_calculate_metrics_with_trades(self, sample_portfolio_values, sample_trades):
        """Test metrics calculation with trades."""
        calc = MetricsCalculator()
        
        metrics = calc.calculate_metrics(sample_portfolio_values, sample_trades)
        
        assert metrics.total_trades == len(sample_trades)
        assert metrics.winning_trades >= 0
        assert metrics.losing_trades >= 0
        assert metrics.win_rate >= 0
        assert metrics.win_rate <= 1
    
    def test_calculate_metrics_with_benchmark(self, sample_portfolio_values):
        """Test metrics calculation with benchmark."""
        calc = MetricsCalculator()
        
        # Create benchmark (similar dates, different performance)
        benchmark_values = sample_portfolio_values * 0.8  # Underperforming benchmark
        
        metrics = calc.calculate_metrics(sample_portfolio_values, 
                                       benchmark_values=benchmark_values)
        
        assert metrics.beta != 0
        assert metrics.alpha != 0
        assert metrics.correlation != 0
        assert -1 <= metrics.correlation <= 1
    
    def test_calculate_rolling_metrics(self, sample_portfolio_values):
        """Test rolling metrics calculation."""
        calc = MetricsCalculator()
        
        rolling_metrics = calc.calculate_rolling_metrics(sample_portfolio_values, window=60)
        
        assert isinstance(rolling_metrics, pd.DataFrame)
        assert 'sharpe_ratio' in rolling_metrics.columns
        assert 'volatility' in rolling_metrics.columns
        assert 'max_drawdown' in rolling_metrics.columns
        assert len(rolling_metrics) > 0
    
    def test_calculate_monthly_returns(self, sample_portfolio_values):
        """Test monthly returns calculation."""
        calc = MetricsCalculator()
        
        monthly_returns = calc.calculate_monthly_returns(sample_portfolio_values)
        
        assert isinstance(monthly_returns, pd.DataFrame)
        assert 'Annual' in monthly_returns.columns
        assert len(monthly_returns) > 0
    
    def test_insufficient_data(self):
        """Test behavior with insufficient data."""
        calc = MetricsCalculator()
        
        # Single data point
        single_point = pd.Series([100000], index=[datetime(2023, 1, 1)])
        
        with pytest.raises(ValueError):
            calc.calculate_metrics(single_point)


class TestPerformanceReporter:
    """Test PerformanceReporter class."""
    
    @pytest.fixture
    def sample_portfolio_values(self):
        """Create sample portfolio values for testing."""
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        returns = np.random.normal(0.001, 0.02, 100)
        
        portfolio_values = pd.Series(100000.0, index=dates)
        for i in range(1, len(portfolio_values)):
            portfolio_values.iloc[i] = portfolio_values.iloc[i-1] * (1 + returns[i])
        
        return portfolio_values
    
    def test_reporter_initialization(self):
        """Test reporter initialization."""
        config = ReportConfig(title="Test Report")
        reporter = PerformanceReporter(config)
        
        assert reporter.config.title == "Test Report"
        assert isinstance(reporter.chart_generator, ChartGenerator)
    
    def test_generate_report_dict_format(self, sample_portfolio_values):
        """Test report generation in dictionary format."""
        reporter = PerformanceReporter()
        
        report = reporter.generate_report(sample_portfolio_values, format=ReportFormat.DICT)
        
        assert isinstance(report, dict)
        assert 'metadata' in report
        assert 'summary' in report
        assert 'performance_metrics' in report
        assert 'risk_metrics' in report
        assert report['metadata']['title'] == "Performance Analysis Report"
    
    def test_generate_report_json_format(self, sample_portfolio_values):
        """Test report generation in JSON format."""
        reporter = PerformanceReporter()
        
        report = reporter.generate_report(sample_portfolio_values, format=ReportFormat.JSON)
        
        assert isinstance(report, str)
        # Should be valid JSON
        import json
        parsed = json.loads(report)
        assert isinstance(parsed, dict)
    
    def test_generate_report_html_format(self, sample_portfolio_values):
        """Test report generation in HTML format."""
        reporter = PerformanceReporter()
        
        report = reporter.generate_report(sample_portfolio_values, format=ReportFormat.HTML)
        
        assert isinstance(report, str)
        assert "<!DOCTYPE html>" in report
        assert "<title>" in report
        assert "</html>" in report
    
    def test_chart_generation(self, sample_portfolio_values):
        """Test chart data generation."""
        config = ReportConfig(include_charts=True)
        reporter = PerformanceReporter(config)
        
        report = reporter.generate_report(sample_portfolio_values)
        
        assert 'charts' in report
        assert 'equity_curve' in report['charts']
        assert 'drawdown' in report['charts']
        assert 'returns_distribution' in report['charts']
    
    def test_report_with_benchmark(self, sample_portfolio_values):
        """Test report generation with benchmark."""
        benchmark = sample_portfolio_values * 0.9  # Underperforming benchmark
        reporter = PerformanceReporter()
        
        report = reporter.generate_report(sample_portfolio_values, 
                                        benchmark_values=benchmark)
        
        assert 'benchmark_comparison' in report
        assert report['benchmark_comparison'] is not None
        assert 'relative_performance' in report['benchmark_comparison']
    
    def test_config_options(self, sample_portfolio_values):
        """Test various configuration options."""
        config = ReportConfig(
            include_charts=False,
            include_monthly_returns=False,
            include_rolling_metrics=False,
            title="Custom Title"
        )
        reporter = PerformanceReporter(config)
        
        report = reporter.generate_report(sample_portfolio_values)
        
        assert report['metadata']['title'] == "Custom Title"
        assert report['charts'] is None
        assert report['monthly_returns'] is None
        assert report['rolling_metrics'] is None


class TestChartGenerator:
    """Test ChartGenerator class."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample data for chart testing."""
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        values = pd.Series(range(100000, 105000, 100), index=dates)
        return values
    
    def test_equity_curve_generation(self, sample_data):
        """Test equity curve chart generation."""
        config = ReportConfig()
        generator = ChartGenerator(config)
        
        chart_data = generator.generate_equity_curve_data(sample_data)
        
        assert chart_data['type'] == 'line_chart'
        assert 'dates' in chart_data['data']
        assert 'portfolio' in chart_data['data']
        assert len(chart_data['data']['dates']) == len(sample_data)
        assert chart_data['data']['portfolio'][0] == 100.0  # Normalized to 100
    
    def test_drawdown_chart_generation(self, sample_data):
        """Test drawdown chart generation."""
        config = ReportConfig()
        generator = ChartGenerator(config)
        
        chart_data = generator.generate_drawdown_chart_data(sample_data)
        
        assert chart_data['type'] == 'area_chart'
        assert 'dates' in chart_data['data']
        assert 'drawdown' in chart_data['data']
        assert len(chart_data['data']['dates']) == len(sample_data)
    
    def test_returns_distribution_generation(self, sample_data):
        """Test returns distribution chart generation."""
        returns = sample_data.pct_change().dropna()
        config = ReportConfig()
        generator = ChartGenerator(config)
        
        chart_data = generator.generate_returns_distribution_data(returns)
        
        assert chart_data['type'] == 'histogram'
        assert 'bins' in chart_data['data']
        assert 'density' in chart_data['data']
        assert len(chart_data['data']['bins']) > 0


class TestStrategyComparator:
    """Test StrategyComparator class."""
    
    @pytest.fixture
    def sample_strategies(self):
        """Create sample strategies for comparison."""
        strategies = []
        
        for i in range(3):
            dates = pd.date_range('2023-01-01', periods=100, freq='D')
            np.random.seed(42 + i)
            returns = np.random.normal(0.001 + i*0.0005, 0.02, 100)
            
            portfolio_values = pd.Series(100000.0, index=dates)
            for j in range(1, len(portfolio_values)):
                portfolio_values.iloc[j] = portfolio_values.iloc[j-1] * (1 + returns[j])
            
            strategy = StrategyData(
                name=f"Strategy_{i+1}",
                portfolio_values=portfolio_values
            )
            strategies.append(strategy)
        
        return strategies
    
    def test_comparator_initialization(self):
        """Test comparator initialization."""
        comparator = StrategyComparator()
        assert comparator.risk_free_rate == 0.02
        
        comparator_custom = StrategyComparator(risk_free_rate=0.03)
        assert comparator_custom.risk_free_rate == 0.03
    
    def test_compare_strategies_basic(self, sample_strategies):
        """Test basic strategy comparison."""
        comparator = StrategyComparator()
        
        result = comparator.compare_strategies(sample_strategies)
        
        assert isinstance(result, ComparisonResult)
        assert len(result.strategy_metrics) == 3
        assert not result.comparison_matrix.empty
        assert 'overall' in result.rankings
        assert not result.correlation_matrix.empty
        assert len(result.optimal_weights) == 3
    
    def test_compare_strategies_with_methods(self, sample_strategies):
        """Test strategy comparison with specific methods."""
        comparator = StrategyComparator()
        
        methods = [ComparisonMethod.SHARPE_RATIO, ComparisonMethod.TOTAL_RETURN]
        result = comparator.compare_strategies(sample_strategies, methods)
        
        assert len(result.comparison_matrix.index) == 2
        assert 'sharpe_ratio' in result.rankings
        assert 'total_return' in result.rankings
    
    def test_ranking_methods(self, sample_strategies):
        """Test different ranking methods."""
        comparator = StrategyComparator()
        
        # Test weighted score ranking
        result1 = comparator.compare_strategies(
            sample_strategies, ranking_method=RankingMethod.WEIGHTED_SCORE
        )
        
        # Test risk adjusted ranking
        result2 = comparator.compare_strategies(
            sample_strategies, ranking_method=RankingMethod.RISK_ADJUSTED_RETURN
        )
        
        # Test consistency ranking
        result3 = comparator.compare_strategies(
            sample_strategies, ranking_method=RankingMethod.CONSISTENCY
        )
        
        assert 'overall' in result1.rankings
        assert 'overall' in result2.rankings
        assert 'overall' in result3.rankings
        
        # Rankings might be different
        # (They could be the same by chance, so we just check they exist)
    
    def test_comparison_report(self, sample_strategies):
        """Test comparison report generation."""
        comparator = StrategyComparator()
        result = comparator.compare_strategies(sample_strategies)
        
        report = comparator.generate_comparison_report(result)
        
        assert isinstance(report, dict)
        assert 'summary' in report
        assert 'rankings' in report
        assert 'key_metrics' in report
        assert len(report['key_metrics']) == 3
    
    def test_comparison_report_with_details(self, sample_strategies):
        """Test detailed comparison report."""
        comparator = StrategyComparator()
        result = comparator.compare_strategies(sample_strategies)
        
        report = comparator.generate_comparison_report(result, include_details=True)
        
        assert 'detailed_analysis' in report
        assert 'correlation_matrix' in report['detailed_analysis']
        assert 'optimal_weights' in report['detailed_analysis']
        assert 'recommendations' in report['detailed_analysis']
    
    def test_insufficient_strategies(self):
        """Test behavior with insufficient strategies."""
        comparator = StrategyComparator()
        
        # Single strategy
        single_strategy = [StrategyData(
            name="Single",
            portfolio_values=pd.Series([100, 101, 102])
        )]
        
        with pytest.raises(ValueError):
            comparator.compare_strategies(single_strategy)


class TestStrategyData:
    """Test StrategyData class."""
    
    def test_strategy_data_creation(self):
        """Test strategy data creation."""
        portfolio_values = pd.Series([100, 101, 102])
        
        strategy = StrategyData(
            name="Test Strategy",
            portfolio_values=portfolio_values
        )
        
        assert strategy.name == "Test Strategy"
        assert len(strategy.portfolio_values) == 3
        assert strategy.trades is None
        assert strategy.benchmark_values is None
        assert isinstance(strategy.metadata, dict)


class TestIntegration:
    """Integration tests for the performance analysis module."""
    
    def test_full_analysis_workflow(self):
        """Test complete analysis workflow."""
        # Create sample data
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        np.random.seed(42)
        returns = np.random.normal(0.0008, 0.02, 252)
        
        portfolio_values = pd.Series(100000.0, index=dates)
        for i in range(1, len(portfolio_values)):
            portfolio_values.iloc[i] = portfolio_values.iloc[i-1] * (1 + returns[i])
        
        # Create trades
        trades = []
        for i in range(5):
            trade = Trade(
                id=f"trade_{i}",
                symbol="AAPL",
                side=OrderSide.BUY if i % 2 == 0 else OrderSide.SELL,
                quantity=100,
                price=150.0,
                timestamp=dates[i*50]
            )
            trade.pnl = 1000 if i % 2 == 0 else -500
            trades.append(trade)
        
        # Calculate metrics
        calculator = MetricsCalculator()
        metrics = calculator.calculate_metrics(portfolio_values, trades)
        
        # Generate report
        reporter = PerformanceReporter()
        report = reporter.generate_report(portfolio_values, trades)
        
        # Create strategies for comparison
        strategies = []
        for i in range(2):
            np.random.seed(42 + i)
            strategy_returns = np.random.normal(0.0008, 0.02, 252)
            
            strategy_values = pd.Series(100000.0, index=dates)
            for j in range(1, len(strategy_values)):
                strategy_values.iloc[j] = strategy_values.iloc[j-1] * (1 + strategy_returns[j])
            
            strategies.append(StrategyData(
                name=f"Strategy_{i+1}",
                portfolio_values=strategy_values
            ))
        
        # Compare strategies
        comparator = StrategyComparator()
        comparison = comparator.compare_strategies(strategies)
        
        # Verify all components work together
        assert isinstance(metrics, PerformanceMetrics)
        assert isinstance(report, dict)
        assert isinstance(comparison, ComparisonResult)
        assert metrics.total_trades == 5
        assert 'summary' in report
        assert len(comparison.strategy_metrics) == 2


if __name__ == "__main__":
    pytest.main([__file__, "-v"])