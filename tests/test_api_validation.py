"""
API验证测试 - 简化版本
"""

import pytest
from datetime import datetime, timedelta
from pydantic import ValidationError

from src.api.routers.data import MarketDataQuery, EconomicDataQuery
from src.api.routers.backtest import BacktestRequest


class TestDataQueryValidation:
    """数据查询参数验证测试"""
    
    def test_market_data_query_validation(self):
        """测试市场数据查询参数验证"""
        # 有效的查询
        valid_query = MarketDataQuery(
            symbols=["AAPL", "GOOGL"],
            start_date=datetime.now() - timedelta(days=30),
            end_date=datetime.now(),
            interval="1d"
        )
        assert len(valid_query.symbols) == 2
        assert valid_query.interval == "1d"
        
        # 测试空符号列表
        with pytest.raises(ValidationError):
            MarketDataQuery(
                symbols=[],
                start_date=datetime.now() - timedelta(days=30),
                end_date=datetime.now()
            )
        
        # 测试过多符号
        with pytest.raises(ValidationError):
            MarketDataQuery(
                symbols=[f"SYMBOL{i}" for i in range(101)],
                start_date=datetime.now() - timedelta(days=30),
                end_date=datetime.now()
            )
        
        # 测试无效间隔
        with pytest.raises(ValidationError):
            MarketDataQuery(
                symbols=["AAPL"],
                start_date=datetime.now() - timedelta(days=30),
                end_date=datetime.now(),
                interval="invalid"
            )
    
    def test_economic_data_query_validation(self):
        """测试经济数据查询参数验证"""
        # 有效的查询
        valid_query = EconomicDataQuery(
            series_ids=["GDP", "UNRATE"],
            limit=1000
        )
        assert len(valid_query.series_ids) == 2
        assert valid_query.limit == 1000
        
        # 测试空序列列表
        with pytest.raises(ValidationError):
            EconomicDataQuery(series_ids=[])
        
        # 测试过多序列
        with pytest.raises(ValidationError):
            EconomicDataQuery(
                series_ids=[f"SERIES{i}" for i in range(51)]
            )
    
    def test_backtest_request_validation(self):
        """测试回测请求参数验证"""
        # 有效的请求
        valid_request = BacktestRequest(
            strategy_id="test_strategy",
            symbols=["AAPL"],
            start_date=datetime.now() - timedelta(days=365),
            end_date=datetime.now() - timedelta(days=1),
            initial_capital=100000.0
        )
        assert valid_request.strategy_id == "test_strategy"
        assert len(valid_request.symbols) == 1
        assert valid_request.initial_capital == 100000.0
        
        # 测试空符号列表
        with pytest.raises(ValidationError):
            BacktestRequest(
                strategy_id="test_strategy",
                symbols=[],
                start_date=datetime.now() - timedelta(days=365),
                end_date=datetime.now() - timedelta(days=1)
            )
        
        # 测试过多符号
        with pytest.raises(ValidationError):
            BacktestRequest(
                strategy_id="test_strategy",
                symbols=[f"SYMBOL{i}" for i in range(21)],
                start_date=datetime.now() - timedelta(days=365),
                end_date=datetime.now() - timedelta(days=1)
            )


class TestResponseModels:
    """响应模型测试"""
    
    def test_market_data_response_creation(self):
        """测试市场数据响应模型创建"""
        from src.api.routers.data import MarketDataResponse
        
        response = MarketDataResponse(
            symbol="AAPL",
            timestamp=datetime.now(),
            open=150.0,
            high=155.0,
            low=148.0,
            close=152.0,
            volume=1000000,
            market="US",
            exchange="NASDAQ"
        )
        
        assert response.symbol == "AAPL"
        assert response.open == 150.0
        assert response.volume == 1000000
        assert response.market == "US"
    
    def test_backtest_result_creation(self):
        """测试回测结果模型创建"""
        from src.api.routers.backtest import BacktestResult
        
        result = BacktestResult(
            id="bt_123",
            strategy_id="strategy_456",
            strategy_name="测试策略",
            symbols=["AAPL"],
            start_date=datetime.now() - timedelta(days=365),
            end_date=datetime.now(),
            initial_capital=100000.0,
            final_capital=120000.0,
            total_return=0.20,
            annual_return=0.20,
            max_drawdown=-0.08,
            sharpe_ratio=1.25,
            sortino_ratio=1.45,
            calmar_ratio=2.5,
            win_rate=0.65,
            profit_factor=1.8,
            total_trades=150,
            winning_trades=98,
            losing_trades=52,
            avg_win=800.0,
            avg_loss=-400.0,
            status="completed",
            created_at=datetime.now()
        )
        
        assert result.id == "bt_123"
        assert result.total_return == 0.20
        assert result.total_trades == 150
        assert result.status == "completed"


class TestAPIDocumentation:
    """API文档测试"""
    
    def test_api_documentation_exists(self):
        """测试API文档是否存在"""
        import os
        doc_path = "src/api/data_query_api_docs.md"
        assert os.path.exists(doc_path), "API文档文件不存在"
        
        with open(doc_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查文档包含关键章节
        assert "# 数据查询API文档" in content
        assert "## 1. 市场数据查询接口" in content
        assert "## 2. 策略配置和管理API" in content
        assert "## 3. 回测结果查询和导出接口" in content
        assert "## 4. 绩效数据和报告API" in content
        
        # 检查包含示例代码
        assert "```python" in content
        assert "```javascript" in content
        assert "```json" in content


if __name__ == "__main__":
    pytest.main([__file__, "-v"])