"""
Tests for Technical Indicators

This module contains unit tests for all technical indicators
to ensure they work correctly with various data inputs.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.indicators.moving_averages import sma, ema, wma
from src.indicators.momentum import rsi, macd, kdj
from src.indicators.volatility import bollinger_bands, atr
from src.indicators.volume import obv, vwap


class TestMovingAverages:
    """Test cases for moving average indicators"""
    
    def setup_method(self):
        """Setup test data"""
        dates = pd.date_range(start='2023-01-01', periods=50, freq='D')
        np.random.seed(42)  # For reproducible results
        prices = 100 + np.cumsum(np.random.randn(50) * 0.5)
        self.data = pd.Series(prices, index=dates)
        self.df_data = pd.DataFrame({'close': prices}, index=dates)
    
    def test_sma_basic(self):
        """Test basic SMA calculation"""
        result = sma(self.data, 10)
        
        # Check that result is a pandas Series
        assert isinstance(result, pd.Series)
        
        # Check that first 9 values are NaN (not enough data)
        assert result.iloc[:9].isna().all()
        
        # Check that 10th value is the mean of first 10 values
        expected = self.data.iloc[:10].mean()
        assert abs(result.iloc[9] - expected) < 1e-10
    
    def test_sma_with_dataframe(self):
        """Test SMA with DataFrame input"""
        result = sma(self.df_data, 10)
        
        assert isinstance(result, pd.Series)
        assert len(result) == len(self.df_data)
    
    def test_sma_invalid_period(self):
        """Test SMA with invalid period"""
        with pytest.raises(ValueError):
            sma(self.data, 0)
        
        with pytest.raises(ValueError):
            sma(self.data, 100)  # Period larger than data
    
    def test_ema_basic(self):
        """Test basic EMA calculation"""
        result = ema(self.data, 10)
        
        assert isinstance(result, pd.Series)
        assert len(result) == len(self.data)
        
        # EMA should not have NaN values after the first value
        assert not result.iloc[1:].isna().any()
    
    def test_wma_basic(self):
        """Test basic WMA calculation"""
        result = wma(self.data, 10)
        
        assert isinstance(result, pd.Series)
        assert result.iloc[:9].isna().all()
        assert not result.iloc[9:].isna().any()


class TestMomentumIndicators:
    """Test cases for momentum indicators"""
    
    def setup_method(self):
        """Setup test data"""
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        # Create more realistic price data with trends
        prices = [100]
        for i in range(99):
            change = np.random.randn() * 0.02  # 2% daily volatility
            prices.append(prices[-1] * (1 + change))
        
        self.data = pd.Series(prices, index=dates)
        self.df_data = pd.DataFrame({'close': prices}, index=dates)
    
    def test_rsi_basic(self):
        """Test basic RSI calculation"""
        result = rsi(self.data, 14)
        
        assert isinstance(result, pd.Series)
        assert len(result) == len(self.data)
        
        # RSI values should be between 0 and 100
        valid_values = result.dropna()
        assert (valid_values >= 0).all()
        assert (valid_values <= 100).all()
    
    def test_rsi_with_dataframe(self):
        """Test RSI with DataFrame input"""
        result = rsi(self.df_data, 14)
        
        assert isinstance(result, pd.Series)
        assert len(result) == len(self.df_data)
    
    def test_macd_basic(self):
        """Test basic MACD calculation"""
        result = macd(self.data)
        
        assert isinstance(result, dict)
        assert 'macd' in result
        assert 'signal' in result
        assert 'histogram' in result
        
        # All components should be pandas Series
        for key, series in result.items():
            assert isinstance(series, pd.Series)
            assert len(series) == len(self.data)
    
    def test_macd_invalid_periods(self):
        """Test MACD with invalid periods"""
        with pytest.raises(ValueError):
            macd(self.data, fast_period=26, slow_period=12)  # fast >= slow


class TestVolatilityIndicators:
    """Test cases for volatility indicators"""
    
    def setup_method(self):
        """Setup OHLCV test data"""
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        # Generate realistic OHLCV data
        closes = [100]
        for i in range(99):
            change = np.random.randn() * 0.02
            closes.append(closes[-1] * (1 + change))
        
        # Generate high, low based on close with some randomness
        highs = [c * (1 + abs(np.random.randn() * 0.01)) for c in closes]
        lows = [c * (1 - abs(np.random.randn() * 0.01)) for c in closes]
        volumes = [np.random.randint(1000, 10000) for _ in range(100)]
        
        self.data = pd.DataFrame({
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes
        }, index=dates)
    
    def test_bollinger_bands_basic(self):
        """Test basic Bollinger Bands calculation"""
        result = bollinger_bands(self.data['close'], 20, 2.0)
        
        assert isinstance(result, dict)
        expected_keys = ['upper', 'middle', 'lower', 'bandwidth', 'percent_b']
        for key in expected_keys:
            assert key in result
            assert isinstance(result[key], pd.Series)
        
        # Upper band should be above middle, middle above lower
        valid_idx = ~result['middle'].isna()
        assert (result['upper'][valid_idx] >= result['middle'][valid_idx]).all()
        assert (result['middle'][valid_idx] >= result['lower'][valid_idx]).all()
    
    def test_atr_basic(self):
        """Test basic ATR calculation"""
        result = atr(self.data, 14)
        
        assert isinstance(result, pd.Series)
        assert len(result) == len(self.data)
        
        # ATR values should be positive
        valid_values = result.dropna()
        assert (valid_values >= 0).all()
    
    def test_atr_missing_columns(self):
        """Test ATR with missing columns"""
        incomplete_data = self.data[['close', 'volume']]
        with pytest.raises(ValueError):
            atr(incomplete_data, 14)


class TestVolumeIndicators:
    """Test cases for volume indicators"""
    
    def setup_method(self):
        """Setup OHLCV test data"""
        dates = pd.date_range(start='2023-01-01', periods=50, freq='D')
        np.random.seed(42)
        
        closes = [100]
        for i in range(49):
            change = np.random.randn() * 0.02
            closes.append(closes[-1] * (1 + change))
        
        highs = [c * (1 + abs(np.random.randn() * 0.01)) for c in closes]
        lows = [c * (1 - abs(np.random.randn() * 0.01)) for c in closes]
        volumes = [np.random.randint(1000, 10000) for _ in range(50)]
        
        self.data = pd.DataFrame({
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes
        }, index=dates)
    
    def test_obv_basic(self):
        """Test basic OBV calculation"""
        result = obv(self.data)
        
        assert isinstance(result, pd.Series)
        assert len(result) == len(self.data)
        
        # OBV should be cumulative, so later values should generally be larger in absolute terms
        assert not result.isna().any()
    
    def test_vwap_basic(self):
        """Test basic VWAP calculation"""
        result = vwap(self.data)
        
        assert isinstance(result, pd.Series)
        assert len(result) == len(self.data)
        
        # VWAP values should be positive and reasonable
        valid_values = result.dropna()
        assert (valid_values > 0).all()
    
    def test_vwap_rolling(self):
        """Test rolling VWAP calculation"""
        result = vwap(self.data, period=20)
        
        assert isinstance(result, pd.Series)
        assert len(result) == len(self.data)
        
        # First 19 values should be NaN
        assert result.iloc[:19].isna().all()
        assert not result.iloc[19:].isna().any()
    
    def test_volume_indicators_missing_columns(self):
        """Test volume indicators with missing columns"""
        incomplete_data = self.data[['close']]
        
        with pytest.raises(ValueError):
            obv(incomplete_data)
        
        with pytest.raises(ValueError):
            vwap(incomplete_data)


class TestIndicatorEdgeCases:
    """Test edge cases and error conditions"""
    
    def test_empty_data(self):
        """Test indicators with empty data"""
        empty_series = pd.Series([], dtype=float)
        empty_df = pd.DataFrame()
        
        with pytest.raises(ValueError):
            sma(empty_series, 10)
    
    def test_single_value_data(self):
        """Test indicators with single value"""
        single_value = pd.Series([100.0])
        
        with pytest.raises(ValueError):
            sma(single_value, 10)  # Period larger than data
    
    def test_all_same_values(self):
        """Test indicators with constant values"""
        constant_data = pd.Series([100.0] * 50)
        
        # SMA of constant data should be constant
        result = sma(constant_data, 10)
        valid_values = result.dropna()
        assert (valid_values == 100.0).all()
        
        # RSI of constant data should be around 50 (no change)
        rsi_result = rsi(constant_data, 14)
        # Note: RSI of constant data is undefined, so we expect NaN
        assert rsi_result.dropna().empty or (rsi_result.dropna() == 50).all()


if __name__ == '__main__':
    pytest.main([__file__])