"""
Tests for Custom Indicator Framework

This module contains unit tests for the custom indicator framework
including formula indicators, composite indicators, and the test framework.
"""

import pytest
import pandas as pd
import numpy as np
import tempfile
import os
from datetime import datetime

from src.indicators.custom import (
    BaseCustomIndicator, FormulaIndicator, CompositeIndicator,
    IndicatorMetadata, CustomIndicatorManager, IndicatorTestFramework,
    IndicatorTemplate
)
from src.indicators.engine import IndicatorEngine


class TestIndicatorMetadata:
    """Test cases for indicator metadata"""
    
    def test_metadata_creation(self):
        """Test creating indicator metadata"""
        metadata = IndicatorMetadata(
            name="test_indicator",
            description="Test indicator",
            category="test",
            author="Test Author",
            tags=["test", "custom"],
            references=["Test Reference"]
        )
        
        assert metadata.name == "test_indicator"
        assert metadata.description == "Test indicator"
        assert metadata.category == "test"
        assert metadata.author == "Test Author"
        assert "test" in metadata.tags
        assert "Test Reference" in metadata.references
        assert isinstance(metadata.created_date, datetime)


class SimpleCustomIndicator(BaseCustomIndicator):
    """Simple custom indicator for testing"""
    
    def __init__(self, name: str):
        super().__init__(name)
        self._parameters = {"multiplier": 2.0}
        self._required_columns = ["close"]
        self._parameter_validators = {
            "multiplier": lambda x: isinstance(x, (int, float)) and x > 0
        }
    
    def calculate(self, data, **params):
        self.validate_data(data)
        self.validate_parameters(params)
        
        multiplier = params.get("multiplier", self._parameters["multiplier"])
        
        if isinstance(data, pd.DataFrame):
            return data["close"] * multiplier
        else:
            return data * multiplier


class TestBaseCustomIndicator:
    """Test cases for base custom indicator"""
    
    def setup_method(self):
        """Setup test data"""
        self.data = pd.Series([100, 101, 102, 103, 104])
        self.df_data = pd.DataFrame({
            'close': [100, 101, 102, 103, 104],
            'volume': [1000, 1100, 1200, 1300, 1400]
        })
        self.indicator = SimpleCustomIndicator("test_indicator")
    
    def test_indicator_creation(self):
        """Test creating a custom indicator"""
        assert self.indicator.name == "test_indicator"
        assert self.indicator.get_default_parameters() == {"multiplier": 2.0}
        assert self.indicator.get_required_columns() == ["close"]
    
    def test_calculate_with_series(self):
        """Test calculating with Series data"""
        result = self.indicator.calculate(self.data, multiplier=3.0)
        
        expected = self.data * 3.0
        pd.testing.assert_series_equal(result, expected)
    
    def test_calculate_with_dataframe(self):
        """Test calculating with DataFrame data"""
        result = self.indicator.calculate(self.df_data, multiplier=2.5)
        
        expected = self.df_data["close"] * 2.5
        pd.testing.assert_series_equal(result, expected)
    
    def test_parameter_validation(self):
        """Test parameter validation"""
        with pytest.raises(ValueError):
            self.indicator.calculate(self.data, multiplier=-1)  # Invalid multiplier
    
    def test_data_validation(self):
        """Test data validation"""
        incomplete_data = pd.DataFrame({'volume': [1000, 1100, 1200]})
        
        with pytest.raises(ValueError):
            self.indicator.calculate(incomplete_data)
    
    def test_to_config(self):
        """Test converting to IndicatorConfig"""
        config = self.indicator.to_config()
        
        assert config.name == "test_indicator"
        assert config.default_params == {"multiplier": 2.0}
        assert config.required_columns == ["close"]
    
    def test_generate_documentation(self):
        """Test documentation generation"""
        # Add some metadata
        self.indicator.metadata.author = "Test Author"
        self.indicator.metadata.tags = ["test", "custom"]
        self.indicator.metadata.examples = [{
            'description': 'Basic usage',
            'code': 'indicator.calculate(data, multiplier=2.0)'
        }]
        
        doc = self.indicator.generate_documentation()
        
        assert "# test_indicator" in doc
        assert "Test Author" in doc
        assert "test, custom" in doc
        assert "Basic usage" in doc


class TestFormulaIndicator:
    """Test cases for formula-based indicators"""
    
    def setup_method(self):
        """Setup test data"""
        self.data = pd.DataFrame({
            'close': [100, 101, 102, 103, 104],
            'volume': [1000, 1100, 1200, 1300, 1400]
        })
    
    def test_simple_formula(self):
        """Test simple formula indicator"""
        formula = "close * 2"
        indicator = FormulaIndicator("double_close", formula)
        
        result = indicator.calculate(self.data)
        expected = self.data['close'] * 2
        
        pd.testing.assert_series_equal(result, expected)
    
    def test_formula_with_parameters(self):
        """Test formula with parameters"""
        formula = "close * multiplier"
        indicator = FormulaIndicator("parameterized", formula)
        
        result = indicator.calculate(self.data, multiplier=3.0)
        expected = self.data['close'] * 3.0
        
        pd.testing.assert_series_equal(result, expected)
    
    def test_complex_formula(self):
        """Test complex formula with multiple operations"""
        formula = "(close + volume / 1000) * factor"
        indicator = FormulaIndicator("complex", formula)
        
        result = indicator.calculate(self.data, factor=1.5)
        expected = (self.data['close'] + self.data['volume'] / 1000) * 1.5
        
        pd.testing.assert_series_equal(result, expected)
    
    def test_formula_with_numpy_functions(self):
        """Test formula with numpy functions"""
        formula = "np.log(close)"
        indicator = FormulaIndicator("log_close", formula)
        
        result = indicator.calculate(self.data)
        expected = np.log(self.data['close'])
        
        pd.testing.assert_series_equal(result, expected)
    
    def test_invalid_formula(self):
        """Test invalid formula syntax"""
        with pytest.raises(ValueError):
            FormulaIndicator("invalid", "close +")  # Invalid syntax
    
    def test_formula_runtime_error(self):
        """Test formula runtime error"""
        formula = "nonexistent_function(close)"
        indicator = FormulaIndicator("error_test", formula)
        
        # This should raise a RuntimeError due to undefined function
        with pytest.raises(RuntimeError):
            indicator.calculate(self.data)


class TestCompositeIndicator:
    """Test cases for composite indicators"""
    
    def setup_method(self):
        """Setup test data and engine"""
        self.data = pd.Series([100, 101, 102, 103, 104] * 10)  # Longer series for indicators
        self.engine = IndicatorEngine()
    
    def test_composite_indicator(self):
        """Test composite indicator combining SMA and EMA"""
        def combine_ma(components, **params):
            sma_data = components['sma']
            ema_data = components['ema']
            weight = params.get('weight', 0.5)
            return sma_data * weight + ema_data * (1 - weight)
        
        components = [
            ('sma', {'period': 10}),
            ('ema', {'period': 10})
        ]
        
        indicator = CompositeIndicator("weighted_ma", components, combine_ma)
        indicator.set_engine(self.engine)
        
        result = indicator.calculate(self.data, weight=0.6)
        
        assert isinstance(result, pd.Series)
        assert len(result) == len(self.data)
    
    def test_composite_without_engine(self):
        """Test composite indicator without engine"""
        def dummy_combine(components):
            return components['sma']
        
        indicator = CompositeIndicator("test", [('sma', {})], dummy_combine)
        
        with pytest.raises(RuntimeError):
            indicator.calculate(self.data)


class TestCustomIndicatorManager:
    """Test cases for custom indicator manager"""
    
    def setup_method(self):
        """Setup manager and test data"""
        self.engine = IndicatorEngine()
        self.manager = CustomIndicatorManager(self.engine)
        self.data = pd.Series([100, 101, 102, 103, 104])
    
    def test_register_indicator(self):
        """Test registering custom indicator"""
        indicator = SimpleCustomIndicator("test_simple")
        self.manager.register_indicator(indicator)
        
        assert "test_simple" in self.manager.list_indicators()
        assert self.manager.get_indicator("test_simple") is indicator
    
    def test_create_formula_indicator(self):
        """Test creating formula indicator"""
        metadata = IndicatorMetadata(
            name="double_price",
            description="Double the price",
            category="test"
        )
        
        indicator = self.manager.create_formula_indicator(
            "double_price", "close * 2", metadata
        )
        
        assert isinstance(indicator, FormulaIndicator)
        assert "double_price" in self.manager.list_indicators()
        
        # Test calculation through engine
        result = self.engine.calculate("double_price", self.data)
        expected = self.data * 2
        pd.testing.assert_series_equal(result.data, expected)
    
    def test_create_composite_indicator(self):
        """Test creating composite indicator"""
        def simple_average(components, **params):
            return (components['sma'] + components['ema']) / 2
        
        components = [('sma', {'period': 5}), ('ema', {'period': 5})]
        
        indicator = self.manager.create_composite_indicator(
            "avg_ma", components, simple_average
        )
        
        assert isinstance(indicator, CompositeIndicator)
        assert "avg_ma" in self.manager.list_indicators()
    
    def test_remove_indicator(self):
        """Test removing indicator"""
        indicator = SimpleCustomIndicator("to_remove")
        self.manager.register_indicator(indicator)
        
        assert "to_remove" in self.manager.list_indicators()
        
        removed = self.manager.remove_indicator("to_remove")
        assert removed == True
        assert "to_remove" not in self.manager.list_indicators()
        
        # Try removing non-existent indicator
        removed = self.manager.remove_indicator("non_existent")
        assert removed == False
    
    def test_export_import_indicator(self):
        """Test exporting and importing indicator"""
        # Create indicator
        metadata = IndicatorMetadata(
            name="export_test",
            description="Test export/import",
            category="test",
            author="Test Author"
        )
        
        original = self.manager.create_formula_indicator(
            "export_test", "close * factor", metadata
        )
        
        # Export to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            self.manager.export_indicator("export_test", temp_path)
            
            # Create new manager and import
            new_manager = CustomIndicatorManager()
            imported = new_manager.import_indicator(temp_path)
            
            assert imported.name == "export_test"
            assert imported.metadata.description == "Test export/import"
            assert imported.metadata.author == "Test Author"
            assert isinstance(imported, FormulaIndicator)
            
        finally:
            os.unlink(temp_path)
    
    def test_generate_documentation(self):
        """Test documentation generation"""
        # Create some indicators
        self.manager.create_formula_indicator("test1", "close * 2")
        self.manager.create_formula_indicator("test2", "close + volume")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            self.manager.generate_documentation(temp_dir)
            
            # Check that files were created
            assert os.path.exists(os.path.join(temp_dir, "README.md"))
            assert os.path.exists(os.path.join(temp_dir, "test1.md"))
            assert os.path.exists(os.path.join(temp_dir, "test2.md"))
            
            # Check README content
            with open(os.path.join(temp_dir, "README.md"), 'r') as f:
                readme_content = f.read()
                assert "test1" in readme_content
                assert "test2" in readme_content


class TestIndicatorTestFramework:
    """Test cases for indicator test framework"""
    
    def setup_method(self):
        """Setup test framework"""
        self.framework = IndicatorTestFramework()
        self.data = pd.Series([100, 101, 102, 103, 104])
        self.indicator = SimpleCustomIndicator("test_indicator")
    
    def test_add_test_case(self):
        """Test adding test case"""
        expected = self.data * 2.0
        
        self.framework.add_test_case(
            "simple_test",
            self.indicator,
            self.data,
            expected,
            {"multiplier": 2.0}
        )
        
        assert len(self.framework.test_cases) == 1
        assert self.framework.test_cases[0]['name'] == "simple_test"
    
    def test_run_tests_pass(self):
        """Test running tests that pass"""
        expected = self.data * 3.0
        
        self.framework.add_test_case(
            "passing_test",
            self.indicator,
            self.data,
            expected,
            {"multiplier": 3.0}
        )
        
        results = self.framework.run_tests()
        
        assert results["passing_test"] == True
    
    def test_run_tests_fail(self):
        """Test running tests that fail"""
        wrong_expected = self.data * 5.0  # Wrong expectation
        
        self.framework.add_test_case(
            "failing_test",
            self.indicator,
            self.data,
            wrong_expected,
            {"multiplier": 2.0}
        )
        
        results = self.framework.run_tests()
        
        assert results["failing_test"] == False
    
    def test_custom_validation_function(self):
        """Test custom validation function"""
        def custom_validator(result):
            # Check that all values are positive
            return (result > 0).all()
        
        self.framework.add_test_case(
            "custom_validation",
            self.indicator,
            self.data,
            custom_validator,
            {"multiplier": 2.0}
        )
        
        results = self.framework.run_tests()
        
        assert results["custom_validation"] == True
    
    def test_generate_test_report(self):
        """Test generating test report"""
        # Add passing test
        self.framework.add_test_case(
            "pass_test",
            self.indicator,
            self.data,
            self.data * 2.0,
            {"multiplier": 2.0}
        )
        
        # Add failing test
        self.framework.add_test_case(
            "fail_test",
            self.indicator,
            self.data,
            self.data * 5.0,  # Wrong expectation
            {"multiplier": 2.0}
        )
        
        report = self.framework.generate_test_report()
        
        assert "**Total Tests:** 2" in report
        assert "**Passed:** 1" in report
        assert "**Failed:** 1" in report
        assert "pass_test: ✅ PASS" in report
        assert "fail_test: ❌ FAIL" in report


class TestIndicatorTemplate:
    """Test cases for indicator templates"""
    
    def test_create_template_indicators(self):
        """Test creating indicators from template"""
        base_indicator = FormulaIndicator("base", "close * multiplier")
        
        parameter_sets = [
            {"multiplier": 1.5},
            {"multiplier": 2.0},
            {"multiplier": 2.5}
        ]
        
        template = IndicatorTemplate("multi_multiplier", base_indicator, parameter_sets)
        indicators = template.create_indicators()
        
        assert len(indicators) == 3
        assert indicators[0].name == "multi_multiplier_1"
        assert indicators[1].name == "multi_multiplier_2"
        assert indicators[2].name == "multi_multiplier_3"
        
        # Check that parameters were updated
        assert indicators[0]._parameters["multiplier"] == 1.5
        assert indicators[1]._parameters["multiplier"] == 2.0
        assert indicators[2]._parameters["multiplier"] == 2.5


if __name__ == '__main__':
    pytest.main([__file__])