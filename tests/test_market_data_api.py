"""
Test for Market Data API endpoints.
"""

import pytest
import sys
from pathlib import Path

# Add src to path for testing
sys.path.append(str(Path(__file__).parent.parent.parent.parent / "src"))

from fastapi.testclient import TestClient
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Import the app factory
from src.api.app import create_app


@pytest.fixture
def client():
    """Create test client."""
    app = create_app()
    return TestClient(app)


@pytest.fixture
def auth_headers():
    """Create auth headers for testing."""
    # For testing with auth disabled
    return {}


class TestMarketDataAPI:
    """Test market data API endpoints."""
    
    def test_health_check(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
    
    def test_api_info(self, client, auth_headers):
        """Test API info endpoint."""
        response = client.get("/api/v1/info", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert "name" in data
        assert "endpoints" in data
    
    @patch('src.data.repository.MarketDataRepository')
    def test_get_market_data_single_symbol(self, mock_repo, client, auth_headers):
        """Test getting market data for a single symbol."""
        # Mock repository response
        mock_data = Mock()
        mock_data.empty = False
        mock_data.to_dict.return_value = [
            {
                'symbol': 'AAPL',
                'timestamp': '2023-01-01T00:00:00',
                'open': 100.0,
                'high': 105.0,
                'low': 99.0,
                'close': 104.0,
                'volume': 1000000
            }
        ]
        
        mock_repo_instance = Mock()
        mock_repo_instance.get_data_by_symbol.return_value = mock_data
        mock_repo.return_value = mock_repo_instance
        
        # Test endpoint
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        response = client.get(
            f"/api/v1/market/data/AAPL?start_date={start_date.isoformat()}&end_date={end_date.isoformat()}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "AAPL" in data["data"]
        assert data["metadata"]["symbol"] == "AAPL"
    
    def test_get_market_data_missing_params(self, client, auth_headers):
        """Test getting market data with missing required parameters."""
        response = client.get("/api/v1/market/data/AAPL", headers=auth_headers)
        # Should fail due to missing start_date parameter
        assert response.status_code == 422  # Validation error
    
    @patch('src.data.repository.MarketDataRepository')
    def test_batch_market_data(self, mock_repo, client, auth_headers):
        """Test batch market data endpoint."""
        # Mock repository response
        mock_data = Mock()
        mock_data.empty = False
        mock_data.to_dict.return_value = [
            {
                'symbol': 'AAPL',
                'timestamp': '2023-01-01T00:00:00',
                'open': 100.0,
                'high': 105.0,
                'low': 99.0,
                'close': 104.0,
                'volume': 1000000
            }
        ]
        mock_data.__len__ = Mock(return_value=1)
        
        mock_repo_instance = Mock()
        mock_repo_instance.get_data_by_symbol.return_value = mock_data
        mock_repo.return_value = mock_repo_instance
        
        # Test endpoint
        query_data = {
            "symbols": ["AAPL", "GOOGL"],
            "start_date": (datetime.now() - timedelta(days=30)).isoformat(),
            "end_date": datetime.now().isoformat(),
            "interval": "1d"
        }
        
        response = client.post(
            "/api/v1/market/data/batch",
            json=query_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "metadata" in data
    
    def test_batch_market_data_validation(self, client, auth_headers):
        """Test batch market data validation."""
        # Test with empty symbols
        query_data = {
            "symbols": [],
            "start_date": datetime.now().isoformat(),
            "end_date": datetime.now().isoformat()
        }
        
        response = client.post(
            "/api/v1/market/data/batch",
            json=query_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Validation error
    
    @patch('src.data.adapters.manager.DataSourceManager')
    def test_data_sources_status(self, mock_manager, client, auth_headers):
        """Test data sources status endpoint."""
        # Mock manager response
        mock_manager_instance = Mock()
        mock_manager_instance.get_all_adapter_status.return_value = {
            "yahoo": {"healthy": True, "last_check": "2023-01-01T00:00:00"},
            "tushare": {"healthy": False, "last_check": "2023-01-01T00:00:00"}
        }
        mock_manager_instance.get_performance_stats.return_value = {
            "total_requests": 100,
            "successful_requests": 95,
            "average_response_time": 0.5
        }
        mock_manager.return_value = mock_manager_instance
        
        response = client.get("/api/v1/market/sources/status", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "sources" in data
        assert "performance" in data
        assert data["total_adapters"] == 2
        assert data["healthy_adapters"] == 1


if __name__ == "__main__":
    # Run a simple test
    app = create_app()
    client = TestClient(app)
    
    # Test health check
    response = client.get("/health")
    print(f"Health check: {response.status_code} - {response.json()}")
    
    # Test API info
    response = client.get("/api/v1/info")
    print(f"API info: {response.status_code} - {response.json()}")
    
    print("Basic Market Data API tests passed!")