"""
数据查询API测试
"""

import pytest
from fastapi.testclient import TestClient
from datetime import datetime, timedelta
import json

from src.api.app import create_app


@pytest.fixture
def client():
    """创建测试客户端"""
    app = create_app()
    return TestClient(app)


@pytest.fixture
def auth_headers():
    """模拟认证头"""
    return {"Authorization": "Bearer test_token"}


class TestMarketDataAPI:
    """市场数据API测试"""
    
    def test_get_symbols(self, client, auth_headers):
        """测试获取交易品种列表"""
        response = client.get("/api/v1/data/symbols", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        
        if data:  # 如果有数据
            symbol = data[0]
            assert "symbol" in symbol
            assert "name" in symbol
            assert "exchange" in symbol
            assert "market" in symbol
    
    def test_get_symbols_with_filters(self, client, auth_headers):
        """测试带筛选条件的品种列表"""
        response = client.get(
            "/api/v1/data/symbols",
            params={"market": "US", "active_only": True},
            headers=auth_headers
        )
        assert response.status_code == 200
    
    def test_get_market_data(self, client, auth_headers):
        """测试获取市场数据"""
        start_date = (datetime.now() - timedelta(days=30)).isoformat()
        end_date = datetime.now().isoformat()
        
        response = client.get(
            "/api/v1/data/market-data/AAPL",
            params={
                "start_date": start_date,
                "end_date": end_date,
                "interval": "1d"
            },
            headers=auth_headers
        )
        
        # 可能返回404（如果没有数据）或200
        assert response.status_code in [200, 404]
        
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, list)
    
    def test_batch_market_data(self, client, auth_headers):
        """测试批量获取市场数据"""
        request_data = {
            "symbols": ["AAPL", "GOOGL"],
            "start_date": (datetime.now() - timedelta(days=7)).isoformat(),
            "end_date": datetime.now().isoformat(),
            "interval": "1d"
        }
        
        response = client.post(
            "/api/v1/data/market-data/batch",
            json=request_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "success" in data
        assert "data" in data
        assert "metadata" in data
    
    def test_get_economic_data(self, client, auth_headers):
        """测试获取经济数据"""
        response = client.get(
            "/api/v1/data/economic/GDP",
            headers=auth_headers
        )
        
        # 可能返回404（如果没有数据）或200
        assert response.status_code in [200, 404]
    
    def test_batch_economic_data(self, client, auth_headers):
        """测试批量获取经济数据"""
        request_data = {
            "series_ids": ["GDP", "UNRATE"],
            "limit": 100
        }
        
        response = client.post(
            "/api/v1/data/economic/batch",
            json=request_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "success" in data
        assert "data" in data
    
    def test_get_data_coverage(self, client, auth_headers):
        """测试获取数据覆盖范围"""
        response = client.get(
            "/api/v1/data/coverage",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_export_market_data(self, client, auth_headers):
        """测试导出市场数据"""
        start_date = (datetime.now() - timedelta(days=7)).isoformat()
        
        response = client.get(
            "/api/v1/data/export/AAPL",
            params={
                "start_date": start_date,
                "format": "csv"
            },
            headers=auth_headers
        )
        
        # 可能返回404（如果没有数据）或200
        assert response.status_code in [200, 404]


class TestStrategiesAPI:
    """策略管理API测试"""
    
    def test_list_strategies(self, client, auth_headers):
        """测试获取策略列表"""
        response = client.get("/api/v1/strategies/list", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "success" in data
        assert "strategies" in data
        assert "total_count" in data
    
    def test_get_strategy_types(self, client, auth_headers):
        """测试获取可用策略类型"""
        response = client.get("/api/v1/strategies/types/available", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "success" in data
        assert "strategy_types" in data
        assert isinstance(data["strategy_types"], list)
    
    def test_create_strategy(self, client, auth_headers):
        """测试创建策略"""
        strategy_data = {
            "name": "测试策略",
            "strategy_type": "moving_average_crossover",
            "parameters": {
                "fast_period": 10,
                "slow_period": 20
            },
            "symbols": ["AAPL"],
            "active": True
        }
        
        response = client.post(
            "/api/v1/strategies/create",
            json=strategy_data,
            headers=auth_headers
        )
        
        # 可能因为缺少实际实现而失败，但应该返回合理的状态码
        assert response.status_code in [200, 400, 500]


class TestBacktestAPI:
    """回测API测试"""
    
    def test_get_backtest_results(self, client, auth_headers):
        """测试获取回测结果列表"""
        response = client.get("/api/v1/backtest/results", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
    
    def test_run_backtest(self, client, auth_headers):
        """测试运行回测"""
        backtest_data = {
            "strategy_id": "test_strategy",
            "symbols": ["AAPL"],
            "start_date": (datetime.now() - timedelta(days=365)).isoformat(),
            "end_date": (datetime.now() - timedelta(days=1)).isoformat(),
            "initial_capital": 100000.0
        }
        
        response = client.post(
            "/api/v1/backtest/run",
            json=backtest_data,
            headers=auth_headers
        )
        
        # 可能因为缺少实际实现而失败，但应该返回合理的状态码
        assert response.status_code in [200, 400, 500]
    
    def test_get_backtest_statistics(self, client, auth_headers):
        """测试获取回测统计信息"""
        response = client.get("/api/v1/backtest/statistics", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "success" in data


class TestPerformanceAPI:
    """绩效分析API测试"""
    
    def test_get_performance_metrics(self, client, auth_headers):
        """测试获取绩效指标"""
        response = client.get(
            "/api/v1/performance/metrics/test_strategy",
            headers=auth_headers
        )
        
        # 可能因为策略不存在而返回404
        assert response.status_code in [200, 404, 500]
    
    def test_compare_strategies(self, client, auth_headers):
        """测试策略比较"""
        compare_data = {
            "strategy_ids": ["strategy1", "strategy2"]
        }
        
        response = client.post(
            "/api/v1/performance/compare/strategies",
            json=compare_data,
            headers=auth_headers
        )
        
        # 可能因为策略不存在而返回404或400
        assert response.status_code in [200, 400, 404, 500]
    
    def test_get_strategy_ranking(self, client, auth_headers):
        """测试策略排名"""
        response = client.get(
            "/api/v1/performance/ranking/strategies",
            params={"metric": "sharpe_ratio", "limit": 10},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "success" in data


class TestAPIValidation:
    """API参数验证测试"""
    
    def test_invalid_date_format(self, client, auth_headers):
        """测试无效日期格式"""
        response = client.get(
            "/api/v1/data/market-data/AAPL",
            params={
                "start_date": "invalid_date",
                "end_date": "2024-01-01"
            },
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_missing_required_params(self, client, auth_headers):
        """测试缺少必需参数"""
        response = client.get(
            "/api/v1/data/market-data/AAPL",
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Missing required parameter
    
    def test_invalid_symbol_format(self, client, auth_headers):
        """测试无效品种格式"""
        start_date = datetime.now().isoformat()
        
        response = client.get(
            "/api/v1/data/market-data/",  # Empty symbol
            params={"start_date": start_date},
            headers=auth_headers
        )
        
        assert response.status_code == 404  # Not found
    
    def test_batch_request_validation(self, client, auth_headers):
        """测试批量请求验证"""
        # 测试空符号列表
        request_data = {
            "symbols": [],
            "start_date": datetime.now().isoformat(),
            "end_date": datetime.now().isoformat()
        }
        
        response = client.post(
            "/api/v1/data/market-data/batch",
            json=request_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Validation error
        
        # 测试过多符号
        request_data = {
            "symbols": [f"SYMBOL{i}" for i in range(101)],  # 超过100个
            "start_date": datetime.now().isoformat(),
            "end_date": datetime.now().isoformat()
        }
        
        response = client.post(
            "/api/v1/data/market-data/batch",
            json=request_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Validation error


class TestAPIAuthentication:
    """API认证测试"""
    
    def test_no_auth_header(self, client):
        """测试无认证头"""
        response = client.get("/api/v1/data/symbols")
        assert response.status_code == 401  # Unauthorized
    
    def test_invalid_auth_header(self, client):
        """测试无效认证头"""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/data/symbols", headers=headers)
        assert response.status_code in [401, 403]  # Unauthorized or Forbidden
    
    def test_missing_bearer_prefix(self, client):
        """测试缺少Bearer前缀"""
        headers = {"Authorization": "test_token"}
        response = client.get("/api/v1/data/symbols", headers=headers)
        assert response.status_code == 401  # Unauthorized


if __name__ == "__main__":
    pytest.main([__file__, "-v"])