"""
Tests for enhanced data source manager functionality.
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import tempfile
import yaml
import os

from src.data.adapters.manager import DataSourceManager
from src.data.adapters.base import DataSourceConfig, MockDataSourceAdapter
from src.models.market_data import UnifiedMarketData, EconomicData
from src.exceptions import DataException


@pytest.fixture
def temp_config_file():
    """Create temporary configuration file."""
    config_data = {
        'mock_us': {
            'adapter_class': 'src.data.adapters.base.MockDataSourceAdapter',
            'enabled': True,
            'priority': 1,
            'rate_limit': {'requests_per_minute': 60},
            'credentials': {},
            'default_params': {}
        },
        'mock_crypto': {
            'adapter_class': 'src.data.adapters.base.MockDataSourceAdapter',
            'enabled': True,
            'priority': 2,
            'rate_limit': {'requests_per_minute': 60},
            'credentials': {},
            'default_params': {}
        }
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.dump(config_data, f)
        temp_file = f.name
    
    yield temp_file
    
    # Cleanup
    os.unlink(temp_file)


@pytest.fixture
def mock_adapters():
    """Create mock adapters for testing."""
    mock_us_adapter = Mock(spec=MockDataSourceAdapter)
    mock_us_adapter.config = DataSourceConfig(
        name='mock_us',
        adapter_class='MockDataSourceAdapter',
        enabled=True,
        priority=1,
        rate_limit={},
        credentials={},
        default_params={}
    )
    mock_us_adapter.is_healthy = True
    mock_us_adapter.last_health_check = datetime.now()
    mock_us_adapter.last_error = None
    mock_us_adapter.get_market_type.return_value = 'US'
    mock_us_adapter.get_exchange_name.return_value = 'MOCK_US'
    mock_us_adapter.validate_symbol.return_value = True
    mock_us_adapter.get_performance_stats.return_value = {
        'total_requests': 10,
        'requests_last_hour': 5,
        'requests_last_day': 10
    }
    mock_us_adapter.get_status.return_value = {
        'healthy': True,
        'enabled': True
    }
    
    mock_crypto_adapter = Mock(spec=MockDataSourceAdapter)
    mock_crypto_adapter.config = DataSourceConfig(
        name='mock_crypto',
        adapter_class='MockDataSourceAdapter',
        enabled=True,
        priority=2,
        rate_limit={},
        credentials={},
        default_params={}
    )
    mock_crypto_adapter.is_healthy = True
    mock_crypto_adapter.last_health_check = datetime.now()
    mock_crypto_adapter.last_error = None
    mock_crypto_adapter.get_market_type.return_value = 'CRYPTO'
    mock_crypto_adapter.get_exchange_name.return_value = 'MOCK_CRYPTO'
    mock_crypto_adapter.validate_symbol.return_value = True
    mock_crypto_adapter.get_performance_stats.return_value = {
        'total_requests': 8,
        'requests_last_hour': 3,
        'requests_last_day': 8
    }
    mock_crypto_adapter.get_status.return_value = {
        'healthy': True,
        'enabled': True
    }
    
    return {
        'mock_us': mock_us_adapter,
        'mock_crypto': mock_crypto_adapter
    }


class TestEnhancedDataSourceManager:
    """Test cases for enhanced data source manager."""
    
    @patch('src.data.adapters.manager.DataSourceManager._create_adapter')
    def test_initialization_with_scheduler(self, mock_create_adapter, temp_config_file, mock_adapters):
        """Test manager initialization with background scheduler."""
        mock_create_adapter.side_effect = lambda config: mock_adapters.get(config.name)
        
        manager = DataSourceManager(temp_config_file)
        
        assert manager.scheduler_running is True
        assert manager.scheduler_thread is not None
        assert len(manager.adapters) == 2
        
        # Cleanup
        manager.stop_scheduler()
    
    @patch('src.data.adapters.manager.DataSourceManager._create_adapter')
    def test_adapter_priority_order(self, mock_create_adapter, temp_config_file, mock_adapters):
        """Test getting adapters in priority order."""
        mock_create_adapter.side_effect = lambda config: mock_adapters.get(config.name)
        
        manager = DataSourceManager(temp_config_file)
        
        priority_order = manager.get_adapter_priority_order('TEST_SYMBOL')
        
        # Should be ordered by priority (lower number = higher priority)
        assert priority_order == ['mock_us', 'mock_crypto']
        
        manager.stop_scheduler()
    
    @patch('src.data.adapters.manager.DataSourceManager._create_adapter')
    def test_failover_adapter(self, mock_create_adapter, temp_config_file, mock_adapters):
        """Test getting failover adapter."""
        mock_create_adapter.side_effect = lambda config: mock_adapters.get(config.name)
        
        manager = DataSourceManager(temp_config_file)
        
        # Test getting failover for primary adapter
        failover = manager.get_failover_adapter('mock_us', 'TEST_SYMBOL')
        assert failover == 'mock_crypto'
        
        # Test when primary is last in priority
        failover = manager.get_failover_adapter('mock_crypto', 'TEST_SYMBOL')
        assert failover is None
        
        manager.stop_scheduler()
    
    @patch('src.data.adapters.manager.DataSourceManager._create_adapter')
    def test_fetch_with_failover(self, mock_create_adapter, temp_config_file, mock_adapters):
        """Test fetching data with automatic failover."""
        mock_create_adapter.side_effect = lambda config: mock_adapters.get(config.name)
        
        # Setup mock data
        sample_data = [
            UnifiedMarketData(
                symbol='TEST',
                timestamp=datetime.now(),
                open=100.0,
                high=105.0,
                low=95.0,
                close=102.0,
                volume=1000.0,
                market='US',
                exchange='MOCK',
                currency='USD'
            )
        ]
        
        # First adapter fails, second succeeds
        mock_adapters['mock_us'].fetch_historical_data.side_effect = Exception("Connection failed")
        mock_adapters['mock_crypto'].fetch_historical_data.return_value = sample_data
        
        manager = DataSourceManager(temp_config_file)
        
        start_date = datetime.now() - timedelta(days=1)
        end_date = datetime.now()
        
        success, data, adapter_name = manager.fetch_with_failover('TEST', start_date, end_date)
        
        assert success is True
        assert len(data) == 1
        assert adapter_name == 'mock_crypto'
        
        manager.stop_scheduler()
    
    @patch('src.data.adapters.manager.DataSourceManager._create_adapter')
    def test_sync_schedule_management(self, mock_create_adapter, temp_config_file, mock_adapters):
        """Test sync schedule management."""
        mock_create_adapter.side_effect = lambda config: mock_adapters.get(config.name)
        
        manager = DataSourceManager(temp_config_file)
        
        # Add sync schedule
        success = manager.add_sync_schedule(
            'test_schedule',
            ['AAPL', 'GOOGL'],
            interval_minutes=30,
            days_back=7
        )
        
        assert success is True
        assert 'test_schedule' in manager.sync_schedules
        
        schedule_config = manager.sync_schedules['test_schedule']
        assert schedule_config['symbols'] == ['AAPL', 'GOOGL']
        assert schedule_config['interval_minutes'] == 30
        assert schedule_config['days_back'] == 7
        
        # Remove sync schedule
        success = manager.remove_sync_schedule('test_schedule')
        assert success is True
        assert 'test_schedule' not in manager.sync_schedules
        
        manager.stop_scheduler()
    
    @patch('src.data.adapters.manager.DataSourceManager._create_adapter')
    def test_comprehensive_performance_report(self, mock_create_adapter, temp_config_file, mock_adapters):
        """Test comprehensive performance report generation."""
        mock_create_adapter.side_effect = lambda config: mock_adapters.get(config.name)
        
        manager = DataSourceManager(temp_config_file)
        
        report = manager.get_comprehensive_performance_report()
        
        assert 'generated_at' in report
        assert report['total_adapters'] == 2
        assert report['healthy_adapters'] == 2
        assert 'adapters' in report
        assert 'summary' in report
        
        # Check adapter details
        assert 'mock_us' in report['adapters']
        assert 'mock_crypto' in report['adapters']
        
        us_adapter_report = report['adapters']['mock_us']
        assert us_adapter_report['market'] == 'US'
        assert us_adapter_report['exchange'] == 'MOCK_US'
        assert us_adapter_report['healthy'] is True
        
        # Check summary statistics
        summary = report['summary']
        assert summary['total_requests_last_hour'] == 8  # 5 + 3
        assert summary['total_requests_last_day'] == 18  # 10 + 8
        assert summary['error_rate'] == 0  # No errors
        
        manager.stop_scheduler()
    
    @patch('src.data.adapters.manager.DataSourceManager._create_adapter')
    def test_market_correlation_analysis(self, mock_create_adapter, temp_config_file, mock_adapters):
        """Test market correlation analysis."""
        mock_create_adapter.side_effect = lambda config: mock_adapters.get(config.name)
        
        # Setup mock data for correlation analysis
        dates = pd.date_range('2023-01-01', periods=10, freq='D')
        
        # Mock data for AAPL (trending up)
        aapl_data = [
            UnifiedMarketData(
                symbol='AAPL',
                timestamp=date.to_pydatetime(),
                open=100.0 + i,
                high=105.0 + i,
                low=95.0 + i,
                close=102.0 + i,
                volume=1000.0,
                market='US',
                exchange='NASDAQ',
                currency='USD'
            ) for i, date in enumerate(dates)
        ]
        
        # Mock data for GOOGL (trending up, correlated)
        googl_data = [
            UnifiedMarketData(
                symbol='GOOGL',
                timestamp=date.to_pydatetime(),
                open=200.0 + i * 2,
                high=205.0 + i * 2,
                low=195.0 + i * 2,
                close=202.0 + i * 2,
                volume=800.0,
                market='US',
                exchange='NASDAQ',
                currency='USD'
            ) for i, date in enumerate(dates)
        ]
        
        def mock_fetch_side_effect(symbol, start_date, end_date, interval='1d'):
            if symbol == 'AAPL':
                return aapl_data
            elif symbol == 'GOOGL':
                return googl_data
            else:
                return []
        
        mock_adapters['mock_us'].fetch_historical_data.side_effect = mock_fetch_side_effect
        
        manager = DataSourceManager(temp_config_file)
        
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 10)
        
        correlation_matrix = manager.analyze_market_correlations(
            ['AAPL', 'GOOGL'],
            start_date,
            end_date
        )
        
        assert correlation_matrix is not None
        assert isinstance(correlation_matrix, pd.DataFrame)
        assert 'AAPL' in correlation_matrix.columns
        assert 'GOOGL' in correlation_matrix.columns
        
        # Should have high positive correlation due to similar trends
        correlation = correlation_matrix.loc['AAPL', 'GOOGL']
        assert correlation > 0.8  # High positive correlation
        
        manager.stop_scheduler()
    
    @patch('src.data.adapters.manager.DataSourceManager._create_adapter')
    def test_economic_market_analysis(self, mock_create_adapter, temp_config_file, mock_adapters):
        """Test economic-market data analysis."""
        mock_create_adapter.side_effect = lambda config: mock_adapters.get(config.name)
        
        # Setup mock economic data (GDP trending up)
        dates = pd.date_range('2023-01-01', periods=4, freq='Q')
        gdp_data = [
            EconomicData(
                series_id='GDP',
                timestamp=date.to_pydatetime(),
                value=20000.0 + i * 100,  # Growing GDP
                market='ECONOMIC',
                source='FRED',
                unit='Billions of Dollars',
                frequency='Quarterly'
            ) for i, date in enumerate(dates)
        ]
        
        # Setup mock market data (stock trending up, correlated with GDP)
        market_dates = pd.date_range('2023-01-01', periods=10, freq='M')
        spy_data = [
            UnifiedMarketData(
                symbol='SPY',
                timestamp=date.to_pydatetime(),
                open=400.0 + i * 5,
                high=405.0 + i * 5,
                low=395.0 + i * 5,
                close=402.0 + i * 5,
                volume=1000000.0,
                market='US',
                exchange='NYSE',
                currency='USD'
            ) for i, date in enumerate(market_dates)
        ]
        
        def mock_fetch_side_effect(symbol, start_date, end_date, interval='1d'):
            if symbol == 'GDP':
                return gdp_data
            elif symbol == 'SPY':
                return spy_data
            else:
                return []
        
        mock_adapters['mock_us'].fetch_historical_data.side_effect = mock_fetch_side_effect
        
        manager = DataSourceManager(temp_config_file)
        
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 12, 31)
        
        analysis = manager.get_economic_market_analysis(
            ['GDP'],
            ['SPY'],
            start_date,
            end_date
        )
        
        assert 'generated_at' in analysis
        assert 'economic_indicators' in analysis
        assert 'market_symbols' in analysis
        assert 'correlations' in analysis
        assert 'summary' in analysis
        
        # Check economic indicators
        assert 'GDP' in analysis['economic_indicators']
        gdp_info = analysis['economic_indicators']['GDP']
        assert gdp_info['data_points'] == 4
        
        # Check market symbols
        assert 'SPY' in analysis['market_symbols']
        spy_info = analysis['market_symbols']['SPY']
        assert spy_info['data_points'] == 10
        
        # Check correlations
        correlation_key = 'GDP_vs_SPY'
        if correlation_key in analysis['correlations']:
            correlation_info = analysis['correlations'][correlation_key]
            assert 'correlation' in correlation_info
            assert 'significance' in correlation_info
        
        manager.stop_scheduler()
    
    @patch('src.data.adapters.manager.DataSourceManager._create_adapter')
    def test_scheduler_health_checks(self, mock_create_adapter, temp_config_file, mock_adapters):
        """Test scheduled health checks."""
        mock_create_adapter.side_effect = lambda config: mock_adapters.get(config.name)
        
        # Set up health check to be due
        mock_adapters['mock_us'].last_health_check = datetime.now() - timedelta(minutes=10)
        mock_adapters['mock_us'].health_check.return_value = (True, "Health check passed")
        
        manager = DataSourceManager(temp_config_file)
        manager.health_check_interval = 300  # 5 minutes
        
        # Manually trigger scheduled health check
        manager._scheduled_health_check()
        
        # Verify health check was called
        mock_adapters['mock_us'].health_check.assert_called()
        
        manager.stop_scheduler()
    
    @patch('src.data.adapters.manager.DataSourceManager._create_adapter')
    def test_scheduler_stop(self, mock_create_adapter, temp_config_file, mock_adapters):
        """Test stopping the scheduler."""
        mock_create_adapter.side_effect = lambda config: mock_adapters.get(config.name)
        
        manager = DataSourceManager(temp_config_file)
        
        assert manager.scheduler_running is True
        
        manager.stop_scheduler()
        
        assert manager.scheduler_running is False
    
    @patch('src.data.adapters.manager.DataSourceManager._create_adapter')
    def test_performance_stats_update(self, mock_create_adapter, temp_config_file, mock_adapters):
        """Test performance statistics update."""
        mock_create_adapter.side_effect = lambda config: mock_adapters.get(config.name)
        
        manager = DataSourceManager(temp_config_file)
        
        # Manually trigger performance stats update
        manager._update_performance_stats()
        
        # Check that performance stats were collected
        assert 'mock_us' in manager.performance_stats
        assert 'mock_crypto' in manager.performance_stats
        
        us_stats = manager.performance_stats['mock_us']
        assert 'total_requests' in us_stats
        assert 'last_updated' in us_stats
        assert 'health_status' in us_stats
        
        manager.stop_scheduler()


if __name__ == '__main__':
    pytest.main([__file__])