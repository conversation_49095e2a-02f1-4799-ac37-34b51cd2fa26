"""
Tests for Binance data adapter.
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

from src.data.adapters.binance_adapter import BinanceAdapter
from src.data.adapters.base import DataSourceConfig
from src.models.market_data import UnifiedMarketData, MarketInfo
from src.exceptions import DataException


@pytest.fixture
def binance_config():
    """Create test configuration for Binance adapter."""
    return DataSourceConfig(
        name='binance_test',
        adapter_class='src.data.adapters.binance_adapter.BinanceAdapter',
        enabled=True,
        priority=1,
        rate_limit={'requests_per_minute': 60},
        credentials={'api_key': 'test_key', 'api_secret': 'test_secret'},
        default_params={}
    )


@pytest.fixture
def mock_binance_client():
    """Create mock Binance client."""
    with patch('src.data.adapters.binance_adapter.Client') as mock_client_class:
        mock_client = Mock()
        mock_client_class.return_value = mock_client
        
        # Mock ping method
        mock_client.ping.return_value = {}
        
        # Mock exchange info
        mock_client.get_exchange_info.return_value = {
            'symbols': [
                {
                    'symbol': 'BTCUSDT',
                    'status': 'TRADING',
                    'baseAsset': 'BTC',
                    'quoteAsset': 'USDT',
                    'filters': [
                        {'filterType': 'LOT_SIZE', 'stepSize': '0.00001000'},
                        {'filterType': 'PRICE_FILTER', 'tickSize': '0.01000000'}
                    ]
                },
                {
                    'symbol': 'ETHUSDT',
                    'status': 'TRADING',
                    'baseAsset': 'ETH',
                    'quoteAsset': 'USDT',
                    'filters': [
                        {'filterType': 'LOT_SIZE', 'stepSize': '0.00100000'},
                        {'filterType': 'PRICE_FILTER', 'tickSize': '0.01000000'}
                    ]
                }
            ]
        }
        
        # Mock historical klines
        mock_client.get_historical_klines.return_value = [
            [
                1640995200000,  # timestamp
                '47000.00',     # open
                '48000.00',     # high
                '46500.00',     # low
                '47500.00',     # close
                '100.50',       # volume
                1641081599999,  # close_time
                '4750000.00',   # quote_asset_volume
                1000,           # number_of_trades
                '50.25',        # taker_buy_base_asset_volume
                '2375000.00',   # taker_buy_quote_asset_volume
                '0'             # ignore
            ],
            [
                1641081600000,
                '47500.00',
                '48500.00',
                '47000.00',
                '48000.00',
                '120.75',
                1641167999999,
                '5760000.00',
                1200,
                '60.375',
                '2880000.00',
                '0'
            ]
        ]
        
        # Mock 24hr ticker
        mock_client.get_ticker.return_value = {
            'symbol': 'BTCUSDT',
            'priceChange': '500.00',
            'priceChangePercent': '1.05',
            'lastPrice': '47500.00',
            'volume': '1000.00',
            'quoteVolume': '47500000.00',
            'highPrice': '48000.00',
            'lowPrice': '46500.00',
            'openPrice': '47000.00'
        }
        
        # Mock server time
        mock_client.get_server_time.return_value = {
            'serverTime': int(datetime.now().timestamp() * 1000)
        }
        
        yield mock_client


class TestBinanceAdapter:
    """Test cases for Binance adapter."""
    
    def test_initialization(self, binance_config, mock_binance_client):
        """Test adapter initialization."""
        adapter = BinanceAdapter(binance_config)
        
        assert adapter.config == binance_config
        assert adapter.get_market_type() == 'CRYPTO'
        assert adapter.get_exchange_name() == 'BINANCE'
        assert adapter.get_default_currency() == 'USDT'
    
    def test_get_available_symbols(self, binance_config, mock_binance_client):
        """Test getting available symbols."""
        adapter = BinanceAdapter(binance_config)
        symbols = adapter.get_available_symbols()
        
        assert isinstance(symbols, list)
        assert 'BTCUSDT' in symbols
        assert 'ETHUSDT' in symbols
    
    def test_validate_symbol(self, binance_config, mock_binance_client):
        """Test symbol validation."""
        adapter = BinanceAdapter(binance_config)
        
        assert adapter.validate_symbol('BTCUSDT') is True
        assert adapter.validate_symbol('ETHUSDT') is True
        assert adapter.validate_symbol('INVALID') is False
    
    def test_get_market_info(self, binance_config, mock_binance_client):
        """Test getting market information."""
        adapter = BinanceAdapter(binance_config)
        market_info = adapter.get_market_info('BTCUSDT')
        
        assert isinstance(market_info, MarketInfo)
        assert market_info.symbol == 'BTCUSDT'
        assert market_info.name == 'BTC/USDT'
        assert market_info.market == 'CRYPTO'
        assert market_info.exchange == 'BINANCE'
        assert market_info.currency == 'USDT'
        assert market_info.lot_size == 0.00001000
        assert market_info.tick_size == 0.01000000
    
    def test_fetch_historical_data(self, binance_config, mock_binance_client):
        """Test fetching historical data."""
        adapter = BinanceAdapter(binance_config)
        
        start_date = datetime(2022, 1, 1)
        end_date = datetime(2022, 1, 3)
        
        data = adapter.fetch_historical_data('BTCUSDT', start_date, end_date, '1d')
        
        assert isinstance(data, list)
        assert len(data) == 2
        
        # Check first record
        first_record = data[0]
        assert isinstance(first_record, UnifiedMarketData)
        assert first_record.symbol == 'BTCUSDT'
        assert first_record.open == 47000.00
        assert first_record.high == 48000.00
        assert first_record.low == 46500.00
        assert first_record.close == 47500.00
        assert first_record.volume == 100.50
        assert first_record.market == 'CRYPTO'
        assert first_record.exchange == 'BINANCE'
        assert first_record.currency == 'USDT'
        assert first_record.turnover == 4750000.00
    
    def test_get_24hr_ticker(self, binance_config, mock_binance_client):
        """Test getting 24hr ticker data."""
        adapter = BinanceAdapter(binance_config)
        ticker = adapter.get_24hr_ticker('BTCUSDT')
        
        assert isinstance(ticker, dict)
        assert ticker['symbol'] == 'BTCUSDT'
        assert ticker['price_change'] == 500.00
        assert ticker['price_change_percent'] == 1.05
        assert ticker['last_price'] == 47500.00
        assert ticker['volume'] == 1000.00
        assert ticker['quote_volume'] == 47500000.00
    
    def test_get_quote_currency(self, binance_config, mock_binance_client):
        """Test quote currency extraction."""
        adapter = BinanceAdapter(binance_config)
        
        assert adapter._get_quote_currency('BTCUSDT') == 'USDT'
        assert adapter._get_quote_currency('ETHBUSD') == 'BUSD'
        assert adapter._get_quote_currency('ADAUSDC') == 'USDC'
        assert adapter._get_quote_currency('BNBBTC') == 'BTC'
    
    def test_get_supported_intervals(self, binance_config, mock_binance_client):
        """Test getting supported intervals."""
        adapter = BinanceAdapter(binance_config)
        intervals = adapter.get_supported_intervals()
        
        assert isinstance(intervals, list)
        assert '1m' in intervals
        assert '5m' in intervals
        assert '1h' in intervals
        assert '1d' in intervals
        assert '1w' in intervals
    
    def test_health_check(self, binance_config, mock_binance_client):
        """Test health check."""
        adapter = BinanceAdapter(binance_config)
        
        # Mock successful health check
        is_healthy, message = adapter.health_check()
        
        assert is_healthy is True
        assert 'Health check passed' in message
        assert adapter.is_healthy is True
    
    def test_search_symbols(self, binance_config, mock_binance_client):
        """Test symbol search."""
        adapter = BinanceAdapter(binance_config)
        results = adapter.search_symbols('BTC', limit=5)
        
        assert isinstance(results, list)
        assert len(results) > 0
        
        # Check first result
        first_result = results[0]
        assert 'symbol' in first_result
        assert 'name' in first_result
        assert 'exchange' in first_result
        assert 'currency' in first_result
        assert 'BTC' in first_result['symbol']
    
    def test_invalid_symbol_handling(self, binance_config, mock_binance_client):
        """Test handling of invalid symbols."""
        adapter = BinanceAdapter(binance_config)
        
        # Mock API exception for invalid symbol
        mock_binance_client.get_historical_klines.side_effect = Exception("Invalid symbol")
        
        with pytest.raises(DataException):
            adapter.fetch_historical_data('INVALID', datetime.now(), datetime.now())
    
    def test_rate_limiting(self, binance_config, mock_binance_client):
        """Test rate limiting functionality."""
        adapter = BinanceAdapter(binance_config)
        
        # Test that rate limiter is working
        assert adapter.rate_limiter is not None
        assert adapter.rate_limiter.can_make_request() is True
    
    def test_retry_mechanism(self, binance_config, mock_binance_client):
        """Test retry mechanism for failed requests."""
        adapter = BinanceAdapter(binance_config)
        
        # Mock function that fails first time, succeeds second time
        call_count = 0
        def mock_function():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise Exception("Temporary failure")
            return "success"
        
        # Test retry mechanism
        result = adapter._make_request_with_retry(mock_function)
        assert result == "success"
        assert call_count == 2
    
    def test_server_time(self, binance_config, mock_binance_client):
        """Test getting server time."""
        adapter = BinanceAdapter(binance_config)
        server_time = adapter.get_server_time()
        
        assert isinstance(server_time, datetime)
    
    @patch('src.data.adapters.binance_adapter.Client')
    def test_initialization_failure(self, mock_client_class, binance_config):
        """Test adapter initialization failure."""
        # Mock client initialization failure
        mock_client_class.side_effect = Exception("Connection failed")
        
        with pytest.raises(DataException):
            BinanceAdapter(binance_config)
    
    def test_data_validation(self, binance_config, mock_binance_client):
        """Test data validation in fetch_historical_data."""
        adapter = BinanceAdapter(binance_config)
        
        # Mock invalid kline data
        mock_binance_client.get_historical_klines.return_value = [
            [
                1640995200000,  # timestamp
                'invalid',      # invalid open price
                '48000.00',     # high
                '46500.00',     # low
                '47500.00',     # close
                '100.50',       # volume
                1641081599999,  # close_time
                '4750000.00',   # quote_asset_volume
                1000,           # number_of_trades
                '50.25',        # taker_buy_base_asset_volume
                '2375000.00',   # taker_buy_quote_asset_volume
                '0'             # ignore
            ]
        ]
        
        start_date = datetime(2022, 1, 1)
        end_date = datetime(2022, 1, 2)
        
        data = adapter.fetch_historical_data('BTCUSDT', start_date, end_date, '1d')
        
        # Should return empty list due to invalid data
        assert len(data) == 0


if __name__ == '__main__':
    pytest.main([__file__])