"""
Simple test for API components without requiring external dependencies.
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent / "src"))

def test_api_components():
    """Test API components individually."""
    
    print("Testing API Framework Components...")
    
    # Test auth module
    try:
        from src.api.auth import JWTManager, UserManager
        
        # Test JWT manager
        jwt_manager = JWTManager("test-secret")
        test_data = {"sub": "testuser", "permissions": ["read"]}
        token = jwt_manager.create_access_token(test_data)
        decoded = jwt_manager.decode_token(token)
        
        assert decoded["sub"] == "testuser"
        print("✓ JWT Manager works correctly")
        
        # Test user manager
        user_manager = UserManager()
        user = user_manager.authenticate_user("admin", "admin123")
        assert user is not None
        assert "admin" in user["roles"]
        print("✓ User Manager works correctly")
        
    except Exception as e:
        print(f"✗ Auth module error: {e}")
        return False
    
    # Test middleware
    try:
        from src.api.middleware import RequestLoggingMiddleware, SecurityHeadersMiddleware
        print("✓ Middleware modules imported successfully")
    except Exception as e:
        print(f"✗ Middleware import error: {e}")
        return False
    
    # Test router structure
    try:
        from src.api.routers import api_router
        print(f"✓ API Router created with {len(api_router.routes)} base routes")
    except Exception as e:
        print(f"✗ Router import error: {e}")
        return False
    
    # Test market data router
    try:
        from api.routers.market_data import router, MarketDataQuery, MarketDataResponse
        
        # Test data models
        query = MarketDataQuery(
            symbols=["AAPL", "GOOGL"],
            start_date="2023-01-01T00:00:00",
            end_date="2023-12-31T00:00:00",
            interval="1d"
        )
        assert len(query.symbols) == 2
        print("✓ Market Data models work correctly")
        
    except Exception as e:
        print(f"✗ Market Data router error: {e}")
        return False
    
    # Test app creation
    try:
        from api.app import create_app
        app = create_app()
        assert app is not None
        print("✓ FastAPI app created successfully")
    except Exception as e:
        print(f"✗ App creation error: {e}")
        return False
    
    return True


if __name__ == "__main__":
    success = test_api_components()
    if success:
        print("\n🎉 All API components tested successfully!")
    else:
        print("\n❌ Some API components failed testing")
        sys.exit(1)