"""
Integration tests for portfolio management with backtest engine.
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock

from src.backtest.engine import BacktestEngine
from src.models.portfolio_manager import PortfolioManager
from src.strategies.base import BaseStrategy


class SimpleTestStrategy(BaseStrategy):
    """Simple test strategy for integration testing."""
    
    def __init__(self):
        super().__init__("SimpleTestStrategy")
    
    def get_parameter_definitions(self):
        """Return empty parameter definitions."""
        return {}
    
    def initialize(self, context):
        """Initialize the strategy."""
        self.context = context
        self._has_bought = False
    
    def on_data(self, market_data):
        """Simple buy and hold strategy."""
        signals = []
        
        # Only buy on first data point
        if not self._has_bought:
            from src.models.trading import Signal, SignalAction
            signal = Signal(
                symbol=market_data.symbol,
                action=SignalAction.BUY,
                quantity=10,
                price=market_data.close
            )
            signals.append(signal)
            self._has_bought = True
        
        return signals


class TestPortfolioIntegration:
    """Test portfolio management integration with backtest engine."""
    
    def test_backtest_with_portfolio_manager(self):
        """Test that backtest engine works with enhanced portfolio management."""
        # Create sample data
        dates = pd.date_range(start='2023-01-01', end='2023-01-10', freq='D')
        data = []
        
        for i, date in enumerate(dates):
            data.append({
                'symbol': 'TEST',
                'open': 100.0 + i,
                'high': 105.0 + i,
                'low': 95.0 + i,
                'close': 102.0 + i,
                'volume': 1000000
            })
        
        df = pd.DataFrame(data, index=dates)
        
        # Create backtest engine
        engine = BacktestEngine(initial_capital=10000.0)
        
        # Verify portfolio manager is created
        assert hasattr(engine, 'portfolio_manager')
        assert isinstance(engine.portfolio_manager, PortfolioManager)
        
        # Add strategy
        strategy = SimpleTestStrategy()
        engine.add_strategy(strategy)
        
        # Set data feed
        engine.set_data_feed(df)
        
        # Run backtest
        result = engine.run_backtest()
        
        # Verify results
        assert result is not None
        assert result.initial_capital == 10000.0
        assert len(result.trades) > 0
        
        # Verify enhanced portfolio metrics are included
        assert hasattr(result, 'portfolio_metrics')
        if result.portfolio_metrics:
            assert 'portfolio_summary' in result.portfolio_metrics
            assert 'performance_metrics' in result.portfolio_metrics
            assert 'risk_metrics' in result.portfolio_metrics
    
    def test_portfolio_manager_access_during_backtest(self):
        """Test that portfolio manager can be accessed during backtest."""
        engine = BacktestEngine(initial_capital=10000.0)
        
        # Verify initial state
        assert engine.portfolio_manager.portfolio.initial_capital == 10000.0
        assert engine.portfolio_manager.portfolio.cash == 10000.0
        assert len(engine.portfolio_manager.portfolio.positions) == 0
        
        # Test portfolio manager methods
        allocation = engine.portfolio_manager.get_current_allocation()
        assert isinstance(allocation, dict)
        
        concentration = engine.portfolio_manager.analyze_position_concentration()
        assert isinstance(concentration, dict)
        assert concentration['number_of_positions'] == 0
    
    def test_portfolio_history_tracking(self):
        """Test that portfolio history is properly tracked during backtest."""
        # Create sample data
        dates = pd.date_range(start='2023-01-01', end='2023-01-05', freq='D')
        data = []
        
        for i, date in enumerate(dates):
            data.append({
                'symbol': 'TEST',
                'open': 100.0,
                'high': 105.0,
                'low': 95.0,
                'close': 100.0,
                'volume': 1000000
            })
        
        df = pd.DataFrame(data, index=dates)
        
        # Create and run backtest
        engine = BacktestEngine(initial_capital=10000.0)
        strategy = SimpleTestStrategy()
        engine.add_strategy(strategy)
        engine.set_data_feed(df)
        
        # Check initial history
        initial_history_length = len(engine.portfolio.history)
        assert initial_history_length >= 1  # Should have initial snapshot
        
        # Run backtest
        result = engine.run_backtest()
        
        # Check that history was recorded during backtest
        final_history_length = len(engine.portfolio.history)
        assert final_history_length > initial_history_length
        
        # Verify history contains expected data
        history_df = engine.portfolio.get_history_dataframe()
        assert not history_df.empty
        assert 'total_value' in history_df.columns
        assert 'cash' in history_df.columns


if __name__ == "__main__":
    pytest.main([__file__])