"""
Tests for main chart component.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock

from src.ui.charts.main_chart import MainChartComponent
from src.ui.charts.chart_config import ChartConfig, TimeInterval
from src.models.trading import Trade, Signal, OrderSide, SignalAction
from src.data.manager import DataManager
from src.indicators.engine import IndicatorEngine


class TestMainChartComponent:
    """Test cases for MainChartComponent."""
    
    @pytest.fixture
    def sample_market_data(self):
        """Create sample market data for testing."""
        dates = pd.date_range(start='2024-01-01', end='2024-01-10', freq='D')
        data = []
        
        base_price = 100.0
        for i, date in enumerate(dates):
            price = base_price + np.random.normal(0, 2)
            data.append({
                'timestamp': date,
                'open': price + np.random.normal(0, 0.5),
                'high': price + abs(np.random.normal(0, 1)),
                'low': price - abs(np.random.normal(0, 1)),
                'close': price,
                'volume': np.random.randint(1000, 10000)
            })
            base_price = price
        
        return pd.DataFrame(data)
    
    @pytest.fixture
    def mock_data_manager(self, sample_market_data):
        """Create mock data manager."""
        manager = Mock(spec=DataManager)
        manager.get_market_data = AsyncMock(return_value=sample_market_data)
        return manager
    
    @pytest.fixture
    def mock_indicator_engine(self):
        """Create mock indicator engine."""
        engine = Mock(spec=IndicatorEngine)
        
        # Mock SMA calculation
        def mock_calculate(indicator_name, data, **params):
            if indicator_name.lower() == 'sma':
                period = params.get('period', 20)
                sma_data = data.copy()
                sma_data['SMA'] = data['close'].rolling(window=period).mean()
                
                # Create a mock result object
                result = Mock()
                result.data = sma_data
                return result
            return None
        
        engine.calculate = Mock(side_effect=mock_calculate)
        return engine
    
    @pytest.fixture
    def chart_component(self, mock_data_manager, mock_indicator_engine):
        """Create chart component for testing."""
        config = ChartConfig()
        return MainChartComponent(
            config=config,
            data_manager=mock_data_manager,
            indicator_engine=mock_indicator_engine
        )
    
    def test_initialization(self, chart_component):
        """Test chart component initialization."""
        assert chart_component.config is not None
        assert chart_component.data_manager is not None
        assert chart_component.indicator_engine is not None
        assert chart_component.state.symbol == ""
        assert chart_component.market_data is None
        assert len(chart_component.indicators_data) == 0
    
    @pytest.mark.asyncio
    async def test_load_data(self, chart_component, sample_market_data):
        """Test loading market data."""
        symbol = "AAPL"
        start_time = datetime(2024, 1, 1)
        end_time = datetime(2024, 1, 10)
        
        result = await chart_component.load_data(
            symbol=symbol,
            start_time=start_time,
            end_time=end_time
        )
        
        assert result is True
        assert chart_component.state.symbol == symbol
        assert chart_component.market_data is not None
        assert len(chart_component.market_data) == len(sample_market_data)
        assert chart_component.state.visible_range[0] is not None
        assert chart_component.state.visible_range[1] is not None
    
    @pytest.mark.asyncio
    async def test_load_data_no_data_manager(self):
        """Test loading data without data manager."""
        chart_component = MainChartComponent()
        
        result = await chart_component.load_data("AAPL")
        
        assert result is False
        assert chart_component.market_data is None
    
    def test_add_indicator(self, chart_component, sample_market_data):
        """Test adding technical indicator."""
        chart_component.market_data = sample_market_data
        
        result = chart_component.add_indicator("SMA", period=20)
        
        assert result is True
        assert "SMA" in chart_component.indicators_data
        assert "SMA" in chart_component.state.selected_indicators
        
        # Verify indicator engine was called
        chart_component.indicator_engine.calculate.assert_called_once()
    
    def test_add_indicator_no_data(self, chart_component):
        """Test adding indicator without market data."""
        result = chart_component.add_indicator("SMA", period=20)
        
        assert result is False
        assert len(chart_component.indicators_data) == 0
    
    def test_remove_indicator(self, chart_component, sample_market_data):
        """Test removing indicator."""
        chart_component.market_data = sample_market_data
        chart_component.add_indicator("SMA", period=20)
        
        result = chart_component.remove_indicator("SMA")
        
        assert result is True
        assert "SMA" not in chart_component.indicators_data
        assert "SMA" not in chart_component.state.selected_indicators
    
    def test_add_trades(self, chart_component):
        """Test adding trade data."""
        trades = [
            Trade(
                id="trade1",
                timestamp=datetime.now(),
                symbol="AAPL",
                side=OrderSide.BUY,
                quantity=100,
                price=150.0,
                strategy_id="strategy1"
            ),
            Trade(
                id="trade2",
                timestamp=datetime.now(),
                symbol="AAPL",
                side=OrderSide.SELL,
                quantity=100,
                price=155.0,
                strategy_id="strategy1"
            )
        ]
        
        chart_component.add_trades(trades)
        
        assert len(chart_component.trades) == 2
        assert chart_component.trades[0].id == "trade1"
        assert chart_component.trades[1].id == "trade2"
    
    def test_add_signals(self, chart_component):
        """Test adding signal data."""
        signals = [
            Signal(
                timestamp=datetime.now(),
                symbol="AAPL",
                action=SignalAction.BUY,
                quantity=100,
                price=150.0,
                confidence=0.8,
                strategy_id="strategy1"
            ),
            Signal(
                timestamp=datetime.now(),
                symbol="AAPL",
                action=SignalAction.SELL,
                quantity=100,
                price=155.0,
                confidence=0.9,
                strategy_id="strategy1"
            )
        ]
        
        chart_component.add_signals(signals)
        
        assert len(chart_component.signals) == 2
        assert chart_component.signals[0].action == SignalAction.BUY
        assert chart_component.signals[1].action == SignalAction.SELL
    
    def test_zoom(self, chart_component, sample_market_data):
        """Test chart zoom functionality."""
        chart_component.market_data = sample_market_data
        chart_component.state.visible_range = (
            sample_market_data['timestamp'].min(),
            sample_market_data['timestamp'].max()
        )
        
        initial_zoom = chart_component.state.zoom_level
        chart_component.zoom(2.0)  # Zoom in by factor of 2
        
        assert chart_component.state.zoom_level == initial_zoom * 2.0
    
    def test_pan(self, chart_component, sample_market_data):
        """Test chart pan functionality."""
        chart_component.market_data = sample_market_data
        chart_component.state.visible_range = (
            sample_market_data['timestamp'].min(),
            sample_market_data['timestamp'].max()
        )
        
        initial_offset = chart_component.state.pan_offset
        chart_component.pan(100.0)  # Pan by 100 seconds
        
        assert chart_component.state.pan_offset == initial_offset + 100.0
    
    def test_set_crosshair_position(self, chart_component):
        """Test setting crosshair position."""
        test_time = datetime.now()
        test_price = 150.0
        
        chart_component.set_crosshair_position(test_time, test_price)
        
        assert chart_component.state.crosshair_position is not None
        assert chart_component.state.crosshair_position['time'] == test_time
        assert chart_component.state.crosshair_position['price'] == test_price
    
    def test_get_chart_data(self, chart_component, sample_market_data):
        """Test getting processed chart data."""
        chart_component.market_data = sample_market_data
        chart_component.add_indicator("SMA", period=20)
        
        chart_data = chart_component.get_chart_data()
        
        assert 'candlestick' in chart_data
        assert 'volume' in chart_data
        assert 'indicators' in chart_data
        assert 'signals' in chart_data
        assert 'trades' in chart_data
        assert 'config' in chart_data
        assert 'state' in chart_data
        
        # Verify candlestick data structure
        assert len(chart_data['candlestick']) > 0
        candlestick_point = chart_data['candlestick'][0]
        assert 'time' in candlestick_point
        assert 'open' in candlestick_point
        assert 'high' in candlestick_point
        assert 'low' in candlestick_point
        assert 'close' in candlestick_point
        
        # Verify volume data structure
        assert len(chart_data['volume']) > 0
        volume_point = chart_data['volume'][0]
        assert 'time' in volume_point
        assert 'value' in volume_point
        assert 'color' in volume_point
    
    def test_render_to_json(self, chart_component, sample_market_data):
        """Test rendering chart to JSON."""
        chart_component.market_data = sample_market_data
        
        json_output = chart_component.render_to_json()
        
        assert isinstance(json_output, str)
        assert len(json_output) > 0
        # Should be valid JSON
        import json
        parsed = json.loads(json_output)
        assert isinstance(parsed, dict)
    
    def test_render_to_html(self, chart_component, sample_market_data):
        """Test rendering chart to HTML."""
        chart_component.market_data = sample_market_data
        
        html_output = chart_component.render_to_html()
        
        assert isinstance(html_output, str)
        assert '<html>' in html_output
        assert '<script>' in html_output
        assert 'LightweightCharts' in html_output
    
    def test_update_config(self, chart_component):
        """Test updating chart configuration."""
        original_height = chart_component.config.theme.chart_height
        new_height = 500
        
        chart_component.update_config(chart_height=new_height)
        
        # Note: This test assumes the config update method exists
        # The actual implementation might need adjustment
        assert chart_component.processor.config == chart_component.config
        assert chart_component.renderer.config == chart_component.config
    
    def test_get_visible_data(self, chart_component, sample_market_data):
        """Test getting visible data within time range."""
        chart_component.market_data = sample_market_data
        
        # Set visible range to first half of data
        mid_point = len(sample_market_data) // 2
        start_time = sample_market_data['timestamp'].iloc[0]
        end_time = sample_market_data['timestamp'].iloc[mid_point]
        
        chart_component.state.visible_range = (start_time, end_time)
        
        visible_data = chart_component.get_visible_data()
        
        assert visible_data is not None
        assert len(visible_data) <= len(sample_market_data)
        assert visible_data['timestamp'].min() >= start_time
        assert visible_data['timestamp'].max() <= end_time
    
    def test_get_price_at_time(self, chart_component, sample_market_data):
        """Test getting price at specific timestamp."""
        chart_component.market_data = sample_market_data
        
        target_time = sample_market_data['timestamp'].iloc[5]
        expected_price = sample_market_data['close'].iloc[5]
        
        price = chart_component.get_price_at_time(target_time)
        
        assert price is not None
        assert abs(price - expected_price) < 0.01  # Allow for small floating point differences
    
    def test_reset_zoom(self, chart_component, sample_market_data):
        """Test resetting zoom to default."""
        chart_component.market_data = sample_market_data
        chart_component.state.zoom_level = 2.0
        chart_component.state.pan_offset = 100.0
        
        chart_component.reset_zoom()
        
        assert chart_component.state.zoom_level == 1.0
        assert chart_component.state.pan_offset == 0.0
        assert chart_component.state.visible_range[0] == sample_market_data['timestamp'].min()
        assert chart_component.state.visible_range[1] == sample_market_data['timestamp'].max()
    
    def test_toggle_indicator(self, chart_component, sample_market_data):
        """Test toggling indicator visibility."""
        chart_component.market_data = sample_market_data
        chart_component.add_indicator("SMA", period=20)
        
        # Initially visible
        assert "SMA" in chart_component.state.selected_indicators
        
        # Toggle off
        result = chart_component.toggle_indicator("SMA")
        assert result is False
        assert "SMA" not in chart_component.state.selected_indicators
        
        # Toggle on
        result = chart_component.toggle_indicator("SMA")
        assert result is True
        assert "SMA" in chart_component.state.selected_indicators
    
    def test_clear_all_indicators(self, chart_component, sample_market_data):
        """Test clearing all indicators."""
        chart_component.market_data = sample_market_data
        chart_component.add_indicator("SMA", period=20)
        chart_component.add_indicator("SMA", period=50)
        
        chart_component.clear_all_indicators()
        
        assert len(chart_component.indicators_data) == 0
        assert len(chart_component.state.selected_indicators) == 0
    
    def test_get_chart_statistics(self, chart_component, sample_market_data):
        """Test getting chart statistics."""
        chart_component.market_data = sample_market_data
        chart_component.state.symbol = "AAPL"
        
        stats = chart_component.get_chart_statistics()
        
        assert stats['symbol'] == "AAPL"
        assert stats['data_points'] == len(sample_market_data)
        assert stats['time_range'] is not None
        assert stats['price_range'] is not None
        assert 'min' in stats['price_range']
        assert 'max' in stats['price_range']
        assert 'current' in stats['price_range']
        assert stats['indicators_count'] == 0
        assert stats['signals_count'] == 0
        assert stats['trades_count'] == 0