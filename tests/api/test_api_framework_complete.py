"""
Comprehensive tests for the REST API framework (Task 9.1).
"""

import pytest
from fastapi.testclient import TestClient
from fastapi import FastAPI
import json
import time
from unittest.mock import Mock, patch
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

from api.app import create_app
from api.simple_app import create_simple_app
from api.testing import APITestClient, APITestSuite
from api.validation import RequestValidator, ValidationErrorHandler
from api.error_handlers import APIErrorHandler, ErrorResponse
from api.docs import APIDocumentationGenerator
from config.settings import config

class TestAPIFrameworkComplete:
    """Complete API framework tests for task 9.1."""
    
    @pytest.fixture
    def app(self):
        """Create test FastAPI application."""
        return create_simple_app()
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def api_test_client(self, app):
        """Create enhanced API test client."""
        return APITestClient(app)
    
    def test_app_creation(self, app):
        """Test 9.1.1: 实现Flask/FastAPI应用结构和路由配置"""
        assert isinstance(app, FastAPI)
        assert app.title == "量化交易系统API"
        assert app.version == "1.0.0"
        assert app.docs_url == "/docs"
        assert app.redoc_url == "/redoc"
    
    def test_health_check_endpoint(self, client):
        """Test basic health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "service" in data
        assert "timestamp" in data
        assert "version" in data
    
    def test_api_info_endpoint(self, client):
        """Test API information endpoint."""
        response = client.get("/api/info")
        assert response.status_code == 200
        
        data = response.json()
        assert "name" in data
        assert "version" in data
        assert "endpoints" in data
        assert isinstance(data["endpoints"], dict)
    
    def test_cors_configuration(self, client):
        """Test CORS middleware configuration."""
        response = client.options("/health")
        # CORS headers should be present
        assert "access-control-allow-origin" in response.headers
    
    def test_security_headers_middleware(self, client):
        """Test security headers middleware."""
        response = client.get("/health")
        
        # Check security headers
        assert response.headers.get("X-Content-Type-Options") == "nosniff"
        assert response.headers.get("X-Frame-Options") == "DENY"
        assert response.headers.get("X-XSS-Protection") == "1; mode=block"
        assert "Strict-Transport-Security" in response.headers
        assert "Referrer-Policy" in response.headers
    
    def test_request_logging_middleware(self, client):
        """Test request logging middleware."""
        response = client.get("/health")
        
        # Check logging headers
        assert "X-Request-ID" in response.headers
        assert "X-Process-Time" in response.headers
        
        # Verify request ID format (UUID)
        request_id = response.headers["X-Request-ID"]
        assert len(request_id) == 36  # UUID length
        assert request_id.count("-") == 4  # UUID format
    
    def test_rate_limiting_middleware(self, client):
        """Test rate limiting middleware."""
        response = client.get("/health")
        
        # Check rate limit headers
        assert "X-RateLimit-Limit" in response.headers
        assert "X-RateLimit-Remaining" in response.headers
        assert "X-RateLimit-Reset" in response.headers
        
        # Verify rate limit values
        limit = int(response.headers["X-RateLimit-Limit"])
        remaining = int(response.headers["X-RateLimit-Remaining"])
        assert limit > 0
        assert remaining <= limit
    
    def test_authentication_demo_endpoint(self, client):
        """Test 9.1.2: 创建API认证和权限管理系统"""
        # Test successful login
        response = client.post("/api/v1/auth/demo-login", params={
            "username": "demo",
            "password": "demo"
        })
        assert response.status_code == 200
        
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"
        assert "expires_in" in data
        
        # Test failed login
        response = client.post("/api/v1/auth/demo-login", params={
            "username": "wrong",
            "password": "wrong"
        })
        assert response.status_code == 401
    
    def test_request_validation_middleware(self, client):
        """Test 9.1.3: 编写请求验证和错误处理中间件"""
        # Test invalid request (this is a basic test since validation is integrated)
        response = client.post("/api/v1/strategies/", json={
            "invalid": "data"
        })
        
        # Should return some form of error response
        assert response.status_code in [400, 422, 404]  # Various error codes possible
    
    def test_error_handling_middleware(self, client):
        """Test error handling middleware."""
        # Test 404 error
        response = client.get("/nonexistent-endpoint")
        assert response.status_code == 404
        
        data = response.json()
        assert "error" in data
        assert not data.get("success", True)  # Should be False or not present
    
    def test_openapi_documentation(self, client):
        """Test 9.1.4: 实现API文档生成和测试工具"""
        # Test OpenAPI JSON endpoint
        response = client.get("/openapi.json")
        assert response.status_code == 200
        
        openapi_spec = response.json()
        assert "openapi" in openapi_spec
        assert "info" in openapi_spec
        assert "paths" in openapi_spec
        
        # Verify basic structure
        assert openapi_spec["info"]["title"] == "量化交易系统API"
        assert openapi_spec["info"]["version"] == "1.0.0"
    
    def test_swagger_ui_documentation(self, client):
        """Test Swagger UI documentation."""
        response = client.get("/docs")
        assert response.status_code == 200
        assert "text/html" in response.headers.get("content-type", "")
    
    def test_redoc_documentation(self, client):
        """Test ReDoc documentation."""
        response = client.get("/redoc")
        assert response.status_code == 200
        assert "text/html" in response.headers.get("content-type", "")
    
    def test_api_routes_structure(self, app):
        """Test API routes structure."""
        routes = [route.path for route in app.routes]
        
        # Check essential routes exist
        essential_routes = [
            "/health",
            "/api/info",
            "/",
            "/docs",
            "/redoc",
            "/openapi.json"
        ]
        
        for route in essential_routes:
            assert route in routes, f"Route {route} not found"
    
    def test_demo_strategies_endpoint(self, client):
        """Test demo strategies endpoint."""
        response = client.get("/api/v1/strategies/")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        
        if len(data) > 0:
            strategy = data[0]
            assert "id" in strategy
            assert "name" in strategy
            assert "description" in strategy
            assert "status" in strategy

class TestAPIValidation:
    """Test API validation components."""
    
    def test_request_validator_date_range(self):
        """Test date range validation."""
        validator = RequestValidator()
        
        # Valid date range
        start, end = validator.validate_date_range(
            "2023-01-01T00:00:00",
            "2023-12-31T23:59:59"
        )
        assert start.year == 2023
        assert end.year == 2023
        assert start < end
        
        # Invalid date range (start > end)
        with pytest.raises(Exception):
            validator.validate_date_range(
                "2023-12-31T23:59:59",
                "2023-01-01T00:00:00"
            )
    
    def test_request_validator_symbol(self):
        """Test symbol validation."""
        validator = RequestValidator()
        
        # Valid symbols
        assert validator.validate_symbol("AAPL") == "AAPL"
        assert validator.validate_symbol("aapl") == "AAPL"
        assert validator.validate_symbol("BTC-USD") == "BTC-USD"
        
        # Invalid symbols
        with pytest.raises(Exception):
            validator.validate_symbol("")
        
        with pytest.raises(Exception):
            validator.validate_symbol("A" * 25)  # Too long
    
    def test_request_validator_pagination(self):
        """Test pagination validation."""
        validator = RequestValidator()
        
        # Valid pagination
        page, size = validator.validate_pagination(1, 20)
        assert page == 1
        assert size == 20
        
        # Invalid pagination
        with pytest.raises(Exception):
            validator.validate_pagination(0, 20)  # Invalid page
        
        with pytest.raises(Exception):
            validator.validate_pagination(1, 200)  # Size too large

class TestAPIErrorHandling:
    """Test API error handling components."""
    
    def test_error_response_creation(self):
        """Test error response creation."""
        error = ErrorResponse(
            error_code="TEST_ERROR",
            message="Test error message",
            details={"field": "value"}
        )
        
        response_dict = error.to_dict()
        assert not response_dict["success"]
        assert response_dict["error"]["code"] == "TEST_ERROR"
        assert response_dict["error"]["message"] == "Test error message"
        assert "request_id" in response_dict["error"]
        assert "timestamp" in response_dict["error"]

class TestAPIDocumentation:
    """Test API documentation generation."""
    
    @pytest.fixture
    def app(self):
        """Create test app."""
        return create_simple_app()
    
    @pytest.fixture
    def doc_generator(self, app):
        """Create documentation generator."""
        return APIDocumentationGenerator(app)
    
    def test_postman_collection_generation(self, doc_generator):
        """Test Postman collection generation."""
        collection = doc_generator.generate_postman_collection()
        
        assert "info" in collection
        assert "item" in collection
        assert collection["info"]["name"] == "量化交易系统API"
        assert isinstance(collection["item"], list)
    
    def test_python_client_generation(self, doc_generator):
        """Test Python client code generation."""
        client_code = doc_generator.generate_api_client_code("python")
        
        assert "class APIClient" in client_code
        assert "def __init__" in client_code
        assert "def get" in client_code
        assert "def post" in client_code
    
    def test_javascript_client_generation(self, doc_generator):
        """Test JavaScript client code generation."""
        client_code = doc_generator.generate_api_client_code("javascript")
        
        assert "class APIClient" in client_code
        assert "constructor" in client_code
        assert "async get" in client_code
        assert "async post" in client_code

class TestAPITestingFramework:
    """Test the API testing framework itself."""
    
    @pytest.fixture
    def app(self):
        """Create test app."""
        return create_simple_app()
    
    @pytest.fixture
    def api_client(self, app):
        """Create API test client."""
        return APITestClient(app)
    
    def test_api_test_client_creation(self, api_client):
        """Test API test client creation."""
        assert api_client.client is not None
        assert api_client.auth_token is None
        assert "Content-Type" in api_client.base_headers
    
    def test_api_test_client_requests(self, api_client):
        """Test API test client request methods."""
        # Test GET request
        response = api_client.get("/health", auth=False)
        assert response["success"]
        assert response["status_code"] == 200
        assert "data" in response
        
        # Test response structure
        assert "status_code" in response
        assert "data" in response
        assert "headers" in response
        assert "success" in response

def run_comprehensive_tests():
    """Run all comprehensive API framework tests."""
    print("🚀 运行API框架完整测试套件...")
    
    # Test 1: Basic framework structure
    print("\n📋 测试 9.1.1: Flask/FastAPI应用结构和路由配置")
    app = create_simple_app()
    client = TestClient(app)
    
    # Health check
    response = client.get("/health")
    assert response.status_code == 200
    print("✅ 健康检查端点正常")
    
    # API info
    response = client.get("/api/info")
    assert response.status_code == 200
    print("✅ API信息端点正常")
    
    # OpenAPI documentation
    response = client.get("/openapi.json")
    assert response.status_code == 200
    print("✅ OpenAPI文档生成正常")
    
    # Test 2: Authentication system
    print("\n🔐 测试 9.1.2: API认证和权限管理系统")
    response = client.post("/api/v1/auth/demo-login", params={
        "username": "demo",
        "password": "demo"
    })
    assert response.status_code == 200
    assert "access_token" in response.json()
    print("✅ 认证系统正常工作")
    
    # Test 3: Request validation and error handling
    print("\n🛡️ 测试 9.1.3: 请求验证和错误处理中间件")
    
    # Test middleware headers
    response = client.get("/health")
    assert "X-Request-ID" in response.headers
    assert "X-Process-Time" in response.headers
    assert "X-RateLimit-Limit" in response.headers
    print("✅ 中间件正常工作")
    
    # Test security headers
    assert response.headers.get("X-Content-Type-Options") == "nosniff"
    assert response.headers.get("X-Frame-Options") == "DENY"
    print("✅ 安全头中间件正常")
    
    # Test 4: API documentation and testing tools
    print("\n📚 测试 9.1.4: API文档生成和测试工具")
    
    # Test Swagger UI
    response = client.get("/docs")
    assert response.status_code == 200
    print("✅ Swagger UI文档正常")
    
    # Test ReDoc
    response = client.get("/redoc")
    assert response.status_code == 200
    print("✅ ReDoc文档正常")
    
    # Test documentation generator
    doc_generator = APIDocumentationGenerator(app)
    collection = doc_generator.generate_postman_collection()
    assert "info" in collection
    print("✅ Postman集合生成正常")
    
    client_code = doc_generator.generate_api_client_code("python")
    assert "class APIClient" in client_code
    print("✅ Python客户端代码生成正常")
    
    print("\n🎉 所有API框架测试通过！")
    print("✨ 任务 9.1 - 创建REST API框架 - 实现完成")
    
    return True

if __name__ == "__main__":
    run_comprehensive_tests()