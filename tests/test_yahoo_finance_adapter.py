"""
Tests for Yahoo Finance data adapter.
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

from src.data.adapters.yahoo_finance import YahooFinanceAdapter
from src.data.adapters.base import DataSourceConfig
from src.models.market_data import UnifiedMarketData, MarketInfo
from src.exceptions import DataException


@pytest.fixture
def yahoo_config():
    """Create test configuration for Yahoo Finance adapter."""
    return DataSourceConfig(
        name='yahoo_finance_test',
        adapter_class='src.data.adapters.yahoo_finance.YahooFinanceAdapter',
        enabled=True,
        priority=1,
        rate_limit={'requests_per_minute': 60},
        credentials={},
        default_params={'interval': '1d'}
    )


@pytest.fixture
def yahoo_adapter(yahoo_config):
    """Create Yahoo Finance adapter instance."""
    return YahooFinanceAdapter(yahoo_config)


@pytest.fixture
def mock_yf_data():
    """Create mock Yahoo Finance data."""
    dates = pd.date_range('2024-01-01', '2024-01-05', freq='D')
    data = {
        'Open': [150.0, 151.0, 152.0, 153.0, 154.0],
        'High': [155.0, 156.0, 157.0, 158.0, 159.0],
        'Low': [149.0, 150.0, 151.0, 152.0, 153.0],
        'Close': [154.0, 155.0, 156.0, 157.0, 158.0],
        'Adj Close': [154.0, 155.0, 156.0, 157.0, 158.0],
        'Volume': [1000000, 1100000, 1200000, 1300000, 1400000]
    }
    return pd.DataFrame(data, index=dates)


@pytest.fixture
def mock_ticker_info():
    """Create mock ticker info."""
    return {
        'symbol': 'AAPL',
        'longName': 'Apple Inc.',
        'currency': 'USD',
        'exchange': 'NMS',
        'regularMarketPrice': 158.0,
        'regularMarketChange': 1.0,
        'regularMarketChangePercent': 0.64,
        'regularMarketVolume': 1400000,
        'marketCap': 2500000000000,
        'trailingPE': 25.5
    }


class TestYahooFinanceAdapter:
    """Test cases for Yahoo Finance adapter."""
    
    def test_initialization(self, yahoo_adapter):
        """Test adapter initialization."""
        assert yahoo_adapter.config.name == 'yahoo_finance_test'
        assert yahoo_adapter.get_market_type() == 'US'
        assert yahoo_adapter.get_default_currency() == 'USD'
        assert yahoo_adapter._symbol_cache == {}
        assert yahoo_adapter._market_info_cache == {}
    
    def test_get_available_symbols(self, yahoo_adapter):
        """Test getting available symbols."""
        symbols = yahoo_adapter.get_available_symbols()
        
        assert isinstance(symbols, list)
        assert len(symbols) > 0
        assert 'AAPL' in symbols
        assert 'MSFT' in symbols
        assert 'GOOGL' in symbols
        
        # Should be sorted
        assert symbols == sorted(symbols)
    
    def test_interval_mapping(self, yahoo_adapter):
        """Test interval mapping."""
        assert yahoo_adapter._map_interval('1d') == '1d'
        assert yahoo_adapter._map_interval('1h') == '1h'
        assert yahoo_adapter._map_interval('5m') == '5m'
        assert yahoo_adapter._map_interval('invalid') == '1d'  # Default
    
    def test_market_hours(self, yahoo_adapter):
        """Test market hours."""
        hours = yahoo_adapter.get_market_hours()
        
        assert hours['open'] == '09:30'
        assert hours['close'] == '16:00'
        assert hours['timezone'] == 'US/Eastern'
    
    def test_supported_intervals(self, yahoo_adapter):
        """Test supported intervals."""
        intervals = yahoo_adapter.get_supported_intervals()
        
        assert '1d' in intervals
        assert '1h' in intervals
        assert '5m' in intervals
        assert '1wk' in intervals
    
    @patch('yfinance.Ticker')
    def test_fetch_historical_data_success(self, mock_ticker_class, yahoo_adapter, mock_yf_data):
        """Test successful historical data fetch."""
        # Setup mock
        mock_ticker = Mock()
        mock_ticker.history.return_value = mock_yf_data
        mock_ticker_class.return_value = mock_ticker
        
        # Mock symbol validation
        with patch.object(yahoo_adapter, 'validate_symbol', return_value=True):
            # Fetch data
            start_date = datetime(2024, 1, 1)
            end_date = datetime(2024, 1, 5)
            
            data = yahoo_adapter.fetch_historical_data('AAPL', start_date, end_date)
            
            # Verify results
            assert isinstance(data, list)
            assert len(data) == 5
            assert all(isinstance(item, UnifiedMarketData) for item in data)
            
            # Check first data point
            first_data = data[0]
            assert first_data.symbol == 'AAPL'
            assert first_data.open == 150.0
            assert first_data.high == 155.0
            assert first_data.low == 149.0
            assert first_data.close == 154.0
            assert first_data.volume == 1000000
            assert first_data.market == 'US'
            assert first_data.currency == 'USD'
            assert first_data.adj_close == 154.0
    
    @patch('yfinance.Ticker')
    def test_fetch_historical_data_empty(self, mock_ticker_class, yahoo_adapter):
        """Test handling of empty data response."""
        # Setup mock to return empty DataFrame
        mock_ticker = Mock()
        mock_ticker.history.return_value = pd.DataFrame()
        mock_ticker_class.return_value = mock_ticker
        
        with patch.object(yahoo_adapter, 'validate_symbol', return_value=True):
            start_date = datetime(2024, 1, 1)
            end_date = datetime(2024, 1, 5)
            
            data = yahoo_adapter.fetch_historical_data('INVALID', start_date, end_date)
            
            assert data == []
    
    def test_fetch_historical_data_invalid_symbol(self, yahoo_adapter):
        """Test handling of invalid symbol."""
        with patch.object(yahoo_adapter, 'validate_symbol', return_value=False):
            start_date = datetime(2024, 1, 1)
            end_date = datetime(2024, 1, 5)
            
            with pytest.raises(DataException, match="Invalid symbol"):
                yahoo_adapter.fetch_historical_data('INVALID', start_date, end_date)
    
    @patch('yfinance.Ticker')
    def test_validate_symbol_success(self, mock_ticker_class, yahoo_adapter, mock_ticker_info):
        """Test successful symbol validation."""
        # Setup mock
        mock_ticker = Mock()
        mock_ticker.info = mock_ticker_info
        mock_ticker_class.return_value = mock_ticker
        
        # Clear cache
        yahoo_adapter._symbol_cache.clear()
        yahoo_adapter._cache_expiry.clear()
        
        result = yahoo_adapter.validate_symbol('AAPL')
        
        assert result is True
        
        # Should be cached
        assert 'validate_AAPL' in yahoo_adapter._symbol_cache
    
    @patch('yfinance.Ticker')
    def test_validate_symbol_failure(self, mock_ticker_class, yahoo_adapter):
        """Test symbol validation failure."""
        # Setup mock to return invalid info
        mock_ticker = Mock()
        mock_ticker.info = {}
        mock_ticker_class.return_value = mock_ticker
        
        # Clear cache
        yahoo_adapter._symbol_cache.clear()
        yahoo_adapter._cache_expiry.clear()
        
        result = yahoo_adapter.validate_symbol('INVALID')
        
        assert result is False
    
    @patch('yfinance.Ticker')
    def test_get_market_info_success(self, mock_ticker_class, yahoo_adapter, mock_ticker_info):
        """Test successful market info retrieval."""
        # Setup mock
        mock_ticker = Mock()
        mock_ticker.info = mock_ticker_info
        mock_ticker_class.return_value = mock_ticker
        
        with patch.object(yahoo_adapter, 'validate_symbol', return_value=True):
            # Clear cache
            yahoo_adapter._market_info_cache.clear()
            yahoo_adapter._cache_expiry.clear()
            
            market_info = yahoo_adapter.get_market_info('AAPL')
            
            assert isinstance(market_info, MarketInfo)
            assert market_info.symbol == 'AAPL'
            assert market_info.name == 'Apple Inc.'
            assert market_info.currency == 'USD'
            assert market_info.exchange == 'NASDAQ'  # NMS mapped to NASDAQ
            assert market_info.market == 'US'
            assert market_info.lot_size == 1.0
            assert market_info.tick_size == 0.01
            assert market_info.is_active is True
    
    def test_get_market_info_invalid_symbol(self, yahoo_adapter):
        """Test market info for invalid symbol."""
        with patch.object(yahoo_adapter, 'validate_symbol', return_value=False):
            market_info = yahoo_adapter.get_market_info('INVALID')
            assert market_info is None
    
    def test_exchange_mapping(self, yahoo_adapter):
        """Test exchange code mapping."""
        assert yahoo_adapter._get_exchange_from_info({'exchange': 'NMS'}) == 'NASDAQ'
        assert yahoo_adapter._get_exchange_from_info({'exchange': 'NYQ'}) == 'NYSE'
        assert yahoo_adapter._get_exchange_from_info({'exchange': 'UNKNOWN'}) == 'UNKNOWN'
    
    @patch('yfinance.Ticker')
    def test_fetch_dividends(self, mock_ticker_class, yahoo_adapter):
        """Test dividend data fetching."""
        # Create mock dividend data
        dates = pd.date_range('2024-01-01', '2024-12-31', freq='QE')
        dividends = pd.Series([0.25, 0.25, 0.25, 0.25], index=dates)
        
        mock_ticker = Mock()
        mock_ticker.dividends = dividends
        mock_ticker_class.return_value = mock_ticker
        
        with patch.object(yahoo_adapter, 'validate_symbol', return_value=True):
            start_date = datetime(2024, 1, 1)
            end_date = datetime(2024, 12, 31)
            
            div_data = yahoo_adapter.fetch_dividends('AAPL', start_date, end_date)
            
            assert isinstance(div_data, pd.DataFrame)
            assert len(div_data) == 4
            assert 'symbol' in div_data.columns
            assert 'dividend' in div_data.columns
            assert all(div_data['symbol'] == 'AAPL')
    
    @patch('yfinance.Ticker')
    def test_fetch_splits(self, mock_ticker_class, yahoo_adapter):
        """Test stock split data fetching."""
        # Create mock split data
        dates = pd.date_range('2024-06-01', periods=1)
        splits = pd.Series([2.0], index=dates)  # 2:1 split
        
        mock_ticker = Mock()
        mock_ticker.splits = splits
        mock_ticker_class.return_value = mock_ticker
        
        with patch.object(yahoo_adapter, 'validate_symbol', return_value=True):
            start_date = datetime(2024, 1, 1)
            end_date = datetime(2024, 12, 31)
            
            split_data = yahoo_adapter.fetch_splits('AAPL', start_date, end_date)
            
            assert isinstance(split_data, pd.DataFrame)
            assert len(split_data) == 1
            assert 'symbol' in split_data.columns
            assert 'split_ratio' in split_data.columns
            assert split_data.iloc[0]['split_ratio'] == 2.0
    
    @patch('yfinance.Ticker')
    def test_get_real_time_quote(self, mock_ticker_class, yahoo_adapter, mock_ticker_info):
        """Test real-time quote retrieval."""
        mock_ticker = Mock()
        mock_ticker.info = mock_ticker_info
        mock_ticker_class.return_value = mock_ticker
        
        with patch.object(yahoo_adapter, 'validate_symbol', return_value=True):
            quote = yahoo_adapter.get_real_time_quote('AAPL')
            
            assert isinstance(quote, dict)
            assert quote['symbol'] == 'AAPL'
            assert quote['price'] == 158.0
            assert quote['change'] == 1.0
            assert quote['change_percent'] == 0.64
            assert quote['currency'] == 'USD'
    
    def test_search_symbols(self, yahoo_adapter):
        """Test symbol search functionality."""
        with patch.object(yahoo_adapter, 'get_market_info') as mock_get_info:
            # Mock market info
            mock_info = MarketInfo(
                symbol='AAPL',
                name='Apple Inc.',
                market='US',
                exchange='NASDAQ',
                currency='USD',
                lot_size=1.0,
                tick_size=0.01,
                trading_hours={'open': '09:30', 'close': '16:00'},
                timezone='US/Eastern'
            )
            mock_get_info.return_value = mock_info
            
            results = yahoo_adapter.search_symbols('AAPL', limit=5)
            
            assert isinstance(results, list)
            assert len(results) >= 1
            assert results[0]['symbol'] == 'AAPL'
            assert results[0]['name'] == 'Apple Inc.'
    
    def test_is_market_open_weekend(self, yahoo_adapter):
        """Test market open check for weekend."""
        # Saturday
        saturday = datetime(2024, 1, 6, 12, 0, 0)  # Saturday noon
        assert yahoo_adapter.is_market_open(saturday) is False
        
        # Sunday
        sunday = datetime(2024, 1, 7, 12, 0, 0)  # Sunday noon
        assert yahoo_adapter.is_market_open(sunday) is False
    
    def test_is_market_open_holiday(self, yahoo_adapter):
        """Test market open check for holidays."""
        # New Year's Day 2024
        new_years = datetime(2024, 1, 1, 12, 0, 0)
        assert yahoo_adapter.is_market_open(new_years) is False
    
    @patch('yfinance.Ticker')
    def test_health_check_success(self, mock_ticker_class, yahoo_adapter, mock_yf_data):
        """Test successful health check."""
        mock_ticker = Mock()
        mock_ticker.history.return_value = mock_yf_data
        mock_ticker_class.return_value = mock_ticker
        
        with patch.object(yahoo_adapter, 'validate_symbol', return_value=True):
            is_healthy, message = yahoo_adapter.health_check()
            
            assert is_healthy is True
            assert 'Health check passed' in message
            assert yahoo_adapter.is_healthy is True
    
    @patch('yfinance.Ticker')
    def test_health_check_failure(self, mock_ticker_class, yahoo_adapter):
        """Test health check failure."""
        mock_ticker = Mock()
        mock_ticker.history.side_effect = Exception("Network error")
        mock_ticker_class.return_value = mock_ticker
        
        with patch.object(yahoo_adapter, 'validate_symbol', return_value=True):
            is_healthy, message = yahoo_adapter.health_check()
            
            assert is_healthy is False
            assert 'Health check failed' in message
            assert yahoo_adapter.is_healthy is False
    
    def test_rate_limiting(self, yahoo_adapter):
        """Test rate limiting functionality."""
        # Test that rate limiter is initialized
        assert yahoo_adapter.rate_limiter is not None
        
        # Test can_make_request
        can_make = yahoo_adapter.rate_limiter.can_make_request()
        assert isinstance(can_make, bool)
        
        # Test wait_time calculation
        wait_time = yahoo_adapter.rate_limiter.get_wait_time()
        assert isinstance(wait_time, (int, float))
        assert wait_time >= 0


class TestYahooFinanceIntegration:
    """Integration tests for Yahoo Finance adapter (requires network)."""
    
    @pytest.mark.integration
    def test_real_data_fetch(self, yahoo_adapter):
        """Test fetching real data from Yahoo Finance."""
        # Skip if not running integration tests
        pytest.importorskip("yfinance")
        
        try:
            # Test with a well-known symbol
            start_date = datetime.now() - timedelta(days=7)
            end_date = datetime.now() - timedelta(days=1)
            
            data = yahoo_adapter.fetch_historical_data('AAPL', start_date, end_date)
            
            # Basic validation
            assert isinstance(data, list)
            if data:  # May be empty on weekends/holidays
                assert all(isinstance(item, UnifiedMarketData) for item in data)
                assert all(item.symbol == 'AAPL' for item in data)
                assert all(item.market == 'US' for item in data)
                
        except Exception as e:
            pytest.skip(f"Integration test skipped due to network/API issue: {e}")
    
    @pytest.mark.integration
    def test_real_symbol_validation(self, yahoo_adapter):
        """Test real symbol validation."""
        pytest.importorskip("yfinance")
        
        try:
            # Test valid symbol
            assert yahoo_adapter.validate_symbol('AAPL') is True
            
            # Test invalid symbol
            assert yahoo_adapter.validate_symbol('INVALID_SYMBOL_12345') is False
            
        except Exception as e:
            pytest.skip(f"Integration test skipped due to network/API issue: {e}")
    
    @pytest.mark.integration
    def test_real_market_info(self, yahoo_adapter):
        """Test real market info retrieval."""
        pytest.importorskip("yfinance")
        
        try:
            market_info = yahoo_adapter.get_market_info('AAPL')
            
            if market_info:  # May fail due to network issues
                assert isinstance(market_info, MarketInfo)
                assert market_info.symbol == 'AAPL'
                assert market_info.market == 'US'
                assert market_info.currency == 'USD'
                
        except Exception as e:
            pytest.skip(f"Integration test skipped due to network/API issue: {e}")


if __name__ == '__main__':
    pytest.main([__file__])