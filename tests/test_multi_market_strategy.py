"""
Tests for multi-market strategy framework.
"""

import pytest
import pandas as pd
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from strategies.multi_market import (
    BaseMultiMarketStrategy, MultiMarketContext, CurrencyConverter,
    MarketSession, MarketFees, SlippageModel, CurrencyRate
)
from strategies.examples.cross_market_arbitrage import CrossMarketArbitrageStrategy
from strategies.market_config import MarketConfigManager
from strategies.multi_market_risk import MultiMarketRiskManager, RiskLimit
from models.market_data import UnifiedMarketData
from models.portfolio import Portfolio, Position
from models.trading import Order
from strategies.signals import Signal


class TestCurrencyConverter:
    """Test currency conversion functionality."""
    
    def test_currency_converter_initialization(self):
        """Test currency converter initialization."""
        converter = CurrencyConverter()
        
        # Should have default rates
        assert converter.get_rate('USD', 'USD') == 1.0
        assert converter.get_rate('CNY', 'USD') is not None
        assert converter.get_rate('EUR', 'USD') is not None
    
    def test_add_and_get_rate(self):
        """Test adding and retrieving exchange rates."""
        converter = CurrencyConverter()
        
        # Add a new rate
        rate = CurrencyRate('GBP', 'USD', 1.25, datetime.now(timezone.utc))
        converter.add_rate(rate)
        
        # Should be able to retrieve it
        assert converter.get_rate('GBP', 'USD') == 1.25
        assert converter.get_rate('USD', 'GBP') == 1.0 / 1.25
    
    def test_currency_conversion(self):
        """Test currency conversion."""
        converter = CurrencyConverter()
        
        # Test same currency
        assert converter.convert(100, 'USD', 'USD') == 100
        
        # Test conversion with known rate
        rate = CurrencyRate('EUR', 'USD', 1.1, datetime.now(timezone.utc))
        converter.add_rate(rate)
        
        assert converter.convert(100, 'EUR', 'USD') == 110
        assert converter.convert(110, 'USD', 'EUR') == pytest.approx(100, rel=1e-10)
    
    def test_indirect_conversion(self):
        """Test indirect currency conversion through base currency."""
        converter = CurrencyConverter()
        
        # Add rates for EUR and GBP to USD
        eur_rate = CurrencyRate('EUR', 'USD', 1.1, datetime.now(timezone.utc))
        gbp_rate = CurrencyRate('GBP', 'USD', 1.25, datetime.now(timezone.utc))
        
        converter.add_rate(eur_rate)
        converter.add_rate(gbp_rate)
        
        # Should be able to convert EUR to GBP through USD
        result = converter.convert(100, 'EUR', 'GBP')
        expected = 100 * 1.1 / 1.25  # EUR -> USD -> GBP
        assert result == pytest.approx(expected, rel=1e-10)


class TestMultiMarketContext:
    """Test multi-market context functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_data_manager = Mock()
        self.mock_indicator_engine = Mock()
        self.mock_portfolio = Mock()
        self.currency_converter = CurrencyConverter()
        
        # Create market sessions
        self.market_sessions = {
            'US': MarketSession('US', 'NYSE', 'US/Eastern', '09:30', '16:00'),
            'CN': MarketSession('CN', 'SSE', 'Asia/Shanghai', '09:30', '15:00'),
            'CRYPTO': MarketSession('CRYPTO', 'BINANCE', 'UTC', '00:00', '23:59', is_24h=True)
        }
        
        # Create fee models
        self.fee_models = {
            'US_NYSE': MarketFees('US', 'NYSE', 0.0025, 0.0035, 1.0, currency='USD'),
            'CN_SSE': MarketFees('CN', 'SSE', 0.0003, 0.0003, 5.0, currency='CNY'),
            'CRYPTO_BINANCE': MarketFees('CRYPTO', 'BINANCE', 0.001, 0.001, 0.0, currency='USDT')
        }
        
        # Create slippage models
        self.slippage_models = {
            'US_NYSE': SlippageModel('US', 'NYSE', 0.0005, 0.000001, 1.2, 0.01),
            'CN_SSE': SlippageModel('CN', 'SSE', 0.001, 0.000002, 1.5, 0.02),
            'CRYPTO_BINANCE': SlippageModel('CRYPTO', 'BINANCE', 0.0002, 0.0000005, 2.0, 0.05)
        }
        
        self.context = MultiMarketContext(
            data_manager=self.mock_data_manager,
            indicator_engine=self.mock_indicator_engine,
            portfolio=self.mock_portfolio,
            currency_converter=self.currency_converter,
            market_sessions=self.market_sessions,
            fee_models=self.fee_models,
            slippage_models=self.slippage_models
        )
    
    def test_currency_conversion(self):
        """Test currency conversion in context."""
        # Add test rate
        rate = CurrencyRate('CNY', 'USD', 0.14, datetime.now(timezone.utc))
        self.currency_converter.add_rate(rate)
        
        result = self.context.convert_currency(100, 'CNY', 'USD')
        assert result == 14.0
    
    def test_market_open_check(self):
        """Test market open status checking."""
        # Crypto market should always be open
        assert self.context.is_market_open('CRYPTO')
        
        # Other markets depend on time - we'll test with a specific time
        # This is a simplified test since actual implementation would need timezone handling
    
    def test_get_market_fees(self):
        """Test getting market fees."""
        fees = self.context.get_market_fees('US', 'NYSE')
        assert fees is not None
        assert fees.maker_fee == 0.0025
        assert fees.taker_fee == 0.0035
        assert fees.currency == 'USD'
    
    def test_get_slippage_model(self):
        """Test getting slippage model."""
        model = self.context.get_slippage_model('CRYPTO', 'BINANCE')
        assert model is not None
        assert model.base_slippage == 0.0002
        assert model.max_slippage == 0.05
    
    def test_calculate_order_cost(self):
        """Test order cost calculation."""
        cost = self.context.calculate_order_cost('AAPL', 'US', 'NYSE', 100, 150.0, 'BUY')
        
        assert 'base_cost' in cost
        assert 'fees' in cost
        assert 'slippage' in cost
        assert 'total_cost' in cost
        
        assert cost['base_cost'] == 15000.0  # 100 * 150
        assert cost['fees'] > 0  # Should have fees
        assert cost['slippage'] > 0  # Should have slippage
        assert cost['total_cost'] > cost['base_cost']  # Total should be higher


class TestBaseMultiMarketStrategy:
    """Test base multi-market strategy functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.strategy = TestMultiMarketStrategy()
        self.context = Mock(spec=MultiMarketContext)
        self.context.is_market_open.return_value = True
    
    def test_strategy_initialization(self):
        """Test strategy initialization."""
        assert self.strategy.name == "TestStrategy"
        assert 'US' in self.strategy.supported_markets
        assert 'CN' in self.strategy.supported_markets
        assert 'CRYPTO' in self.strategy.supported_markets
    
    def test_add_remove_market(self):
        """Test adding and removing markets."""
        # Add new market
        assert self.strategy.add_market('EU')
        assert 'EU' in self.strategy.supported_markets
        
        # Remove market
        assert self.strategy.remove_market('EU')
        assert 'EU' not in self.strategy.supported_markets
        
        # Try to remove non-existent market
        assert not self.strategy.remove_market('NONEXISTENT')
    
    def test_market_support_check(self):
        """Test market support checking."""
        assert self.strategy.is_market_supported('US')
        assert self.strategy.is_market_supported('CN')
        assert not self.strategy.is_market_supported('NONEXISTENT')
    
    def test_get_active_markets(self):
        """Test getting active markets."""
        self.strategy.set_multi_market_context(self.context)
        
        active_markets = self.strategy.get_active_markets()
        assert isinstance(active_markets, list)
        # All markets should be active since mock returns True
        assert len(active_markets) == len(self.strategy.supported_markets)
    
    def test_create_cross_market_signal(self):
        """Test creating cross-market signals."""
        signal = self.strategy.create_cross_market_signal(
            'AAPL', 'US', 'AAPL.HK', 'CN', 'BUY', 100, 0.8
        )
        
        assert isinstance(signal, Signal)
        assert signal.symbol == 'AAPL'
        assert signal.action == 'BUY'
        assert signal.quantity == 100
        assert signal.confidence == 0.8
        assert signal.metadata['strategy_type'] == 'cross_market'
        assert signal.metadata['primary_market'] == 'US'
        assert signal.metadata['secondary_market'] == 'CN'


class TestCrossMarketArbitrageStrategy:
    """Test cross-market arbitrage strategy."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.strategy = CrossMarketArbitrageStrategy()
        self.context = Mock(spec=MultiMarketContext)
        
        # Mock context methods
        self.context.is_market_open.return_value = True
        self.context.convert_currency.side_effect = lambda amount, from_curr, to_curr: amount * 1.0  # 1:1 conversion
        self.context.get_portfolio.return_value = Mock(total_value=100000)
        
        # Mock data manager
        mock_data_manager = Mock()
        mock_data_manager.get_market_info.return_value = Mock(currency='USD', exchange='NYSE')
        self.context.data_manager = mock_data_manager
        
        self.strategy.set_multi_market_context(self.context)
        self.strategy.initialize(self.context)
    
    def test_strategy_initialization(self):
        """Test strategy initialization."""
        assert self.strategy.is_initialized
        assert len(self.strategy.supported_markets) == 3
        assert 'US' in self.strategy.supported_markets
        assert 'CN' in self.strategy.supported_markets
        assert 'CRYPTO' in self.strategy.supported_markets
    
    def test_parameter_definitions(self):
        """Test parameter definitions."""
        params = self.strategy.get_parameter_definitions()
        
        assert 'min_profit_threshold' in params
        assert 'max_position_size' in params
        assert 'correlation_threshold' in params
        assert 'max_holding_time' in params
        
        # Check default values
        assert params['min_profit_threshold'].default_value == 0.005
        assert params['max_position_size'].default_value == 10000.0
    
    def test_find_arbitrage_opportunities(self):
        """Test finding arbitrage opportunities."""
        # Create test market data with price difference
        market_data = {
            'US': [UnifiedMarketData(
                symbol='AAPL',
                timestamp=datetime.now(timezone.utc),
                open=150.0, high=152.0, low=149.0, close=151.0, volume=1000000,
                market='US', exchange='NASDAQ', currency='USD'
            )],
            'CN': [UnifiedMarketData(
                symbol='AAPL',
                timestamp=datetime.now(timezone.utc),
                open=155.0, high=157.0, low=154.0, close=156.0, volume=500000,
                market='CN', exchange='SSE', currency='CNY'
            )]
        }
        
        # Mock validation to return True
        self.strategy.validate_cross_market_opportunity = Mock(return_value=True)
        
        opportunities = self.strategy._find_arbitrage_opportunities(market_data)
        
        # Should find one opportunity due to price difference
        assert len(opportunities) >= 0  # May be 0 if profit threshold not met
        
        if opportunities:
            opp = opportunities[0]
            assert opp['symbol'] == 'AAPL'
            assert opp['market1'] in ['US', 'CN']
            assert opp['market2'] in ['US', 'CN']
            assert opp['profit_percentage'] > 0
    
    def test_create_arbitrage_signal(self):
        """Test creating arbitrage signals."""
        # Create test opportunity
        opportunity = {
            'symbol': 'AAPL',
            'market1': 'US',
            'market2': 'CN',
            'exchange1': 'NASDAQ',
            'exchange2': 'SSE',
            'price1': 150.0,
            'price2': 155.0,
            'price1_usd': 150.0,
            'price2_usd': 155.0,
            'profit_percentage': 0.033,  # 3.3% profit
            'correlation': 0.95,
            'timestamp': datetime.now(timezone.utc)
        }
        
        signal = self.strategy._create_arbitrage_signal(opportunity)
        
        if signal:  # May be None if risk checks fail
            assert isinstance(signal, Signal)
            assert signal.symbol == 'AAPL'
            assert signal.action in ['BUY', 'SELL']
            assert signal.quantity > 0
            assert 'arbitrage_type' in signal.metadata
            assert signal.metadata['arbitrage_type'] == 'cross_market'
    
    def test_on_multi_market_data(self):
        """Test processing multi-market data."""
        market_data = {
            'US': [UnifiedMarketData(
                symbol='AAPL',
                timestamp=datetime.now(timezone.utc),
                open=150.0, high=152.0, low=149.0, close=151.0, volume=1000000,
                market='US', exchange='NASDAQ', currency='USD'
            )],
            'CRYPTO': [UnifiedMarketData(
                symbol='BTC',
                timestamp=datetime.now(timezone.utc),
                open=50000.0, high=51000.0, low=49500.0, close=50500.0, volume=100,
                market='CRYPTO', exchange='BINANCE', currency='USDT'
            )]
        }
        
        signals = self.strategy.on_multi_market_data(market_data)
        
        # Should return a list (may be empty if no opportunities)
        assert isinstance(signals, list)


class TestMarketConfigManager:
    """Test market configuration manager."""
    
    def test_initialization(self):
        """Test manager initialization."""
        manager = MarketConfigManager()
        
        # Should have default sessions
        assert len(manager.sessions) > 0
        assert len(manager.fees) > 0
        assert len(manager.slippage_models) > 0
    
    def test_get_market_session(self):
        """Test getting market sessions."""
        manager = MarketConfigManager()
        
        session = manager.get_market_session('US', 'NYSE')
        assert session is not None
        assert session.market == 'US'
        assert session.exchange == 'NYSE'
        assert session.timezone == 'US/Eastern'
    
    def test_get_market_fees(self):
        """Test getting market fees."""
        manager = MarketConfigManager()
        
        fees = manager.get_market_fees('US', 'NYSE')
        assert fees is not None
        assert fees.market == 'US'
        assert fees.exchange == 'NYSE'
        assert fees.currency == 'USD'
    
    def test_get_all_markets(self):
        """Test getting all markets."""
        manager = MarketConfigManager()
        
        markets = manager.get_all_markets()
        assert isinstance(markets, list)
        assert 'US' in markets
        assert 'CN' in markets
        assert 'CRYPTO' in markets
    
    def test_get_exchanges_for_market(self):
        """Test getting exchanges for market."""
        manager = MarketConfigManager()
        
        exchanges = manager.get_exchanges_for_market('US')
        assert isinstance(exchanges, list)
        assert 'NYSE' in exchanges
        assert 'NASDAQ' in exchanges


class TestMultiMarketRiskManager:
    """Test multi-market risk manager."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.risk_manager = MultiMarketRiskManager()
        
        # Create test portfolio
        self.portfolio = Portfolio(
            cash=50000.0,
            positions={
                'AAPL': Position('AAPL', 100, 150.0, 15000.0, 0.0),
                'GOOGL': Position('GOOGL', 50, 2800.0, 140000.0, 0.0)
            },
            total_value=205000.0,
            unrealized_pnl=0.0,
            realized_pnl=0.0
        )
    
    def test_initialization(self):
        """Test risk manager initialization."""
        assert len(self.risk_manager.risk_limits) > 0
        assert 'max_portfolio_risk' in self.risk_manager.risk_limits
        assert 'max_market_exposure' in self.risk_manager.risk_limits
        assert 'max_position_size' in self.risk_manager.risk_limits
    
    def test_add_remove_risk_limit(self):
        """Test adding and removing risk limits."""
        # Add new limit
        limit = RiskLimit('test_limit', 'percentage', 0.05)
        self.risk_manager.add_risk_limit(limit)
        assert 'test_limit' in self.risk_manager.risk_limits
        
        # Remove limit
        assert self.risk_manager.remove_risk_limit('test_limit')
        assert 'test_limit' not in self.risk_manager.risk_limits
    
    def test_calculate_market_exposures(self):
        """Test calculating market exposures."""
        exposures = self.risk_manager.calculate_market_exposures(self.portfolio)
        
        assert isinstance(exposures, dict)
        # Should have US market exposure (simplified logic in implementation)
        assert 'US' in exposures
        
        us_exposure = exposures['US']
        assert us_exposure.market == 'US'
        assert us_exposure.total_value > 0
        assert us_exposure.percentage_of_portfolio > 0
    
    def test_calculate_currency_exposures(self):
        """Test calculating currency exposures."""
        exposures = self.risk_manager.calculate_currency_exposures(self.portfolio)
        
        assert isinstance(exposures, dict)
        # Should have USD exposure
        assert 'USD' in exposures
        
        usd_exposure = exposures['USD']
        assert usd_exposure.currency == 'USD'
        assert usd_exposure.total_value > 0
        assert usd_exposure.percentage_of_portfolio > 0
    
    def test_check_order_risk(self):
        """Test checking order risk."""
        # Create test order
        order = Order(
            symbol='TSLA',
            side='BUY',
            quantity=100,
            price=800.0,
            order_type='MARKET'
        )
        
        can_execute, violations = self.risk_manager.check_order_risk(order, self.portfolio)
        
        assert isinstance(can_execute, bool)
        assert isinstance(violations, list)
    
    def test_get_risk_summary(self):
        """Test getting risk summary."""
        summary = self.risk_manager.get_risk_summary(self.portfolio)
        
        assert 'timestamp' in summary
        assert 'portfolio_value' in summary
        assert 'leverage' in summary
        assert 'market_exposures' in summary
        assert 'currency_exposures' in summary
        assert 'risk_limits' in summary
        
        assert summary['portfolio_value'] == self.portfolio.total_value
        assert summary['total_positions'] == len(self.portfolio.positions)


# Helper class for testing
class TestMultiMarketStrategy(BaseMultiMarketStrategy):
    """Test implementation of multi-market strategy."""
    
    def __init__(self):
        super().__init__("TestStrategy", supported_markets=['US', 'CN', 'CRYPTO'])
    
    def get_parameter_definitions(self):
        return {}
    
    def initialize(self, context):
        self.set_multi_market_context(context)
        self.is_initialized = True
    
    def on_multi_market_data(self, market_data):
        return []


if __name__ == '__main__':
    pytest.main([__file__])