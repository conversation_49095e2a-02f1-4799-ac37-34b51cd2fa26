"""
Tests for risk metrics calculation module.
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.risk.metrics import (
    RiskMetricsCalculator, VaRMethod, StressTestType,
    VaRResult, VolatilityMetrics, CorrelationAnalysis, StressTestResult, RiskReport
)
from src.risk.models import RiskConfig
from src.models.portfolio import Portfolio, Position
from src.models.trading import OrderSide


class TestRiskMetricsCalculator:
    """Test cases for RiskMetricsCalculator."""
    
    @pytest.fixture
    def calculator(self):
        """Create risk metrics calculator."""
        config = RiskConfig()
        return RiskMetricsCalculator(config)
    
    @pytest.fixture
    def sample_portfolio(self):
        """Create sample portfolio for testing."""
        portfolio = Portfolio(initial_capital=100000, cash=20000)
        
        # Add positions
        portfolio.positions['AAPL'] = Position(
            symbol='AAPL',
            quantity=100,
            avg_price=150.0,
            market_value=15000,
            unrealized_pnl=0
        )
        
        portfolio.positions['GOOGL'] = Position(
            symbol='GOOGL',
            quantity=50,
            avg_price=2500.0,
            market_value=125000,
            unrealized_pnl=0
        )
        
        portfolio.positions['MSFT'] = Position(
            symbol='MSFT',
            quantity=200,
            avg_price=300.0,
            market_value=60000,
            unrealized_pnl=0
        )
        
        portfolio.total_value = 200000
        return portfolio
    
    @pytest.fixture
    def sample_returns_data(self):
        """Create sample returns data."""
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        
        # Generate correlated returns
        np.random.seed(42)
        n_days = len(dates)
        
        # Base market return
        market_returns = np.random.normal(0.0005, 0.02, n_days)
        
        # Individual stock returns with different correlations to market
        aapl_returns = 0.8 * market_returns + np.random.normal(0, 0.015, n_days)
        googl_returns = 0.7 * market_returns + np.random.normal(0, 0.018, n_days)
        msft_returns = 0.75 * market_returns + np.random.normal(0, 0.016, n_days)
        
        return {
            'AAPL': pd.Series(aapl_returns, index=dates),
            'GOOGL': pd.Series(googl_returns, index=dates),
            'MSFT': pd.Series(msft_returns, index=dates)
        }
    
    def test_calculate_historical_var(self, calculator, sample_portfolio, sample_returns_data):
        """Test historical VaR calculation."""
        var_results = calculator.calculate_var(
            sample_portfolio, 
            sample_returns_data,
            confidence_levels=[0.95],
            methods=[VaRMethod.HISTORICAL]
        )
        
        assert 'historical_0.95' in var_results
        var_result = var_results['historical_0.95']
        
        assert isinstance(var_result, VaRResult)
        assert var_result.var_1d > 0
        assert var_result.var_5d > var_result.var_1d
        assert var_result.expected_shortfall_1d >= var_result.var_1d
        assert var_result.confidence_level == 0.95
        assert var_result.method == VaRMethod.HISTORICAL
        assert var_result.portfolio_value == sample_portfolio.total_value
    
    def test_calculate_parametric_var(self, calculator, sample_portfolio, sample_returns_data):
        """Test parametric VaR calculation."""
        var_results = calculator.calculate_var(
            sample_portfolio,
            sample_returns_data,
            confidence_levels=[0.99],
            methods=[VaRMethod.PARAMETRIC]
        )
        
        assert 'parametric_0.99' in var_results
        var_result = var_results['parametric_0.99']
        
        assert isinstance(var_result, VaRResult)
        assert var_result.var_1d > 0
        assert var_result.expected_shortfall_1d >= var_result.var_1d
        assert var_result.confidence_level == 0.99
        assert var_result.method == VaRMethod.PARAMETRIC
        
        # Check details
        assert 'mean_return' in var_result.details
        assert 'std_return' in var_result.details
        assert 'z_score' in var_result.details
    
    def test_calculate_monte_carlo_var(self, calculator, sample_portfolio, sample_returns_data):
        """Test Monte Carlo VaR calculation."""
        var_results = calculator.calculate_var(
            sample_portfolio,
            sample_returns_data,
            confidence_levels=[0.95],
            methods=[VaRMethod.MONTE_CARLO]
        )
        
        assert 'monte_carlo_0.95' in var_results
        var_result = var_results['monte_carlo_0.95']
        
        assert isinstance(var_result, VaRResult)
        assert var_result.var_1d > 0
        assert var_result.method == VaRMethod.MONTE_CARLO
        
        # Check simulation details
        assert 'num_simulations' in var_result.details
        assert var_result.details['num_simulations'] == 10000
    
    def test_calculate_volatility_metrics(self, calculator, sample_portfolio, sample_returns_data):
        """Test volatility metrics calculation."""
        vol_metrics = calculator.calculate_volatility_metrics(sample_returns_data, sample_portfolio)
        
        assert isinstance(vol_metrics, VolatilityMetrics)
        assert vol_metrics.daily_volatility > 0
        assert vol_metrics.weekly_volatility > vol_metrics.daily_volatility
        assert vol_metrics.monthly_volatility > vol_metrics.weekly_volatility
        assert vol_metrics.annualized_volatility > vol_metrics.monthly_volatility
        assert vol_metrics.volatility_trend in ['increasing', 'decreasing', 'stable']
        assert vol_metrics.ewma_volatility is not None
        assert vol_metrics.realized_volatility is not None
        assert vol_metrics.volatility_percentile is not None
    
    def test_calculate_correlation_analysis(self, calculator, sample_portfolio, sample_returns_data):
        """Test correlation analysis."""
        corr_analysis = calculator.calculate_correlation_analysis(sample_returns_data, sample_portfolio)
        
        assert isinstance(corr_analysis, CorrelationAnalysis)
        assert not corr_analysis.correlation_matrix.empty
        assert corr_analysis.correlation_matrix.shape == (3, 3)  # 3 positions
        assert -1 <= corr_analysis.average_correlation <= 1
        assert -1 <= corr_analysis.max_correlation <= 1
        assert -1 <= corr_analysis.min_correlation <= 1
        assert corr_analysis.diversification_ratio >= 1.0
        assert corr_analysis.effective_positions > 0
        
        # Check for highly correlated pairs
        assert isinstance(corr_analysis.highly_correlated_pairs, list)
        for pair in corr_analysis.highly_correlated_pairs:
            assert len(pair) == 3  # (symbol1, symbol2, correlation)
            assert abs(pair[2]) > 0.7  # High correlation threshold
    
    def test_run_stress_tests(self, calculator, sample_portfolio, sample_returns_data):
        """Test stress testing."""
        stress_results = calculator.run_stress_tests(
            sample_portfolio,
            sample_returns_data,
            scenarios=[StressTestType.MARKET_CRASH, StressTestType.VOLATILITY_SPIKE]
        )
        
        assert len(stress_results) == 2
        
        for result in stress_results:
            assert isinstance(result, StressTestResult)
            assert result.portfolio_loss >= 0
            assert result.portfolio_loss_pct >= 0
            assert len(result.position_impacts) == len(sample_portfolio.positions)
            assert isinstance(result.var_breach, bool)
            assert result.scenario_probability is not None
            assert 0 <= result.scenario_probability <= 1
    
    def test_market_crash_stress_test(self, calculator, sample_portfolio, sample_returns_data):
        """Test market crash stress test specifically."""
        result = calculator._stress_test_market_crash(sample_portfolio, sample_returns_data)
        
        assert result.scenario_type == StressTestType.MARKET_CRASH
        assert result.portfolio_loss > 0
        assert result.portfolio_loss_pct == 0.20  # 20% market crash
        assert result.var_breach is True  # Market crash should breach VaR
        assert result.recovery_time_estimate == 180  # 6 months
        assert result.scenario_probability == 0.05  # 5%
        
        # Check position impacts
        for symbol, position in sample_portfolio.positions.items():
            expected_loss = position.market_value * -0.20  # Negative loss
            assert abs(result.position_impacts[symbol] - expected_loss) < 0.01
    
    def test_volatility_spike_stress_test(self, calculator, sample_portfolio, sample_returns_data):
        """Test volatility spike stress test."""
        result = calculator._stress_test_volatility_spike(sample_portfolio, sample_returns_data)
        
        assert result.scenario_type == StressTestType.VOLATILITY_SPIKE
        assert result.portfolio_loss > 0
        assert result.var_breach is True
        assert 'volatility_multiplier' in result.details
        assert result.details['volatility_multiplier'] == 3.0
    
    def test_correlation_breakdown_stress_test(self, calculator, sample_portfolio, sample_returns_data):
        """Test correlation breakdown stress test."""
        result = calculator._stress_test_correlation_breakdown(sample_portfolio, sample_returns_data)
        
        assert result.scenario_type == StressTestType.CORRELATION_BREAKDOWN
        assert result.portfolio_loss >= 0
        assert result.var_breach is False  # Usually doesn't breach VaR by itself
        assert 'correlation_assumption' in result.details
        assert result.details['correlation_assumption'] == 1.0
    
    def test_generate_risk_report(self, calculator, sample_portfolio, sample_returns_data):
        """Test comprehensive risk report generation."""
        report = calculator.generate_risk_report(
            sample_portfolio,
            sample_returns_data,
            include_stress_tests=True
        )
        
        assert isinstance(report, RiskReport)
        assert report.portfolio_value == sample_portfolio.total_value
        assert isinstance(report.var_results, VaRResult)
        assert isinstance(report.volatility_metrics, VolatilityMetrics)
        assert isinstance(report.correlation_analysis, CorrelationAnalysis)
        assert len(report.stress_test_results) > 0
        assert isinstance(report.risk_summary, dict)
        assert isinstance(report.recommendations, list)
        
        # Check risk summary contents
        assert 'overall_risk_level' in report.risk_summary
        assert report.risk_summary['overall_risk_level'] in ['LOW', 'MEDIUM', 'HIGH']
        assert 'var_1d_amount' in report.risk_summary
        assert 'portfolio_volatility' in report.risk_summary
        assert 'diversification_ratio' in report.risk_summary
        
        # Check recommendations
        assert len(report.recommendations) > 0
        assert all(isinstance(rec, str) for rec in report.recommendations)
    
    def test_portfolio_returns_calculation(self, calculator, sample_portfolio, sample_returns_data):
        """Test portfolio returns calculation."""
        portfolio_returns = calculator._calculate_portfolio_returns(sample_portfolio, sample_returns_data)
        
        assert isinstance(portfolio_returns, pd.Series)
        assert len(portfolio_returns) > 0
        assert not portfolio_returns.isna().all()
        
        # Check that returns are reasonable (not too extreme)
        assert portfolio_returns.std() > 0
        assert abs(portfolio_returns.mean()) < 0.1  # Daily mean return should be small
    
    def test_portfolio_weights_calculation(self, calculator, sample_portfolio):
        """Test portfolio weights calculation."""
        weights = calculator._get_portfolio_weights(sample_portfolio)
        
        assert isinstance(weights, np.ndarray)
        assert len(weights) == len(sample_portfolio.positions)
        assert abs(weights.sum() - 1.0) < 0.001  # Weights should sum to 1
        assert all(w >= 0 for w in weights)  # All weights should be non-negative
    
    def test_ewma_volatility_calculation(self, calculator):
        """Test EWMA volatility calculation."""
        # Create sample returns
        returns = pd.Series(np.random.normal(0, 0.02, 100))
        
        ewma_vol = calculator._calculate_ewma_volatility(returns)
        
        assert ewma_vol > 0
        assert ewma_vol < 1.0  # Should be reasonable volatility
    
    def test_empty_portfolio_handling(self, calculator):
        """Test handling of empty portfolio."""
        empty_portfolio = Portfolio(initial_capital=100000, cash=100000)
        returns_data = {'AAPL': pd.Series([0.01, -0.02, 0.015])}
        
        # Should handle empty portfolio gracefully
        var_results = calculator.calculate_var(empty_portfolio, returns_data)
        assert len(var_results) == 0
        
        vol_metrics = calculator.calculate_volatility_metrics(returns_data, empty_portfolio)
        assert vol_metrics.daily_volatility == 0.0
        
        corr_analysis = calculator.calculate_correlation_analysis(returns_data, empty_portfolio)
        assert corr_analysis.correlation_matrix.empty
    
    def test_insufficient_data_handling(self, calculator, sample_portfolio):
        """Test handling of insufficient data."""
        # Very limited returns data
        limited_returns = {
            'AAPL': pd.Series([0.01]),
            'GOOGL': pd.Series([0.02]),
            'MSFT': pd.Series([-0.01])
        }
        
        # Should handle gracefully without crashing
        var_results = calculator.calculate_var(sample_portfolio, limited_returns)
        # May return empty results or fallback values
        
        vol_metrics = calculator.calculate_volatility_metrics(limited_returns, sample_portfolio)
        assert isinstance(vol_metrics, VolatilityMetrics)
    
    def test_risk_level_classification(self, calculator, sample_portfolio, sample_returns_data):
        """Test risk level classification in risk summary."""
        report = calculator.generate_risk_report(sample_portfolio, sample_returns_data)
        
        risk_level = report.risk_summary['overall_risk_level']
        var_pct = report.risk_summary['var_1d_pct']
        
        # Check risk level logic
        if var_pct > 5.0:
            assert risk_level == 'HIGH'
        elif var_pct > 2.0:
            assert risk_level == 'MEDIUM'
        else:
            assert risk_level == 'LOW'
    
    def test_recommendations_generation(self, calculator):
        """Test recommendations generation logic."""
        # Create high-risk portfolio for testing recommendations
        high_risk_portfolio = Portfolio(initial_capital=100000, cash=10000)
        
        # Single large position (high concentration)
        high_risk_portfolio.positions['AAPL'] = Position(
            symbol='AAPL',
            quantity=600,
            avg_price=150.0,
            market_value=90000,
            unrealized_pnl=0
        )
        high_risk_portfolio.total_value = 100000
        
        # High volatility returns
        high_vol_returns = {
            'AAPL': pd.Series(np.random.normal(0, 0.05, 100))  # 5% daily volatility
        }
        
        report = calculator.generate_risk_report(high_risk_portfolio, high_vol_returns)
        
        # Should generate relevant recommendations
        recommendations = report.recommendations
        assert len(recommendations) > 0
        
        # Check for concentration warning
        concentration_warning = any('concentration' in rec.lower() for rec in recommendations)
        volatility_warning = any('volatility' in rec.lower() for rec in recommendations)
        
        # At least one of these should be present for high-risk portfolio
        assert concentration_warning or volatility_warning


if __name__ == '__main__':
    pytest.main([__file__])