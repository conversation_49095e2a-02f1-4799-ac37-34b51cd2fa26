@echo off
REM 量化交易系统快速设置脚本 (Windows)

echo 🔧 量化交易系统快速设置
echo ==========================

REM 检查Python版本
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)
echo ✅ Python检查通过

REM 创建虚拟环境
if not exist "venv" (
    echo 📦 创建虚拟环境...
    python -m venv venv
    echo ✅ 虚拟环境创建完成
) else (
    echo ✅ 虚拟环境已存在
)

REM 激活虚拟环境
echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat

REM 安装依赖
echo 📦 安装依赖包...
python -m pip install --upgrade pip
pip install -r requirements.txt

REM 创建必要目录
echo 📁 创建目录结构...
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "config" mkdir config

REM 初始化数据库
echo 🗄️ 初始化数据库...
python -c "from src.data.manager import DataManager; DataManager(use_sqlite=True)"

REM 创建环境变量文件
if not exist ".env" (
    echo ⚙️ 创建环境变量文件...
    (
        echo # 数据库配置
        echo DATABASE_URL=sqlite:///data/trading_system.db
        echo.
        echo # API配置
        echo JWT_SECRET=your_jwt_secret_here
        echo SECRET_KEY=your_secret_key_here
        echo.
        echo # 数据源API密钥（可选）
        echo # TUSHARE_TOKEN=your_tushare_token
        echo # FRED_API_KEY=your_fred_api_key
    ) > .env
    echo ✅ 环境变量文件创建完成
)

echo.
echo 🎉 设置完成！
echo.
echo 📋 使用方法:
echo   venv\Scripts\activate.bat     # 激动虚拟环境
echo   python start.py --mode gui    # 启动GUI应用
echo   python start.py --mode example # 运行示例
echo.
echo 📚 更多信息请查看 docs\user_manual.md
pause