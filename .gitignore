# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.venv/
.env

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/*.log
*.log

# Data files
data/*.db
data/*.sqlite
data/*.sqlite3
data/cache/*
data/exports/*

# Temporary files
temp/*
backup/*
*.tmp
*.temp

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Package files
*.tar.gz
*.zip

# Documentation builds
docs/_build/
docs/site/

# Monitoring exports
monitoring_export_*.json
risk_monitoring.log

# Chart files
chart_data.json
chart_demo.html
test_export.json

# Keep directory structure but ignore contents
data/cache/.gitkeep
data/exports/.gitkeep
logs/.gitkeep
temp/.gitkeep
backup/.gitkeep