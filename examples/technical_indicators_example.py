#!/usr/bin/env python3
"""
Technical Indicators Library Example

This example demonstrates the usage of the technical indicators library
including built-in indicators, calculation engine, and custom indicators.
"""

import pandas as pd
import numpy as np
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.indicators import (
    sma, ema, rsi, macd, bollinger_bands, atr, obv, vwap,
    IndicatorEngine, get_engine, calculate_indicator,
    FormulaIndicator, CompositeIndicator, CustomIndicatorManager,
    IndicatorMetadata, IndicatorTestFramework
)


def create_sample_data():
    """Create sample OHLCV data for demonstration"""
    np.random.seed(42)
    dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
    
    # Generate realistic price data
    prices = [100]
    for i in range(99):
        change = np.random.randn() * 0.02  # 2% daily volatility
        prices.append(prices[-1] * (1 + change))
    
    # Generate OHLCV data
    closes = np.array(prices)
    highs = closes * (1 + np.abs(np.random.randn(100) * 0.01))
    lows = closes * (1 - np.abs(np.random.randn(100) * 0.01))
    opens = closes * (1 + np.random.randn(100) * 0.005)
    volumes = np.random.randint(1000, 10000, 100)
    
    return pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    }, index=dates)


def demonstrate_basic_indicators():
    """Demonstrate basic technical indicators"""
    print("=== Basic Technical Indicators ===")
    
    data = create_sample_data()
    print(f"Sample data shape: {data.shape}")
    print(f"Date range: {data.index[0]} to {data.index[-1]}")
    print()
    
    # Moving Averages
    print("1. Moving Averages:")
    sma_20 = sma(data['close'], 20)
    ema_20 = ema(data['close'], 20)
    print(f"   SMA(20) last value: {sma_20.iloc[-1]:.2f}")
    print(f"   EMA(20) last value: {ema_20.iloc[-1]:.2f}")
    print()
    
    # Momentum Indicators
    print("2. Momentum Indicators:")
    rsi_14 = rsi(data['close'], 14)
    macd_result = macd(data['close'])
    print(f"   RSI(14) last value: {rsi_14.iloc[-1]:.2f}")
    print(f"   MACD last value: {macd_result['macd'].iloc[-1]:.4f}")
    print(f"   MACD Signal last value: {macd_result['signal'].iloc[-1]:.4f}")
    print()
    
    # Volatility Indicators
    print("3. Volatility Indicators:")
    bb_result = bollinger_bands(data['close'], 20, 2.0)
    atr_14 = atr(data, 14)
    print(f"   Bollinger Upper: {bb_result['upper'].iloc[-1]:.2f}")
    print(f"   Bollinger Lower: {bb_result['lower'].iloc[-1]:.2f}")
    print(f"   ATR(14): {atr_14.iloc[-1]:.2f}")
    print()
    
    # Volume Indicators
    print("4. Volume Indicators:")
    obv_result = obv(data)
    vwap_result = vwap(data, period=20)
    print(f"   OBV last value: {obv_result.iloc[-1]:.0f}")
    print(f"   VWAP(20) last value: {vwap_result.iloc[-1]:.2f}")
    print()


def demonstrate_indicator_engine():
    """Demonstrate the indicator calculation engine"""
    print("=== Indicator Calculation Engine ===")
    
    data = create_sample_data()
    engine = get_engine()
    
    print(f"Available indicators: {len(engine.get_available_indicators())}")
    print(f"Categories: {set(engine.get_indicators_by_category('moving_averages'))}")
    print()
    
    # Single indicator calculation
    print("1. Single Indicator Calculation:")
    result = calculate_indicator('sma', data['close'], period=20)
    print(f"   Indicator: {result.indicator_name}")
    print(f"   Parameters: {result.params}")
    print(f"   Calculation time: {result.calculation_time:.4f}s")
    print(f"   Cache hit: {result.cache_hit}")
    print()
    
    # Multiple indicators
    print("2. Multiple Indicators:")
    indicators = [
        ('sma', {'period': 10}),
        ('ema', {'period': 10}),
        ('rsi', {'period': 14})
    ]
    
    results = engine.calculate_multiple(indicators, data['close'])
    for name, result in results.items():
        print(f"   {name}: last value = {result.data.iloc[-1]:.2f}")
    print()
    
    # Caching demonstration
    print("3. Caching Performance:")
    import time
    
    # First calculation (no cache)
    start = time.time()
    result1 = calculate_indicator('bollinger_bands', data['close'], period=20)
    time1 = time.time() - start
    
    # Second calculation (cached)
    start = time.time()
    result2 = calculate_indicator('bollinger_bands', data['close'], period=20)
    time2 = time.time() - start
    
    print(f"   First calculation: {time1:.4f}s (cache hit: {result1.cache_hit})")
    print(f"   Second calculation: {time2:.4f}s (cache hit: {result2.cache_hit})")
    print(f"   Speed improvement: {time1/time2:.1f}x")
    print()


def demonstrate_custom_indicators():
    """Demonstrate custom indicator framework"""
    print("=== Custom Indicator Framework ===")
    
    data = create_sample_data()
    manager = CustomIndicatorManager(get_engine())
    
    # 1. Formula-based indicator
    print("1. Formula-based Indicator:")
    
    metadata = IndicatorMetadata(
        name="price_momentum",
        description="Price momentum indicator",
        category="custom",
        author="Demo User",
        tags=["momentum", "custom"]
    )
    
    # Create a simple momentum indicator: (close - close[n]) / close[n] * 100
    formula_indicator = manager.create_formula_indicator(
        "price_momentum",
        "(close - close.shift(period)) / close.shift(period) * 100",
        metadata
    )
    
    # Calculate using the engine
    momentum_result = calculate_indicator('price_momentum', data['close'], period=10)
    print(f"   Price Momentum (10-day): {momentum_result.data.iloc[-1]:.2f}%")
    print()
    
    # 2. Composite indicator
    print("2. Composite Indicator:")
    
    def trend_strength(components, **params):
        """Combine SMA and EMA to create trend strength indicator"""
        sma_data = components['sma']
        ema_data = components['ema']
        
        # When EMA > SMA, trend is strong (positive)
        # When EMA < SMA, trend is weak (negative)
        strength = (ema_data - sma_data) / sma_data * 100
        return strength
    
    components = [
        ('sma', {'period': 20}),
        ('ema', {'period': 20})
    ]
    
    composite_indicator = manager.create_composite_indicator(
        "trend_strength",
        components,
        trend_strength,
        IndicatorMetadata(
            name="trend_strength",
            description="Trend strength based on SMA/EMA divergence",
            category="trend"
        )
    )
    
    trend_result = calculate_indicator('trend_strength', data['close'])
    print(f"   Trend Strength: {trend_result.data.iloc[-1]:.2f}%")
    print()
    
    # 3. Custom indicator testing
    print("3. Custom Indicator Testing:")
    
    test_framework = IndicatorTestFramework()
    
    # Create test data
    test_data = pd.Series([100, 102, 104, 106, 108])
    expected_momentum = (test_data - test_data.shift(2)) / test_data.shift(2) * 100
    
    # Add test case
    test_framework.add_test_case(
        "momentum_test",
        formula_indicator,
        test_data,
        expected_momentum,
        {"period": 2},
        tolerance=1e-10
    )
    
    # Run tests
    test_results = test_framework.run_tests()
    print(f"   Test results: {test_results}")
    print()
    
    # 4. Documentation generation
    print("4. Documentation Generation:")
    doc = formula_indicator.generate_documentation()
    print("   Generated documentation preview:")
    print("   " + "\n   ".join(doc.split('\n')[:10]))
    print("   ...")
    print()


def demonstrate_performance_comparison():
    """Demonstrate performance comparison between different approaches"""
    print("=== Performance Comparison ===")
    
    # Create larger dataset for performance testing
    np.random.seed(42)
    large_data = pd.Series(np.random.randn(10000).cumsum() + 100)
    
    import time
    
    # Direct function call
    start = time.time()
    direct_result = sma(large_data, 50)
    direct_time = time.time() - start
    
    # Engine call (first time - no cache)
    engine = get_engine()
    engine.clear_cache()  # Clear cache for fair comparison
    
    start = time.time()
    engine_result = calculate_indicator('sma', large_data, period=50)
    engine_time = time.time() - start
    
    # Engine call (second time - cached)
    start = time.time()
    cached_result = calculate_indicator('sma', large_data, period=50)
    cached_time = time.time() - start
    
    print(f"Dataset size: {len(large_data)} points")
    print(f"Direct function call: {direct_time:.4f}s")
    print(f"Engine call (no cache): {engine_time:.4f}s")
    print(f"Engine call (cached): {cached_time:.4f}s")
    print(f"Cache speedup: {engine_time/cached_time:.1f}x")
    print()
    
    # Verify results are identical
    results_match = np.allclose(direct_result.dropna(), engine_result.data.dropna())
    print(f"Results identical: {results_match}")
    print()


def main():
    """Main demonstration function"""
    print("Technical Indicators Library Demonstration")
    print("=" * 50)
    print()
    
    try:
        demonstrate_basic_indicators()
        demonstrate_indicator_engine()
        demonstrate_custom_indicators()
        demonstrate_performance_comparison()
        
        print("=== Summary ===")
        print("✅ Basic indicators working correctly")
        print("✅ Indicator engine with caching functional")
        print("✅ Custom indicator framework operational")
        print("✅ Performance optimization effective")
        print()
        print("The technical indicators library is ready for use!")
        
    except Exception as e:
        print(f"❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()