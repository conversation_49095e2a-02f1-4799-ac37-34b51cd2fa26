#!/usr/bin/env python3
"""
Data validation and cleaning example for the quantitative trading system.

This example demonstrates how to use the data validation and cleaning functionality
to ensure data quality for market data analysis and backtesting.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src and project root to path for imports
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, os.path.join(project_root, 'src'))
sys.path.insert(0, project_root)

from src.data.validation import (
    DataValidator, OHLCVValidator, TimeSeriesValidator, 
    AnomalyDetector, DataCleaner, DataQualityReporter
)
from src.models.market_data import UnifiedMarketData


def create_sample_data_with_issues():
    """Create sample market data with various quality issues for demonstration."""
    print("Creating sample data with quality issues...")
    
    # Create date range
    dates = pd.date_range('2023-01-01', periods=50, freq='D')
    
    # Create base data
    np.random.seed(42)  # For reproducible results
    base_price = 100
    prices = []
    volumes = []
    
    for i in range(len(dates)):
        # Simulate price movement with some volatility
        change = np.random.normal(0, 0.02)  # 2% daily volatility
        base_price *= (1 + change)
        
        # Create OHLC data
        open_price = base_price
        high_price = base_price * (1 + abs(np.random.normal(0, 0.01)))
        low_price = base_price * (1 - abs(np.random.normal(0, 0.01)))
        close_price = base_price
        
        prices.append([open_price, high_price, low_price, close_price])
        volumes.append(np.random.randint(1000, 10000))
    
    # Create DataFrame
    df = pd.DataFrame(prices, columns=['open', 'high', 'low', 'close'], index=dates)
    df['volume'] = volumes
    
    # Introduce various data quality issues
    print("Introducing data quality issues...")
    
    # 1. Missing values
    df.loc[dates[5], 'close'] = np.nan
    df.loc[dates[15], 'volume'] = np.nan
    
    # 2. OHLC inconsistencies
    df.loc[dates[10], 'high'] = df.loc[dates[10], 'low'] - 1  # High < Low
    df.loc[dates[20], 'low'] = df.loc[dates[20], 'close'] + 2  # Low > Close
    
    # 3. Negative values
    df.loc[dates[25], 'open'] = -10
    df.loc[dates[30], 'volume'] = -500
    
    # 4. Zero volume
    df.loc[dates[35], 'volume'] = 0
    
    # 5. Price jumps (anomalies)
    df.loc[dates[40], 'close'] = df.loc[dates[39], 'close'] * 1.5  # 50% jump
    
    # 6. Volume spikes
    df.loc[dates[45], 'volume'] = df.loc[dates[44], 'volume'] * 10  # 10x volume
    
    print(f"Created sample data with {len(df)} records and various quality issues")
    return df


def demonstrate_ohlcv_validation():
    """Demonstrate OHLCV data validation."""
    print("\n" + "="*60)
    print("OHLCV VALIDATION DEMONSTRATION")
    print("="*60)
    
    # Create sample data
    df = create_sample_data_with_issues()
    
    # Initialize validator
    validator = OHLCVValidator()
    
    # Validate the data
    print("\nValidating OHLCV data...")
    result = validator.validate_dataframe(df, "SAMPLE_STOCK")
    
    print(f"Validation Result: {'PASSED' if result.is_valid else 'FAILED'}")
    print(f"Total Issues Found: {len(result.issues)}")
    print(f"Issue Summary: {result.summary}")
    
    # Show first few issues
    if result.issues:
        print("\nFirst 5 validation issues:")
        for i, issue in enumerate(result.issues[:5]):
            print(f"{i+1}. [{issue.severity.value.upper()}] {issue.issue_type}: {issue.message}")
            if issue.timestamp:
                print(f"   Timestamp: {issue.timestamp}")
            if issue.value is not None:
                print(f"   Value: {issue.value}")


def demonstrate_timeseries_validation():
    """Demonstrate time series validation."""
    print("\n" + "="*60)
    print("TIME SERIES VALIDATION DEMONSTRATION")
    print("="*60)
    
    # Create sample data with time series issues
    dates = pd.date_range('2023-01-01', periods=20, freq='D')
    df = pd.DataFrame({
        'open': np.random.uniform(95, 105, 20),
        'high': np.random.uniform(100, 110, 20),
        'low': np.random.uniform(90, 100, 20),
        'close': np.random.uniform(95, 105, 20),
        'volume': np.random.randint(1000, 5000, 20)
    }, index=dates)
    
    # Introduce time series issues
    # Add duplicate timestamp
    duplicate_row = df.iloc[5].copy()
    df = pd.concat([df, pd.DataFrame([duplicate_row], index=[dates[5]])])
    
    # Initialize validator
    validator = TimeSeriesValidator()
    
    # Validate the data
    print("\nValidating time series completeness...")
    result = validator.validate_completeness(df, "SAMPLE_STOCK")
    
    print(f"Validation Result: {'PASSED' if result.is_valid else 'FAILED'}")
    print(f"Total Issues Found: {len(result.issues)}")
    
    # Show issues
    if result.issues:
        print("\nTime series validation issues:")
        for i, issue in enumerate(result.issues):
            print(f"{i+1}. [{issue.severity.value.upper()}] {issue.issue_type}: {issue.message}")


def demonstrate_anomaly_detection():
    """Demonstrate anomaly detection."""
    print("\n" + "="*60)
    print("ANOMALY DETECTION DEMONSTRATION")
    print("="*60)
    
    # Create sample data
    df = create_sample_data_with_issues()
    
    # Initialize anomaly detector
    detector = AnomalyDetector()
    
    # Detect anomalies
    print("\nDetecting price and volume anomalies...")
    anomalies = detector.detect_all_anomalies(df)
    
    print(f"Total Anomalies Found: {len(anomalies)}")
    
    # Group anomalies by type
    anomaly_types = {}
    for anomaly in anomalies:
        if anomaly.issue_type not in anomaly_types:
            anomaly_types[anomaly.issue_type] = []
        anomaly_types[anomaly.issue_type].append(anomaly)
    
    print("\nAnomalies by type:")
    for anomaly_type, anomaly_list in anomaly_types.items():
        print(f"  {anomaly_type}: {len(anomaly_list)} occurrences")
    
    # Show first few anomalies
    if anomalies:
        print("\nFirst 5 anomalies detected:")
        for i, anomaly in enumerate(anomalies[:5]):
            print(f"{i+1}. [{anomaly.severity.value.upper()}] {anomaly.issue_type}: {anomaly.message}")
            if anomaly.timestamp:
                print(f"   Timestamp: {anomaly.timestamp}")
            if anomaly.value is not None:
                print(f"   Value: {anomaly.value}")


def demonstrate_data_cleaning():
    """Demonstrate data cleaning functionality."""
    print("\n" + "="*60)
    print("DATA CLEANING DEMONSTRATION")
    print("="*60)
    
    # Create sample data with issues
    df = create_sample_data_with_issues()
    
    print(f"Original data shape: {df.shape}")
    print(f"Missing values before cleaning: {df.isnull().sum().sum()}")
    
    # Initialize data cleaner
    cleaner = DataCleaner({
        'fill_method': 'forward',
        'outlier_method': 'clip',
        'max_fill_limit': 3
    })
    
    # Clean the data
    print("\nCleaning data...")
    cleaned_df, cleaning_report = cleaner.clean_data(df)
    
    print(f"Cleaned data shape: {cleaned_df.shape}")
    print(f"Missing values after cleaning: {cleaned_df.isnull().sum().sum()}")
    print(f"Rows removed: {cleaning_report['rows_removed']}")
    
    print("\nCleaning operations performed:")
    for operation in cleaning_report['operations']:
        print(f"  - {operation}")


def demonstrate_quality_reporting():
    """Demonstrate comprehensive data quality reporting."""
    print("\n" + "="*60)
    print("DATA QUALITY REPORTING DEMONSTRATION")
    print("="*60)
    
    # Create sample data
    df = create_sample_data_with_issues()
    
    # Initialize quality reporter
    reporter = DataQualityReporter()
    
    # Generate quality report
    print("\nGenerating comprehensive data quality report...")
    report = reporter.generate_quality_report(df, "SAMPLE_STOCK", "US")
    
    print(f"Report generated for: {report['symbol']} ({report['market']})")
    print(f"Total records: {report['data_summary']['total_records']}")
    print(f"Date range: {report['data_summary']['date_range']['start']} to {report['data_summary']['date_range']['end']}")
    
    # OHLCV validation summary
    ohlcv_summary = report['validation_results']['ohlcv']['summary']
    print(f"\nOHLCV Validation Summary:")
    print(f"  Valid: {report['validation_results']['ohlcv']['is_valid']}")
    print(f"  Critical issues: {ohlcv_summary['critical']}")
    print(f"  Errors: {ohlcv_summary['errors']}")
    print(f"  Warnings: {ohlcv_summary['warnings']}")
    
    # Time series validation summary
    ts_summary = report['validation_results']['timeseries']['summary']
    print(f"\nTime Series Validation Summary:")
    print(f"  Valid: {report['validation_results']['timeseries']['is_valid']}")
    print(f"  Errors: {ts_summary['errors']}")
    print(f"  Warnings: {ts_summary['warnings']}")
    
    # Anomaly detection summary
    print(f"\nAnomaly Detection Summary:")
    print(f"  Total anomalies: {report['anomaly_detection']['total_anomalies']}")
    anomaly_types = report['anomaly_detection']['anomaly_types']
    for anomaly_type, count in anomaly_types.items():
        print(f"  {anomaly_type}: {count}")
    
    # Recommendations
    print(f"\nRecommendations:")
    for i, recommendation in enumerate(report['recommendations'], 1):
        print(f"  {i}. {recommendation}")


def demonstrate_integrated_validation():
    """Demonstrate integrated validation and cleaning workflow."""
    print("\n" + "="*60)
    print("INTEGRATED VALIDATION & CLEANING WORKFLOW")
    print("="*60)
    
    # Create sample data
    df = create_sample_data_with_issues()
    
    # Initialize main data validator
    validator = DataValidator({
        'ohlcv': {'min_price': 0.01, 'max_price': 1000},
        'timeseries': {'min_data_points': 10},
        'anomaly': {'price_jump_threshold': 0.15},
        'cleaning': {'fill_method': 'forward', 'outlier_method': 'clip'}
    })
    
    print(f"Original data: {df.shape[0]} records")
    print(f"Missing values: {df.isnull().sum().sum()}")
    
    # Perform integrated validation and cleaning
    print("\nPerforming integrated validation and cleaning...")
    cleaned_df, combined_report = validator.validate_and_clean(df, "SAMPLE_STOCK")
    
    print(f"Cleaned data: {cleaned_df.shape[0]} records")
    print(f"Missing values after cleaning: {cleaned_df.isnull().sum().sum()}")
    
    # Show validation summary
    validation_summary = combined_report['validation']
    print(f"\nValidation Summary:")
    print(f"  Valid: {validation_summary['is_valid']}")
    print(f"  Issues found: {validation_summary['issues_count']}")
    
    # Show cleaning summary
    cleaning_summary = combined_report['cleaning']
    print(f"\nCleaning Summary:")
    print(f"  Rows removed: {cleaning_summary['rows_removed']}")
    print(f"  Operations: {len(cleaning_summary['operations'])}")
    
    # Show final quality metrics
    final_quality = combined_report['final_quality']
    print(f"\nFinal Quality Assessment:")
    print(f"  OHLCV valid: {final_quality['validation_results']['ohlcv']['is_valid']}")
    print(f"  Time series valid: {final_quality['validation_results']['timeseries']['is_valid']}")
    print(f"  Remaining anomalies: {final_quality['anomaly_detection']['total_anomalies']}")


def main():
    """Run all data validation and cleaning demonstrations."""
    print("QUANTITATIVE TRADING SYSTEM - DATA VALIDATION & CLEANING EXAMPLES")
    print("="*80)
    
    try:
        # Run all demonstrations
        demonstrate_ohlcv_validation()
        demonstrate_timeseries_validation()
        demonstrate_anomaly_detection()
        demonstrate_data_cleaning()
        demonstrate_quality_reporting()
        demonstrate_integrated_validation()
        
        print("\n" + "="*80)
        print("ALL DEMONSTRATIONS COMPLETED SUCCESSFULLY!")
        print("="*80)
        
    except Exception as e:
        print(f"\nError during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()