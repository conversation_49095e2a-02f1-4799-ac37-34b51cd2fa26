#!/usr/bin/env python3
"""
Enhanced Data Source Manager usage example.

This example demonstrates the advanced features of the multi-data source management system.
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path
import time

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.adapters.manager import DataSourceManager


def main():
    """Main example function."""
    print("=== Enhanced Data Source Manager Example ===\n")
    
    try:
        # Initialize manager with configuration
        print("1. Initializing Data Source Manager...")
        manager = DataSourceManager("config/data_sources.yaml")
        
        print(f"   ✓ Manager initialized with {len(manager.adapters)} adapters")
        print(f"   ✓ Background scheduler started: {manager.scheduler_running}")
        print()
        
        # Show available adapters
        print("2. Available data source adapters:")
        for name, adapter in manager.adapters.items():
            status = "✓ Healthy" if adapter.is_healthy else "✗ Unhealthy"
            print(f"   {name}:")
            print(f"     Market: {adapter.get_market_type()}")
            print(f"     Exchange: {adapter.get_exchange_name()}")
            print(f"     Priority: {adapter.config.priority}")
            print(f"     Status: {status}")
            print(f"     Enabled: {adapter.config.enabled}")
        print()
        
        # Health check all adapters
        print("3. Performing health checks on all adapters...")
        health_results = manager.health_check_all()
        
        for name, result in health_results.items():
            status = "✓ Healthy" if result['healthy'] else "✗ Unhealthy"
            print(f"   {name}: {status}")
            if result['message']:
                print(f"     Message: {result['message']}")
        print()
        
        # Test symbol validation across adapters
        print("4. Testing symbol validation across adapters:")
        test_symbols = ['AAPL', 'BTCUSDT', '000001.SH', 'GDP']
        
        for symbol in test_symbols:
            print(f"   {symbol}:")
            priority_order = manager.get_adapter_priority_order(symbol)
            if priority_order:
                print(f"     Available in: {', '.join(priority_order)}")
                print(f"     Primary adapter: {priority_order[0]}")
                
                # Get failover adapter
                failover = manager.get_failover_adapter(priority_order[0], symbol)
                if failover:
                    print(f"     Failover adapter: {failover}")
                else:
                    print(f"     No failover available")
            else:
                print(f"     Not available in any adapter")
        print()
        
        # Demonstrate failover functionality
        print("5. Testing failover functionality with AAPL...")
        start_date = datetime.now() - timedelta(days=7)
        end_date = datetime.now()
        
        success, data_or_error, adapter_used = manager.fetch_with_failover(
            'AAPL', start_date, end_date, '1d', max_attempts=3
        )
        
        if success:
            data = data_or_error
            print(f"   ✓ Successfully fetched {len(data)} records using {adapter_used}")
            if data:
                latest = data[-1]
                print(f"   Latest data: {latest.timestamp.strftime('%Y-%m-%d')}")
                print(f"   Close price: ${latest.close:.2f}")
        else:
            print(f"   ✗ Failed to fetch data: {data_or_error}")
        print()
        
        # Add and manage sync schedules
        print("6. Managing data synchronization schedules...")
        
        # Add a sync schedule
        success = manager.add_sync_schedule(
            'daily_stocks',
            ['AAPL', 'GOOGL', 'MSFT'],
            interval_minutes=60,  # Every hour
            days_back=1
        )
        
        if success:
            print("   ✓ Added daily stocks sync schedule")
        
        # Add another schedule
        success = manager.add_sync_schedule(
            'crypto_updates',
            ['BTCUSDT', 'ETHUSDT'],
            interval_minutes=30,  # Every 30 minutes
            days_back=1
        )
        
        if success:
            print("   ✓ Added crypto updates sync schedule")
        
        print(f"   Active schedules: {len(manager.sync_schedules)}")
        for schedule_id, config in manager.sync_schedules.items():
            print(f"     {schedule_id}: {len(config['symbols'])} symbols, every {config['interval_minutes']} min")
        print()
        
        # Get comprehensive performance report
        print("7. Generating comprehensive performance report...")
        report = manager.get_comprehensive_performance_report()
        
        print(f"   Report generated at: {report['generated_at']}")
        print(f"   Total adapters: {report['total_adapters']}")
        print(f"   Healthy adapters: {report['healthy_adapters']}")
        print(f"   Total requests (last hour): {report['summary']['total_requests_last_hour']}")
        print(f"   Total requests (last day): {report['summary']['total_requests_last_day']}")
        print(f"   Error rate: {report['summary']['error_rate']:.1f}%")
        
        print("   Adapter details:")
        for name, adapter_info in report['adapters'].items():
            if 'error' not in adapter_info:
                print(f"     {name}:")
                print(f"       Market: {adapter_info['market']}")
                print(f"       Healthy: {adapter_info['healthy']}")
                print(f"       Priority: {adapter_info['priority']}")
                perf = adapter_info['performance']
                print(f"       Requests (hour): {perf.get('requests_last_hour', 0)}")
        print()
        
        # Test market correlation analysis
        print("8. Analyzing market correlations...")
        correlation_symbols = ['AAPL', 'GOOGL']  # Tech stocks
        
        correlation_matrix = manager.analyze_market_correlations(
            correlation_symbols,
            start_date,
            end_date,
            cache_duration_hours=1
        )
        
        if correlation_matrix is not None:
            print("   ✓ Correlation analysis completed")
            print("   Correlation matrix:")
            for symbol1 in correlation_symbols:
                for symbol2 in correlation_symbols:
                    if symbol1 != symbol2:
                        corr = correlation_matrix.loc[symbol1, symbol2]
                        print(f"     {symbol1} vs {symbol2}: {corr:.3f}")
        else:
            print("   ✗ Correlation analysis failed (insufficient data)")
        print()
        
        # Test economic-market analysis (if FRED is available)
        print("9. Economic-market data analysis...")
        if any(adapter.get_market_type() == 'ECONOMIC' for adapter in manager.adapters.values()):
            analysis = manager.get_economic_market_analysis(
                ['GDP', 'UNRATE'],  # Economic indicators
                ['AAPL'],           # Market symbols
                start_date - timedelta(days=365),  # Longer period for economic data
                end_date
            )
            
            if 'error' not in analysis:
                print("   ✓ Economic-market analysis completed")
                print(f"   Economic indicators analyzed: {len(analysis['economic_indicators'])}")
                print(f"   Market symbols analyzed: {len(analysis['market_symbols'])}")
                print(f"   Correlations calculated: {len(analysis['correlations'])}")
                
                if analysis['correlations']:
                    print("   Key correlations:")
                    for corr_name, corr_info in list(analysis['correlations'].items())[:3]:
                        print(f"     {corr_name}: {corr_info['correlation']:.3f} ({corr_info['significance']})")
            else:
                print(f"   ✗ Economic analysis failed: {analysis['error']}")
        else:
            print("   ⚠ No economic data adapters available")
        print()
        
        # Show sync status
        print("10. Data synchronization status:")
        sync_status = manager.get_sync_status()
        
        print(f"    Total synced symbols: {sync_status['total_synced_symbols']}")
        if sync_status['last_sync_times']:
            print("    Recent sync times:")
            for symbol, sync_time in list(sync_status['last_sync_times'].items())[:5]:
                print(f"      {symbol}: {sync_time}")
        
        print("    Adapter status:")
        for name, status in sync_status['adapters_status'].items():
            health = "✓" if status['healthy'] else "✗"
            print(f"      {name}: {health} (enabled: {status['enabled']})")
        print()
        
        # Data coverage report
        print("11. Data coverage report:")
        coverage = manager.get_data_coverage_report()
        
        print(f"    Generated at: {coverage['generated_at']}")
        print(f"    Total adapters: {coverage['total_adapters']}")
        print(f"    Healthy adapters: {coverage['healthy_adapters']}")
        
        print("    Coverage by adapter:")
        for name, adapter_coverage in coverage['adapters'].items():
            if 'error' not in adapter_coverage:
                print(f"      {name}:")
                print(f"        Market: {adapter_coverage['market']}")
                print(f"        Exchange: {adapter_coverage['exchange']}")
                print(f"        Symbols: {adapter_coverage['symbol_count']}")
                if adapter_coverage['symbols']:
                    print(f"        Sample: {', '.join(adapter_coverage['symbols'][:3])}...")
        print()
        
        # Cleanup sync schedules
        print("12. Cleaning up sync schedules...")
        for schedule_id in list(manager.sync_schedules.keys()):
            success = manager.remove_sync_schedule(schedule_id)
            if success:
                print(f"    ✓ Removed schedule: {schedule_id}")
        print()
        
        print("=== Example completed successfully! ===")
        
        # Stop the scheduler
        print("\nStopping background scheduler...")
        manager.stop_scheduler()
        print("✓ Scheduler stopped")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()