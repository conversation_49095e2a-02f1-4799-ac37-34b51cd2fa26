"""
Example usage of the Risk Monitoring System.

This example demonstrates how to use the comprehensive risk monitoring system
to track portfolio risk in real-time, generate alerts, and maintain audit logs.
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.risk.monitoring_system import (
    RiskMonitoringSystem, RiskThreshold, AlertSeverity, AlertStatus
)
from src.risk.manager import RiskManager
from src.risk.models import RiskConfig, RiskMetrics
from src.models.portfolio import Portfolio, Position


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('risk_monitoring.log')
        ]
    )


def create_sample_portfolio() -> Portfolio:
    """Create a sample portfolio for demonstration."""
    portfolio = Portfolio(initial_capital=1000000.0, cash=200000.0)
    
    # Add some positions
    positions = [
        Position(symbol="AAPL", quantity=1000, avg_price=150.0, last_price=155.0),
        Position(symbol="GOOGL", quantity=200, avg_price=2800.0, last_price=2850.0),
        Position(symbol="MSFT", quantity=800, avg_price=300.0, last_price=310.0),
        Position(symbol="TSLA", quantity=300, avg_price=800.0, last_price=750.0),
        Position(symbol="AMZN", quantity=150, avg_price=3200.0, last_price=3300.0)
    ]
    
    for position in positions:
        position.update_market_value(position.last_price)
        portfolio.positions[position.symbol] = position
    
    portfolio.update_total_value()
    return portfolio


def create_risk_metrics(portfolio: Portfolio, scenario: str = "normal") -> RiskMetrics:
    """Create risk metrics for different scenarios."""
    base_metrics = RiskMetrics(
        timestamp=datetime.now(),
        portfolio_value=portfolio.total_value,
        cash=portfolio.cash,
        total_positions=len(portfolio.positions),
        largest_position_pct=15.0,
        largest_position_symbol="GOOGL",
        portfolio_beta=1.2,
        portfolio_var_1d=25000.0,
        portfolio_var_5d=55000.0,
        expected_shortfall=35000.0,
        current_drawdown_pct=5.0,
        max_drawdown_pct=8.0,
        drawdown_duration_days=3,
        gross_leverage=1.1,
        net_leverage=1.0,
        long_exposure_pct=95.0,
        short_exposure_pct=5.0,
        top_5_positions_pct=85.0,
        daily_risk_used_pct=45.0,
        weekly_risk_used_pct=65.0,
        monthly_risk_used_pct=40.0
    )
    
    # Modify metrics based on scenario
    if scenario == "high_risk":
        base_metrics.current_drawdown_pct = 18.0  # Breach critical threshold
        base_metrics.largest_position_pct = 30.0  # High concentration
        base_metrics.daily_risk_used_pct = 95.0   # Near budget limit
        base_metrics.gross_leverage = 2.5         # High leverage
    elif scenario == "warning":
        base_metrics.current_drawdown_pct = 12.0  # Warning level
        base_metrics.largest_position_pct = 18.0  # Moderate concentration
        base_metrics.daily_risk_used_pct = 85.0   # Warning level
    elif scenario == "low_cash":
        base_metrics.cash = portfolio.total_value * 0.01  # Only 1% cash
    
    return base_metrics


def alert_callback(alert):
    """Callback function for handling alerts."""
    print(f"\n🚨 ALERT RECEIVED:")
    print(f"   Severity: {alert.severity.value.upper()}")
    print(f"   Title: {alert.title}")
    print(f"   Message: {alert.message}")
    print(f"   Source: {alert.source}")
    if alert.symbol:
        print(f"   Symbol: {alert.symbol}")
    print(f"   Timestamp: {alert.timestamp}")


def event_callback(event):
    """Callback function for handling events."""
    if event.event_type in ["alert_generated", "risk_decision"]:
        print(f"\n📝 EVENT LOGGED: {event.event_type} - {event.description}")


def demonstrate_basic_monitoring():
    """Demonstrate basic monitoring functionality."""
    print("=" * 60)
    print("BASIC RISK MONITORING DEMONSTRATION")
    print("=" * 60)
    
    # Setup
    risk_config = RiskConfig(
        max_position_size_pct=0.20,
        max_drawdown_pct=0.15,
        daily_risk_budget=0.05,
        max_leverage=2.0
    )
    
    risk_manager = RiskManager(risk_config)
    portfolio = create_sample_portfolio()
    
    # Create monitoring system
    monitoring_system = RiskMonitoringSystem(
        risk_manager=risk_manager,
        config=risk_config,
        db_path="data/risk_monitoring_demo.db"
    )
    
    # Add callbacks
    monitoring_system.add_alert_callback(alert_callback)
    monitoring_system.add_event_callback(event_callback)
    
    print(f"Portfolio Value: ${portfolio.total_value:,.2f}")
    print(f"Cash: ${portfolio.cash:,.2f}")
    print(f"Positions: {len(portfolio.positions)}")
    
    # Start monitoring
    print("\n🟢 Starting risk monitoring system...")
    monitoring_system.start_monitoring()
    
    # Simulate normal conditions
    print("\n📊 Testing normal risk conditions...")
    normal_metrics = create_risk_metrics(portfolio, "normal")
    monitoring_system._check_risk_thresholds(normal_metrics)
    
    time.sleep(1)
    
    # Simulate warning conditions
    print("\n⚠️  Testing warning conditions...")
    warning_metrics = create_risk_metrics(portfolio, "warning")
    monitoring_system._check_risk_thresholds(warning_metrics)
    
    time.sleep(1)
    
    # Simulate high risk conditions
    print("\n🔴 Testing high risk conditions...")
    high_risk_metrics = create_risk_metrics(portfolio, "high_risk")
    monitoring_system._check_risk_thresholds(high_risk_metrics)
    
    time.sleep(1)
    
    # Show monitoring status
    status = monitoring_system.get_monitoring_status()
    print(f"\n📈 MONITORING STATUS:")
    print(f"   System Status: {status['status']}")
    print(f"   Active Alerts: {status['active_alerts']}")
    print(f"   Total Alerts Generated: {status['total_alerts_generated']}")
    print(f"   Total Events Logged: {status['total_events_logged']}")
    
    # Stop monitoring
    print("\n🔴 Stopping monitoring system...")
    monitoring_system.stop_monitoring()
    
    return monitoring_system


def demonstrate_alert_management():
    """Demonstrate alert acknowledgment and resolution."""
    print("\n" + "=" * 60)
    print("ALERT MANAGEMENT DEMONSTRATION")
    print("=" * 60)
    
    risk_manager = RiskManager()
    monitoring_system = RiskMonitoringSystem(risk_manager, db_path="data/risk_monitoring_demo.db")
    
    # Start monitoring
    monitoring_system.start_monitoring()
    
    # Generate some alerts
    portfolio = create_sample_portfolio()
    high_risk_metrics = create_risk_metrics(portfolio, "high_risk")
    monitoring_system._check_risk_thresholds(high_risk_metrics)
    
    time.sleep(0.5)
    
    # Get active alerts
    active_alerts = monitoring_system.get_active_alerts()
    print(f"\n📋 Active Alerts: {len(active_alerts)}")
    
    if active_alerts:
        alert = active_alerts[0]
        print(f"\n🔍 Managing Alert: {alert.title}")
        
        # Acknowledge alert
        print("   ✅ Acknowledging alert...")
        monitoring_system.acknowledge_alert(
            alert.id, 
            "risk_manager", 
            "Alert acknowledged - investigating issue"
        )
        
        time.sleep(0.5)
        
        # Resolve alert
        print("   ✅ Resolving alert...")
        monitoring_system.resolve_alert(
            alert.id,
            "risk_manager",
            "Issue resolved - position size reduced"
        )
        
        # Record decision
        monitoring_system.record_decision(
            decision_type="position_reduction",
            description="Reduced GOOGL position by 50% to manage concentration risk",
            user_id="risk_manager",
            symbol="GOOGL",
            metadata={"original_position": 200, "new_position": 100}
        )
    
    # Show updated status
    status = monitoring_system.get_monitoring_status()
    print(f"\n📊 Updated Status:")
    print(f"   Active Alerts: {status['active_alerts']}")
    print(f"   Decisions Recorded: {status['decisions_recorded']}")
    
    monitoring_system.stop_monitoring()
    return monitoring_system


def demonstrate_custom_thresholds():
    """Demonstrate custom threshold configuration."""
    print("\n" + "=" * 60)
    print("CUSTOM THRESHOLDS DEMONSTRATION")
    print("=" * 60)
    
    risk_manager = RiskManager()
    monitoring_system = RiskMonitoringSystem(risk_manager, db_path="data/risk_monitoring_demo.db")
    
    # Add custom threshold
    custom_threshold = RiskThreshold(
        name="Sector Concentration",
        metric_name="tech_sector_pct",
        warning_threshold=60.0,
        critical_threshold=80.0,
        comparison_operator="gt",
        description="Technology sector concentration limit",
        cooldown_minutes=2
    )
    
    print(f"➕ Adding custom threshold: {custom_threshold.name}")
    monitoring_system.add_threshold(custom_threshold)
    
    # Show all thresholds
    print(f"\n📋 Configured Thresholds: {len(monitoring_system.thresholds)}")
    for name, threshold in monitoring_system.thresholds.items():
        print(f"   • {name}: {threshold.warning_threshold} / {threshold.critical_threshold}")
    
    # Test threshold suppression
    print(f"\n🔇 Suppressing 'Portfolio Drawdown' alerts for 1 minute...")
    monitoring_system.suppress_alert_type("Portfolio Drawdown", duration_minutes=1)
    
    status = monitoring_system.get_monitoring_status()
    print(f"   Suppressed Alert Types: {status['suppressed_alert_types']}")
    
    return monitoring_system


def demonstrate_reporting():
    """Demonstrate monitoring reports and history."""
    print("\n" + "=" * 60)
    print("REPORTING AND HISTORY DEMONSTRATION")
    print("=" * 60)
    
    risk_manager = RiskManager()
    monitoring_system = RiskMonitoringSystem(risk_manager, db_path="data/risk_monitoring_demo.db")
    monitoring_system.start_monitoring()
    
    # Generate some activity
    portfolio = create_sample_portfolio()
    
    # Create different scenarios over time
    scenarios = ["normal", "warning", "high_risk", "low_cash"]
    for i, scenario in enumerate(scenarios):
        print(f"   Simulating {scenario} scenario...")
        metrics = create_risk_metrics(portfolio, scenario)
        monitoring_system._check_risk_thresholds(metrics)
        time.sleep(0.2)
    
    # Generate comprehensive report
    print("\n📊 Generating monitoring report...")
    report = monitoring_system.generate_monitoring_report(hours=1)
    
    print(f"\n📈 MONITORING REPORT:")
    print(f"   Report Period: {report['report_period_hours']} hours")
    print(f"   System Uptime: {report['system_status']['uptime_percentage']:.1f}%")
    print(f"   Total Alerts: {report['alert_summary']['total_alerts']}")
    print(f"   Active Alerts: {report['alert_summary']['active_alerts']}")
    print(f"   Total Events: {report['event_summary']['total_events']}")
    
    # Show alert breakdown
    alerts_by_severity = report['alert_summary']['alerts_by_severity']
    print(f"\n🚨 Alerts by Severity:")
    for severity, count in alerts_by_severity.items():
        print(f"   {severity.upper()}: {count}")
    
    # Show threshold breaches
    threshold_breaches = report['alert_summary']['threshold_breaches']
    if threshold_breaches:
        print(f"\n⚠️  Threshold Breaches:")
        for threshold, count in threshold_breaches.items():
            print(f"   {threshold}: {count}")
    
    # Get recent history
    alert_history = monitoring_system.get_alert_history(hours=1)
    event_history = monitoring_system.get_event_history(hours=1)
    
    print(f"\n📜 Recent History:")
    print(f"   Alerts (last hour): {len(alert_history)}")
    print(f"   Events (last hour): {len(event_history)}")
    
    monitoring_system.stop_monitoring()
    return monitoring_system


def main():
    """Main demonstration function."""
    setup_logging()
    
    print("🚀 RISK MONITORING SYSTEM DEMONSTRATION")
    print("This example shows comprehensive risk monitoring capabilities")
    print("including real-time alerts, event logging, and audit trails.\n")
    
    try:
        # Run demonstrations
        monitoring_system1 = demonstrate_basic_monitoring()
        monitoring_system2 = demonstrate_alert_management()
        monitoring_system3 = demonstrate_custom_thresholds()
        monitoring_system4 = demonstrate_reporting()
        
        print("\n" + "=" * 60)
        print("DEMONSTRATION COMPLETE")
        print("=" * 60)
        print("✅ All risk monitoring features demonstrated successfully!")
        print("📁 Check 'data/risk_monitoring_demo.db' for persistent data")
        print("📄 Check 'risk_monitoring.log' for detailed logs")
        
    except Exception as e:
        print(f"\n❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()