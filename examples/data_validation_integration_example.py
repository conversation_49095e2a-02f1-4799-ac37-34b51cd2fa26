#!/usr/bin/env python3
"""
Integration example showing how to use data validation with the data manager.

This example demonstrates how to validate and clean data before storing it
in the database using the DataManager.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src and project root to path for imports
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, os.path.join(project_root, 'src'))
sys.path.insert(0, project_root)

from src.data.validation import DataValidator
from src.data.manager import get_data_manager
from src.models.market_data import UnifiedMarketData


def create_sample_market_data():
    """Create sample market data with some quality issues."""
    print("Creating sample market data...")
    
    # Create date range (weekdays only to simulate trading days)
    dates = pd.bdate_range('2023-01-01', periods=30)
    
    # Generate realistic price data
    np.random.seed(42)
    base_price = 100.0
    data_records = []
    
    for i, date in enumerate(dates):
        # Simulate price movement
        change = np.random.normal(0, 0.015)  # 1.5% daily volatility
        base_price *= (1 + change)
        
        # Create OHLC with realistic relationships
        open_price = base_price
        daily_range = abs(np.random.normal(0, 0.01)) * base_price
        high_price = base_price + daily_range * np.random.uniform(0.3, 1.0)
        low_price = base_price - daily_range * np.random.uniform(0.3, 1.0)
        close_price = np.random.uniform(low_price, high_price)
        
        # Generate volume
        volume = np.random.randint(10000, 100000)
        
        # Create UnifiedMarketData record
        record = UnifiedMarketData(
            symbol="AAPL",
            timestamp=datetime.combine(date, datetime.min.time()),
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=volume,
            market="US",
            exchange="NASDAQ",
            currency="USD",
            adj_close=close_price  # Simplified - same as close
        )
        
        data_records.append(record)
    
    # Introduce some data quality issues
    print("Introducing data quality issues...")
    
    # Issue 1: OHLC inconsistency
    data_records[5].high = data_records[5].low - 1
    
    # Issue 2: Negative price
    data_records[10].open = -5.0
    
    # Issue 3: Zero volume
    data_records[15].volume = 0
    
    # Issue 4: Missing adjusted close
    data_records[20].adj_close = None
    
    print(f"Created {len(data_records)} market data records with quality issues")
    return data_records


def validate_before_storage():
    """Demonstrate validation before storing data."""
    print("\n" + "="*60)
    print("VALIDATION BEFORE STORAGE DEMONSTRATION")
    print("="*60)
    
    # Create sample data
    data_records = create_sample_market_data()
    
    # Convert to DataFrame for validation
    df_data = []
    for record in data_records:
        df_data.append({
            'timestamp': record.timestamp,
            'open': record.open,
            'high': record.high,
            'low': record.low,
            'close': record.close,
            'volume': record.volume,
            'adj_close': record.adj_close
        })
    
    df = pd.DataFrame(df_data)
    df.set_index('timestamp', inplace=True)
    
    # Initialize validator
    validator = DataValidator({
        'ohlcv': {
            'min_price': 0.01,
            'max_price': 10000,
            'min_volume': 0
        },
        'cleaning': {
            'fill_method': 'forward',
            'outlier_method': 'clip'
        }
    })
    
    print(f"Original data: {len(df)} records")
    print(f"Data issues present: OHLC inconsistency, negative price, zero volume")
    
    # Validate and clean data
    print("\nValidating and cleaning data...")
    cleaned_df, report = validator.validate_and_clean(df, "AAPL")
    
    # Show validation results
    validation_summary = report['validation']
    print(f"\nValidation Results:")
    print(f"  Data valid: {validation_summary['is_valid']}")
    print(f"  Issues found: {validation_summary['issues_count']}")
    
    # Show cleaning results
    cleaning_summary = report['cleaning']
    print(f"\nCleaning Results:")
    print(f"  Original rows: {cleaning_summary['original_rows']}")
    print(f"  Cleaned rows: {cleaning_summary['cleaned_rows']}")
    print(f"  Rows removed: {cleaning_summary['rows_removed']}")
    
    print(f"\nCleaning operations:")
    for operation in cleaning_summary['operations']:
        print(f"  - {operation}")
    
    # Convert cleaned data back to UnifiedMarketData objects
    cleaned_records = []
    for timestamp, row in cleaned_df.iterrows():
        record = UnifiedMarketData(
            symbol="AAPL",
            timestamp=timestamp,
            open=row['open'],
            high=row['high'],
            low=row['low'],
            close=row['close'],
            volume=int(row['volume']),
            market="US",
            exchange="NASDAQ",
            currency="USD",
            adj_close=row['adj_close']
        )
        cleaned_records.append(record)
    
    print(f"\nCleaned data ready for storage: {len(cleaned_records)} records")
    return cleaned_records


def demonstrate_data_quality_monitoring():
    """Demonstrate ongoing data quality monitoring."""
    print("\n" + "="*60)
    print("DATA QUALITY MONITORING DEMONSTRATION")
    print("="*60)
    
    # Get data manager (using SQLite for this example)
    data_manager = get_data_manager(use_sqlite=True)
    
    # Create and store some sample data
    sample_records = create_sample_market_data()
    
    print("Storing sample data...")
    stored_count = data_manager.save_market_data_batch(sample_records)
    print(f"Stored {stored_count} records")
    
    # Get data quality report from data manager
    print("\nGenerating data quality report...")
    quality_report = data_manager.get_data_quality_report("AAPL", "US")
    
    if 'error' not in quality_report:
        print(f"Quality Report for {quality_report['symbol']}:")
        print(f"  Date range: {quality_report['date_range']['start']} to {quality_report['date_range']['end']}")
        print(f"  Total records: {quality_report['records']['total']}")
        print(f"  Data completeness: {quality_report['records']['completeness']:.2%}")
        
        print(f"\nMissing values:")
        for column, count in quality_report['missing_values'].items():
            if count > 0:
                print(f"  {column}: {count}")
        
        print(f"\nPrice anomalies:")
        anomalies = quality_report['price_anomalies']
        for anomaly_type, count in anomalies.items():
            if count > 0:
                print(f"  {anomaly_type}: {count}")
    else:
        print(f"Error generating quality report: {quality_report['error']}")
    
    # Clean up
    data_manager.close()


def demonstrate_validation_workflow():
    """Demonstrate complete validation workflow."""
    print("\n" + "="*60)
    print("COMPLETE VALIDATION WORKFLOW DEMONSTRATION")
    print("="*60)
    
    # Step 1: Create raw data with issues
    print("Step 1: Creating raw data with quality issues...")
    raw_data = create_sample_market_data()
    
    # Step 2: Validate individual records
    print("\nStep 2: Validating individual records...")
    validator = DataValidator()
    valid_records = []
    invalid_count = 0
    
    for record in raw_data:
        validation_result = validator.validate_market_data(record)
        if validation_result.is_valid:
            valid_records.append(record)
        else:
            invalid_count += 1
            print(f"  Invalid record at {record.timestamp}: {len(validation_result.issues)} issues")
    
    print(f"  Valid records: {len(valid_records)}")
    print(f"  Invalid records: {invalid_count}")
    
    # Step 3: Batch validation and cleaning
    print("\nStep 3: Batch validation and cleaning...")
    
    # Convert to DataFrame for batch processing
    df_data = []
    for record in raw_data:
        df_data.append({
            'timestamp': record.timestamp,
            'open': record.open,
            'high': record.high,
            'low': record.low,
            'close': record.close,
            'volume': record.volume,
            'adj_close': record.adj_close
        })
    
    df = pd.DataFrame(df_data)
    df.set_index('timestamp', inplace=True)
    
    # Perform batch validation and cleaning
    cleaned_df, comprehensive_report = validator.validate_and_clean(df, "AAPL")
    
    # Step 4: Generate quality report
    print("\nStep 4: Generating final quality report...")
    final_quality = comprehensive_report['final_quality']
    
    print(f"Final Quality Assessment:")
    print(f"  OHLCV validation: {'PASSED' if final_quality['validation_results']['ohlcv']['is_valid'] else 'FAILED'}")
    print(f"  Time series validation: {'PASSED' if final_quality['validation_results']['timeseries']['is_valid'] else 'FAILED'}")
    print(f"  Anomalies detected: {final_quality['anomaly_detection']['total_anomalies']}")
    
    print(f"\nRecommendations:")
    for i, recommendation in enumerate(final_quality['recommendations'], 1):
        print(f"  {i}. {recommendation}")
    
    # Step 5: Convert back to objects for storage
    print(f"\nStep 5: Data ready for storage")
    print(f"  Cleaned records: {len(cleaned_df)}")
    print(f"  Data quality: {'ACCEPTABLE' if final_quality['validation_results']['ohlcv']['summary']['critical'] == 0 else 'NEEDS ATTENTION'}")


def main():
    """Run all integration demonstrations."""
    print("QUANTITATIVE TRADING SYSTEM - DATA VALIDATION INTEGRATION EXAMPLES")
    print("="*80)
    
    try:
        # Run demonstrations
        validate_before_storage()
        demonstrate_data_quality_monitoring()
        demonstrate_validation_workflow()
        
        print("\n" + "="*80)
        print("ALL INTEGRATION DEMONSTRATIONS COMPLETED SUCCESSFULLY!")
        print("="*80)
        
        print("\nKey Takeaways:")
        print("1. Always validate data before storage to maintain data quality")
        print("2. Use batch validation and cleaning for efficiency")
        print("3. Monitor data quality regularly with comprehensive reports")
        print("4. Implement automated data cleaning workflows")
        print("5. Set up alerts for critical data quality issues")
        
    except Exception as e:
        print(f"\nError during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()