#!/usr/bin/env python3
"""
实时监控API使用示例
演示如何使用WebSocket和REST API进行系统监控
"""

import asyncio
import websockets
import json
import aiohttp
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MonitoringClient:
    """监控API客户端"""
    
    def __init__(self, base_url="http://localhost:8000", ws_url="ws://localhost:8000"):
        self.base_url = base_url
        self.ws_url = ws_url
        self.session = None
        self.websocket = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
        if self.websocket:
            await self.websocket.close()

    async def get_system_health(self):
        """获取系统健康状态"""
        try:
            async with self.session.get(f"{self.base_url}/api/v1/monitoring/system/health") as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"System Health: {data['overall_status']}")
                    logger.info(f"Components: {len(data['components'])}")
                    
                    for component in data['components']:
                        status = component['status']
                        name = component['component']
                        response_time = component.get('response_time_ms', 'N/A')
                        logger.info(f"  - {name}: {status} ({response_time}ms)")
                    
                    return data
                else:
                    logger.error(f"Failed to get system health: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return None

    async def get_strategy_status(self):
        """获取策略状态"""
        try:
            async with self.session.get(f"{self.base_url}/api/v1/monitoring/strategies") as response:
                if response.status == 200:
                    strategies = await response.json()
                    logger.info(f"Found {len(strategies)} strategies:")
                    
                    for strategy in strategies:
                        name = strategy['strategy_name']
                        status = strategy['status']
                        pnl = strategy['pnl']
                        positions = strategy['positions']
                        logger.info(f"  - {name}: {status}, PnL: ${pnl:.2f}, Positions: {positions}")
                    
                    return strategies
                else:
                    logger.error(f"Failed to get strategy status: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error getting strategy status: {e}")
            return None

    async def get_alerts(self):
        """获取告警列表"""
        try:
            async with self.session.get(f"{self.base_url}/api/v1/monitoring/alerts") as response:
                if response.status == 200:
                    alerts = await response.json()
                    logger.info(f"Found {len(alerts)} alerts:")
                    
                    for alert in alerts:
                        severity = alert['severity']
                        title = alert['title']
                        timestamp = alert['timestamp']
                        acknowledged = alert['acknowledged']
                        status_text = "✓ Acknowledged" if acknowledged else "⚠ Pending"
                        logger.info(f"  - [{severity.upper()}] {title} - {status_text}")
                    
                    return alerts
                else:
                    logger.error(f"Failed to get alerts: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error getting alerts: {e}")
            return None

    async def get_audit_logs(self, limit=10):
        """获取审计日志"""
        try:
            params = {"limit": limit}
            async with self.session.get(f"{self.base_url}/api/v1/monitoring/audit-logs", params=params) as response:
                if response.status == 200:
                    logs = await response.json()
                    logger.info(f"Found {len(logs)} audit log entries:")
                    
                    for log in logs[:5]:  # 只显示前5条
                        username = log['username']
                        action = log['action']
                        resource = log['resource']
                        timestamp = log['timestamp']
                        success = "✓" if log['success'] else "✗"
                        logger.info(f"  - {success} {username}: {action} on {resource}")
                    
                    return logs
                else:
                    logger.error(f"Failed to get audit logs: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error getting audit logs: {e}")
            return None

    async def connect_websocket(self, endpoint="/ws/monitoring"):
        """连接WebSocket监控端点"""
        try:
            uri = f"{self.ws_url}{endpoint}"
            self.websocket = await websockets.connect(uri)
            logger.info(f"Connected to WebSocket: {uri}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to WebSocket: {e}")
            return False

    async def subscribe_to_channel(self, channel):
        """订阅监控频道"""
        if not self.websocket:
            logger.error("WebSocket not connected")
            return False
            
        try:
            message = {
                "type": "subscribe",
                "channel": channel,
                "timestamp": datetime.now().isoformat()
            }
            await self.websocket.send(json.dumps(message))
            logger.info(f"Subscribed to channel: {channel}")
            return True
        except Exception as e:
            logger.error(f"Failed to subscribe to channel {channel}: {e}")
            return False

    async def listen_to_messages(self, duration=30):
        """监听WebSocket消息"""
        if not self.websocket:
            logger.error("WebSocket not connected")
            return
            
        logger.info(f"Listening for messages for {duration} seconds...")
        
        try:
            end_time = asyncio.get_event_loop().time() + duration
            
            while asyncio.get_event_loop().time() < end_time:
                try:
                    # 设置超时以便定期检查时间
                    message = await asyncio.wait_for(self.websocket.recv(), timeout=1.0)
                    data = json.loads(message)
                    
                    msg_type = data.get("type", "unknown")
                    channel = data.get("channel", "")
                    timestamp = data.get("timestamp", "")
                    
                    if msg_type == "system_status":
                        system_data = data.get("data", {})
                        cpu = system_data.get("cpu_usage", 0)
                        memory = system_data.get("memory_usage", 0)
                        logger.info(f"[SYSTEM] CPU: {cpu:.1f}%, Memory: {memory:.1f}%")
                        
                    elif msg_type == "strategy_status":
                        strategy_data = data.get("data", {})
                        name = strategy_data.get("strategy_name", "Unknown")
                        status = strategy_data.get("status", "unknown")
                        pnl = strategy_data.get("pnl", 0)
                        logger.info(f"[STRATEGY] {name}: {status}, PnL: ${pnl:.2f}")
                        
                    elif msg_type == "alert":
                        alert_data = data.get("data", {})
                        severity = alert_data.get("severity", "unknown")
                        message_text = alert_data.get("message", "")
                        logger.info(f"[ALERT] {severity.upper()}: {message_text}")
                        
                    elif msg_type == "audit_log":
                        log_data = data.get("data", {})
                        username = log_data.get("username", "unknown")
                        action = log_data.get("action", "unknown")
                        logger.info(f"[AUDIT] {username}: {action}")
                        
                    else:
                        logger.info(f"[{msg_type.upper()}] {channel}: {data.get('message', 'No message')}")
                        
                except asyncio.TimeoutError:
                    continue  # 继续监听
                    
        except Exception as e:
            logger.error(f"Error listening to messages: {e}")

    async def send_ping(self):
        """发送ping消息"""
        if not self.websocket:
            logger.error("WebSocket not connected")
            return False
            
        try:
            message = {
                "type": "ping",
                "timestamp": datetime.now().isoformat()
            }
            await self.websocket.send(json.dumps(message))
            logger.info("Sent ping message")
            return True
        except Exception as e:
            logger.error(f"Failed to send ping: {e}")
            return False

async def demo_rest_api():
    """演示REST API使用"""
    logger.info("=== REST API Demo ===")
    
    async with MonitoringClient() as client:
        # 获取系统健康状态
        await client.get_system_health()
        await asyncio.sleep(1)
        
        # 获取策略状态
        await client.get_strategy_status()
        await asyncio.sleep(1)
        
        # 获取告警
        await client.get_alerts()
        await asyncio.sleep(1)
        
        # 获取审计日志
        await client.get_audit_logs()

async def demo_websocket():
    """演示WebSocket实时监控"""
    logger.info("=== WebSocket Demo ===")
    
    async with MonitoringClient() as client:
        # 连接WebSocket
        if not await client.connect_websocket():
            return
        
        # 订阅系统健康频道
        await client.subscribe_to_channel("system_health")
        await asyncio.sleep(0.5)
        
        # 订阅策略状态频道
        await client.subscribe_to_channel("strategy_status")
        await asyncio.sleep(0.5)
        
        # 订阅告警频道
        await client.subscribe_to_channel("alerts")
        await asyncio.sleep(0.5)
        
        # 发送ping测试连接
        await client.send_ping()
        await asyncio.sleep(1)
        
        # 监听消息
        await client.listen_to_messages(duration=15)

async def demo_specific_websocket_endpoints():
    """演示特定的WebSocket端点"""
    logger.info("=== Specific WebSocket Endpoints Demo ===")
    
    endpoints = [
        "/ws/system-health",
        "/ws/strategy-status", 
        "/ws/alerts"
    ]
    
    for endpoint in endpoints:
        logger.info(f"\n--- Testing {endpoint} ---")
        
        async with MonitoringClient() as client:
            if await client.connect_websocket(endpoint):
                await client.send_ping()
                await client.listen_to_messages(duration=5)

async def main():
    """主演示函数"""
    logger.info("🚀 Starting Monitoring API Demo\n")
    
    try:
        # 演示REST API
        await demo_rest_api()
        await asyncio.sleep(2)
        
        # 演示通用WebSocket监控
        await demo_websocket()
        await asyncio.sleep(2)
        
        # 演示特定WebSocket端点
        await demo_specific_websocket_endpoints()
        
        logger.info("\n🎉 Monitoring API demo completed!")
        
    except KeyboardInterrupt:
        logger.info("\n⏹ Demo interrupted by user")
    except Exception as e:
        logger.error(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())