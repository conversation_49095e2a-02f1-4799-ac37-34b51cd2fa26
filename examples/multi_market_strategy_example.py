#!/usr/bin/env python3
"""
Multi-market strategy example demonstrating cross-market arbitrage and coordination.

This example shows how to:
1. Set up multi-market strategy framework
2. Configure different markets with their specific parameters
3. Implement cross-market arbitrage strategies
4. Handle currency conversion and timezone differences
5. Manage risk across multiple markets
6. Coordinate multiple strategies
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from datetime import datetime, timezone, timedelta
import pandas as pd
import numpy as np
from typing import Dict, List, Any

# Import multi-market framework components
from src.strategies.multi_market import (
    MultiMarketContext, CurrencyConverter, MarketSession, 
    MarketFees, SlippageModel, CurrencyRate
)
from src.strategies.multi_market_manager import (
    MultiMarketStrategyManager, StrategyAllocation, MarketAllocation, StrategyPriority
)
from src.strategies.market_config import MarketConfigManager
from src.strategies.multi_market_risk import MultiMarketRiskManager
from src.strategies.examples.cross_market_arbitrage import CrossMarketArbitrageStrategy
from src.models.market_data import UnifiedMarketData, MarketInfo
from src.models.portfolio import Portfolio, Position

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_sample_market_data() -> Dict[str, UnifiedMarketData]:
    """Create sample market data for demonstration."""
    current_time = datetime.now(timezone.utc)
    
    # Sample data for the same asset (e.g., Apple stock) across different markets
    sample_data = {
        'AAPL_US': UnifiedMarketData(
            symbol='AAPL',
            timestamp=current_time,
            open=150.0,
            high=152.0,
            low=149.0,
            close=151.0,
            volume=1000000,
            market='US',
            exchange='NASDAQ',
            currency='USD'
        ),
        'AAPL_EU': UnifiedMarketData(
            symbol='AAPL',
            timestamp=current_time - timedelta(seconds=5),  # Slight delay
            open=140.0,  # Price difference for arbitrage opportunity
            high=142.0,
            low=139.0,
            close=141.0,
            volume=500000,
            market='EU',
            exchange='XETRA',
            currency='EUR'
        ),
        'BTC_BINANCE': UnifiedMarketData(
            symbol='BTCUSDT',
            timestamp=current_time,
            open=45000.0,
            high=45500.0,
            low=44800.0,
            close=45200.0,
            volume=100.0,
            market='CRYPTO',
            exchange='BINANCE',
            currency='USDT'
        ),
        'BTC_COINBASE': UnifiedMarketData(
            symbol='BTCUSD',
            timestamp=current_time - timedelta(seconds=2),
            open=45100.0,  # Slight price difference
            high=45600.0,
            low=44900.0,
            close=45350.0,  # Higher price for arbitrage
            volume=80.0,
            market='CRYPTO',
            exchange='COINBASE',
            currency='USD'
        ),
        'TSLA_CN': UnifiedMarketData(
            symbol='TSLA.SH',
            timestamp=current_time - timedelta(seconds=10),
            open=1000.0,  # Price in CNY
            high=1020.0,
            low=995.0,
            close=1010.0,
            volume=50000,
            market='CN',
            exchange='SSE',
            currency='CNY'
        )
    }
    
    return sample_data


def setup_currency_converter() -> CurrencyConverter:
    """Set up currency converter with sample rates."""
    converter = CurrencyConverter()
    
    # Add some sample exchange rates
    current_time = datetime.now(timezone.utc)
    
    sample_rates = [
        CurrencyRate('EUR', 'USD', 1.08, current_time, 'sample'),
        CurrencyRate('CNY', 'USD', 0.14, current_time, 'sample'),
        CurrencyRate('USDT', 'USD', 1.0, current_time, 'sample'),
        CurrencyRate('GBP', 'USD', 1.25, current_time, 'sample'),
    ]
    
    for rate in sample_rates:
        converter.add_rate(rate)
    
    logger.info("Currency converter set up with sample rates")
    return converter


def setup_market_config() -> MarketConfigManager:
    """Set up market configuration with sessions, fees, and slippage models."""
    config_manager = MarketConfigManager()
    
    # Add additional market sessions
    config_manager.add_market_session('EU_XETRA', MarketSession(
        market='EU',
        exchange='XETRA',
        timezone='Europe/Frankfurt',
        open_time='09:00',
        close_time='17:30',
        trading_days=[0, 1, 2, 3, 4]
    ))
    
    # Add fee structures
    config_manager.add_market_fees('EU_XETRA', MarketFees(
        market='EU',
        exchange='XETRA',
        maker_fee=0.002,  # 0.2%
        taker_fee=0.003,  # 0.3%
        min_fee=2.0,
        currency='EUR'
    ))
    
    # Add slippage models
    config_manager.add_slippage_model('EU_XETRA', SlippageModel(
        market='EU',
        exchange='XETRA',
        base_slippage=0.0008,
        volume_impact=0.000001,
        volatility_multiplier=1.1,
        max_slippage=0.015
    ))
    
    logger.info("Market configuration set up")
    return config_manager


def setup_risk_manager(currency_converter: CurrencyConverter) -> MultiMarketRiskManager:
    """Set up multi-market risk manager."""
    risk_manager = MultiMarketRiskManager(currency_converter)
    
    # Customize risk limits for multi-market trading
    from src.strategies.multi_market_risk import RiskLimit
    
    # Add specific limits for cross-market arbitrage
    risk_manager.add_risk_limit(RiskLimit(
        name='max_arbitrage_exposure',
        limit_type='percentage',
        value=0.2,  # 20% max exposure to arbitrage strategies
        scope='portfolio'
    ))
    
    risk_manager.add_risk_limit(RiskLimit(
        name='max_cross_market_correlation',
        limit_type='ratio',
        value=0.8,  # Max 80% in correlated cross-market positions
        scope='portfolio'
    ))
    
    logger.info("Multi-market risk manager set up")
    return risk_manager


def create_sample_portfolio() -> Portfolio:
    """Create a sample portfolio for testing."""
    positions = {
        'AAPL': Position(
            symbol='AAPL',
            quantity=100,
            avg_price=150.0,
            market_value=15000.0,
            unrealized_pnl=100.0
        ),
        'BTCUSDT': Position(
            symbol='BTCUSDT',
            quantity=0.5,
            avg_price=45000.0,
            market_value=22500.0,
            unrealized_pnl=100.0
        )
    }
    
    portfolio = Portfolio(
        initial_capital=100000.0,
        cash=50000.0,
        positions=positions,
        total_value=87500.0,
        unrealized_pnl=200.0,
        realized_pnl=1000.0
    )
    
    return portfolio


def demonstrate_multi_market_context():
    """Demonstrate multi-market context capabilities."""
    logger.info("=== Demonstrating Multi-Market Context ===")
    
    # Set up components
    currency_converter = setup_currency_converter()
    market_config = setup_market_config()
    risk_manager = setup_risk_manager(currency_converter)
    portfolio = create_sample_portfolio()
    
    # Create multi-market context
    context = MultiMarketContext(
        portfolio=portfolio,
        currency_converter=currency_converter,
        market_sessions=market_config.sessions,
        fee_models=market_config.fees,
        slippage_models=market_config.slippage_models,
        risk_manager=risk_manager
    )
    
    # Test currency conversion
    logger.info("Testing currency conversion:")
    usd_amount = 1000.0
    eur_amount = context.convert_currency(usd_amount, 'USD', 'EUR')
    cny_amount = context.convert_currency(usd_amount, 'USD', 'CNY')
    logger.info(f"  ${usd_amount} USD = €{eur_amount:.2f} EUR = ¥{cny_amount:.2f} CNY")
    
    # Test market hours
    logger.info("Testing market hours:")
    current_time = datetime.now(timezone.utc)
    for market in ['US', 'EU', 'CN', 'CRYPTO']:
        is_open = context.is_market_open(market, current_time)
        logger.info(f"  {market} market is {'OPEN' if is_open else 'CLOSED'}")
    
    # Test market overlap
    logger.info("Testing market overlap:")
    overlap = context.get_market_overlap_hours('US', 'EU')
    if overlap:
        logger.info(f"  US-EU overlap: {overlap[0][0]} - {overlap[0][1]} UTC")
    else:
        logger.info("  No US-EU overlap today")
    
    # Test order cost calculation
    logger.info("Testing order cost calculation:")
    cost_info = context.calculate_order_cost('AAPL', 'US', 'NASDAQ', 100, 150.0, 'BUY')
    logger.info(f"  Order cost breakdown: {cost_info}")
    
    return context


def demonstrate_cross_market_arbitrage():
    """Demonstrate cross-market arbitrage strategy."""
    logger.info("=== Demonstrating Cross-Market Arbitrage ===")
    
    # Set up context
    context = demonstrate_multi_market_context()
    
    # Create arbitrage strategy
    arbitrage_strategy = CrossMarketArbitrageStrategy(
        name="Demo_Arbitrage",
        parameters={
            'min_profit_threshold': 0.01,  # 1% minimum profit
            'max_position_size': 10000.0,
            'price_staleness_threshold': 60,
            'correlation_threshold': 0.9
        }
    )
    
    # Initialize strategy
    arbitrage_strategy.initialize(context)
    
    # Create sample market data with arbitrage opportunities
    market_data = create_sample_market_data()
    
    # Group data by market for strategy processing
    market_grouped_data = {}
    for symbol, data in market_data.items():
        market = data.market
        if market not in market_grouped_data:
            market_grouped_data[market] = []
        market_grouped_data[market].append(data)
    
    # Process data and generate signals
    logger.info("Processing market data for arbitrage opportunities...")
    signals = arbitrage_strategy.on_multi_market_data(market_grouped_data)
    
    logger.info(f"Generated {len(signals)} arbitrage signals:")
    for i, signal in enumerate(signals):
        logger.info(f"  Signal {i+1}:")
        logger.info(f"    Symbol: {signal.symbol}")
        logger.info(f"    Action: {signal.action}")
        logger.info(f"    Quantity: {signal.quantity}")
        logger.info(f"    Confidence: {signal.confidence:.3f}")
        logger.info(f"    Expected Profit: ${signal.metadata.get('expected_profit', 0):.2f}")
        logger.info(f"    Profit %: {signal.metadata.get('profit_percentage', 0)*100:.2f}%")
    
    # Get strategy status
    status = arbitrage_strategy.get_strategy_status()
    logger.info(f"Strategy Status: {status}")
    
    return arbitrage_strategy


def demonstrate_strategy_manager():
    """Demonstrate multi-market strategy manager."""
    logger.info("=== Demonstrating Multi-Market Strategy Manager ===")
    
    # Set up components
    currency_converter = setup_currency_converter()
    market_config = setup_market_config()
    risk_manager = setup_risk_manager(currency_converter)
    
    # Create strategy manager
    manager = MultiMarketStrategyManager(market_config, risk_manager)
    
    # Create and register multiple strategies
    strategies = []
    
    # Arbitrage strategy 1 - Conservative
    arb_strategy_1 = CrossMarketArbitrageStrategy(
        name="Conservative_Arbitrage",
        parameters={
            'min_profit_threshold': 0.015,  # 1.5% minimum
            'max_position_size': 5000.0,
            'risk_limit': 0.05
        }
    )
    
    allocation_1 = StrategyAllocation(
        strategy_id="arb_1",
        capital_allocation=0.3,
        risk_allocation=0.2,
        priority=StrategyPriority.HIGH,
        max_positions=5
    )
    
    manager.register_strategy(arb_strategy_1, allocation_1)
    strategies.append(arb_strategy_1)
    
    # Arbitrage strategy 2 - Aggressive
    arb_strategy_2 = CrossMarketArbitrageStrategy(
        name="Aggressive_Arbitrage",
        parameters={
            'min_profit_threshold': 0.005,  # 0.5% minimum
            'max_position_size': 15000.0,
            'risk_limit': 0.15
        }
    )
    
    allocation_2 = StrategyAllocation(
        strategy_id="arb_2",
        capital_allocation=0.4,
        risk_allocation=0.5,
        priority=StrategyPriority.MEDIUM,
        max_positions=10
    )
    
    manager.register_strategy(arb_strategy_2, allocation_2)
    strategies.append(arb_strategy_2)
    
    # Process market data through manager
    market_data = create_sample_market_data()
    logger.info("Processing market data through strategy manager...")
    
    coordinated_signals = manager.process_market_data(market_data)
    
    logger.info(f"Manager coordinated {len(coordinated_signals)} signals:")
    for i, signal in enumerate(coordinated_signals):
        logger.info(f"  Coordinated Signal {i+1}:")
        logger.info(f"    Symbol: {signal.symbol}, Action: {signal.action}")
        logger.info(f"    Quantity: {signal.quantity}, Confidence: {signal.confidence:.3f}")
    
    # Get manager status
    status = manager.get_manager_status()
    logger.info("Manager Status:")
    logger.info(f"  Total Strategies: {status['total_strategies']}")
    logger.info(f"  Active Strategies: {status['active_strategies']}")
    logger.info(f"  Supported Markets: {status['supported_markets']}")
    logger.info(f"  Total Capital Allocated: {status['total_capital_allocated']:.1%}")
    
    # Test allocation optimization
    logger.info("Testing allocation optimization...")
    
    # Simulate some performance data
    from src.strategies.multi_market_manager import StrategyPerformance
    
    for i, strategy in enumerate(strategies):
        strategy_id = list(manager.strategies.keys())[i]
        performance = StrategyPerformance(
            strategy_id=strategy_id,
            total_return=0.15 + (i * 0.05),  # Different returns
            sharpe_ratio=1.2 + (i * 0.3),
            max_drawdown=0.08 - (i * 0.02),
            win_rate=0.6 + (i * 0.1),
            total_trades=100 + (i * 50),
            avg_trade_duration=30.0,
            last_updated=datetime.now(timezone.utc)
        )
        manager.update_strategy_performance(strategy_id, performance)
    
    # Optimize allocations
    optimized_allocations = manager.optimize_allocations()
    logger.info(f"Optimized allocations: {optimized_allocations}")
    
    # Clean up
    manager.cleanup()
    
    return manager


def demonstrate_risk_management():
    """Demonstrate multi-market risk management."""
    logger.info("=== Demonstrating Multi-Market Risk Management ===")
    
    # Set up risk manager
    currency_converter = setup_currency_converter()
    risk_manager = setup_risk_manager(currency_converter)
    
    # Create sample portfolio
    portfolio = create_sample_portfolio()
    
    # Calculate risk metrics
    logger.info("Calculating risk metrics...")
    
    market_exposures = risk_manager.calculate_market_exposures(portfolio)
    logger.info("Market Exposures:")
    for market, exposure in market_exposures.items():
        logger.info(f"  {market}: ${exposure.total_value:.2f} ({exposure.percentage_of_portfolio:.1%})")
    
    currency_exposures = risk_manager.calculate_currency_exposures(portfolio)
    logger.info("Currency Exposures:")
    for currency, exposure in currency_exposures.items():
        logger.info(f"  {currency}: ${exposure.value_usd:.2f} ({exposure.percentage_of_portfolio:.1%})")
    
    # Test risk limit checking
    from src.models.trading import Order
    
    test_order = Order(
        id='test_order_001',
        symbol='AAPL',
        side='BUY',
        quantity=200,
        price=150.0,
        order_type='MARKET',
        timestamp=datetime.now(timezone.utc)
    )
    
    logger.info("Testing risk limit checking...")
    is_allowed, violations = risk_manager.check_order_risk(test_order, portfolio)
    
    logger.info(f"Order allowed: {is_allowed}")
    if violations:
        logger.info(f"Risk violations ({len(violations)}):")
        for violation in violations:
            logger.info(f"  {violation.rule_name}: {violation.violation_type}")
            logger.info(f"    Current: {violation.current_value:.3f}, Limit: {violation.limit_value:.3f}")
    
    # Get risk summary
    risk_summary = risk_manager.get_risk_summary(portfolio)
    logger.info("Risk Summary:")
    logger.info(f"  Portfolio Value: ${risk_summary['portfolio_value']:,.2f}")
    logger.info(f"  Leverage: {risk_summary['leverage']:.2f}x")
    logger.info(f"  Total Positions: {risk_summary['total_positions']}")
    logger.info(f"  Recent Violations: {risk_summary['recent_violations']}")
    
    return risk_manager


def main():
    """Main demonstration function."""
    logger.info("Starting Multi-Market Strategy Framework Demonstration")
    logger.info("=" * 60)
    
    try:
        # Demonstrate each component
        demonstrate_multi_market_context()
        print()
        
        demonstrate_cross_market_arbitrage()
        print()
        
        demonstrate_strategy_manager()
        print()
        
        demonstrate_risk_management()
        print()
        
        logger.info("=" * 60)
        logger.info("Multi-Market Strategy Framework Demonstration Complete!")
        logger.info("Key Features Demonstrated:")
        logger.info("✓ Multi-market data handling and synchronization")
        logger.info("✓ Currency conversion and unified pricing")
        logger.info("✓ Market timezone and trading hours management")
        logger.info("✓ Cross-market arbitrage strategy implementation")
        logger.info("✓ Multi-strategy coordination and conflict resolution")
        logger.info("✓ Market-specific fee and slippage models")
        logger.info("✓ Multi-market risk management and limits")
        logger.info("✓ Performance tracking and allocation optimization")
        
    except Exception as e:
        logger.error(f"Demonstration failed: {e}")
        raise


if __name__ == "__main__":
    main()