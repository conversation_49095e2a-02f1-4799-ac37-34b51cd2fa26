"""
Example usage of the backtesting engine.

This script demonstrates how to use the backtesting engine with a simple
momentum strategy.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.backtest.engine import BacktestEngine
from src.backtest.executor import ExecutionConfig
from src.strategies.base import BaseStrategy, StrategyContext
from src.strategies.parameters import StrategyParameter, ParameterType
from src.models.market_data import MarketData
from src.models.trading import Signal, SignalAction


class SimpleMomentumStrategy(BaseStrategy):
    """
    Simple momentum strategy that buys on upward momentum
    and sells on downward momentum.
    """
    
    def get_parameter_definitions(self):
        return {
            'lookback_period': StrategyParameter(
                name='lookback_period',
                param_type=ParameterType.INTEGER,
                default_value=5,
                description='Lookback period for momentum calculation'
            ),
            'momentum_threshold': StrategyParameter(
                name='momentum_threshold',
                param_type=ParameterType.FLOAT,
                default_value=0.02,
                description='Momentum threshold for signal generation'
            ),
            'position_size': StrategyParameter(
                name='position_size',
                param_type=ParameterType.FLOAT,
                default_value=1000.0,
                description='Position size in dollars'
            )
        }
    
    def initialize(self, context):
        self.context = context
        self.is_initialized = True
        self.last_signal_time = None
        
    def on_data(self, data):
        if not isinstance(data, MarketData):
            return []
        
        # Avoid generating signals too frequently
        if (self.last_signal_time and 
            (data.timestamp - self.last_signal_time).days < 1):
            return []
        
        # Get historical data for momentum calculation
        lookback = self.get_parameter('lookback_period', 5)
        historical_data = self.context.get_historical_data(
            data.symbol, 
            lookback + 1,
            data.timestamp
        )
        
        if len(historical_data) < lookback + 1:
            return []
        
        # Calculate momentum (price change over lookback period)
        current_price = data.close
        past_price = historical_data['close'].iloc[0]
        momentum = (current_price - past_price) / past_price
        
        threshold = self.get_parameter('momentum_threshold', 0.02)
        position_size = self.get_parameter('position_size', 1000.0)
        
        # Calculate quantity based on position size
        quantity = int(position_size / current_price)
        
        if quantity <= 0:
            return []
        
        # Get current position
        current_position = self.context.get_position(data.symbol)
        
        signals = []
        
        # Generate buy signal on positive momentum
        if momentum > threshold and current_position <= 0:
            signals.append(Signal(
                symbol=data.symbol,
                action=SignalAction.BUY,
                quantity=quantity,
                timestamp=data.timestamp,
                confidence=min(momentum / threshold, 1.0),
                metadata={'momentum': momentum}
            ))
            self.last_signal_time = data.timestamp
        
        # Generate sell signal on negative momentum
        elif momentum < -threshold and current_position > 0:
            # Sell current position
            signals.append(Signal(
                symbol=data.symbol,
                action=SignalAction.SELL,
                quantity=min(quantity, int(current_position)),
                timestamp=data.timestamp,
                confidence=min(abs(momentum) / threshold, 1.0),
                metadata={'momentum': momentum}
            ))
            self.last_signal_time = data.timestamp
        
        return signals


def generate_sample_data(symbol='AAPL', days=252, start_date=None):
    """
    Generate sample market data for backtesting.
    
    Args:
        symbol: Stock symbol
        days: Number of days to generate
        start_date: Start date for data generation
        
    Returns:
        DataFrame with OHLCV data
    """
    if start_date is None:
        start_date = datetime.now() - timedelta(days=days)
    
    dates = pd.date_range(start_date, periods=days, freq='D')
    
    # Generate realistic price data with random walk
    np.random.seed(42)  # For reproducible results
    
    initial_price = 150.0
    returns = np.random.normal(0.0005, 0.02, days)  # Daily returns
    prices = [initial_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        # Generate OHLC from close price
        daily_volatility = 0.015
        high = price * (1 + np.random.uniform(0, daily_volatility))
        low = price * (1 - np.random.uniform(0, daily_volatility))
        open_price = prices[i-1] if i > 0 else price
        
        # Ensure OHLC relationships are valid
        high = max(high, price, open_price)
        low = min(low, price, open_price)
        
        data.append({
            'symbol': symbol,
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(price, 2),
            'volume': int(np.random.uniform(1000000, 5000000))
        })
    
    return pd.DataFrame(data, index=dates)


def run_backtest_example():
    """Run a complete backtesting example."""
    print("=== Backtesting Engine Example ===\n")
    
    # Generate sample data
    print("Generating sample market data...")
    data = generate_sample_data(symbol='AAPL', days=252)  # 1 year of data
    print(f"Generated {len(data)} days of data from {data.index[0].date()} to {data.index[-1].date()}")
    print(f"Price range: ${data['close'].min():.2f} - ${data['close'].max():.2f}\n")
    
    # Create execution configuration
    execution_config = ExecutionConfig(
        commission_rate=0.001,    # 0.1% commission
        slippage_rate=0.0005,     # 0.05% slippage
        market_impact_rate=0.0001 # 0.01% market impact
    )
    
    # Create backtesting engine
    print("Initializing backtesting engine...")
    engine = BacktestEngine(
        initial_capital=100000.0,
        execution_config=execution_config
    )
    
    # Create and add strategy
    strategy = SimpleMomentumStrategy(
        name="MomentumStrategy",
        parameters={
            'lookback_period': 10,
            'momentum_threshold': 0.03,  # 3% momentum threshold
            'position_size': 5000.0      # $5000 per position
        }
    )
    
    strategy_id = engine.add_strategy(strategy)
    print(f"Added strategy: {strategy_id}")
    
    # Set data feed
    engine.set_data_feed(data)
    print("Data feed configured\n")
    
    # Set up progress tracking
    def progress_callback(progress):
        if progress.current_step % 50 == 0 or progress.current_step == progress.total_steps:
            print(f"Progress: {progress.get_progress_pct():.1f}% "
                  f"({progress.current_step}/{progress.total_steps})")
    
    engine.set_progress_callback(progress_callback)
    
    # Run backtest
    print("Running backtest...")
    start_time = datetime.now()
    
    result = engine.run_backtest()
    
    end_time = datetime.now()
    print(f"Backtest completed in {(end_time - start_time).total_seconds():.2f} seconds\n")
    
    # Display results
    print("=== Backtest Results ===")
    print(f"Initial Capital: ${result.initial_capital:,.2f}")
    print(f"Final Capital: ${result.final_capital:,.2f}")
    print(f"Total Return: {result.total_return:.2%}")
    print(f"Annualized Return: {result.annualized_return:.2%}")
    print(f"Volatility: {result.volatility:.2%}")
    print(f"Sharpe Ratio: {result.sharpe_ratio:.2f}")
    print(f"Maximum Drawdown: {result.max_drawdown:.2%}")
    print()
    
    print("=== Trading Statistics ===")
    print(f"Total Trades: {result.total_trades}")
    print(f"Winning Trades: {result.winning_trades}")
    print(f"Losing Trades: {result.losing_trades}")
    print(f"Win Rate: {result.win_rate:.2%}")
    print(f"Profit Factor: {result.profit_factor:.2f}")
    print(f"Average Win: ${result.avg_win:.2f}")
    print(f"Average Loss: ${result.avg_loss:.2f}")
    print(f"Largest Win: ${result.largest_win:.2f}")
    print(f"Largest Loss: ${result.largest_loss:.2f}")
    print()
    
    # Show sample trades
    if result.trades:
        print("=== Sample Trades ===")
        for i, trade in enumerate(result.trades[:5]):  # Show first 5 trades
            print(f"Trade {i+1}: {trade.side.value} {trade.quantity} shares at "
                  f"${trade.price:.2f} on {trade.timestamp.date()}")
        
        if len(result.trades) > 5:
            print(f"... and {len(result.trades) - 5} more trades")
        print()
    
    # Show portfolio progression
    if result.portfolio_history:
        print("=== Portfolio Progression (Sample) ===")
        sample_indices = [0, len(result.portfolio_history)//4, 
                         len(result.portfolio_history)//2,
                         3*len(result.portfolio_history)//4, -1]
        
        for i in sample_indices:
            portfolio = result.portfolio_history[i]
            print(f"{portfolio.timestamp.date()}: "
                  f"Total Value: ${portfolio.total_value:,.2f}, "
                  f"Cash: ${portfolio.cash:,.2f}, "
                  f"Positions: {len(portfolio.positions)}")
    
    print("\n=== Example Complete ===")
    return result


if __name__ == "__main__":
    try:
        result = run_backtest_example()
    except Exception as e:
        print(f"Error running backtest example: {e}")
        import traceback
        traceback.print_exc()