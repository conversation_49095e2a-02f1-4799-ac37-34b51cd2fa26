#!/usr/bin/env python3
"""
Direct test of import functionality without module dependencies.
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import tempfile
import shutil

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

def test_importers_direct():
    """Test the import functionality directly without module imports."""
    print("Testing Data Import Components (Direct)")
    print("=" * 50)
    
    try:
        # Import directly from files
        sys.path.insert(0, str(Path(__file__).parent.parent / 'src' / 'data'))
        
        from importers import CSVImporter, ExcelImporter, DataImportManager
        from models.market_data import UnifiedMarketData
        
        # Create temporary directory
        temp_dir = tempfile.mkdtemp()
        print(f"Using temporary directory: {temp_dir}")
        
        try:
            # Test CSV Importer
            print("\n1. Testing CSV Importer...")
            csv_importer = CSVImporter()
            print(f"   Supported formats: {csv_importer.supported_formats}")
            
            # Create sample CSV data
            sample_data = []
            base_date = datetime(2023, 1, 1)
            
            for i in range(10):
                date = base_date + timedelta(days=i)
                sample_data.append({
                    'symbol': 'TEST',
                    'timestamp': date.strftime('%Y-%m-%d'),
                    'open': 100.0 + i,
                    'high': 102.0 + i,
                    'low': 98.0 + i,
                    'close': 101.0 + i,
                    'volume': 1000000 + i * 1000,
                    'adj_close': 101.0 + i
                })
            
            df = pd.DataFrame(sample_data)
            csv_path = Path(temp_dir) / 'test_data.csv'
            df.to_csv(csv_path, index=False)
            print(f"   Created test CSV: {csv_path}")
            
            # Test validation
            is_valid = csv_importer.validate_file(csv_path)
            print(f"   File validation: {'✓ PASS' if is_valid else '✗ FAIL'}")
            
            # Test preview
            preview = csv_importer.get_import_preview(csv_path, rows=3)
            print(f"   Preview rows: {len(preview)}")
            print(f"   Preview columns: {list(preview.columns)}")
            
            # Test import
            imported_data = csv_importer.import_data(csv_path, market='US', exchange='TEST', currency='USD')
            print(f"   Imported records: {len(imported_data)}")
            
            if imported_data:
                sample_record = imported_data[0]
                print(f"   Sample record: {sample_record.symbol} @ {sample_record.timestamp}")
                print(f"   Price: {sample_record.close}, Volume: {sample_record.volume}")
                print(f"   Market: {sample_record.market}, Exchange: {sample_record.exchange}")
            
            # Test Excel Importer
            print("\n2. Testing Excel Importer...")
            excel_importer = ExcelImporter()
            print(f"   Supported formats: {excel_importer.supported_formats}")
            
            # Create sample Excel data
            excel_data = []
            for i in range(5):
                date = base_date + timedelta(days=i + 20)
                excel_data.append({
                    'symbol': 'EXCEL_TEST',
                    'timestamp': date.strftime('%Y-%m-%d'),
                    'open': 200.0 + i,
                    'high': 202.0 + i,
                    'low': 198.0 + i,
                    'close': 201.0 + i,
                    'volume': 2000000 + i * 1000
                })
            
            excel_df = pd.DataFrame(excel_data)
            excel_path = Path(temp_dir) / 'test_data.xlsx'
            excel_df.to_excel(excel_path, index=False)
            print(f"   Created test Excel: {excel_path}")
            
            # Test Excel validation and import
            is_valid = excel_importer.validate_file(excel_path)
            print(f"   File validation: {'✓ PASS' if is_valid else '✗ FAIL'}")
            
            if is_valid:
                excel_imported = excel_importer.import_data(excel_path, market='US', exchange='TEST', currency='USD')
                print(f"   Imported records: {len(excel_imported)}")
                
                if excel_imported:
                    sample_record = excel_imported[0]
                    print(f"   Sample Excel record: {sample_record.symbol} @ {sample_record.timestamp}")
            
            # Test Import Manager
            print("\n3. Testing Import Manager...")
            import_manager = DataImportManager()
            
            supported_formats = import_manager.get_supported_formats()
            print(f"   All supported formats: {supported_formats}")
            
            # Test file type detection
            csv_type = import_manager.detect_file_type(csv_path)
            excel_type = import_manager.detect_file_type(excel_path)
            print(f"   CSV type detection: {csv_type}")
            print(f"   Excel type detection: {excel_type}")
            
            # Test unified import
            success, result = import_manager.import_file(csv_path, market='US', exchange='TEST', currency='USD')
            print(f"   Unified CSV import: {'✓ SUCCESS' if success else '✗ FAILED'}")
            if success:
                print(f"   Records imported: {len(result)}")
            else:
                print(f"   Error: {result}")
            
            # Test DataFrame import
            print("\n4. Testing DataFrame Import...")
            df_data = []
            for i in range(3):
                date = base_date + timedelta(days=i + 30)
                df_data.append({
                    'symbol': 'DF_TEST',
                    'timestamp': date,
                    'open': 300.0 + i,
                    'high': 302.0 + i,
                    'low': 298.0 + i,
                    'close': 301.0 + i,
                    'volume': 3000000 + i * 1000
                })
            
            test_df = pd.DataFrame(df_data)
            success, result = import_manager.import_from_dataframe(test_df, market='US', exchange='TEST', currency='USD')
            print(f"   DataFrame import: {'✓ SUCCESS' if success else '✗ FAILED'}")
            if success:
                print(f"   Records imported: {len(result)}")
                if result:
                    sample_record = result[0]
                    print(f"   Sample DF record: {sample_record.symbol} @ {sample_record.timestamp}")
            
            print("\n✓ All import tests completed successfully!")
            
        finally:
            # Clean up
            shutil.rmtree(temp_dir, ignore_errors=True)
            
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        import traceback
        traceback.print_exc()


def test_cache_direct():
    """Test cache components directly."""
    print("\n" + "=" * 50)
    print("Testing Cache Components (Direct)")
    print("=" * 50)
    
    try:
        # Import cache components directly
        sys.path.insert(0, str(Path(__file__).parent.parent / 'src' / 'data'))
        
        from cache import MemoryCache, CacheEntry, DiskCache
        
        # Test CacheEntry
        print("\n1. Testing CacheEntry...")
        test_data = {'test': 'data', 'number': 42}
        entry = CacheEntry(test_data, ttl_seconds=60)
        
        print(f"   Created entry with TTL: {entry.ttl_seconds}s")
        print(f"   Is expired: {entry.is_expired()}")
        print(f"   Age: {entry.get_age_seconds():.2f}s")
        
        # Access data
        accessed_data = entry.access()
        print(f"   Access count: {entry.access_count}")
        print(f"   Data matches: {accessed_data == test_data}")
        
        # Test MemoryCache
        print("\n2. Testing MemoryCache...")
        cache = MemoryCache(max_size=10, default_ttl=60)
        
        # Test put and get
        cache.put('test_key', test_data)
        retrieved = cache.get('test_key')
        print(f"   Cache put/get: {'✓ SUCCESS' if retrieved == test_data else '✗ FAILED'}")
        
        # Test cache miss
        missing = cache.get('nonexistent_key')
        print(f"   Cache miss: {'✓ SUCCESS' if missing is None else '✗ FAILED'}")
        
        # Test multiple entries
        for i in range(5):
            cache.put(f'key_{i}', {'data': i})
        
        # Test stats
        stats = cache.get_stats()
        print(f"   Cache stats: size={stats['size']}, hits={stats['hits']}, misses={stats['misses']}")
        print(f"   Hit rate: {stats['hit_rate']:.2%}")
        
        # Test cleanup
        removed = cache.cleanup_expired()
        print(f"   Cleanup removed: {removed} entries")
        
        # Test DiskCache
        print("\n3. Testing DiskCache...")
        temp_cache_dir = tempfile.mkdtemp()
        
        try:
            disk_cache = DiskCache(cache_dir=temp_cache_dir, max_size_mb=10)
            
            # Test disk cache operations
            test_disk_data = {'disk_test': 'data', 'values': [1, 2, 3, 4, 5]}
            success = disk_cache.put('disk_key', test_disk_data, ttl_seconds=300)
            print(f"   Disk cache put: {'✓ SUCCESS' if success else '✗ FAILED'}")
            
            retrieved_disk = disk_cache.get('disk_key')
            print(f"   Disk cache get: {'✓ SUCCESS' if retrieved_disk == test_disk_data else '✗ FAILED'}")
            
            # Test disk cache stats
            disk_stats = disk_cache.get_stats()
            print(f"   Disk cache stats: entries={disk_stats['entries']}, size={disk_stats['total_size_mb']:.2f}MB")
            
        finally:
            shutil.rmtree(temp_cache_dir, ignore_errors=True)
        
        print("\n✓ Cache components test completed!")
        
    except Exception as e:
        print(f"✗ Cache test failed: {e}")
        import traceback
        traceback.print_exc()


def test_coverage_direct():
    """Test coverage API components directly."""
    print("\n" + "=" * 50)
    print("Testing Coverage API Components (Direct)")
    print("=" * 50)
    
    try:
        # Import coverage components directly
        sys.path.insert(0, str(Path(__file__).parent.parent / 'src' / 'data'))
        
        from coverage_api import DateRange, DataCoverage, MarketCoverage
        
        # Test DateRange
        print("\n1. Testing DateRange...")
        start = datetime(2023, 1, 1)
        end = datetime(2023, 1, 31)
        date_range = DateRange(start=start, end=end, days=31)
        
        print(f"   Date range: {date_range.start} to {date_range.end}")
        print(f"   Days: {date_range.days}")
        
        range_dict = date_range.to_dict()
        print(f"   Dict keys: {list(range_dict.keys())}")
        print(f"   Dict start: {range_dict['start']}")
        
        # Test invalid range
        try:
            invalid_range = DateRange(start=end, end=start, days=0)
            print("   ✗ Should have failed with invalid range")
        except ValueError:
            print("   ✓ Correctly rejected invalid date range")
        
        # Test DataCoverage
        print("\n2. Testing DataCoverage...")
        coverage = DataCoverage(
            symbol='TEST',
            market='US',
            exchange='NASDAQ',
            currency='USD',
            database_coverage=date_range,
            source_coverage={'yahoo': date_range},
            gaps=[],
            completeness_ratio=0.95,
            last_updated=datetime.now()
        )
        
        print(f"   Coverage symbol: {coverage.symbol}")
        print(f"   Completeness: {coverage.completeness_ratio:.2%}")
        print(f"   Sources: {list(coverage.source_coverage.keys())}")
        
        coverage_dict = coverage.to_dict()
        print(f"   Dict keys: {list(coverage_dict.keys())}")
        
        # Test MarketCoverage
        print("\n3. Testing MarketCoverage...")
        market_coverage = MarketCoverage(
            market='US',
            total_symbols=100,
            symbols_with_data=85,
            coverage_ratio=0.85,
            date_range=date_range,
            top_symbols=['AAPL', 'GOOGL', 'MSFT'],
            data_sources=['yahoo', 'alpha_vantage'],
            last_updated=datetime.now()
        )
        
        print(f"   Market: {market_coverage.market}")
        print(f"   Coverage: {market_coverage.symbols_with_data}/{market_coverage.total_symbols}")
        print(f"   Ratio: {market_coverage.coverage_ratio:.2%}")
        print(f"   Top symbols: {market_coverage.top_symbols}")
        
        market_dict = market_coverage.to_dict()
        print(f"   Dict keys: {list(market_dict.keys())}")
        
        print("\n✓ Coverage API components test completed!")
        
    except Exception as e:
        print(f"✗ Coverage API test failed: {e}")
        import traceback
        traceback.print_exc()


def test_adapters_direct():
    """Test adapter components directly."""
    print("\n" + "=" * 50)
    print("Testing Adapter Components (Direct)")
    print("=" * 50)
    
    try:
        # Import adapter components directly
        sys.path.insert(0, str(Path(__file__).parent.parent / 'src' / 'data' / 'adapters'))
        
        from base import DataSourceConfig, RateLimiter, MockDataSourceAdapter
        
        # Test DataSourceConfig
        print("\n1. Testing DataSourceConfig...")
        config = DataSourceConfig(
            name='test_source',
            adapter_class='TestAdapter',
            enabled=True,
            priority=1,
            rate_limit={'requests_per_minute': 60},
            credentials={'api_key': 'test_key'},
            default_params={'interval': '1d'}
        )
        
        print(f"   Config name: {config.name}")
        print(f"   Enabled: {config.enabled}")
        print(f"   Priority: {config.priority}")
        print(f"   Rate limit: {config.rate_limit}")
        
        # Test RateLimiter
        print("\n2. Testing RateLimiter...")
        rate_limiter = RateLimiter({'requests_per_minute': 5})
        
        print(f"   Can make request: {rate_limiter.can_make_request()}")
        
        # Make some requests
        for i in range(3):
            if rate_limiter.can_make_request():
                rate_limiter.record_request()
                print(f"   Request {i+1}: ✓ Allowed")
            else:
                print(f"   Request {i+1}: ✗ Rate limited")
        
        wait_time = rate_limiter.get_wait_time()
        print(f"   Wait time: {wait_time:.2f}s")
        
        # Test MockDataSourceAdapter
        print("\n3. Testing MockDataSourceAdapter...")
        mock_adapter = MockDataSourceAdapter(config)
        
        print(f"   Adapter name: {mock_adapter.config.name}")
        print(f"   Market type: {mock_adapter.get_market_type()}")
        print(f"   Exchange: {mock_adapter.get_exchange_name()}")
        print(f"   Currency: {mock_adapter.get_default_currency()}")
        
        # Test available symbols
        symbols = mock_adapter.get_available_symbols()
        print(f"   Available symbols: {symbols}")
        
        # Test symbol validation
        valid_symbol = symbols[0] if symbols else 'AAPL'
        is_valid = mock_adapter.validate_symbol(valid_symbol)
        print(f"   Symbol validation ({valid_symbol}): {'✓ VALID' if is_valid else '✗ INVALID'}")
        
        # Test market info
        market_info = mock_adapter.get_market_info(valid_symbol)
        if market_info:
            print(f"   Market info: {market_info.name} ({market_info.symbol})")
        
        # Test data fetching
        try:
            start_date = datetime(2023, 1, 1)
            end_date = datetime(2023, 1, 10)
            data = mock_adapter.fetch_historical_data(valid_symbol, start_date, end_date)
            print(f"   Fetched data: {len(data)} records")
            
            if data:
                sample = data[0]
                print(f"   Sample data: {sample.symbol} @ {sample.timestamp}")
                print(f"   Price: {sample.close}, Volume: {sample.volume}")
        except Exception as e:
            print(f"   Data fetch failed: {e}")
        
        # Test health check
        is_healthy, message = mock_adapter.health_check()
        print(f"   Health check: {'✓ HEALTHY' if is_healthy else '✗ UNHEALTHY'} - {message}")
        
        # Test status
        status = mock_adapter.get_status()
        print(f"   Status keys: {list(status.keys())}")
        
        print("\n✓ Adapter components test completed!")
        
    except Exception as e:
        print(f"✗ Adapter test failed: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Run all direct tests."""
    print("Direct Data Import and Management Test")
    print("=" * 70)
    
    # Run individual component tests
    test_importers_direct()
    test_cache_direct()
    test_coverage_direct()
    test_adapters_direct()
    
    print("\n" + "=" * 70)
    print("DIRECT TESTS COMPLETED")
    print("=" * 70)


if __name__ == "__main__":
    main()