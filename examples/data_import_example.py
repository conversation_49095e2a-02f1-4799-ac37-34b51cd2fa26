#!/usr/bin/env python3
"""
Example demonstrating data import and management functionality.
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from src.data.manager import get_data_manager
from src.models.market_data import UnifiedMarketData


def create_sample_csv():
    """Create a sample CSV file for testing import functionality."""
    # Create sample data
    dates = pd.date_range(start='2023-01-01', end='2023-01-10', freq='D')
    data = []
    
    base_price = 100.0
    for i, date in enumerate(dates):
        price = base_price + (i * 0.5)
        data.append({
            'symbol': 'AAPL',
            'timestamp': date.strftime('%Y-%m-%d'),
            'open': price,
            'high': price * 1.02,
            'low': price * 0.98,
            'close': price * 1.01,
            'volume': 1000000 + (i * 10000),
            'adj_close': price * 1.01
        })
    
    df = pd.DataFrame(data)
    csv_path = Path('data/sample_market_data.csv')
    csv_path.parent.mkdir(exist_ok=True)
    df.to_csv(csv_path, index=False)
    
    print(f"Created sample CSV file: {csv_path}")
    return csv_path


def demonstrate_import_functionality():
    """Demonstrate data import functionality."""
    print("=== Data Import and Management Example ===\n")
    
    # Initialize data manager
    data_manager = get_data_manager(use_sqlite=True)
    
    # 1. Create sample CSV file
    print("1. Creating sample CSV file...")
    csv_path = create_sample_csv()
    
    # 2. Validate file
    print("\n2. Validating import file...")
    is_valid, message = data_manager.validate_import_file(csv_path)
    print(f"   Validation result: {is_valid}")
    print(f"   Message: {message}")
    
    if not is_valid:
        print("   File validation failed, exiting...")
        return
    
    # 3. Get import preview
    print("\n3. Getting import preview...")
    success, preview = data_manager.get_import_preview(csv_path, rows=3)
    if success:
        print("   Preview (first 3 rows):")
        print(preview.to_string(index=False))
    else:
        print(f"   Failed to get preview: {preview}")
    
    # 4. Import data from file
    print("\n4. Importing data from CSV file...")
    success, result = data_manager.import_from_file(
        csv_path,
        market='US',
        exchange='NASDAQ',
        currency='USD',
        save_to_db=True
    )
    
    if success:
        print(f"   Successfully imported {result} records")
    else:
        print(f"   Import failed: {result}")
        return
    
    # 5. Query imported data
    print("\n5. Querying imported data...")
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 1, 10)
    
    df = data_manager.get_market_data('AAPL', start_date, end_date, market='US')
    print(f"   Retrieved {len(df)} records from database")
    print("   Sample data:")
    print(df.head().to_string())
    
    # 6. Test DataFrame import
    print("\n6. Testing DataFrame import...")
    sample_df = pd.DataFrame([
        {
            'symbol': 'GOOGL',
            'timestamp': '2023-01-01',
            'open': 150.0,
            'high': 152.0,
            'low': 149.0,
            'close': 151.0,
            'volume': 2000000
        },
        {
            'symbol': 'GOOGL',
            'timestamp': '2023-01-02',
            'open': 151.0,
            'high': 153.0,
            'low': 150.0,
            'close': 152.0,
            'volume': 2100000
        }
    ])
    
    success, result = data_manager.import_from_dataframe(
        sample_df,
        market='US',
        exchange='NASDAQ',
        currency='USD',
        save_to_db=True
    )
    
    if success:
        print(f"   Successfully imported {result} records from DataFrame")
    else:
        print(f"   DataFrame import failed: {result}")
    
    # 7. Test data source functionality
    print("\n7. Testing data source functionality...")
    
    # Get available data sources
    sources = data_manager.get_available_data_sources()
    print(f"   Available data sources: {len(sources.get('adapters', {}))}")
    for name, info in sources.get('adapters', {}).items():
        print(f"   - {name}: {info.get('symbol_count', 0)} symbols")
    
    # Health check data sources
    health_status = data_manager.health_check_data_sources()
    print(f"   Data source health check:")
    for name, status in health_status.items():
        print(f"   - {name}: {'Healthy' if status['healthy'] else 'Unhealthy'}")
    
    # 8. Test caching functionality
    print("\n8. Testing cache functionality...")
    
    # Get cache stats
    cache_stats = data_manager.get_cache_stats()
    print(f"   Cache statistics:")
    print(f"   - Memory cache size: {cache_stats['memory_cache']['size']}")
    print(f"   - Memory cache hit rate: {cache_stats['memory_cache']['hit_rate']:.2%}")
    print(f"   - Disk cache entries: {cache_stats['disk_cache']['entries']}")
    
    # Test cached data retrieval
    print("   Testing cached data retrieval...")
    df_cached = data_manager.get_market_data('AAPL', start_date, end_date, market='US', use_cache=True)
    print(f"   Retrieved {len(df_cached)} records (potentially from cache)")
    
    # 9. Get data coverage summary
    print("\n9. Data coverage summary...")
    coverage = data_manager.get_data_coverage_summary()
    print(f"   Database coverage:")
    db_coverage = coverage.get('database', {})
    if 'market_data' in db_coverage:
        market_data = db_coverage['market_data']
        print(f"   - Total symbols: {market_data.get('total_symbols', 0)}")
        print(f"   - Total records: {market_data.get('total_records', 0)}")
    
    # 10. Get symbol-specific coverage
    print("\n10. Symbol-specific coverage...")
    symbol_coverage = data_manager.get_symbol_coverage_details('AAPL')
    print(f"   Coverage for AAPL:")
    for key, info in symbol_coverage.items():
        if info.get('available'):
            if 'start_date' in info:
                print(f"   - {key}: {info['start_date']} to {info['end_date']} ({info.get('days', 0)} days)")
            else:
                print(f"   - {key}: Available")
        else:
            print(f"   - {key}: Not available")
    
    # 11. Cleanup
    print("\n11. Cleanup...")
    cleanup_result = data_manager.cleanup_cache()
    print(f"   Cleaned up cache: {cleanup_result}")
    
    data_manager.close()
    print("\nExample completed successfully!")


if __name__ == '__main__':
    try:
        demonstrate_import_functionality()
    except Exception as e:
        print(f"Error running example: {e}")
        import traceback
        traceback.print_exc()