#!/usr/bin/env python3
"""
Simple test of data import functionality without full system dependencies.
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import tempfile
import shutil

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

# Test individual components
def test_importers():
    """Test the import functionality directly."""
    print("Testing Data Import Components")
    print("=" * 40)
    
    try:
        from data.importers import CSVImporter, ExcelImporter, DataImportManager
        from models.market_data import UnifiedMarketData
        
        # Create temporary directory
        temp_dir = tempfile.mkdtemp()
        print(f"Using temporary directory: {temp_dir}")
        
        try:
            # Test CSV Importer
            print("\n1. Testing CSV Importer...")
            csv_importer = CSVImporter()
            print(f"   Supported formats: {csv_importer.supported_formats}")
            
            # Create sample CSV data
            sample_data = []
            base_date = datetime(2023, 1, 1)
            
            for i in range(10):
                date = base_date + timedelta(days=i)
                sample_data.append({
                    'symbol': 'TEST',
                    'timestamp': date.strftime('%Y-%m-%d'),
                    'open': 100.0 + i,
                    'high': 102.0 + i,
                    'low': 98.0 + i,
                    'close': 101.0 + i,
                    'volume': 1000000 + i * 1000,
                    'adj_close': 101.0 + i
                })
            
            df = pd.DataFrame(sample_data)
            csv_path = Path(temp_dir) / 'test_data.csv'
            df.to_csv(csv_path, index=False)
            print(f"   Created test CSV: {csv_path}")
            
            # Test validation
            is_valid = csv_importer.validate_file(csv_path)
            print(f"   File validation: {'✓ PASS' if is_valid else '✗ FAIL'}")
            
            # Test preview
            preview = csv_importer.get_import_preview(csv_path, rows=3)
            print(f"   Preview rows: {len(preview)}")
            print(f"   Preview columns: {list(preview.columns)}")
            
            # Test import
            imported_data = csv_importer.import_data(csv_path, market='US', exchange='TEST', currency='USD')
            print(f"   Imported records: {len(imported_data)}")
            
            if imported_data:
                sample_record = imported_data[0]
                print(f"   Sample record: {sample_record.symbol} @ {sample_record.timestamp}")
                print(f"   Price: {sample_record.close}, Volume: {sample_record.volume}")
            
            # Test Excel Importer
            print("\n2. Testing Excel Importer...")
            excel_importer = ExcelImporter()
            print(f"   Supported formats: {excel_importer.supported_formats}")
            
            # Create sample Excel data
            excel_data = []
            for i in range(5):
                date = base_date + timedelta(days=i + 20)
                excel_data.append({
                    'symbol': 'EXCEL_TEST',
                    'timestamp': date.strftime('%Y-%m-%d'),
                    'open': 200.0 + i,
                    'high': 202.0 + i,
                    'low': 198.0 + i,
                    'close': 201.0 + i,
                    'volume': 2000000 + i * 1000
                })
            
            excel_df = pd.DataFrame(excel_data)
            excel_path = Path(temp_dir) / 'test_data.xlsx'
            excel_df.to_excel(excel_path, index=False)
            print(f"   Created test Excel: {excel_path}")
            
            # Test Excel validation and import
            is_valid = excel_importer.validate_file(excel_path)
            print(f"   File validation: {'✓ PASS' if is_valid else '✗ FAIL'}")
            
            if is_valid:
                excel_imported = excel_importer.import_data(excel_path, market='US', exchange='TEST', currency='USD')
                print(f"   Imported records: {len(excel_imported)}")
            
            # Test Import Manager
            print("\n3. Testing Import Manager...")
            import_manager = DataImportManager()
            
            supported_formats = import_manager.get_supported_formats()
            print(f"   All supported formats: {supported_formats}")
            
            # Test file type detection
            csv_type = import_manager.detect_file_type(csv_path)
            excel_type = import_manager.detect_file_type(excel_path)
            print(f"   CSV type detection: {csv_type}")
            print(f"   Excel type detection: {excel_type}")
            
            # Test unified import
            success, result = import_manager.import_file(csv_path, market='US', exchange='TEST', currency='USD')
            print(f"   Unified CSV import: {'✓ SUCCESS' if success else '✗ FAILED'}")
            if success:
                print(f"   Records imported: {len(result)}")
            else:
                print(f"   Error: {result}")
            
            # Test DataFrame import
            print("\n4. Testing DataFrame Import...")
            df_data = []
            for i in range(3):
                date = base_date + timedelta(days=i + 30)
                df_data.append({
                    'symbol': 'DF_TEST',
                    'timestamp': date,
                    'open': 300.0 + i,
                    'high': 302.0 + i,
                    'low': 298.0 + i,
                    'close': 301.0 + i,
                    'volume': 3000000 + i * 1000
                })
            
            test_df = pd.DataFrame(df_data)
            success, result = import_manager.import_from_dataframe(test_df, market='US', exchange='TEST', currency='USD')
            print(f"   DataFrame import: {'✓ SUCCESS' if success else '✗ FAILED'}")
            if success:
                print(f"   Records imported: {len(result)}")
            
            print("\n✓ All import tests completed successfully!")
            
        finally:
            # Clean up
            shutil.rmtree(temp_dir, ignore_errors=True)
            
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        import traceback
        traceback.print_exc()


def test_coverage_api():
    """Test the coverage API components."""
    print("\n" + "=" * 40)
    print("Testing Coverage API Components")
    print("=" * 40)
    
    try:
        from data.coverage_api import DateRange, DataCoverage
        
        # Test DateRange
        print("\n1. Testing DateRange...")
        start = datetime(2023, 1, 1)
        end = datetime(2023, 1, 31)
        date_range = DateRange(start=start, end=end, days=31)
        
        print(f"   Date range: {date_range.start} to {date_range.end}")
        print(f"   Days: {date_range.days}")
        
        range_dict = date_range.to_dict()
        print(f"   Dict keys: {list(range_dict.keys())}")
        
        # Test invalid range
        try:
            invalid_range = DateRange(start=end, end=start, days=0)
            print("   ✗ Should have failed with invalid range")
        except ValueError:
            print("   ✓ Correctly rejected invalid date range")
        
        print("\n✓ Coverage API components test completed!")
        
    except Exception as e:
        print(f"✗ Coverage API test failed: {e}")
        import traceback
        traceback.print_exc()


def test_cache_components():
    """Test cache components."""
    print("\n" + "=" * 40)
    print("Testing Cache Components")
    print("=" * 40)
    
    try:
        from data.cache import MemoryCache, CacheEntry
        
        # Test CacheEntry
        print("\n1. Testing CacheEntry...")
        test_data = {'test': 'data'}
        entry = CacheEntry(test_data, ttl_seconds=60)
        
        print(f"   Created entry with TTL: {entry.ttl_seconds}s")
        print(f"   Is expired: {entry.is_expired()}")
        print(f"   Age: {entry.get_age_seconds():.2f}s")
        
        # Access data
        accessed_data = entry.access()
        print(f"   Access count: {entry.access_count}")
        print(f"   Data matches: {accessed_data == test_data}")
        
        # Test MemoryCache
        print("\n2. Testing MemoryCache...")
        cache = MemoryCache(max_size=10, default_ttl=60)
        
        # Test put and get
        cache.put('test_key', test_data)
        retrieved = cache.get('test_key')
        print(f"   Cache put/get: {'✓ SUCCESS' if retrieved == test_data else '✗ FAILED'}")
        
        # Test cache miss
        missing = cache.get('nonexistent_key')
        print(f"   Cache miss: {'✓ SUCCESS' if missing is None else '✗ FAILED'}")
        
        # Test stats
        stats = cache.get_stats()
        print(f"   Cache stats: size={stats['size']}, hits={stats['hits']}, misses={stats['misses']}")
        
        print("\n✓ Cache components test completed!")
        
    except Exception as e:
        print(f"✗ Cache test failed: {e}")
        import traceback
        traceback.print_exc()


def test_data_models():
    """Test data models."""
    print("\n" + "=" * 40)
    print("Testing Data Models")
    print("=" * 40)
    
    try:
        from models.market_data import UnifiedMarketData, MarketInfo
        
        # Test UnifiedMarketData
        print("\n1. Testing UnifiedMarketData...")
        market_data = UnifiedMarketData(
            symbol='TEST',
            timestamp=datetime.now(),
            open=100.0,
            high=102.0,
            low=98.0,
            close=101.0,
            volume=1000000,
            market='US',
            exchange='TEST',
            currency='USD'
        )
        
        print(f"   Created market data: {market_data.symbol}")
        print(f"   Price: {market_data.close}")
        print(f"   Volume: {market_data.volume}")
        
        # Test validation
        is_valid = market_data.validate()
        print(f"   Validation: {'✓ PASS' if is_valid else '✗ FAIL'}")
        
        # Test MarketInfo
        print("\n2. Testing MarketInfo...")
        market_info = MarketInfo(
            symbol='TEST',
            name='Test Company',
            market='US',
            exchange='TEST',
            currency='USD',
            lot_size=1.0,
            tick_size=0.01,
            trading_hours={'open': '09:30', 'close': '16:00'},
            timezone='US/Eastern'
        )
        
        print(f"   Created market info: {market_info.name}")
        print(f"   Market: {market_info.market}")
        print(f"   Exchange: {market_info.exchange}")
        
        # Test validation
        is_valid = market_info.validate()
        print(f"   Validation: {'✓ PASS' if is_valid else '✗ FAIL'}")
        
        print("\n✓ Data models test completed!")
        
    except Exception as e:
        print(f"✗ Data models test failed: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Run all simple tests."""
    print("Simple Data Import and Management Test")
    print("=" * 60)
    
    # Run individual component tests
    test_data_models()
    test_importers()
    test_coverage_api()
    test_cache_components()
    
    print("\n" + "=" * 60)
    print("SIMPLE TESTS COMPLETED")
    print("=" * 60)


if __name__ == "__main__":
    main()