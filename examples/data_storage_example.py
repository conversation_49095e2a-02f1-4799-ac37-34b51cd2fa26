#!/usr/bin/env python3
"""
Example demonstrating the data storage and access layer functionality.
"""

import sys
import os
from datetime import datetime, timedelta

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data.manager import DataManager
from src.models.market_data import UnifiedMarketData, EconomicData, MarketInfo


def main():
    """Demonstrate data storage functionality."""
    print("=== Quantitative Trading System - Data Storage Example ===\n")
    
    # Initialize data manager with SQLite
    print("1. Initializing data manager...")
    data_manager = DataManager(use_sqlite=True)
    print("✓ Data manager initialized with SQLite backend\n")
    
    # Create sample market data
    print("2. Creating sample market data...")
    sample_data = [
        UnifiedMarketData(
            symbol="AAPL",
            timestamp=datetime(2023, 1, 1) + timedelta(days=i),
            open=150.0 + i,
            high=155.0 + i,
            low=149.0 + i,
            close=154.0 + i,
            volume=1000000 + i * 10000,
            market="US",
            exchange="NASDAQ",
            currency="USD",
            adj_close=154.0 + i
        ) for i in range(5)
    ]
    
    # Save market data in batch
    saved_count = data_manager.save_market_data_batch(sample_data)
    print(f"✓ Saved {saved_count} market data records\n")
    
    # Retrieve and display market data
    print("3. Retrieving market data...")
    start_date = datetime(2022, 12, 31)
    end_date = datetime(2023, 1, 10)
    
    df = data_manager.get_market_data("AAPL", start_date, end_date, "US")
    print(f"✓ Retrieved {len(df)} records for AAPL")
    print("Sample data:")
    print(df.head())
    print()
    
    # Create and save market info
    print("4. Creating market information...")
    market_info = MarketInfo(
        symbol="AAPL",
        name="Apple Inc.",
        market="US",
        exchange="NASDAQ",
        currency="USD",
        lot_size=1.0,
        tick_size=0.01,
        trading_hours={"open": "09:30", "close": "16:00"},
        timezone="America/New_York"
    )
    
    data_manager.save_market_info(market_info)
    print("✓ Market information saved\n")
    
    # Create and save economic data
    print("5. Creating economic data...")
    economic_data = EconomicData(
        series_id="GDP",
        timestamp=datetime(2023, 1, 1),
        value=25000.0,
        unit="Billions of Dollars",
        frequency="Quarterly",
        seasonal_adjustment="Seasonally Adjusted"
    )
    
    data_manager.save_economic_data(economic_data)
    print("✓ Economic data saved\n")
    
    # Get data coverage report
    print("6. Generating data coverage report...")
    coverage_report = data_manager.get_data_coverage_report()
    print("Coverage Report:")
    print(f"- Total symbols: {coverage_report['market_data']['total_symbols']}")
    print(f"- Total records: {coverage_report['market_data']['total_records']}")
    print(f"- Available markets: {coverage_report['markets']}")
    print()
    
    # Get data quality report
    print("7. Generating data quality report...")
    quality_report = data_manager.get_data_quality_report("AAPL", "US")
    print("Quality Report for AAPL:")
    print(f"- Date range: {quality_report['date_range']['start']} to {quality_report['date_range']['end']}")
    print(f"- Total records: {quality_report['records']['total']}")
    print(f"- Completeness: {quality_report['records']['completeness']:.2%}")
    print(f"- Zero volume records: {quality_report['price_anomalies']['zero_volume']}")
    print()
    
    # Get available symbols
    print("8. Getting available symbols...")
    symbols = data_manager.get_available_symbols("US")
    print(f"✓ Available US symbols: {symbols}")
    print()
    
    # Get latest data
    print("9. Getting latest data...")
    latest = data_manager.get_latest_market_data("AAPL", "US")
    if latest:
        print(f"✓ Latest AAPL data: {latest.timestamp} - Close: ${latest.close}")
    print()
    
    # Database statistics
    print("10. Database statistics...")
    stats = data_manager.get_database_stats()
    print("Database Statistics:")
    for table, table_stats in stats.items():
        if isinstance(table_stats, dict) and 'row_count' in table_stats:
            print(f"- {table}: {table_stats['row_count']} rows")
    print()
    
    # Cleanup
    print("11. Cleaning up...")
    data_manager.close()
    print("✓ Data manager closed\n")
    
    print("=== Example completed successfully! ===")


if __name__ == "__main__":
    main()