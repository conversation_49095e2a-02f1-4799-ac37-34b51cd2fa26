"""
宏观经济策略使用示例

演示如何使用宏观经济策略进行投资决策
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
import pandas as pd
import logging

from src.strategies.economic_base import EconomicContext
from src.strategies.examples.asset_allocation_strategy import AssetAllocationStrategy
from src.strategies.examples.economic_cycle_strategy import EconomicCycleStrategy
from src.strategies.examples.inflation_hedge_strategy import InflationHedgeStrategy
from src.strategies.economic_correlation import EconomicCorrelationAnalyzer
from src.models.market_data import MarketData, EconomicData

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockEconomicDataManager:
    """模拟经济数据管理器"""
    
    def get_economic_data(self, series_id, start_date, end_date):
        """模拟获取经济数据"""
        # 生成模拟数据
        date_range = pd.date_range(start_date, end_date, freq='M')
        
        # 根据不同指标生成不同的模拟数据
        if series_id == 'GDPC1':  # GDP
            base_value = 20000
            trend = 0.005  # 月增长率0.5%
        elif series_id == 'UNRATE':  # 失业率
            base_value = 5.0
            trend = -0.01  # 月下降0.01%
        elif series_id == 'CPIAUCSL':  # CPI
            base_value = 250
            trend = 0.002  # 月增长率0.2%
        elif series_id == 'FEDFUNDS':  # 联邦基金利率
            base_value = 2.5
            trend = 0.01  # 月增长0.01%
        else:
            base_value = 100
            trend = 0.001
        
        economic_data_list = []
        for i, date in enumerate(date_range):
            value = base_value * (1 + trend) ** i
            economic_data = EconomicData(
                series_id=series_id,
                timestamp=date.to_pydatetime(),
                value=value,
                market='ECONOMIC',
                source='MOCK'
            )
            economic_data_list.append(economic_data)
        
        return economic_data_list


def create_mock_context():
    """创建模拟的经济上下文"""
    mock_economic_data_manager = MockEconomicDataManager()
    
    context = EconomicContext(
        economic_data_manager=mock_economic_data_manager
    )
    
    return context


def demo_asset_allocation_strategy():
    """演示资产配置策略"""
    logger.info("=== 资产配置策略演示 ===")
    
    # 创建策略
    strategy = AssetAllocationStrategy(
        name="Demo Asset Allocation",
        parameters={
            'min_confidence': 0.6,
            'min_weight_change': 0.05
        }
    )
    
    # 创建上下文
    context = create_mock_context()
    strategy.initialize(context)
    
    # 创建模拟市场数据
    market_data = {
        'SPY': MarketData(
            symbol='SPY',
            timestamp=datetime.now(),
            open=400.0,
            high=405.0,
            low=395.0,
            close=402.0,
            volume=1000000
        ),
        'TLT': MarketData(
            symbol='TLT',
            timestamp=datetime.now(),
            open=100.0,
            high=102.0,
            low=99.0,
            close=101.0,
            volume=500000
        )
    }
    
    # 执行策略
    signals = strategy.on_data(market_data)
    
    logger.info(f"生成了 {len(signals)} 个交易信号:")
    for signal in signals:
        logger.info(f"  {signal.symbol}: {signal.signal_type.value} {signal.quantity} @ {signal.price}")
        logger.info(f"    置信度: {signal.confidence:.2f}")
        logger.info(f"    元数据: {signal.metadata}")


def demo_economic_cycle_strategy():
    """演示经济周期策略"""
    logger.info("\n=== 经济周期策略演示 ===")
    
    # 创建策略
    strategy = EconomicCycleStrategy(
        name="Demo Economic Cycle",
        parameters={
            'min_cycle_confidence': 0.6,
            'base_position_size': 100
        }
    )
    
    # 创建上下文
    context = create_mock_context()
    strategy.initialize(context)
    
    # 创建模拟市场数据
    market_data = MarketData(
        symbol='SPY',
        timestamp=datetime.now(),
        open=400.0,
        high=405.0,
        low=395.0,
        close=402.0,
        volume=1000000
    )
    
    # 执行策略
    signals = strategy.on_data(market_data)
    
    logger.info(f"生成了 {len(signals)} 个交易信号:")
    for signal in signals:
        logger.info(f"  {signal.symbol}: {signal.signal_type.value} {signal.quantity} @ {signal.price}")
        logger.info(f"    置信度: {signal.confidence:.2f}")
        logger.info(f"    周期阶段: {signal.metadata.get('cycle_phase')}")


def demo_inflation_hedge_strategy():
    """演示通胀对冲策略"""
    logger.info("\n=== 通胀对冲策略演示 ===")
    
    # 创建策略
    strategy = InflationHedgeStrategy(
        name="Demo Inflation Hedge",
        parameters={
            'portfolio_value': 100000.0,
            'min_trade_size': 10
        }
    )
    
    # 创建上下文
    context = create_mock_context()
    strategy.initialize(context)
    
    # 创建模拟市场数据
    market_data = {
        'GLD': MarketData(
            symbol='GLD',
            timestamp=datetime.now(),
            open=180.0,
            high=182.0,
            low=179.0,
            close=181.0,
            volume=2000000
        ),
        'SCHP': MarketData(
            symbol='SCHP',
            timestamp=datetime.now(),
            open=50.0,
            high=50.5,
            low=49.8,
            close=50.2,
            volume=1000000
        )
    }
    
    # 执行策略
    signals = strategy.on_data(market_data)
    
    logger.info(f"生成了 {len(signals)} 个交易信号:")
    for signal in signals:
        logger.info(f"  {signal.symbol}: {signal.signal_type.value} {signal.quantity} @ {signal.price}")
        logger.info(f"    置信度: {signal.confidence:.2f}")
        logger.info(f"    资产类别: {signal.metadata.get('asset_class')}")
        logger.info(f"    通胀环境: {signal.metadata.get('inflation_regime')}")


def demo_correlation_analysis():
    """演示相关性分析"""
    logger.info("\n=== 经济相关性分析演示 ===")
    
    # 创建上下文和分析器
    context = create_mock_context()
    analyzer = EconomicCorrelationAnalyzer(context)
    
    # 计算相关性矩阵
    market_symbols = ['SPY', 'TLT', 'GLD']
    economic_indicators = ['GDPC1', 'UNRATE', 'CPIAUCSL', 'FEDFUNDS']
    
    try:
        correlation_matrix = analyzer.calculate_correlation_matrix(
            market_symbols, economic_indicators, lookback_days=365
        )
        
        logger.info("相关性矩阵:")
        logger.info(f"\n{correlation_matrix}")
        
        # 寻找先行指标
        leading_indicators = analyzer.find_leading_indicators('SPY', lookback_days=365)
        
        logger.info(f"\nSPY的先行指标 (前3个):")
        for indicator in leading_indicators[:3]:
            logger.info(f"  {indicator['indicator']}: 相关性={indicator['correlation']:.3f}, "
                       f"滞后期={indicator['lag_months']}个月")
    
    except Exception as e:
        logger.error(f"相关性分析失败: {e}")


def demo_economic_analysis():
    """演示经济状况分析"""
    logger.info("\n=== 经济状况分析演示 ===")
    
    # 创建上下文
    context = create_mock_context()
    
    # 分析经济周期
    cycle_phase = context.get_economic_cycle_phase()
    logger.info(f"当前经济周期阶段: {cycle_phase}")
    
    # 分析经济趋势
    gdp_trend = context.get_economic_trend('GDPC1', 90)
    unemployment_trend = context.get_economic_trend('UNRATE', 90)
    inflation_trend = context.get_economic_trend('CPIAUCSL', 90)
    
    logger.info(f"GDP趋势: {gdp_trend}")
    logger.info(f"失业率趋势: {unemployment_trend}")
    logger.info(f"通胀趋势: {inflation_trend}")
    
    # 分析指标相关性
    gdp_unemployment_corr = context.get_economic_indicator_correlation('GDPC1', 'UNRATE', 365)
    logger.info(f"GDP与失业率相关性: {gdp_unemployment_corr:.3f}")


def main():
    """主函数"""
    logger.info("宏观经济策略系统演示开始")
    
    try:
        # 演示各种策略
        demo_asset_allocation_strategy()
        demo_economic_cycle_strategy()
        demo_inflation_hedge_strategy()
        demo_correlation_analysis()
        demo_economic_analysis()
        
        logger.info("\n=== 演示完成 ===")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()