#!/usr/bin/env python3
"""
Example usage of TushareAdapter for A-share market data.

This example demonstrates how to use the TushareAdapter to fetch Chinese A-share
market data including stocks and indices.

Note: You need a valid Tushare Pro token to run this example.
Get your token from: https://tushare.pro/register
"""

import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from data.adapters.base import DataSourceConfig
from data.adapters.tushare_adapter import TushareAdapter


def main():
    """Main example function."""
    print("=== Tushare Adapter Example ===\n")
    
    # Configuration
    # Note: In production, store the token in environment variables or config files
    token = os.getenv('TUSHARE_TOKEN', 'your_tushare_token_here')
    
    if token == 'your_tushare_token_here':
        print("⚠️  Please set your Tushare token:")
        print("   export TUSHARE_TOKEN='your_actual_token'")
        print("   Or modify the token variable in this script")
        print("\n   Get your token from: https://tushare.pro/register")
        return
    
    config = DataSourceConfig(
        name='tushare_example',
        adapter_class='src.data.adapters.tushare_adapter.TushareAdapter',
        enabled=True,
        priority=1,
        rate_limit={'requests_per_minute': 200},
        credentials={'token': token},
        default_params={'adj': 'qfq'}
    )
    
    try:
        # Initialize adapter
        print("1. Initializing Tushare adapter...")
        adapter = TushareAdapter(config)
        print("✅ Adapter initialized successfully")
        
        # Health check
        print("\n2. Performing health check...")
        is_healthy, message = adapter.health_check()
        if is_healthy:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
            return
        
        # Get available symbols (sample)
        print("\n3. Getting available symbols...")
        symbols = adapter.get_available_symbols()
        print(f"✅ Found {len(symbols)} symbols")
        print(f"   Sample symbols: {symbols[:10]}")
        
        # Test with a popular A-share stock (平安银行)
        test_symbol = '000001.SZ'
        print(f"\n4. Testing with symbol: {test_symbol}")
        
        # Validate symbol
        is_valid = adapter.validate_symbol(test_symbol)
        print(f"   Symbol validation: {'✅ Valid' if is_valid else '❌ Invalid'}")
        
        if not is_valid:
            print("   Skipping data fetch for invalid symbol")
            return
        
        # Get market info
        print(f"\n5. Getting market info for {test_symbol}...")
        market_info = adapter.get_market_info(test_symbol)
        if market_info:
            print(f"   Name: {market_info.name}")
            print(f"   Exchange: {market_info.exchange}")
            print(f"   Currency: {market_info.currency}")
            print(f"   Lot size: {market_info.lot_size}")
            print(f"   Tick size: {market_info.tick_size}")
        
        # Fetch historical data
        print(f"\n6. Fetching historical data for {test_symbol}...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)  # Last 30 days
        
        data = adapter.fetch_historical_data(test_symbol, start_date, end_date)
        print(f"✅ Fetched {len(data)} records")
        
        if data:
            print("   Sample data (first 3 records):")
            for i, record in enumerate(data[:3]):
                print(f"   {i+1}. {record.timestamp.strftime('%Y-%m-%d')}: "
                      f"Open={record.open:.2f}, High={record.high:.2f}, "
                      f"Low={record.low:.2f}, Close={record.close:.2f}, "
                      f"Volume={record.volume:,.0f}")
        
        # Test with major index (上证指数)
        index_symbol = '000001.SH'
        print(f"\n7. Testing with index: {index_symbol}")
        
        market_info = adapter.get_market_info(index_symbol)
        if market_info:
            print(f"   Index name: {market_info.name}")
        
        index_data = adapter.fetch_historical_data(index_symbol, start_date, end_date)
        print(f"✅ Fetched {len(index_data)} index records")
        
        if index_data:
            latest = index_data[-1]
            print(f"   Latest: {latest.timestamp.strftime('%Y-%m-%d')}: "
                  f"Close={latest.close:.2f}")
        
        # Get trading calendar
        print(f"\n8. Getting trading calendar...")
        calendar_start = datetime.now() - timedelta(days=10)
        calendar_end = datetime.now()
        trading_days = adapter.get_trading_calendar(calendar_start, calendar_end)
        print(f"✅ Found {len(trading_days)} trading days in the last 10 days")
        
        # Search stocks
        print(f"\n9. Searching stocks with keyword '银行'...")
        search_results = adapter.search_stocks('银行', limit=5)
        print(f"✅ Found {len(search_results)} matching stocks:")
        for result in search_results:
            print(f"   {result['symbol']}: {result['name']} ({result['exchange']})")
        
        # Get stocks by exchange
        print(f"\n10. Getting stocks from SZSE...")
        szse_stocks = adapter.get_stock_list_by_exchange('SZSE')
        print(f"✅ Found {len(szse_stocks)} stocks on SZSE")
        print(f"    Sample: {szse_stocks[:5]}")
        
        print(f"\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()