"""
Analysis and Reporting Interface Demo

This script demonstrates how to use the analysis and reporting interface
to generate performance metrics, charts, comparisons, and reports.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

from src.ui.analysis_reporting.main_interface import AnalysisReportingInterface
from src.analysis.reports import ReportFormat
from src.models.trading import Trade, OrderSide
from src.ui.charts.chart_config import ChartConfig
from src.analysis.reports import ReportConfig


def generate_sample_data():
    """Generate sample portfolio and trade data for demonstration."""
    
    # Generate sample portfolio values
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    
    # Strategy 1: Trending upward with some volatility
    np.random.seed(42)
    returns1 = np.random.normal(0.0008, 0.02, len(dates))  # Daily returns
    portfolio1 = pd.Series(10000 * (1 + returns1).cumprod(), index=dates)
    
    # Strategy 2: More volatile but higher returns
    np.random.seed(123)
    returns2 = np.random.normal(0.001, 0.025, len(dates))
    portfolio2 = pd.Series(10000 * (1 + returns2).cumprod(), index=dates)
    
    # Strategy 3: Lower volatility, steady growth
    np.random.seed(456)
    returns3 = np.random.normal(0.0005, 0.015, len(dates))
    portfolio3 = pd.Series(10000 * (1 + returns3).cumprod(), index=dates)
    
    # Generate benchmark (market index)
    np.random.seed(789)
    benchmark_returns = np.random.normal(0.0006, 0.018, len(dates))
    benchmark = pd.Series(10000 * (1 + benchmark_returns).cumprod(), index=dates)
    
    # Generate sample trades for Strategy 1
    trades1 = []
    trade_dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='W')
    
    for i, date in enumerate(trade_dates):
        # Alternate between buy and sell
        side = OrderSide.BUY if i % 2 == 0 else OrderSide.SELL
        quantity = np.random.uniform(10, 100)
        price = np.random.uniform(50, 150)
        
        trade = Trade(
            id=f"trade_{i+1}",
            symbol="AAPL",
            side=side,
            quantity=quantity,
            price=price,
            timestamp=date,
            commission=quantity * price * 0.001,  # 0.1% commission
            strategy_id="strategy_1"
        )
        
        # Add P&L (simplified calculation)
        if side == OrderSide.SELL and i > 0:
            trade.pnl = np.random.normal(50, 200)  # Random P&L
        else:
            trade.pnl = 0
        
        trades1.append(trade)
    
    return {
        'strategy_1': {
            'portfolio_values': portfolio1,
            'trades': trades1,
            'benchmark_values': benchmark
        },
        'strategy_2': {
            'portfolio_values': portfolio2,
            'trades': None,
            'benchmark_values': benchmark
        },
        'strategy_3': {
            'portfolio_values': portfolio3,
            'trades': None,
            'benchmark_values': benchmark
        }
    }


def demo_performance_analysis():
    """Demonstrate performance analysis functionality."""
    print("=== Performance Analysis Demo ===")
    
    # Initialize interface
    chart_config = ChartConfig()
    report_config = ReportConfig(
        title="Demo Performance Analysis",
        include_charts=True,
        include_monthly_returns=True
    )
    
    interface = AnalysisReportingInterface(chart_config, report_config)
    
    # Generate sample data
    sample_data = generate_sample_data()
    
    # Add strategy data
    for strategy_id, data in sample_data.items():
        interface.add_strategy_data(
            strategy_id=strategy_id,
            portfolio_values=data['portfolio_values'],
            trades=data['trades'],
            benchmark_values=data['benchmark_values'],
            metadata={'description': f'Sample {strategy_id}'}
        )
    
    print(f"Added {len(sample_data)} strategies to analysis")
    
    # Calculate performance metrics for each strategy
    for strategy_id in sample_data.keys():
        print(f"\nCalculating metrics for {strategy_id}...")
        metrics = interface.calculate_performance_metrics(strategy_id)
        
        if metrics:
            print(f"  Total Return: {metrics.total_return:.2%}")
            print(f"  Sharpe Ratio: {metrics.sharpe_ratio:.3f}")
            print(f"  Max Drawdown: {metrics.max_drawdown:.2%}")
            print(f"  Volatility: {metrics.volatility:.2%}")
    
    # Generate performance charts
    print(f"\nGenerating charts for strategy_1...")
    charts = interface.generate_performance_charts('strategy_1')
    
    if charts and 'error' not in charts:
        print(f"  Generated {len(charts)} chart types:")
        for chart_name in charts.keys():
            print(f"    - {chart_name}")
    
    return interface


def demo_strategy_comparison(interface):
    """Demonstrate strategy comparison functionality."""
    print("\n=== Strategy Comparison Demo ===")
    
    # Compare all strategies
    comparison_result = interface.compare_strategies()
    
    if comparison_result:
        print(f"Compared {len(comparison_result.strategy_metrics)} strategies")
        
        # Show rankings
        if comparison_result.rankings and 'overall' in comparison_result.rankings:
            print("\nOverall Rankings:")
            for i, strategy in enumerate(comparison_result.rankings['overall'], 1):
                print(f"  {i}. {strategy}")
        
        # Show correlation analysis
        if not comparison_result.correlation_matrix.empty:
            print(f"\nCorrelation Matrix:")
            print(comparison_result.correlation_matrix.round(3))
        
        # Show recommendations
        if comparison_result.recommendations:
            print(f"\nRecommendations ({len(comparison_result.recommendations)}):")
            for rec in comparison_result.recommendations:
                print(f"  - {rec['type']}: {rec['reason']}")
    
    return comparison_result


def demo_report_generation(interface):
    """Demonstrate report generation functionality."""
    print("\n=== Report Generation Demo ===")
    
    # Generate individual strategy report
    print("Generating performance report for strategy_1...")
    
    performance_report = interface.generate_performance_report(
        strategy_id='strategy_1',
        format=ReportFormat.JSON,
        include_charts=True
    )
    
    if performance_report and 'error' not in performance_report:
        print("  Performance report generated successfully")
        print(f"  Report contains {len(performance_report)} sections")
        
        # Show summary metrics
        if 'summary' in performance_report:
            summary = performance_report['summary']
            print(f"  Period: {summary.get('period', {}).get('start_date')} to {summary.get('period', {}).get('end_date')}")
            
            key_metrics = summary.get('key_metrics', {})
            for metric, value in key_metrics.items():
                print(f"    {metric}: {value}")
    
    # Generate comparison report
    print("\nGenerating comparison report...")
    
    comparison_report = interface.generate_comparison_report(
        format=ReportFormat.HTML
    )
    
    if comparison_report and 'error' not in comparison_report:
        print("  Comparison report generated successfully")
        print(f"  Report type: {comparison_report.get('report_type', 'unknown')}")
    
    # Export reports
    print("\nExporting reports...")
    
    # Get available reports
    available_reports = interface.get_available_reports()
    print(f"  Available reports: {len(available_reports)}")
    
    # Export first report as example
    if available_reports:
        report_key = available_reports[0]
        export_path = f"demo_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        export_result = interface.export_report(report_key, export_path)
        
        if export_result and 'error' not in export_result:
            print(f"  Report exported to: {export_path}")
            print(f"  File size: {export_result.get('file_size', 0)} bytes")


def demo_dashboard_data(interface):
    """Demonstrate dashboard data functionality."""
    print("\n=== Dashboard Data Demo ===")
    
    dashboard_data = interface.get_dashboard_data()
    
    if dashboard_data and 'error' not in dashboard_data:
        print("Dashboard Summary:")
        summary = dashboard_data.get('summary', {})
        print(f"  Total Strategies: {summary.get('total_strategies', 0)}")
        print(f"  Total Reports: {summary.get('total_reports', 0)}")
        print(f"  Last Updated: {summary.get('last_updated', 'Unknown')}")
        
        # Show strategy summaries
        strategies = dashboard_data.get('strategies', {})
        if strategies:
            print(f"\nStrategy Summaries:")
            for strategy_id, strategy_data in strategies.items():
                print(f"  {strategy_id}:")
                print(f"    Total Return: {strategy_data.get('total_return', 0)}%")
                print(f"    Sharpe Ratio: {strategy_data.get('sharpe_ratio', 0)}")
                print(f"    Max Drawdown: {strategy_data.get('max_drawdown', 0)}%")
        
        # Show comparison data
        comparison = dashboard_data.get('comparison')
        if comparison:
            print(f"\nComparison Summary:")
            print(f"  Strategies Compared: {comparison.get('strategies_compared', 0)}")
            print(f"  Top Strategy: {comparison.get('top_strategy', 'Unknown')}")
            print(f"  Recommendations: {comparison.get('recommendations_count', 0)}")


def main():
    """Main demo function."""
    print("Analysis and Reporting Interface Demo")
    print("=" * 50)
    
    try:
        # Run performance analysis demo
        interface = demo_performance_analysis()
        
        # Run strategy comparison demo
        comparison_result = demo_strategy_comparison(interface)
        
        # Run report generation demo
        demo_report_generation(interface)
        
        # Run dashboard data demo
        demo_dashboard_data(interface)
        
        print("\n" + "=" * 50)
        print("Demo completed successfully!")
        
        # Show final statistics
        print(f"\nFinal Statistics:")
        print(f"  Strategies analyzed: {len(interface.get_available_strategies())}")
        print(f"  Reports generated: {len(interface.get_available_reports())}")
        
    except Exception as e:
        print(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()