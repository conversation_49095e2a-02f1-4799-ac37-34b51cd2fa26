#!/usr/bin/env python3
"""
Example usage of Yahoo Finance data adapter.

This script demonstrates how to use the Yahoo Finance adapter to:
1. Fetch historical stock data
2. Get market information
3. Retrieve dividends and splits
4. Get real-time quotes
5. Search for symbols
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.data.adapters.yahoo_finance import YahooFinanceAdapter
from src.data.adapters.base import DataSourceConfig


def create_yahoo_adapter():
    """Create and configure Yahoo Finance adapter."""
    config = DataSourceConfig(
        name='yahoo_finance_example',
        adapter_class='src.data.adapters.yahoo_finance.YahooFinanceAdapter',
        enabled=True,
        priority=1,
        rate_limit={
            'requests_per_minute': 60,
            'requests_per_hour': 2000
        },
        credentials={},
        default_params={
            'interval': '1d',
            'auto_adjust': False,
            'prepost': False
        }
    )
    
    return YahooFinanceAdapter(config)


def demonstrate_historical_data(adapter):
    """Demonstrate historical data fetching."""
    print("\n" + "="*60)
    print("HISTORICAL DATA EXAMPLE")
    print("="*60)
    
    symbol = 'AAPL'
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    print(f"Fetching 30 days of data for {symbol}...")
    print(f"Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    
    try:
        data = adapter.fetch_historical_data(symbol, start_date, end_date)
        
        if data:
            print(f"\nSuccessfully fetched {len(data)} records")
            print("\nFirst 5 records:")
            print("-" * 80)
            print(f"{'Date':<12} {'Open':<8} {'High':<8} {'Low':<8} {'Close':<8} {'Volume':<12}")
            print("-" * 80)
            
            for i, record in enumerate(data[:5]):
                print(f"{record.timestamp.strftime('%Y-%m-%d'):<12} "
                      f"{record.open:<8.2f} {record.high:<8.2f} {record.low:<8.2f} "
                      f"{record.close:<8.2f} {record.volume:<12,.0f}")
            
            if len(data) > 5:
                print(f"... and {len(data) - 5} more records")
        else:
            print("No data returned (possibly weekend/holiday)")
            
    except Exception as e:
        print(f"Error fetching data: {e}")


def demonstrate_market_info(adapter):
    """Demonstrate market information retrieval."""
    print("\n" + "="*60)
    print("MARKET INFORMATION EXAMPLE")
    print("="*60)
    
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN']
    
    for symbol in symbols:
        try:
            market_info = adapter.get_market_info(symbol)
            
            if market_info:
                print(f"\n{symbol}:")
                print(f"  Name: {market_info.name}")
                print(f"  Exchange: {market_info.exchange}")
                print(f"  Currency: {market_info.currency}")
                print(f"  Market: {market_info.market}")
                print(f"  Trading Hours: {market_info.trading_hours}")
                print(f"  Active: {market_info.is_active}")
            else:
                print(f"\n{symbol}: No market info available")
                
        except Exception as e:
            print(f"\n{symbol}: Error - {e}")


def demonstrate_dividends_and_splits(adapter):
    """Demonstrate dividend and split data retrieval."""
    print("\n" + "="*60)
    print("DIVIDENDS AND SPLITS EXAMPLE")
    print("="*60)
    
    symbol = 'AAPL'
    end_date = datetime.now()
    start_date = datetime(end_date.year - 2, 1, 1)  # Last 2 years
    
    print(f"Fetching dividends and splits for {symbol} since {start_date.year}...")
    
    try:
        # Fetch dividends
        dividends = adapter.fetch_dividends(symbol, start_date, end_date)
        
        if not dividends.empty:
            print(f"\nDividends ({len(dividends)} records):")
            print("-" * 40)
            for _, row in dividends.iterrows():
                print(f"{row['date'].strftime('%Y-%m-%d')}: ${row['dividend']:.4f}")
        else:
            print("\nNo dividend data found")
        
        # Fetch splits
        splits = adapter.fetch_splits(symbol, start_date, end_date)
        
        if not splits.empty:
            print(f"\nStock Splits ({len(splits)} records):")
            print("-" * 40)
            for _, row in splits.iterrows():
                print(f"{row['date'].strftime('%Y-%m-%d')}: {row['split_ratio']:.1f}:1")
        else:
            print("\nNo stock split data found")
            
    except Exception as e:
        print(f"Error fetching corporate actions: {e}")


def demonstrate_real_time_quote(adapter):
    """Demonstrate real-time quote retrieval."""
    print("\n" + "="*60)
    print("REAL-TIME QUOTE EXAMPLE")
    print("="*60)
    
    symbols = ['AAPL', 'MSFT', 'GOOGL']
    
    print("Fetching real-time quotes...")
    print("-" * 80)
    print(f"{'Symbol':<8} {'Price':<10} {'Change':<10} {'Change%':<10} {'Volume':<12}")
    print("-" * 80)
    
    for symbol in symbols:
        try:
            quote = adapter.get_real_time_quote(symbol)
            
            if quote:
                price = quote.get('price', 0)
                change = quote.get('change', 0)
                change_pct = quote.get('change_percent', 0)
                volume = quote.get('volume', 0)
                
                print(f"{symbol:<8} ${price:<9.2f} ${change:<9.2f} {change_pct:<9.2f}% {volume:<12,.0f}")
            else:
                print(f"{symbol:<8} No quote available")
                
        except Exception as e:
            print(f"{symbol:<8} Error: {e}")


def demonstrate_symbol_search(adapter):
    """Demonstrate symbol search functionality."""
    print("\n" + "="*60)
    print("SYMBOL SEARCH EXAMPLE")
    print("="*60)
    
    search_queries = ['APPL', 'MICRO', 'GOOG']
    
    for query in search_queries:
        print(f"\nSearching for '{query}':")
        try:
            results = adapter.search_symbols(query, limit=5)
            
            if results:
                for result in results:
                    print(f"  {result['symbol']}: {result['name']} ({result['exchange']})")
            else:
                print("  No results found")
                
        except Exception as e:
            print(f"  Error: {e}")


def demonstrate_market_status(adapter):
    """Demonstrate market status checking."""
    print("\n" + "="*60)
    print("MARKET STATUS EXAMPLE")
    print("="*60)
    
    now = datetime.now()
    is_open = adapter.is_market_open(now)
    
    print(f"Current time: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"US Market is: {'OPEN' if is_open else 'CLOSED'}")
    
    # Check market hours
    market_hours = adapter.get_market_hours()
    print(f"Market hours: {market_hours['open']} - {market_hours['close']} {market_hours['timezone']}")
    
    # Check supported intervals
    intervals = adapter.get_supported_intervals()
    print(f"Supported intervals: {', '.join(intervals)}")


def demonstrate_health_check(adapter):
    """Demonstrate adapter health check."""
    print("\n" + "="*60)
    print("HEALTH CHECK EXAMPLE")
    print("="*60)
    
    print("Performing health check...")
    
    try:
        is_healthy, message = adapter.health_check()
        
        print(f"Health Status: {'HEALTHY' if is_healthy else 'UNHEALTHY'}")
        print(f"Message: {message}")
        
        # Get performance stats
        stats = adapter.get_performance_stats()
        print(f"\nPerformance Stats:")
        print(f"  Total requests: {stats['total_requests']}")
        print(f"  Requests last hour: {stats['requests_last_hour']}")
        print(f"  Can make request: {stats['can_make_request']}")
        print(f"  Wait time: {stats['wait_time']:.2f} seconds")
        
    except Exception as e:
        print(f"Health check failed: {e}")


def main():
    """Main demonstration function."""
    print("Yahoo Finance Adapter Example")
    print("=" * 60)
    
    # Create adapter
    print("Initializing Yahoo Finance adapter...")
    adapter = create_yahoo_adapter()
    
    # Run demonstrations
    try:
        demonstrate_historical_data(adapter)
        demonstrate_market_info(adapter)
        demonstrate_dividends_and_splits(adapter)
        demonstrate_real_time_quote(adapter)
        demonstrate_symbol_search(adapter)
        demonstrate_market_status(adapter)
        demonstrate_health_check(adapter)
        
    except KeyboardInterrupt:
        print("\n\nExample interrupted by user")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
    
    print("\n" + "="*60)
    print("Example completed!")
    print("="*60)


if __name__ == '__main__':
    main()