#!/usr/bin/env python3
"""
FRED adapter usage example.

This example demonstrates how to use the FRED adapter to fetch US economic data.
Note: You need a FRED API key to run this example. Get one free at:
https://fred.stlouisfed.org/docs/api/api_key.html
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.adapters.fred_adapter import FREDAdapter
from src.data.adapters.base import DataSourceConfig


def main():
    """Main example function."""
    print("=== FRED Adapter Example ===\n")
    
    # Get API key from environment variable
    api_key = os.getenv('FRED_API_KEY')
    if not api_key:
        print("❌ Error: FRED_API_KEY environment variable not set")
        print("Please get a free API key from https://fred.stlouisfed.org/docs/api/api_key.html")
        print("Then set it with: export FRED_API_KEY=your_api_key_here")
        return
    
    # Create configuration
    config = DataSourceConfig(
        name='fred_example',
        adapter_class='src.data.adapters.fred_adapter.FREDAdapter',
        enabled=True,
        priority=1,
        rate_limit={
            'requests_per_minute': 120,
            'requests_per_day': 100000
        },
        credentials={
            'api_key': api_key
        },
        default_params={}
    )
    
    try:
        # Initialize adapter
        print("1. Initializing FRED adapter...")
        adapter = FREDAdapter(config)
        print(f"   ✓ Adapter initialized successfully")
        print(f"   Market: {adapter.get_market_type()}")
        print(f"   Exchange: {adapter.get_exchange_name()}")
        print(f"   Default Currency: {adapter.get_default_currency()}")
        print()
        
        # Health check
        print("2. Performing health check...")
        is_healthy, message = adapter.health_check()
        print(f"   Health Status: {'✓ Healthy' if is_healthy else '✗ Unhealthy'}")
        print(f"   Message: {message}")
        print()
        
        # Get supported intervals
        print("3. Supported time intervals:")
        intervals = adapter.get_supported_intervals()
        interval_names = {
            'd': 'Daily', 'w': 'Weekly', 'm': 'Monthly', 
            'q': 'Quarterly', 'a': 'Annual'
        }
        for interval in intervals:
            print(f"   {interval} - {interval_names.get(interval, interval)}")
        print()
        
        # Get popular series
        print("4. Popular economic indicators (first 15):")
        popular_series = adapter.get_popular_series()[:15]
        for i, series_id in enumerate(popular_series, 1):
            series_info = adapter.get_series_info(series_id)
            title = series_info.get('title', series_id) if series_info else series_id
            print(f"   {i:2d}. {series_id}: {title}")
        print(f"   ... and {len(adapter.get_popular_series()) - 15} more")
        print()
        
        # Get categories
        print("5. Economic data categories:")
        categories = adapter.get_categories()
        for i, (category, series_list) in enumerate(categories.items(), 1):
            print(f"   {i:2d}. {category} ({len(series_list)} indicators)")
        print()
        
        # Get series info for key indicators
        print("6. Key economic indicators information:")
        key_indicators = ['GDP', 'UNRATE', 'CPIAUCSL', 'FEDFUNDS']
        for indicator in key_indicators:
            series_info = adapter.get_series_info(indicator)
            if series_info:
                print(f"   {indicator}:")
                print(f"     Title: {series_info.get('title', 'N/A')}")
                print(f"     Units: {series_info.get('units', 'N/A')}")
                print(f"     Frequency: {series_info.get('frequency', 'N/A')}")
                print(f"     Seasonal Adj: {series_info.get('seasonal_adjustment', 'N/A')}")
            else:
                print(f"   {indicator}: No info available")
        print()
        
        # Fetch historical data for GDP
        print("7. Fetching historical GDP data...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365*2)  # Last 2 years
        
        print(f"   Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        print(f"   Series: GDP (Gross Domestic Product)")
        
        gdp_data = adapter.fetch_historical_data('GDP', start_date, end_date)
        
        if gdp_data:
            print(f"   ✓ Fetched {len(gdp_data)} records")
            print("   Recent GDP data (last 5 quarters):")
            for i, record in enumerate(gdp_data[-5:], 1):
                print(f"     {i}. {record.timestamp.strftime('%Y-%m-%d')}:")
                print(f"        Value: ${record.value:,.1f} {record.unit}")
                print(f"        Frequency: {record.frequency}")
        else:
            print("   ✗ No data retrieved")
        print()
        
        # Get latest values for key indicators
        print("8. Latest values for key indicators:")
        key_indicators = ['GDP', 'UNRATE', 'CPIAUCSL', 'FEDFUNDS']
        for indicator in key_indicators:
            latest = adapter.get_latest_value(indicator)
            if latest:
                print(f"   {indicator}:")
                print(f"     Latest Value: {latest['value']:.2f} {latest['units']}")
                print(f"     Date: {latest['date'].strftime('%Y-%m-%d')}")
                print(f"     Title: {latest['title']}")
            else:
                print(f"   {indicator}: No latest data available")
        print()
        
        # Search for inflation-related series
        print("9. Searching for inflation-related series:")
        search_results = adapter.search_series('inflation', limit=5)
        for i, result in enumerate(search_results, 1):
            print(f"   {i}. {result['id']}: {result['title']}")
            print(f"      Units: {result['units']}, Frequency: {result['frequency']}")
        print()
        
        # Fetch multiple series
        print("10. Fetching multiple economic indicators...")
        multi_series = ['UNRATE', 'CPIAUCSL', 'FEDFUNDS']
        start_date_multi = datetime.now() - timedelta(days=365)  # Last year
        
        multi_data = adapter.fetch_multiple_series(multi_series, start_date_multi, end_date)
        
        print(f"    Fetched data for {len(multi_data)} series:")
        for series_id, data in multi_data.items():
            print(f"     {series_id}: {len(data)} data points")
            if data:
                latest_point = data[-1]
                print(f"       Latest: {latest_point.value:.2f} on {latest_point.timestamp.strftime('%Y-%m-%d')}")
        print()
        
        # Get series by category
        print("11. Employment indicators:")
        employment_series = adapter.get_series_by_category('Employment and Labor')
        for series_id in employment_series[:5]:  # First 5
            series_info = adapter.get_series_info(series_id)
            title = series_info.get('title', series_id) if series_info else series_id
            print(f"    {series_id}: {title}")
        print()
        
        # Performance stats
        print("12. Adapter performance statistics:")
        stats = adapter.get_performance_stats()
        print(f"    Total Requests: {stats['total_requests']}")
        print(f"    Requests (Last Hour): {stats['requests_last_hour']}")
        print(f"    Requests (Last Day): {stats['requests_last_day']}")
        print(f"    Can Make Request: {stats['can_make_request']}")
        print(f"    Wait Time: {stats['wait_time']:.2f} seconds")
        print()
        
        print("=== Example completed successfully! ===")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()