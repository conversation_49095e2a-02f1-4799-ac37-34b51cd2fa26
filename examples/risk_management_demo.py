"""
Risk Management System Demonstration.

This example shows how to use the comprehensive risk management system
to control trading risks, monitor portfolio health, and enforce risk limits.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timed<PERSON><PERSON>
from src.risk.manager import RiskManager
from src.risk.models import RiskConfig, RiskLevel
from src.models.portfolio import Portfolio, Position
from src.models.trading import Order, OrderSide, OrderType


def risk_alert_callback(violation):
    """Callback function for risk alerts."""
    print(f"🚨 RISK ALERT: {violation.rule_name}")
    print(f"   Type: {violation.violation_type}")
    print(f"   Severity: {violation.severity.value}")
    print(f"   Current: {violation.current_value:.4f}")
    print(f"   Limit: {violation.limit_value:.4f}")
    if violation.symbol:
        print(f"   Symbol: {violation.symbol}")
    print()


def main():
    """Demonstrate risk management system."""
    print("=== Risk Management System Demo ===\n")
    
    # 1. Create risk configuration
    print("1. Setting up risk configuration...")
    config = RiskConfig(
        max_position_size_pct=0.15,      # 15% max position size
        max_portfolio_risk_pct=0.03,     # 3% max risk per trade
        default_stop_loss_pct=0.05,      # 5% stop loss
        max_drawdown_pct=0.20,           # 20% max drawdown
        daily_risk_budget=0.05,          # 5% daily risk budget
        max_leverage=1.5                 # 1.5:1 max leverage
    )
    print(f"   Max position size: {config.max_position_size_pct:.1%}")
    print(f"   Max drawdown: {config.max_drawdown_pct:.1%}")
    print(f"   Daily risk budget: {config.daily_risk_budget:.1%}")
    print()
    
    # 2. Initialize risk manager
    print("2. Initializing risk manager...")
    risk_manager = RiskManager(config)
    risk_manager.add_alert_callback(risk_alert_callback)
    print(f"   Rules initialized: {len(risk_manager.rules)}")
    print(f"   Monitors initialized: {len(risk_manager.monitors)}")
    print()
    
    # 3. Create sample portfolio
    print("3. Creating sample portfolio...")
    portfolio = Portfolio(initial_capital=100000, cash=60000)
    
    # Add some positions
    portfolio.positions['AAPL'] = Position(
        symbol='AAPL',
        quantity=200,
        avg_price=150.0,
        market_value=30000
    )
    
    portfolio.positions['MSFT'] = Position(
        symbol='MSFT',
        quantity=50,
        avg_price=200.0,
        market_value=10000
    )
    
    portfolio.total_value = 100000  # 60k cash + 40k positions
    
    print(f"   Portfolio value: ${portfolio.total_value:,.2f}")
    print(f"   Cash: ${portfolio.cash:,.2f}")
    print(f"   Positions: {len(portfolio.positions)}")
    print()
    
    # 4. Test order validation
    print("4. Testing order validation...")
    
    # Test 1: Valid small order
    print("   Test 1: Small valid order")
    small_order = Order(
        id='order_1',
        symbol='GOOGL',
        side=OrderSide.BUY,
        order_type=OrderType.MARKET,
        quantity=5,
        price=2000.0  # $10,000 order (10% of portfolio)
    )
    
    allowed, violations = risk_manager.check_order(small_order, portfolio)
    print(f"   Order allowed: {allowed}")
    print(f"   Violations: {len(violations)}")
    print()
    
    # Test 2: Order exceeding position size limit
    print("   Test 2: Order exceeding position size limit")
    large_order = Order(
        id='order_2',
        symbol='TSLA',
        side=OrderSide.BUY,
        order_type=OrderType.MARKET,
        quantity=100,
        price=800.0  # $80,000 order (80% of portfolio)
    )
    
    allowed, violations = risk_manager.check_order(large_order, portfolio)
    print(f"   Order allowed: {allowed}")
    print(f"   Violations: {len(violations)}")
    print()
    
    # Test 3: Insufficient cash order
    print("   Test 3: Order with insufficient cash")
    cash_order = Order(
        id='order_3',
        symbol='AMZN',
        side=OrderSide.BUY,
        order_type=OrderType.MARKET,
        quantity=500,
        price=150.0  # $75,000 order (more than available cash)
    )
    
    allowed, violations = risk_manager.check_order(cash_order, portfolio)
    print(f"   Order allowed: {allowed}")
    print(f"   Violations: {len(violations)}")
    print()
    
    # 5. Add stop losses
    print("5. Adding stop loss protection...")
    
    # Add stop loss for AAPL position
    success = risk_manager.add_stop_loss('AAPL', 142.50, 200, 'fixed')
    print(f"   AAPL stop loss added: {success}")
    
    # Add trailing stop for MSFT
    success = risk_manager.add_stop_loss('MSFT', 190.0, 50, 'trailing', trail_amount=10.0)
    print(f"   MSFT trailing stop added: {success}")
    
    # Add take profit for AAPL
    success = risk_manager.add_take_profit('AAPL', 180.0, 200)
    print(f"   AAPL take profit added: {success}")
    print()
    
    # 6. Test stop triggers
    print("6. Testing stop loss triggers...")
    market_data = {
        'AAPL': 140.0,  # Below stop loss
        'MSFT': 195.0   # Above current price
    }
    
    # Update monitors with market data
    risk_manager.update_monitors(portfolio, market_data)
    
    # Check for triggers
    stop_loss_rule = risk_manager.rules['stop_loss']
    triggers = stop_loss_rule.check_triggers(market_data)
    
    print(f"   Stop triggers found: {len(triggers)}")
    for trigger in triggers:
        print(f"   - {trigger['type']} for {trigger['symbol']} at ${trigger['current_price']:.2f}")
    print()
    
    # 7. Calculate risk metrics
    print("7. Calculating risk metrics...")
    metrics = risk_manager.calculate_risk_metrics(portfolio, market_data)
    
    print(f"   Portfolio value: ${metrics.portfolio_value:,.2f}")
    print(f"   Total positions: {metrics.total_positions}")
    print(f"   Largest position: {metrics.largest_position_pct:.1%} ({metrics.largest_position_symbol})")
    print(f"   Gross leverage: {metrics.gross_leverage:.2f}x")
    print(f"   Long exposure: {metrics.long_exposure_pct:.1%}")
    print(f"   Current drawdown: {metrics.current_drawdown_pct:.1%}")
    print()
    
    # 8. Portfolio risk check
    print("8. Checking portfolio risk...")
    portfolio_violations = risk_manager.check_portfolio(portfolio)
    
    print(f"   Portfolio violations: {len(portfolio_violations)}")
    for violation in portfolio_violations:
        print(f"   - {violation.rule_name}: {violation.violation_type}")
    print()
    
    # 9. Risk budget tracking
    print("9. Risk budget tracking...")
    
    # Record some risk usage
    risk_manager.record_trade_risk('AAPL', 0.02, 'strategy_1')  # 2% risk
    risk_manager.record_trade_risk('MSFT', 0.015, 'strategy_2')  # 1.5% risk
    
    # Get budget status
    risk_budget_rule = risk_manager.rules['risk_budget']
    budget_status = risk_budget_rule.get_budget_status()
    
    if 'daily' in budget_status:
        daily = budget_status['daily']
        print(f"   Daily budget used: {daily['utilization_pct']:.1f}%")
        print(f"   Daily remaining: {daily['remaining']:.3f}")
        print(f"   Daily trades: {daily['trades_count']}")
    print()
    
    # 10. Risk summary
    print("10. Risk summary...")
    summary = risk_manager.get_risk_summary()
    
    print(f"   Emergency stop active: {summary['emergency_stop_active']}")
    print(f"   Recent violations: {summary['recent_violations']}")
    print(f"   Total violations: {summary['total_violations']}")
    
    print("\n   Active rules:")
    for rule_name, rule_info in summary['rules'].items():
        status = "✓" if rule_info['enabled'] else "✗"
        print(f"   {status} {rule_name}: {rule_info['type']}")
    
    print("\n   Monitor status:")
    for monitor_name, monitor_info in summary['monitors'].items():
        if 'last_update' in monitor_info and monitor_info['last_update']:
            print(f"   ✓ {monitor_name}: Active")
        else:
            print(f"   ✗ {monitor_name}: Inactive")
    print()
    
    # 11. Demonstrate emergency stop
    print("11. Demonstrating emergency stop...")
    
    # Simulate emergency condition
    print("   Activating emergency stop...")
    risk_manager.emergency_stop_active = True
    
    # Try to place order during emergency stop
    emergency_order = Order(
        id='emergency_order',
        symbol='SPY',
        side=OrderSide.BUY,
        order_type=OrderType.MARKET,
        quantity=10,
        price=400.0
    )
    
    allowed, violations = risk_manager.check_order(emergency_order, portfolio)
    print(f"   Order during emergency stop allowed: {allowed}")
    print(f"   Emergency violations: {len(violations)}")
    
    # Deactivate emergency stop
    deactivated = risk_manager.deactivate_emergency_stop()
    print(f"   Emergency stop deactivated: {deactivated}")
    print()
    
    # 12. Rule and monitor management
    print("12. Rule and monitor management...")
    
    # Disable a rule
    disabled = risk_manager.disable_rule('position_size')
    print(f"   Position size rule disabled: {disabled}")
    
    # Re-enable the rule
    enabled = risk_manager.enable_rule('position_size')
    print(f"   Position size rule re-enabled: {enabled}")
    
    # Disable a monitor
    disabled = risk_manager.disable_monitor('drawdown')
    print(f"   Drawdown monitor disabled: {disabled}")
    
    # Re-enable the monitor
    enabled = risk_manager.enable_monitor('drawdown')
    print(f"   Drawdown monitor re-enabled: {enabled}")
    print()
    
    print("=== Risk Management Demo Complete ===")
    print("\nKey Features Demonstrated:")
    print("✓ Order validation with multiple risk rules")
    print("✓ Position size and cash availability checks")
    print("✓ Stop loss and take profit management")
    print("✓ Real-time risk monitoring")
    print("✓ Risk budget tracking")
    print("✓ Emergency stop functionality")
    print("✓ Comprehensive risk metrics")
    print("✓ Alert system with callbacks")
    print("✓ Rule and monitor management")


if __name__ == "__main__":
    main()