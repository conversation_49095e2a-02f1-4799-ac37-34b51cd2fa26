"""
Example usage of the data import and management interface.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import pandas as pd
from datetime import datetime, timedelta
import time
import logging

from src.data.management_interface import get_data_management_interface

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_sample_data_file():
    """Create a sample CSV file for testing."""
    # Generate sample market data
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA']
    
    data = []
    for symbol in symbols:
        base_price = 100.0 if symbol == 'AAPL' else 150.0
        for i, date in enumerate(dates):
            price = base_price + (i * 0.1) + (i % 10 - 5)  # Add some variation
            data.append({
                'symbol': symbol,
                'timestamp': date.strftime('%Y-%m-%d'),
                'open': round(price, 2),
                'high': round(price * 1.02, 2),
                'low': round(price * 0.98, 2),
                'close': round(price * 1.01, 2),
                'volume': 1000000 + (i * 1000),
                'adj_close': round(price * 1.01, 2)
            })
    
    df = pd.DataFrame(data)
    sample_file = 'data/sample_market_data.csv'
    df.to_csv(sample_file, index=False)
    logger.info(f"Created sample data file: {sample_file}")
    return sample_file


def example_file_validation():
    """Example of file validation."""
    logger.info("=== File Validation Example ===")
    
    # Create sample data
    sample_file = create_sample_data_file()
    
    # Get data management interface
    dm = get_data_management_interface()
    
    # Validate file
    validation_result = dm.validate_import_file(sample_file)
    logger.info(f"Validation result: {validation_result}")
    
    # Get preview
    success, preview = dm.preview_import_file(sample_file, rows=5)
    if success:
        logger.info(f"Preview:\n{preview}")
    else:
        logger.error(f"Preview failed: {preview}")


def example_import_task():
    """Example of asynchronous import task."""
    logger.info("=== Import Task Example ===")
    
    # Create sample data
    sample_file = create_sample_data_file()
    
    # Get data management interface
    dm = get_data_management_interface()
    
    # Register event callbacks
    def on_import_started(data):
        logger.info(f"Import started: {data['id']}")
    
    def on_import_progress(data):
        logger.info(f"Import progress: {data['progress']:.1%}")
    
    def on_import_completed(data):
        logger.info(f"Import completed: {data['result']}")
    
    def on_import_failed(data):
        logger.error(f"Import failed: {data['error_message']}")
    
    dm.register_event_callback('import_started', on_import_started)
    dm.register_event_callback('import_progress', on_import_progress)
    dm.register_event_callback('import_completed', on_import_completed)
    dm.register_event_callback('import_failed', on_import_failed)
    
    # Create and start import task
    task_id = dm.create_import_task(
        file_path=sample_file,
        market='US',
        exchange='NASDAQ',
        currency='USD'
    )
    
    logger.info(f"Created import task: {task_id}")
    
    # Start the task
    success = dm.start_import_task(task_id)
    if success:
        logger.info("Import task started")
        
        # Monitor task progress
        while True:
            status = dm.get_import_task_status(task_id)
            if status and status['status'] in ['completed', 'failed']:
                break
            time.sleep(1)
        
        logger.info(f"Final task status: {status}")
    else:
        logger.error("Failed to start import task")


def example_data_sync():
    """Example of data synchronization from external sources."""
    logger.info("=== Data Sync Example ===")
    
    dm = get_data_management_interface()
    
    # Register event callbacks
    def on_sync_started(data):
        logger.info(f"Sync started: {data['id']} for {len(data['symbols'])} symbols")
    
    def on_sync_progress(data):
        logger.info(f"Sync progress: {data['progress']:.1%} ({data['processed']}/{data['total']})")
    
    def on_sync_completed(data):
        result = data['result']
        logger.info(f"Sync completed: {len(result['successful_syncs'])} successful, {len(result['failed_syncs'])} failed")
    
    dm.register_event_callback('sync_started', on_sync_started)
    dm.register_event_callback('sync_progress', on_sync_progress)
    dm.register_event_callback('sync_completed', on_sync_completed)
    
    # Create sync task
    symbols = ['AAPL', 'GOOGL', 'MSFT']
    start_date = datetime.now() - timedelta(days=30)
    end_date = datetime.now()
    
    task_id = dm.create_sync_task(
        symbols=symbols,
        start_date=start_date,
        end_date=end_date,
        interval='1d'
    )
    
    logger.info(f"Created sync task: {task_id}")
    
    # Start the task
    success = dm.start_sync_task(task_id)
    if success:
        logger.info("Sync task started")
        
        # Monitor task progress
        while True:
            status = dm.get_sync_task_status(task_id)
            if status and status['status'] in ['completed', 'failed']:
                break
            time.sleep(1)
        
        logger.info(f"Final sync status: {status}")
    else:
        logger.error("Failed to start sync task")


def example_coverage_analysis():
    """Example of data coverage analysis."""
    logger.info("=== Coverage Analysis Example ===")
    
    dm = get_data_management_interface()
    
    # Get overall coverage summary
    coverage_summary = dm.get_data_coverage_summary()
    logger.info(f"Coverage summary: {coverage_summary}")
    
    # Get coverage for specific symbol
    symbol_coverage = dm.get_symbol_coverage('AAPL', 'US')
    if symbol_coverage:
        logger.info(f"AAPL coverage: {symbol_coverage}")
    
    # Get market coverage
    market_coverage = dm.get_market_coverage('US')
    if market_coverage:
        logger.info(f"US market coverage: {market_coverage}")
    
    # Find data gaps
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)
    gaps = dm.find_data_gaps('AAPL', start_date, end_date, 'US')
    logger.info(f"Data gaps for AAPL: {len(gaps)} gaps found")


def example_cache_management():
    """Example of cache management."""
    logger.info("=== Cache Management Example ===")
    
    dm = get_data_management_interface()
    
    # Get cache statistics
    cache_stats = dm.get_cache_statistics()
    logger.info(f"Cache statistics: {cache_stats}")
    
    # Clean up cache
    cleanup_result = dm.cleanup_cache()
    logger.info(f"Cache cleanup result: {cleanup_result}")
    
    # Clear specific symbol cache
    clear_result = dm.clear_cache('AAPL')
    logger.info(f"Clear AAPL cache result: {clear_result}")


def example_system_health():
    """Example of system health monitoring."""
    logger.info("=== System Health Example ===")
    
    dm = get_data_management_interface()
    
    # Get system health
    health = dm.get_system_health()
    logger.info(f"System health: {health}")
    
    # Get data sources status
    sources_status = dm.get_data_sources_status()
    logger.info(f"Data sources status: {sources_status}")
    
    # Get database statistics
    db_stats = dm.get_database_statistics()
    logger.info(f"Database statistics: {db_stats}")


def example_export_operations():
    """Example of data export operations."""
    logger.info("=== Export Operations Example ===")
    
    dm = get_data_management_interface()
    
    # Export coverage report
    report_path = 'data/coverage_report.html'
    success = dm.export_coverage_report('html', report_path)
    if success:
        logger.info(f"Coverage report exported to: {report_path}")
    
    # Export market data
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)
    data_export_path = 'data/aapl_2023.csv'
    
    success = dm.export_data(
        symbol='AAPL',
        start_date=start_date,
        end_date=end_date,
        output_path=data_export_path,
        output_format='csv',
        market='US'
    )
    
    if success:
        logger.info(f"Market data exported to: {data_export_path}")


def main():
    """Run all examples."""
    logger.info("Starting data import and management examples")
    
    try:
        # Run examples
        example_file_validation()
        example_import_task()
        example_data_sync()
        example_coverage_analysis()
        example_cache_management()
        example_system_health()
        example_export_operations()
        
        logger.info("All examples completed successfully")
        
    except Exception as e:
        logger.error(f"Example failed: {e}")
        raise
    
    finally:
        # Cleanup
        dm = get_data_management_interface()
        dm.shutdown()


if __name__ == "__main__":
    main()