"""
Risk metrics calculation example.

This example demonstrates how to use the risk metrics calculator
to analyze portfolio risk using VaR, volatility analysis, correlation
analysis, and stress testing.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.risk.metrics import RiskMetricsCalculator, VaRMethod, StressTestType
from src.risk.models import RiskConfig
from src.models.portfolio import Portfolio, Position


def create_sample_portfolio():
    """Create a sample portfolio for demonstration."""
    portfolio = Portfolio(initial_capital=1000000, cash=200000)
    
    # Add diversified positions
    portfolio.positions['AAPL'] = Position(
        symbol='AAPL',
        quantity=1000,
        avg_price=150.0,
        market_value=150000,
        unrealized_pnl=5000
    )
    
    portfolio.positions['GOOGL'] = Position(
        symbol='GOOGL',
        quantity=200,
        avg_price=2500.0,
        market_value=500000,
        unrealized_pnl=-10000
    )
    
    portfolio.positions['MSFT'] = Position(
        symbol='MSFT',
        quantity=800,
        avg_price=300.0,
        market_value=240000,
        unrealized_pnl=8000
    )
    
    portfolio.positions['TSLA'] = Position(
        symbol='TSLA',
        quantity=500,
        avg_price=200.0,
        market_value=100000,
        unrealized_pnl=-5000
    )
    
    portfolio.total_value = 1190000
    return portfolio


def generate_sample_returns():
    """Generate sample historical returns data."""
    # Generate 1 year of daily returns
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    n_days = len(dates)
    
    # Set random seed for reproducible results
    np.random.seed(42)
    
    # Generate correlated market returns
    market_factor = np.random.normal(0.0005, 0.015, n_days)
    
    # Individual stock returns with different market exposures
    returns_data = {
        'AAPL': 0.8 * market_factor + np.random.normal(0, 0.012, n_days),
        'GOOGL': 0.9 * market_factor + np.random.normal(0, 0.018, n_days),
        'MSFT': 0.7 * market_factor + np.random.normal(0, 0.014, n_days),
        'TSLA': 1.2 * market_factor + np.random.normal(0, 0.025, n_days)  # Higher volatility
    }
    
    # Convert to pandas Series with dates
    for symbol in returns_data:
        returns_data[symbol] = pd.Series(returns_data[symbol], index=dates)
    
    return returns_data


def main():
    """Main example function."""
    print("=== Risk Metrics Calculation Example ===\n")
    
    # Create sample data
    portfolio = create_sample_portfolio()
    returns_data = generate_sample_returns()
    
    print("Portfolio Summary:")
    print(f"Total Value: ${portfolio.total_value:,.2f}")
    print(f"Cash: ${portfolio.cash:,.2f}")
    print(f"Number of Positions: {len(portfolio.positions)}")
    print("\nPositions:")
    for symbol, position in portfolio.positions.items():
        weight = position.market_value / portfolio.total_value * 100
        print(f"  {symbol}: ${position.market_value:,.2f} ({weight:.1f}%)")
    
    # Initialize risk calculator
    config = RiskConfig()
    calculator = RiskMetricsCalculator(config)
    
    print("\n" + "="*50)
    print("1. VALUE AT RISK (VaR) ANALYSIS")
    print("="*50)
    
    # Calculate VaR using multiple methods
    var_results = calculator.calculate_var(
        portfolio, 
        returns_data,
        confidence_levels=[0.95, 0.99],
        methods=[VaRMethod.HISTORICAL, VaRMethod.PARAMETRIC, VaRMethod.MONTE_CARLO]
    )
    
    for method_conf, var_result in var_results.items():
        parts = method_conf.split('_')
        method = parts[0]
        conf = parts[-1]  # Take the last part as confidence level
        conf_pct = float(conf) * 100
        
        print(f"\n{method.upper()} VaR ({conf_pct}% confidence):")
        print(f"  1-day VaR: ${var_result.var_1d:,.2f} ({var_result.var_1d/portfolio.total_value*100:.2f}%)")
        print(f"  5-day VaR: ${var_result.var_5d:,.2f} ({var_result.var_5d/portfolio.total_value*100:.2f}%)")
        print(f"  Expected Shortfall (1-day): ${var_result.expected_shortfall_1d:,.2f}")
    
    print("\n" + "="*50)
    print("2. VOLATILITY ANALYSIS")
    print("="*50)
    
    # Calculate volatility metrics
    vol_metrics = calculator.calculate_volatility_metrics(returns_data, portfolio)
    
    print(f"Daily Volatility: {vol_metrics.daily_volatility*100:.2f}%")
    print(f"Annualized Volatility: {vol_metrics.annualized_volatility*100:.2f}%")
    print(f"Volatility Trend: {vol_metrics.volatility_trend}")
    print(f"EWMA Volatility: {vol_metrics.ewma_volatility*100:.2f}%")
    if vol_metrics.volatility_percentile:
        print(f"Volatility Percentile: {vol_metrics.volatility_percentile:.1f}%")
    
    print("\n" + "="*50)
    print("3. CORRELATION ANALYSIS")
    print("="*50)
    
    # Calculate correlation analysis
    corr_analysis = calculator.calculate_correlation_analysis(returns_data, portfolio)
    
    print(f"Average Correlation: {corr_analysis.average_correlation:.3f}")
    print(f"Max Correlation: {corr_analysis.max_correlation:.3f}")
    print(f"Min Correlation: {corr_analysis.min_correlation:.3f}")
    print(f"Diversification Ratio: {corr_analysis.diversification_ratio:.2f}")
    print(f"Effective Number of Positions: {corr_analysis.effective_positions:.1f}")
    
    print("\nCorrelation Matrix:")
    print(corr_analysis.correlation_matrix.round(3))
    
    if corr_analysis.highly_correlated_pairs:
        print("\nHighly Correlated Pairs (>70%):")
        for symbol1, symbol2, corr in corr_analysis.highly_correlated_pairs:
            print(f"  {symbol1} - {symbol2}: {corr:.3f}")
    
    print("\n" + "="*50)
    print("4. STRESS TESTING")
    print("="*50)
    
    # Run stress tests
    stress_results = calculator.run_stress_tests(
        portfolio,
        returns_data,
        scenarios=[
            StressTestType.MARKET_CRASH,
            StressTestType.VOLATILITY_SPIKE,
            StressTestType.CORRELATION_BREAKDOWN,
            StressTestType.INTEREST_RATE_SHOCK
        ]
    )
    
    for result in stress_results:
        print(f"\n{result.scenario_name}:")
        print(f"  Portfolio Loss: ${result.portfolio_loss:,.2f} ({result.portfolio_loss_pct*100:.2f}%)")
        print(f"  VaR Breach: {'Yes' if result.var_breach else 'No'}")
        print(f"  Recovery Time: {result.recovery_time_estimate} days")
        print(f"  Scenario Probability: {result.scenario_probability*100:.1f}%")
        
        # Show top 3 position impacts
        sorted_impacts = sorted(result.position_impacts.items(), 
                              key=lambda x: abs(x[1]), reverse=True)
        print("  Top Position Impacts:")
        for symbol, impact in sorted_impacts[:3]:
            print(f"    {symbol}: ${abs(impact):,.2f}")
    
    print("\n" + "="*50)
    print("5. COMPREHENSIVE RISK REPORT")
    print("="*50)
    
    # Generate comprehensive risk report
    risk_report = calculator.generate_risk_report(portfolio, returns_data)
    
    print(f"Report Date: {risk_report.report_date.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Portfolio Value: ${risk_report.portfolio_value:,.2f}")
    
    print("\nRisk Summary:")
    for key, value in risk_report.risk_summary.items():
        if isinstance(value, float):
            if 'pct' in key or 'ratio' in key:
                print(f"  {key.replace('_', ' ').title()}: {value:.2f}")
            else:
                print(f"  {key.replace('_', ' ').title()}: {value:,.2f}")
        else:
            print(f"  {key.replace('_', ' ').title()}: {value}")
    
    print("\nRisk Management Recommendations:")
    for i, recommendation in enumerate(risk_report.recommendations, 1):
        print(f"  {i}. {recommendation}")
    
    print("\n" + "="*50)
    print("ANALYSIS COMPLETE")
    print("="*50)


if __name__ == "__main__":
    main()