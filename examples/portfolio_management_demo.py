"""
Portfolio Management Demo

This example demonstrates the enhanced portfolio management capabilities
including position tracking, performance analysis, and rebalancing.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from src.models.portfolio import Portfolio
from src.models.portfolio_manager import PortfolioManager, PortfolioAllocation
from src.models.trading import Trade, OrderSide


def create_sample_trade(trade_id: str, symbol: str, side: str, quantity: float, 
                       price: float, commission: float = 5.0) -> Trade:
    """Create a sample trade for demonstration."""
    return Trade(
        id=trade_id,
        symbol=symbol,
        side=OrderSide.BUY if side == "BUY" else OrderSide.SELL,
        quantity=quantity,
        price=price,
        timestamp=datetime.now(),
        commission=commission
    )


def main():
    """Demonstrate portfolio management functionality."""
    print("=== Portfolio Management Demo ===\n")
    
    # 1. Create portfolio with initial capital
    print("1. Creating portfolio with $100,000 initial capital")
    portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
    manager = PortfolioManager(portfolio)
    
    print(f"Initial portfolio value: ${portfolio.total_value:,.2f}")
    print(f"Initial cash: ${portfolio.cash:,.2f}\n")
    
    # 2. Add some trades
    print("2. Adding sample trades")
    trades = [
        create_sample_trade("trade_1", "AAPL", "BUY", 100, 150.0),
        create_sample_trade("trade_2", "GOOGL", "BUY", 25, 2000.0),
        create_sample_trade("trade_3", "MSFT", "BUY", 50, 300.0),
        create_sample_trade("trade_4", "TSLA", "BUY", 30, 800.0),
    ]
    
    for trade in trades:
        portfolio.add_trade(trade)
        print(f"Added trade: {trade.side.value} {trade.quantity} {trade.symbol} @ ${trade.price}")
    
    print(f"\nPortfolio value after trades: ${portfolio.total_value:,.2f}")
    print(f"Cash remaining: ${portfolio.cash:,.2f}\n")
    
    # 3. Update market prices to simulate market movements
    print("3. Updating market prices")
    market_prices = {
        "AAPL": 160.0,   # +6.67%
        "GOOGL": 2100.0, # +5.00%
        "MSFT": 290.0,   # -3.33%
        "TSLA": 850.0,   # +6.25%
    }
    
    portfolio.update_positions_market_value(market_prices)
    
    for symbol, price in market_prices.items():
        position = portfolio.get_position(symbol)
        if position:
            pnl_pct = (price - position.avg_price) / position.avg_price * 100
            print(f"{symbol}: ${position.avg_price:.2f} -> ${price:.2f} ({pnl_pct:+.2f}%)")
    
    print(f"\nUpdated portfolio value: ${portfolio.total_value:,.2f}")
    print(f"Total unrealized P&L: ${portfolio.unrealized_pnl:,.2f}\n")
    
    # 4. Analyze current allocation
    print("4. Current Portfolio Allocation")
    allocation = manager.get_current_allocation()
    for symbol, weight in allocation.items():
        print(f"{symbol}: {weight:.2%}")
    
    print()
    
    # 5. Set target allocation and analyze drift
    print("5. Setting target allocation and analyzing drift")
    target_allocations = [
        PortfolioAllocation("AAPL", 0.25),
        PortfolioAllocation("GOOGL", 0.25),
        PortfolioAllocation("MSFT", 0.25),
        PortfolioAllocation("TSLA", 0.20),
        PortfolioAllocation("CASH", 0.05)
    ]
    
    manager.set_target_allocation(target_allocations)
    
    print("Target allocation:")
    for alloc in target_allocations:
        print(f"{alloc.symbol}: {alloc.target_weight:.2%}")
    
    print("\nAllocation drift:")
    drift = manager.analyze_allocation_drift()
    for symbol, drift_value in drift.items():
        print(f"{symbol}: {drift_value:+.2%}")
    
    print()
    
    # 6. Get rebalancing recommendations
    print("6. Rebalancing Recommendations")
    recommendations = manager.get_rebalance_recommendations(market_prices)
    
    if recommendations:
        for rec in recommendations:
            print(f"{rec.symbol}: {rec.recommended_action} {rec.recommended_quantity:.2f} shares "
                  f"(${rec.recommended_value:,.2f})")
    else:
        print("No rebalancing needed at current thresholds")
    
    print()
    
    # 7. Portfolio concentration analysis
    print("7. Portfolio Concentration Analysis")
    concentration = manager.analyze_position_concentration()
    print(f"Number of positions: {concentration['number_of_positions']}")
    print(f"Concentration ratio (top 5): {concentration['concentration_ratio']:.2%}")
    print(f"Herfindahl Index: {concentration['herfindahl_index']:.4f}")
    print(f"Effective positions: {concentration['effective_positions']:.2f}")
    print(f"Largest position weight: {concentration['largest_position_weight']:.2%}")
    print()
    
    # 8. Add some daily returns for performance metrics
    print("8. Performance Metrics (simulated daily returns)")
    # Simulate some daily returns
    np.random.seed(42)  # For reproducible results
    daily_returns = np.random.normal(0.001, 0.02, 30)  # 30 days of returns
    portfolio.daily_returns = daily_returns.tolist()
    
    metrics = portfolio.get_performance_metrics()
    print(f"Total return: {metrics['total_return']:.2%}")
    print(f"Annualized return: {metrics['annualized_return']:.2%}")
    print(f"Volatility: {metrics['volatility']:.2%}")
    print(f"Sharpe ratio: {metrics['sharpe_ratio']:.2f}")
    print(f"Sortino ratio: {metrics['sortino_ratio']:.2f}")
    print(f"Max drawdown: {metrics['max_drawdown']:.2%}")
    print()
    
    # 9. Risk metrics
    print("9. Risk Metrics")
    var_95 = manager.calculate_var(0.05)
    var_99 = manager.calculate_var(0.01)
    es_95 = manager.calculate_expected_shortfall(0.05)
    
    print(f"Value at Risk (95%): ${var_95:,.2f}")
    print(f"Value at Risk (99%): ${var_99:,.2f}")
    print(f"Expected Shortfall (95%): ${es_95:,.2f}")
    print()
    
    # 10. Cash flow summary
    print("10. Cash Flow Summary")
    cash_flow_summary = portfolio.get_cash_flow_summary()
    print(f"Total outflow: ${cash_flow_summary['total_outflow']:,.2f}")
    print(f"Trade flows: ${cash_flow_summary['trade_flows']:,.2f}")
    print(f"Commission paid: ${cash_flow_summary['commission_paid']:,.2f}")
    print()
    
    # 11. Generate comprehensive report
    print("11. Comprehensive Performance Report")
    report = manager.generate_performance_report()
    
    print("Portfolio Summary:")
    summary = report['portfolio_summary']
    print(f"  Initial Capital: ${summary['initial_capital']:,.2f}")
    print(f"  Current Value: ${summary['current_value']:,.2f}")
    print(f"  Number of Positions: {summary['number_of_positions']}")
    print(f"  Total Trades: {summary['total_trades']}")
    
    print("\nRisk Metrics:")
    risk_metrics = report['risk_metrics']
    print(f"  VaR (95%): ${risk_metrics['var_95']:,.2f}")
    print(f"  VaR (99%): ${risk_metrics['var_99']:,.2f}")
    print()
    
    # 12. Export data to DataFrames
    print("12. Exporting Data to DataFrames")
    dataframes = manager.export_to_dataframe()
    
    print("Available DataFrames:")
    for name, df in dataframes.items():
        if not df.empty:
            print(f"  {name}: {len(df)} records")
        else:
            print(f"  {name}: empty")
    
    print("\nPortfolio History (last 5 records):")
    if not dataframes['portfolio_history'].empty:
        print(dataframes['portfolio_history'].tail().to_string())
    
    print("\n=== Demo Complete ===")


if __name__ == "__main__":
    main()