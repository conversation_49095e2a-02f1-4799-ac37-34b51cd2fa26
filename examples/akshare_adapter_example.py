#!/usr/bin/env python3
"""
Example usage of AkshareAdapter for A-share market data.

This example demonstrates how to use the AkshareAdapter to fetch Chinese A-share
market data including stocks and indices.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path
import os

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# Set up environment to find config module
os.environ['PYTHONPATH'] = str(project_root)

from src.data.adapters.base import DataSourceConfig
from src.data.adapters.akshare_adapter import AkshareAdapter


def main():
    """Main example function."""
    print("=== Akshare Adapter Example ===\n")
    
    config = DataSourceConfig(
        name='akshare_example',
        adapter_class='src.data.adapters.akshare_adapter.AkshareAdapter',
        enabled=True,
        priority=1,
        rate_limit={'requests_per_minute': 100},
        credentials={},
        default_params={}
    )
    
    try:
        # Initialize adapter
        print("1. Initializing Akshare adapter...")
        adapter = AkshareAdapter(config)
        print("✅ Adapter initialized successfully")
        
        # Health check
        print("\n2. Performing health check...")
        is_healthy, message = adapter.health_check()
        if is_healthy:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
            return
        
        # Get available symbols (sample)
        print("\n3. Getting available symbols...")
        symbols = adapter.get_available_symbols()
        print(f"✅ Found {len(symbols)} symbols")
        print(f"   Sample symbols: {symbols[:10]}")
        
        # Test with a popular A-share stock (平安银行)
        test_symbol = '000001.SZ'
        print(f"\n4. Testing with symbol: {test_symbol}")
        
        # Validate symbol
        is_valid = adapter.validate_symbol(test_symbol)
        print(f"   Symbol validation: {'✅ Valid' if is_valid else '❌ Invalid'}")
        
        if not is_valid:
            print("   Skipping data fetch for invalid symbol")
            return
        
        # Get market info
        print(f"\n5. Getting market info for {test_symbol}...")
        market_info = adapter.get_market_info(test_symbol)
        if market_info:
            print(f"   Name: {market_info.name}")
            print(f"   Exchange: {market_info.exchange}")
            print(f"   Currency: {market_info.currency}")
            print(f"   Lot size: {market_info.lot_size}")
            print(f"   Tick size: {market_info.tick_size}")
        
        # Fetch historical data
        print(f"\n6. Fetching historical data for {test_symbol}...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)  # Last 30 days
        
        data = adapter.fetch_historical_data(test_symbol, start_date, end_date)
        print(f"✅ Fetched {len(data)} records")
        
        if data:
            print("   Sample data (first 3 records):")
            for i, record in enumerate(data[:3]):
                print(f"   {i+1}. {record.timestamp.strftime('%Y-%m-%d')}: "
                      f"Open={record.open:.2f}, High={record.high:.2f}, "
                      f"Low={record.low:.2f}, Close={record.close:.2f}, "
                      f"Volume={record.volume:,.0f}")
        
        # Test with major index (上证指数)
        index_symbol = '000001.SH'
        print(f"\n7. Testing with index: {index_symbol}")
        
        market_info = adapter.get_market_info(index_symbol)
        if market_info:
            print(f"   Index name: {market_info.name}")
        
        index_data = adapter.fetch_historical_data(index_symbol, start_date, end_date)
        print(f"✅ Fetched {len(index_data)} index records")
        
        if index_data:
            latest = index_data[-1]
            print(f"   Latest: {latest.timestamp.strftime('%Y-%m-%d')}: "
                  f"Close={latest.close:.2f}")
        
        # Get trading calendar
        print(f"\n8. Getting trading calendar...")
        calendar_start = datetime.now() - timedelta(days=10)
        calendar_end = datetime.now()
        trading_days = adapter.get_trading_calendar(calendar_start, calendar_end)
        print(f"✅ Found {len(trading_days)} trading days in the last 10 days")
        
        # Search stocks
        print(f"\n9. Searching stocks with keyword '银行'...")
        search_results = adapter.search_stocks('银行', limit=5)
        print(f"✅ Found {len(search_results)} matching stocks:")
        for result in search_results:
            print(f"   {result['symbol']}: {result['name']} ({result['exchange']})")
        
        print(f"\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()