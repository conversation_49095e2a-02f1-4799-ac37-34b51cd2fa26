#!/usr/bin/env python3
"""
Simple test to verify Akshare adapter integration with DataSourceManager.
"""

import sys
from pathlib import Path
import os

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# Set up environment to find config module
os.environ['PYTHONPATH'] = str(project_root)

from src.data.adapters.manager import DataSourceManager


def main():
    """Test Akshare adapter integration."""
    print("=== Testing Akshare Adapter Integration ===\n")
    
    try:
        # Initialize manager
        print("1. Initializing DataSourceManager...")
        manager = DataSourceManager("config/data_sources.yaml")
        print(f"✅ Manager initialized with {len(manager.adapters)} adapters")
        
        # Check if Akshare adapter is loaded
        print("\n2. Checking Akshare adapter...")
        akshare_adapter = manager.get_adapter('akshare')
        if akshare_adapter:
            print("✅ Akshare adapter loaded successfully")
            print(f"   Market type: {akshare_adapter.get_market_type()}")
            print(f"   Exchange: {akshare_adapter.get_exchange_name()}")
            print(f"   Currency: {akshare_adapter.get_default_currency()}")
            print(f"   Supported intervals: {akshare_adapter.get_supported_intervals()}")
        else:
            print("❌ Akshare adapter not found")
            return
        
        # Test health check
        print("\n3. Performing health check...")
        is_healthy, message = akshare_adapter.health_check()
        print(f"   Health status: {'Healthy' if is_healthy else 'Unhealthy'}")
        print(f"   Message: {message}")
        
        # Test getting available symbols (even if it fails due to network)
        print("\n4. Getting available symbols...")
        try:
            symbols = akshare_adapter.get_available_symbols()
            print(f"   Retrieved {len(symbols)} symbols")
            if symbols:
                print(f"   Sample symbols: {symbols[:5]}")
        except Exception as e:
            print(f"   Error getting symbols (may be network issue): {e}")
        
        # Test symbol validation
        print("\n5. Testing symbol validation...")
        test_symbols = ["000001.SZ", "000001.SH", "INVALID"]
        for symbol in test_symbols:
            try:
                is_valid = akshare_adapter.validate_symbol(symbol)
                print(f"   {symbol}: {'Valid' if is_valid else 'Invalid'}")
            except Exception as e:
                print(f"   {symbol}: Error validating ({e})")
        
        print("\n🎉 Integration test completed!")
        
    except Exception as e:
        print(f"❌ Error during integration test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()