#!/usr/bin/env python3
"""
Demo script for the main chart component functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

from src.ui.charts.main_chart import MainChartComponent
from src.ui.charts.chart_config import ChartConfig, TimeInterval, ChartTheme
from src.models.trading import Trade, Signal, OrderSide, SignalAction
from src.indicators.engine import IndicatorEngine


def generate_sample_data(symbol: str = "AAPL", days: int = 30) -> pd.DataFrame:
    """Generate sample market data for demonstration."""
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), end=datetime.now(), freq='H')
    data = []
    
    base_price = 150.0
    for i, date in enumerate(dates):
        # Simulate price movement with some randomness
        price_change = np.random.normal(0, 2)
        base_price = max(base_price + price_change, 10.0)  # Ensure price doesn't go negative
        
        open_price = base_price + np.random.normal(0, 0.5)
        high_price = open_price + abs(np.random.normal(0, 1))
        low_price = open_price - abs(np.random.normal(0, 1))
        close_price = open_price + np.random.normal(0, 0.8)
        volume = np.random.randint(10000, 100000)
        
        data.append({
            'timestamp': date,
            'open': round(open_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'close': round(close_price, 2),
            'volume': volume
        })
    
    return pd.DataFrame(data)


def generate_sample_trades(data: pd.DataFrame, symbol: str = "AAPL") -> list:
    """Generate sample trades based on market data."""
    trades = []
    
    # Generate some random trades
    for i in range(0, len(data), 20):  # Every 20 data points
        if i + 1 < len(data):
            row = data.iloc[i]
            
            # Simulate buy trade
            buy_trade = Trade(
                id=f"trade_buy_{i}",
                timestamp=row['timestamp'],
                symbol=symbol,
                side=OrderSide.BUY,
                quantity=100,
                price=row['close'],
                strategy_id="demo_strategy",
                pnl=np.random.normal(0, 50)  # Random P&L
            )
            trades.append(buy_trade)
            
            # Simulate corresponding sell trade a few periods later
            if i + 10 < len(data):
                sell_row = data.iloc[i + 10]
                sell_trade = Trade(
                    id=f"trade_sell_{i}",
                    timestamp=sell_row['timestamp'],
                    symbol=symbol,
                    side=OrderSide.SELL,
                    quantity=100,
                    price=sell_row['close'],
                    strategy_id="demo_strategy",
                    pnl=(sell_row['close'] - row['close']) * 100  # Calculate P&L
                )
                trades.append(sell_trade)
    
    return trades


def generate_sample_signals(data: pd.DataFrame, symbol: str = "AAPL") -> list:
    """Generate sample trading signals."""
    signals = []
    
    # Simple moving average crossover signals
    data['sma_short'] = data['close'].rolling(window=5).mean()
    data['sma_long'] = data['close'].rolling(window=20).mean()
    
    for i in range(20, len(data)):
        prev_short = data['sma_short'].iloc[i-1]
        prev_long = data['sma_long'].iloc[i-1]
        curr_short = data['sma_short'].iloc[i]
        curr_long = data['sma_long'].iloc[i]
        
        # Golden cross (buy signal)
        if prev_short <= prev_long and curr_short > curr_long:
            signal = Signal(
                timestamp=data['timestamp'].iloc[i],
                symbol=symbol,
                action=SignalAction.BUY,
                quantity=100,
                price=data['close'].iloc[i],
                confidence=0.7 + np.random.random() * 0.3,
                strategy_id="sma_crossover"
            )
            signals.append(signal)
        
        # Death cross (sell signal)
        elif prev_short >= prev_long and curr_short < curr_long:
            signal = Signal(
                timestamp=data['timestamp'].iloc[i],
                symbol=symbol,
                action=SignalAction.SELL,
                quantity=100,
                price=data['close'].iloc[i],
                confidence=0.6 + np.random.random() * 0.4,
                strategy_id="sma_crossover"
            )
            signals.append(signal)
    
    return signals


async def demo_main_chart():
    """Demonstrate main chart component functionality."""
    print("🚀 Starting Main Chart Component Demo")
    print("=" * 50)
    
    # Create chart configuration
    config = ChartConfig()
    config.time_interval = TimeInterval.HOUR_1
    config.show_volume = True
    config.show_crosshair = True
    config.enable_zoom = True
    config.enable_pan = True
    
    # Create indicator engine
    indicator_engine = IndicatorEngine()
    
    # Create main chart component
    chart = MainChartComponent(
        config=config,
        indicator_engine=indicator_engine
    )
    
    print("✅ Chart component initialized")
    
    # Generate sample data
    print("📊 Generating sample market data...")
    symbol = "AAPL"
    market_data = generate_sample_data(symbol, days=30)
    
    # Manually set market data (simulating data manager)
    chart.market_data = market_data
    chart.state.symbol = symbol
    chart.state.visible_range = (
        market_data['timestamp'].min(),
        market_data['timestamp'].max()
    )
    
    print(f"✅ Loaded {len(market_data)} data points for {symbol}")
    
    # Add technical indicators
    print("📈 Adding technical indicators...")
    
    # Add moving averages
    chart.add_indicator("SMA", period=5)
    chart.add_indicator("SMA", period=20)
    chart.add_indicator("SMA", period=50)
    
    print("✅ Added moving average indicators")
    
    # Generate and add trades
    print("💰 Generating sample trades...")
    trades = generate_sample_trades(market_data, symbol)
    chart.add_trades(trades)
    
    print(f"✅ Added {len(trades)} sample trades")
    
    # Generate and add signals
    print("🎯 Generating trading signals...")
    signals = generate_sample_signals(market_data, symbol)
    chart.add_signals(signals)
    
    print(f"✅ Added {len(signals)} trading signals")
    
    # Test chart functionality
    print("\n🔧 Testing chart functionality...")
    
    # Test zoom
    print("🔍 Testing zoom functionality...")
    chart.zoom(2.0)  # Zoom in
    print(f"   Zoom level: {chart.state.zoom_level}")
    
    # Test pan
    print("↔️  Testing pan functionality...")
    chart.pan(3600)  # Pan by 1 hour
    print(f"   Pan offset: {chart.state.pan_offset}")
    
    # Test crosshair
    print("✚ Testing crosshair...")
    test_time = market_data['timestamp'].iloc[len(market_data)//2]
    test_price = market_data['close'].iloc[len(market_data)//2]
    chart.set_crosshair_position(test_time, test_price)
    print(f"   Crosshair at: {test_time}, ${test_price:.2f}")
    
    # Get chart data
    print("\n📋 Generating chart data...")
    chart_data = chart.get_chart_data()
    
    print(f"✅ Chart data generated:")
    print(f"   - Candlestick points: {len(chart_data['candlestick'])}")
    print(f"   - Volume points: {len(chart_data['volume'])}")
    print(f"   - Indicators: {list(chart_data['indicators'].keys())}")
    print(f"   - Buy signals: {len(chart_data['signals'].get('buy_signals', []))}")
    print(f"   - Sell signals: {len(chart_data['signals'].get('sell_signals', []))}")
    print(f"   - Buy trades: {len(chart_data['trades'].get('buy_signals', []))}")
    print(f"   - Sell trades: {len(chart_data['trades'].get('sell_signals', []))}")
    
    # Test JSON rendering
    print("\n📄 Testing JSON rendering...")
    json_output = chart.render_to_json()
    print(f"✅ JSON output generated ({len(json_output)} characters)")
    
    # Test HTML rendering
    print("🌐 Testing HTML rendering...")
    html_output = chart.render_to_html("demo-chart")
    print(f"✅ HTML output generated ({len(html_output)} characters)")
    
    # Save outputs for inspection
    print("\n💾 Saving outputs...")
    
    with open("chart_data.json", "w") as f:
        f.write(json_output)
    print("✅ Chart data saved to chart_data.json")
    
    with open("chart_demo.html", "w") as f:
        f.write(html_output)
    print("✅ Chart HTML saved to chart_demo.html")
    
    # Get chart statistics
    print("\n📊 Chart Statistics:")
    stats = chart.get_chart_statistics()
    for key, value in stats.items():
        if isinstance(value, dict):
            print(f"   {key}:")
            for sub_key, sub_value in value.items():
                print(f"     {sub_key}: {sub_value}")
        else:
            print(f"   {key}: {value}")
    
    # Test indicator management
    print("\n🔧 Testing indicator management...")
    
    # Toggle indicator visibility
    chart.toggle_indicator("SMA")
    print("✅ Toggled SMA indicator visibility")
    
    # Remove an indicator
    chart.remove_indicator("SMA")
    print("✅ Removed SMA indicator")
    
    # Reset zoom
    chart.reset_zoom()
    print("✅ Reset zoom to default")
    
    print("\n🎉 Demo completed successfully!")
    print("📁 Check chart_demo.html to view the interactive chart")
    print("📁 Check chart_data.json to see the raw chart data")


if __name__ == "__main__":
    asyncio.run(demo_main_chart())