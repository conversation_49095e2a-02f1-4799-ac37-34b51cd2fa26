#!/usr/bin/env python3
"""
Binance adapter usage example.

This example demonstrates how to use the Binance adapter to fetch cryptocurrency data.
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.adapters.binance_adapter import BinanceAdapter
from src.data.adapters.base import DataSourceConfig


def main():
    """Main example function."""
    print("=== Binance Adapter Example ===\n")
    
    # Create configuration
    config = DataSourceConfig(
        name='binance_example',
        adapter_class='src.data.adapters.binance_adapter.BinanceAdapter',
        enabled=True,
        priority=1,
        rate_limit={
            'requests_per_minute': 60,
            'requests_per_second': 10
        },
        credentials={
            'api_key': '',  # Optional - leave empty for public data
            'api_secret': ''
        },
        default_params={}
    )
    
    try:
        # Initialize adapter
        print("1. Initializing Binance adapter...")
        adapter = BinanceAdapter(config)
        print(f"   ✓ Adapter initialized successfully")
        print(f"   Market: {adapter.get_market_type()}")
        print(f"   Exchange: {adapter.get_exchange_name()}")
        print(f"   Default Currency: {adapter.get_default_currency()}")
        print()
        
        # Health check
        print("2. Performing health check...")
        is_healthy, message = adapter.health_check()
        print(f"   Health Status: {'✓ Healthy' if is_healthy else '✗ Unhealthy'}")
        print(f"   Message: {message}")
        print()
        
        # Get supported intervals
        print("3. Supported time intervals:")
        intervals = adapter.get_supported_intervals()
        print(f"   {', '.join(intervals)}")
        print()
        
        # Get available symbols (first 20)
        print("4. Available trading pairs (first 20):")
        symbols = adapter.get_available_symbols()[:20]
        for i, symbol in enumerate(symbols, 1):
            print(f"   {i:2d}. {symbol}")
        print(f"   ... and {len(adapter.get_available_symbols()) - 20} more")
        print()
        
        # Test symbol validation
        print("5. Symbol validation:")
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'INVALID_SYMBOL']
        for symbol in test_symbols:
            is_valid = adapter.validate_symbol(symbol)
            status = '✓ Valid' if is_valid else '✗ Invalid'
            print(f"   {symbol}: {status}")
        print()
        
        # Get market info for popular pairs
        print("6. Market information for popular pairs:")
        popular_pairs = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
        for symbol in popular_pairs:
            market_info = adapter.get_market_info(symbol)
            if market_info:
                print(f"   {symbol}:")
                print(f"     Name: {market_info.name}")
                print(f"     Currency: {market_info.currency}")
                print(f"     Lot Size: {market_info.lot_size}")
                print(f"     Tick Size: {market_info.tick_size}")
                print(f"     Active: {market_info.is_active}")
            else:
                print(f"   {symbol}: No market info available")
        print()
        
        # Fetch historical data
        print("7. Fetching historical data for BTCUSDT...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)  # Last 7 days
        
        print(f"   Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        print(f"   Interval: 1d (daily)")
        
        data = adapter.fetch_historical_data('BTCUSDT', start_date, end_date, '1d')
        
        if data:
            print(f"   ✓ Fetched {len(data)} records")
            print("   Sample data (first 3 records):")
            for i, record in enumerate(data[:3], 1):
                print(f"     {i}. {record.timestamp.strftime('%Y-%m-%d')}:")
                print(f"        Open: ${record.open:,.2f}")
                print(f"        High: ${record.high:,.2f}")
                print(f"        Low: ${record.low:,.2f}")
                print(f"        Close: ${record.close:,.2f}")
                print(f"        Volume: {record.volume:,.2f} BTC")
                print(f"        Turnover: ${record.turnover:,.2f}")
        else:
            print("   ✗ No data retrieved")
        print()
        
        # Get 24hr ticker
        print("8. Getting 24hr ticker for BTCUSDT...")
        ticker = adapter.get_24hr_ticker('BTCUSDT')
        if ticker:
            print(f"   Last Price: ${ticker['last_price']:,.2f}")
            print(f"   24h Change: ${ticker['price_change']:,.2f} ({ticker['price_change_percent']:.2f}%)")
            print(f"   24h High: ${ticker['high_price']:,.2f}")
            print(f"   24h Low: ${ticker['low_price']:,.2f}")
            print(f"   24h Volume: {ticker['volume']:,.2f} BTC")
            print(f"   24h Quote Volume: ${ticker['quote_volume']:,.2f}")
        else:
            print("   ✗ No ticker data available")
        print()
        
        # Search symbols
        print("9. Searching for symbols containing 'ETH':")
        search_results = adapter.search_symbols('ETH', limit=5)
        for i, result in enumerate(search_results, 1):
            print(f"   {i}. {result['symbol']} - {result['name']}")
            print(f"      Base: {result['base_asset']}, Quote: {result['quote_asset']}")
        print()
        
        # Get server time
        print("10. Binance server time:")
        server_time = adapter.get_server_time()
        print(f"    Server Time: {server_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print()
        
        # Performance stats
        print("11. Adapter performance statistics:")
        stats = adapter.get_performance_stats()
        print(f"    Total Requests: {stats['total_requests']}")
        print(f"    Requests (Last Hour): {stats['requests_last_hour']}")
        print(f"    Requests (Last Day): {stats['requests_last_day']}")
        print(f"    Can Make Request: {stats['can_make_request']}")
        print(f"    Wait Time: {stats['wait_time']:.2f} seconds")
        print()
        
        print("=== Example completed successfully! ===")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()