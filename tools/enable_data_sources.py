#!/usr/bin/env python3
"""
数据源启用工具
帮助用户快速配置和启用所有数据源
"""

import os
import yaml
from pathlib import Path

def main():
    print("🔧 数据源配置工具")
    print("=" * 40)
    
    # 检查当前配置
    config_path = Path("config/data_sources.yaml")
    if not config_path.exists():
        print("❌ 配置文件不存在")
        return
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print("📊 当前数据源状态:")
    for name, settings in config.items():
        status = "✅ 启用" if settings.get('enabled', False) else "🔴 禁用"
        print(f"  - {name}: {status}")
    
    print("\n🔧 配置指南:")
    
    # Tushare配置
    if not config.get('tushare', {}).get('enabled', False):
        print("\n📈 Tushare (A股数据) - 当前禁用")
        print("  1. 访问 https://tushare.pro/ 注册账号")
        print("  2. 获取Token后运行:")
        print("     export TUSHARE_TOKEN=your_token")
        print("  3. 或添加到.env文件:")
        print("     echo 'TUSHARE_TOKEN=your_token' >> .env")
        
        token = input("\n输入Tushare Token (回车跳过): ").strip()
        if token:
            config['tushare']['enabled'] = True
            config['tushare']['credentials']['token'] = token
            print("✅ Tushare已启用")
    
    # FRED配置
    if not config.get('fred', {}).get('enabled', False):
        print("\n🏦 FRED (经济数据) - 当前禁用")
        print("  1. 访问 https://fred.stlouisfed.org/docs/api/api_key.html")
        print("  2. 免费申请API Key后运行:")
        print("     export FRED_API_KEY=your_key")
        print("  3. 或添加到.env文件:")
        print("     echo 'FRED_API_KEY=your_key' >> .env")
        
        api_key = input("\n输入FRED API Key (回车跳过): ").strip()
        if api_key:
            config['fred']['enabled'] = True
            config['fred']['credentials']['api_key'] = api_key
            print("✅ FRED已启用")
    
    # 保存配置
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print("\n📊 更新后的数据源状态:")
    for name, settings in config.items():
        status = "✅ 启用" if settings.get('enabled', False) else "🔴 禁用"
        print(f"  - {name}: {status}")
    
    print("\n💡 提示:")
    print("  - Yahoo Finance: 免费，无需配置")
    print("  - Binance: 免费，无需配置")
    print("  - Tushare: 需要Token，付费服务")
    print("  - FRED: 需要API Key，免费服务")
    
    print("\n🎉 配置完成！")

if __name__ == "__main__":
    main()