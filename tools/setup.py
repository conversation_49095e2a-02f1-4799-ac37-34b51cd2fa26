"""
Setup script for the quantitative trading system.
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="quantitative-trading-system",
    version="0.1.0",
    author="Trading System Team",
    author_email="<EMAIL>",
    description="A comprehensive quantitative trading system for strategy development and backtesting",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/quantitative-trading-system",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Investment",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.2.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "ta": [
            "ta-lib>=0.4.0",
        ],
        "redis": [
            "redis>=4.5.0",
        ],
        "celery": [
            "celery>=5.2.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "trading-system=src.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "config": ["*.yaml", "*.yml"],
    },
)