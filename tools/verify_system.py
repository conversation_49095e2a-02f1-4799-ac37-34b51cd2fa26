#!/usr/bin/env python3
"""
系统验证脚本
验证量化交易系统的核心功能是否正常工作
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试核心模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from src.data.manager import DataManager
        from src.strategies.base import BaseStrategy
        from src.backtest.engine import BacktestEngine
        from src.risk.manager import RiskManager
        from src.analysis.metrics import MetricsCalculator
        from src.models.portfolio import Portfolio
        from src.models.market_data import UnifiedMarketData
        from src.models.trading import Order, OrderSide, OrderType
        print("✅ 所有核心模块导入成功")
        print("ℹ️  API模块已移除 - 系统现在使用直接Python调用")
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_data_manager():
    """测试数据管理器"""
    print("🗄️  测试数据管理器...")
    
    try:
        from src.data.manager import DataManager
        
        # 初始化数据管理器
        data_manager = DataManager(use_sqlite=True)
        
        # 测试数据库连接
        coverage = data_manager.get_data_coverage_report()
        
        print("✅ 数据管理器工作正常")
        return True
    except Exception as e:
        print(f"❌ 数据管理器测试失败: {e}")
        return False

def test_portfolio():
    """测试投资组合管理"""
    print("💼 测试投资组合管理...")
    
    try:
        from src.models.portfolio import Portfolio
        from src.models.trading import Trade, OrderSide
        
        # 创建投资组合
        portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        
        # 创建交易
        trade = Trade(
            id="test_001",
            symbol="AAPL",
            side=OrderSide.BUY,
            quantity=100,
            price=150.0,
            timestamp=datetime.now()
        )
        
        # 添加交易
        portfolio.add_trade(trade)
        
        # 验证持仓
        position = portfolio.get_position("AAPL")
        assert position is not None
        assert position.quantity == 100
        
        print("✅ 投资组合管理工作正常")
        return True
    except Exception as e:
        print(f"❌ 投资组合测试失败: {e}")
        return False

def test_backtest_engine():
    """测试回测引擎"""
    print("🔄 测试回测引擎...")
    
    try:
        from src.backtest.engine import BacktestEngine
        
        # 创建回测引擎
        engine = BacktestEngine(initial_capital=100000.0)
        
        print("✅ 回测引擎初始化成功")
        return True
    except Exception as e:
        print(f"❌ 回测引擎测试失败: {e}")
        return False

def test_risk_manager():
    """测试风险管理器"""
    print("⚠️  测试风险管理器...")
    
    try:
        from src.risk.manager import RiskManager
        from src.models.portfolio import Portfolio
        
        # 创建风险管理器
        risk_manager = RiskManager()
        
        # 创建测试投资组合
        portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        
        # 计算风险指标
        metrics = risk_manager.calculate_risk_metrics(portfolio)
        
        print("✅ 风险管理器工作正常")
        return True
    except Exception as e:
        print(f"❌ 风险管理器测试失败: {e}")
        return False

def test_indicators():
    """测试技术指标"""
    print("📊 测试技术指标...")
    
    try:
        from src.indicators.engine import IndicatorEngine
        import pandas as pd
        import numpy as np
        
        # 创建指标引擎
        engine = IndicatorEngine()
        
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        prices = 100 + np.cumsum(np.random.randn(100) * 0.01)
        
        data = pd.DataFrame({
            'timestamp': dates,
            'close': prices,
            'high': prices * 1.01,
            'low': prices * 0.99,
            'open': prices,
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        # 计算SMA指标
        result = engine.calculate('sma', data, period=20)
        
        print("✅ 技术指标计算正常")
        return True
    except Exception as e:
        print(f"❌ 技术指标测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 量化交易系统验证")
    print("=" * 40)
    
    tests = [
        ("模块导入", test_imports),
        ("数据管理器", test_data_manager),
        ("投资组合", test_portfolio),
        ("回测引擎", test_backtest_engine),
        ("风险管理器", test_risk_manager),
        ("技术指标", test_indicators),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 测试结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 系统验证通过！所有功能正常工作。")
        return 0
    else:
        print(f"⚠️  系统存在 {total - passed} 个问题需要解决。")
        return 1

if __name__ == "__main__":
    sys.exit(main())