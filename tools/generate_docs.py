#!/usr/bin/env python3
"""
文档生成脚本

自动生成项目文档，包括API文档、代码覆盖率报告和文档网站。

功能:
- 生成Sphinx API文档
- 检查文档覆盖率
- 生成代码注释报告
- 构建文档网站
- 验证文档链接

使用方法:
    python scripts/generate_docs.py [options]

选项:
    --api-only: 只生成API文档
    --coverage: 生成文档覆盖率报告
    --serve: 启动文档服务器
    --check-links: 检查文档链接
"""

import os
import sys
import subprocess
import argparse
import webbrowser
from pathlib import Path
from typing import List, Dict, Any
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DocumentationGenerator:
    """文档生成器"""
    
    def __init__(self, project_root: str = None):
        """
        初始化文档生成器
        
        Args:
            project_root: 项目根目录路径
        """
        self.project_root = Path(project_root or os.getcwd())
        self.docs_dir = self.project_root / 'docs'
        self.src_dir = self.project_root / 'src'
        self.build_dir = self.docs_dir / 'build'
        
        # 确保目录存在
        self.docs_dir.mkdir(exist_ok=True)
        self.build_dir.mkdir(exist_ok=True)
    
    def generate_api_docs(self) -> bool:
        """
        生成API文档
        
        Returns:
            bool: 生成是否成功
        """
        logger.info("开始生成API文档...")
        
        try:
            # 清理旧的API文档
            api_dir = self.docs_dir / 'api'
            if api_dir.exists():
                import shutil
                shutil.rmtree(api_dir)
            
            # 使用sphinx-apidoc生成API文档
            cmd = [
                'sphinx-apidoc',
                '-o', str(api_dir),
                str(self.src_dir),
                '--separate',
                '--force',
                '--module-first'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"API文档生成失败: {result.stderr}")
                return False
            
            logger.info("API文档生成成功")
            return True
            
        except Exception as e:
            logger.error(f"生成API文档时出错: {e}")
            return False
    
    def build_html_docs(self) -> bool:
        """
        构建HTML文档
        
        Returns:
            bool: 构建是否成功
        """
        logger.info("开始构建HTML文档...")
        
        try:
            # 切换到docs目录
            original_cwd = os.getcwd()
            os.chdir(self.docs_dir)
            
            # 运行sphinx-build
            cmd = [
                'sphinx-build',
                '-b', 'html',
                '.',
                str(self.build_dir / 'html'),
                '-W',  # 将警告视为错误
                '-E'   # 重新构建所有文件
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            # 恢复工作目录
            os.chdir(original_cwd)
            
            if result.returncode != 0:
                logger.error(f"HTML文档构建失败: {result.stderr}")
                return False
            
            logger.info("HTML文档构建成功")
            return True
            
        except Exception as e:
            logger.error(f"构建HTML文档时出错: {e}")
            return False
    
    def check_documentation_coverage(self) -> Dict[str, Any]:
        """
        检查文档覆盖率
        
        Returns:
            Dict[str, Any]: 覆盖率统计信息
        """
        logger.info("检查文档覆盖率...")
        
        stats = {
            'total_files': 0,
            'documented_files': 0,
            'total_functions': 0,
            'documented_functions': 0,
            'total_classes': 0,
            'documented_classes': 0,
            'issues': []
        }
        
        try:
            import ast
            
            # 遍历所有Python文件
            for py_file in self.src_dir.rglob('*.py'):
                if '__pycache__' in str(py_file):
                    continue
                
                stats['total_files'] += 1
                
                with open(py_file, 'r', encoding='utf-8') as f:
                    try:
                        tree = ast.parse(f.read())
                        file_has_docs = False
                        
                        for node in ast.walk(tree):
                            if isinstance(node, ast.ClassDef):
                                stats['total_classes'] += 1
                                if ast.get_docstring(node):
                                    stats['documented_classes'] += 1
                                    file_has_docs = True
                                else:
                                    stats['issues'].append({
                                        'file': str(py_file.relative_to(self.project_root)),
                                        'line': node.lineno,
                                        'type': 'class',
                                        'name': node.name,
                                        'issue': 'missing_docstring'
                                    })
                            
                            elif isinstance(node, ast.FunctionDef):
                                # 跳过私有方法
                                if node.name.startswith('_') and node.name != '__init__':
                                    continue
                                
                                stats['total_functions'] += 1
                                if ast.get_docstring(node):
                                    stats['documented_functions'] += 1
                                    file_has_docs = True
                                else:
                                    stats['issues'].append({
                                        'file': str(py_file.relative_to(self.project_root)),
                                        'line': node.lineno,
                                        'type': 'function',
                                        'name': node.name,
                                        'issue': 'missing_docstring'
                                    })
                        
                        if file_has_docs:
                            stats['documented_files'] += 1
                    
                    except SyntaxError as e:
                        logger.warning(f"语法错误 {py_file}: {e}")
            
            # 计算覆盖率
            stats['file_coverage'] = (stats['documented_files'] / stats['total_files'] * 100 
                                    if stats['total_files'] > 0 else 0)
            stats['function_coverage'] = (stats['documented_functions'] / stats['total_functions'] * 100 
                                        if stats['total_functions'] > 0 else 0)
            stats['class_coverage'] = (stats['documented_classes'] / stats['total_classes'] * 100 
                                     if stats['total_classes'] > 0 else 0)
            
            logger.info(f"文档覆盖率检查完成:")
            logger.info(f"  文件覆盖率: {stats['file_coverage']:.1f}%")
            logger.info(f"  函数覆盖率: {stats['function_coverage']:.1f}%")
            logger.info(f"  类覆盖率: {stats['class_coverage']:.1f}%")
            
            return stats
            
        except Exception as e:
            logger.error(f"检查文档覆盖率时出错: {e}")
            return stats
    
    def generate_coverage_report(self, stats: Dict[str, Any]) -> bool:
        """
        生成文档覆盖率报告
        
        Args:
            stats: 覆盖率统计信息
            
        Returns:
            bool: 生成是否成功
        """
        logger.info("生成文档覆盖率报告...")
        
        try:
            report_path = self.docs_dir / 'documentation_coverage_report.md'
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("# 文档覆盖率报告\n\n")
                f.write(f"生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # 总体统计
                f.write("## 总体统计\n\n")
                f.write(f"- 总文件数: {stats['total_files']}\n")
                f.write(f"- 已文档化文件: {stats['documented_files']}\n")
                f.write(f"- 文件覆盖率: {stats['file_coverage']:.1f}%\n\n")
                
                f.write(f"- 总函数数: {stats['total_functions']}\n")
                f.write(f"- 已文档化函数: {stats['documented_functions']}\n")
                f.write(f"- 函数覆盖率: {stats['function_coverage']:.1f}%\n\n")
                
                f.write(f"- 总类数: {stats['total_classes']}\n")
                f.write(f"- 已文档化类: {stats['documented_classes']}\n")
                f.write(f"- 类覆盖率: {stats['class_coverage']:.1f}%\n\n")
                
                # 问题详情
                if stats['issues']:
                    f.write("## 缺失文档的项目\n\n")
                    f.write("| 文件 | 行号 | 类型 | 名称 | 问题 |\n")
                    f.write("|------|------|------|------|------|\n")
                    
                    for issue in stats['issues'][:50]:  # 限制显示前50个问题
                        f.write(f"| {issue['file']} | {issue['line']} | {issue['type']} | {issue['name']} | {issue['issue']} |\n")
                    
                    if len(stats['issues']) > 50:
                        f.write(f"\n... 还有 {len(stats['issues']) - 50} 个问题未显示\n")
                
                # 建议
                f.write("\n## 改进建议\n\n")
                if stats['file_coverage'] < 80:
                    f.write("- 文件覆盖率较低，建议为主要模块添加文档字符串\n")
                if stats['function_coverage'] < 80:
                    f.write("- 函数覆盖率较低，建议为公共函数添加文档字符串\n")
                if stats['class_coverage'] < 90:
                    f.write("- 类覆盖率较低，建议为所有公共类添加文档字符串\n")
            
            logger.info(f"文档覆盖率报告已生成: {report_path}")
            return True
            
        except Exception as e:
            logger.error(f"生成覆盖率报告时出错: {e}")
            return False
    
    def check_links(self) -> bool:
        """
        检查文档中的链接
        
        Returns:
            bool: 检查是否成功
        """
        logger.info("检查文档链接...")
        
        try:
            # 使用sphinx-build的linkcheck构建器
            cmd = [
                'sphinx-build',
                '-b', 'linkcheck',
                str(self.docs_dir),
                str(self.build_dir / 'linkcheck')
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.warning(f"链接检查发现问题: {result.stdout}")
            else:
                logger.info("所有链接检查通过")
            
            return True
            
        except Exception as e:
            logger.error(f"检查链接时出错: {e}")
            return False
    
    def serve_docs(self, port: int = 8080) -> None:
        """
        启动文档服务器
        
        Args:
            port: 服务器端口
        """
        html_dir = self.build_dir / 'html'
        
        if not html_dir.exists():
            logger.error("HTML文档不存在，请先构建文档")
            return
        
        logger.info(f"启动文档服务器，端口: {port}")
        logger.info(f"访问地址: http://localhost:{port}")
        
        try:
            import http.server
            import socketserver
            import threading
            
            os.chdir(html_dir)
            
            handler = http.server.SimpleHTTPRequestHandler
            httpd = socketserver.TCPServer(("", port), handler)
            
            # 在新线程中启动服务器
            server_thread = threading.Thread(target=httpd.serve_forever)
            server_thread.daemon = True
            server_thread.start()
            
            # 打开浏览器
            webbrowser.open(f'http://localhost:{port}')
            
            print(f"文档服务器已启动: http://localhost:{port}")
            print("按 Ctrl+C 停止服务器")
            
            try:
                while True:
                    import time
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("停止文档服务器")
                httpd.shutdown()
                
        except Exception as e:
            logger.error(f"启动文档服务器时出错: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成项目文档')
    parser.add_argument('--api-only', action='store_true', help='只生成API文档')
    parser.add_argument('--coverage', action='store_true', help='生成文档覆盖率报告')
    parser.add_argument('--serve', action='store_true', help='启动文档服务器')
    parser.add_argument('--check-links', action='store_true', help='检查文档链接')
    parser.add_argument('--port', type=int, default=8080, help='文档服务器端口')
    
    args = parser.parse_args()
    
    # 创建文档生成器
    generator = DocumentationGenerator()
    
    success = True
    
    if args.api_only:
        # 只生成API文档
        success = generator.generate_api_docs()
    elif args.coverage:
        # 生成覆盖率报告
        stats = generator.check_documentation_coverage()
        success = generator.generate_coverage_report(stats)
    elif args.serve:
        # 启动文档服务器
        generator.serve_docs(args.port)
    elif args.check_links:
        # 检查链接
        success = generator.check_links()
    else:
        # 完整的文档生成流程
        logger.info("开始完整的文档生成流程...")
        
        # 1. 生成API文档
        if not generator.generate_api_docs():
            success = False
        
        # 2. 构建HTML文档
        if success and not generator.build_html_docs():
            success = False
        
        # 3. 检查文档覆盖率
        if success:
            stats = generator.check_documentation_coverage()
            generator.generate_coverage_report(stats)
        
        # 4. 检查链接（可选，因为可能比较慢）
        # generator.check_links()
        
        if success:
            logger.info("文档生成完成！")
            logger.info(f"HTML文档: {generator.build_dir / 'html' / 'index.html'}")
            
            # 询问是否启动服务器
            try:
                response = input("是否启动文档服务器? (y/N): ")
                if response.lower() in ['y', 'yes']:
                    generator.serve_docs(args.port)
            except KeyboardInterrupt:
                pass
        else:
            logger.error("文档生成失败")
            sys.exit(1)

if __name__ == '__main__':
    main()