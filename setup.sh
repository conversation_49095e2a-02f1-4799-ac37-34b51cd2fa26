#!/bin/bash
# 量化交易系统快速设置脚本 (Linux/Mac)

echo "🔧 量化交易系统快速设置"
echo "=========================="

# 检查Python版本
python_version=$(python3 --version 2>&1 | grep -oP '\d+\.\d+')
if [[ $(echo "$python_version >= 3.8" | bc -l) -eq 0 ]]; then
    echo "❌ 需要Python 3.8或更高版本，当前版本: $python_version"
    exit 1
fi
echo "✅ Python版本检查通过: $python_version"

# 创建虚拟环境
if [ ! -d "venv" ]; then
    echo "📦 创建虚拟环境..."
    python3 -m venv venv
    echo "✅ 虚拟环境创建完成"
else
    echo "✅ 虚拟环境已存在"
fi

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "📦 安装依赖包..."
pip install --upgrade pip
pip install -r requirements.txt

# 创建必要目录
echo "📁 创建目录结构..."
mkdir -p data logs config

# 初始化数据库
echo "🗄️  初始化数据库..."
python -c "from src.data.manager import DataManager; DataManager(use_sqlite=True)"

# 创建环境变量文件
if [ ! -f ".env" ]; then
    echo "⚙️  创建环境变量文件..."
    cat > .env << EOF
# 数据库配置
DATABASE_URL=sqlite:///data/trading_system.db

# API配置
JWT_SECRET=$(openssl rand -hex 32)
SECRET_KEY=$(openssl rand -hex 32)

# 数据源API密钥（可选）
# TUSHARE_TOKEN=your_tushare_token
# FRED_API_KEY=your_fred_api_key
EOF
    echo "✅ 环境变量文件创建完成"
fi

echo ""
echo "🎉 设置完成！"
echo ""
echo "📋 使用方法:"
echo "  source venv/bin/activate      # 激活虚拟环境"
echo "  python start.py --mode gui    # 启动GUI应用"
echo "  python start.py --mode example # 运行示例"
echo ""
echo "📚 更多信息请查看 docs/user_manual.md"