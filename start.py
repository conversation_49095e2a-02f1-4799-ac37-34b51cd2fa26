#!/usr/bin/env python3
"""
量化交易系统启动脚本
简化的启动流程，替代Docker部署
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✅ Python版本: {sys.version}")

def check_venv():
    """检查虚拟环境"""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 虚拟环境已激活")
        return True
    else:
        print("⚠️  建议在虚拟环境中运行")
        return False

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖包...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ 依赖安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        sys.exit(1)

def init_database():
    """初始化数据库"""
    print("🗄️  初始化数据库...")
    try:
        # 创建数据目录
        os.makedirs("data", exist_ok=True)
        
        # 初始化数据库
        from src.data.manager import DataManager
        data_manager = DataManager(use_sqlite=True)
        print("✅ 数据库初始化完成")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        sys.exit(1)

def start_api():
    """API功能已移除"""
    print("ℹ️  API功能已移除，请使用GUI界面或Python脚本")
    print("  python start.py --mode gui    # 启动GUI应用")
    print("  python start.py --mode example # 运行示例")

def start_gui():
    """启动GUI应用"""
    print("🖥️  启动GUI应用...")
    try:
        subprocess.run([sys.executable, "src/ui/main_app.py"], check=True)
    except KeyboardInterrupt:
        print("\n⏹️  GUI应用已关闭")
    except Exception as e:
        print(f"❌ GUI应用启动失败: {e}")

def run_example():
    """运行示例"""
    print("📊 运行基础示例...")
    try:
        subprocess.run([sys.executable, "examples/basic_usage.py"], check=True)
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")

def main():
    parser = argparse.ArgumentParser(description="量化交易系统启动脚本")
    parser.add_argument("--mode", choices=["gui", "example", "setup"], 
                       default="gui", help="启动模式")
    parser.add_argument("--install-deps", action="store_true", 
                       help="安装依赖包")
    parser.add_argument("--init-db", action="store_true", 
                       help="初始化数据库")
    
    args = parser.parse_args()
    
    print("🔧 量化交易系统启动器")
    print("=" * 40)
    
    # 检查Python版本
    check_python_version()
    
    # 检查虚拟环境
    check_venv()
    
    # 安装依赖
    if args.install_deps or args.mode == "setup":
        install_dependencies()
    
    # 初始化数据库
    if args.init_db or args.mode == "setup":
        init_database()
    
    # 根据模式启动
    if args.mode == "gui":
        start_gui()
    elif args.mode == "example":
        run_example()
    elif args.mode == "setup":
        print("✅ 系统设置完成！")
        print("\n📋 后续步骤:")
        print("  python start.py --mode gui    # 启动GUI应用")
        print("  python start.py --mode example # 运行示例")

if __name__ == "__main__":
    main()