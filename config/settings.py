"""
Configuration management for the quantitative trading system.
"""

import os
import yaml
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class DatabaseConfig:
    """Database configuration."""
    host: str = "localhost"
    port: int = 5432
    database: str = "trading_system"
    username: str = "postgres"
    password: str = ""
    pool_size: int = 10
    echo: bool = False


@dataclass
class DataSourceConfig:
    """Data source configuration."""
    name: str
    adapter_class: str
    enabled: bool = True
    priority: int = 1
    rate_limit: Dict[str, int] = field(default_factory=dict)
    credentials: Dict[str, str] = field(default_factory=dict)
    default_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RiskConfig:
    """Risk management configuration."""
    max_position_size: float = 0.1  # 10% of portfolio
    max_portfolio_risk: float = 0.02  # 2% daily VaR
    stop_loss_pct: float = 0.05  # 5% stop loss
    max_drawdown_pct: float = 0.2  # 20% max drawdown
    max_correlation: float = 0.8  # Maximum correlation between positions
    position_limit_per_symbol: float = 0.05  # 5% per symbol


@dataclass
class BacktestConfig:
    """Backtest configuration."""
    initial_capital: float = 100000.0
    commission_rate: float = 0.001  # 0.1% commission
    slippage_rate: float = 0.0005  # 0.05% slippage
    benchmark_symbol: str = "SPY"
    cash_interest_rate: float = 0.02  # 2% annual interest on cash


@dataclass
class SecurityConfig:
    """Security configuration."""
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    password_min_length: int = 8
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 15

@dataclass
class APIConfig:
    """API configuration."""
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    cors_origins: list = field(default_factory=lambda: ["*"])
    rate_limit: str = "100/minute"
    auth_enabled: bool = True
    jwt_secret: str = "your-secret-key"
    docs_enabled: bool = True
    max_request_size: int = 16 * 1024 * 1024  # 16MB
    request_timeout: int = 30  # seconds


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str = "logs/trading_system.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


class ConfigManager:
    """Configuration manager for the trading system."""
    
    def __init__(self, config_dir: str = "config", environment: str = None):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # Determine environment
        self.environment = environment or os.getenv('TRADING_ENV', 'development')
        
        # Default configurations
        self.database = DatabaseConfig()
        self.risk = RiskConfig()
        self.backtest = BacktestConfig()
        self.api = APIConfig()
        self.security = SecurityConfig()
        self.logging = LoggingConfig()
        self.data_sources: Dict[str, DataSourceConfig] = {}
        
        # Load configurations
        self._load_configurations()
    
    def _load_configurations(self) -> None:
        """Load configurations from files."""
        # Load base configuration
        main_config_file = self.config_dir / "config.yaml"
        if main_config_file.exists():
            with open(main_config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
                self._update_from_dict(config_data)
        
        # Load environment-specific configuration
        env_config_file = self.config_dir / "environments" / f"{self.environment}.yaml"
        if env_config_file.exists():
            with open(env_config_file, 'r', encoding='utf-8') as f:
                env_config_data = yaml.safe_load(f)
                # Environment config overrides base config
                self._update_from_dict(env_config_data)
        
        # Load data sources configuration
        data_sources_file = self.config_dir / "data_sources.yaml"
        if data_sources_file.exists():
            with open(data_sources_file, 'r', encoding='utf-8') as f:
                data_sources_data = yaml.safe_load(f)
                self._load_data_sources(data_sources_data)
        
        # Load environment variables (highest priority)
        self._load_from_environment()
        
        # Expand environment variables in configuration values
        self._expand_environment_variables()
    
    def _update_from_dict(self, config_data: Dict[str, Any]) -> None:
        """Update configuration from dictionary."""
        if 'database' in config_data:
            self._update_dataclass(self.database, config_data['database'])
        
        if 'risk' in config_data:
            self._update_dataclass(self.risk, config_data['risk'])
        
        if 'backtest' in config_data:
            self._update_dataclass(self.backtest, config_data['backtest'])
        
        if 'api' in config_data:
            self._update_dataclass(self.api, config_data['api'])
        
        if 'logging' in config_data:
            self._update_dataclass(self.logging, config_data['logging'])
        
        if 'security' in config_data:
            self._update_dataclass(self.security, config_data['security'])
    
    def _update_dataclass(self, obj, data: Dict[str, Any]) -> None:
        """Update dataclass object with dictionary data."""
        for key, value in data.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
    
    def _load_data_sources(self, data_sources_data: Dict[str, Any]) -> None:
        """Load data sources configuration."""
        for name, config in data_sources_data.items():
            self.data_sources[name] = DataSourceConfig(
                name=name,
                **config
            )
    
    def _load_from_environment(self) -> None:
        """Load configuration from environment variables."""
        # Database
        if os.getenv('DB_HOST'):
            self.database.host = os.getenv('DB_HOST')
        if os.getenv('DB_PORT'):
            self.database.port = int(os.getenv('DB_PORT'))
        if os.getenv('DB_NAME'):
            self.database.database = os.getenv('DB_NAME')
        if os.getenv('DB_USER'):
            self.database.username = os.getenv('DB_USER')
        if os.getenv('DB_PASSWORD'):
            self.database.password = os.getenv('DB_PASSWORD')
        
        # API
        if os.getenv('API_HOST'):
            self.api.host = os.getenv('API_HOST')
        if os.getenv('API_PORT'):
            self.api.port = int(os.getenv('API_PORT'))
        if os.getenv('JWT_SECRET'):
            self.api.jwt_secret = os.getenv('JWT_SECRET')
        
        # Environment
        if os.getenv('DEBUG'):
            self.api.debug = os.getenv('DEBUG').lower() == 'true'
    
    def save_configuration(self) -> None:
        """Save current configuration to files."""
        # Save main configuration
        main_config = {
            'database': self._dataclass_to_dict(self.database),
            'risk': self._dataclass_to_dict(self.risk),
            'backtest': self._dataclass_to_dict(self.backtest),
            'api': self._dataclass_to_dict(self.api),
            'logging': self._dataclass_to_dict(self.logging)
        }
        
        main_config_file = self.config_dir / "config.yaml"
        with open(main_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(main_config, f, default_flow_style=False, allow_unicode=True)
        
        # Save data sources configuration
        data_sources_config = {}
        for name, config in self.data_sources.items():
            data_sources_config[name] = self._dataclass_to_dict(config)
            # Remove name field as it's the key
            data_sources_config[name].pop('name', None)
        
        data_sources_file = self.config_dir / "data_sources.yaml"
        with open(data_sources_file, 'w', encoding='utf-8') as f:
            yaml.dump(data_sources_config, f, default_flow_style=False, allow_unicode=True)
    
    def _dataclass_to_dict(self, obj) -> Dict[str, Any]:
        """Convert dataclass to dictionary."""
        result = {}
        for key, value in obj.__dict__.items():
            if not key.startswith('_'):
                result[key] = value
        return result
    
    def get_data_source_config(self, name: str) -> Optional[DataSourceConfig]:
        """Get data source configuration by name."""
        return self.data_sources.get(name)
    
    def add_data_source_config(self, config: DataSourceConfig) -> None:
        """Add or update data source configuration."""
        self.data_sources[config.name] = config
    
    def _expand_environment_variables(self) -> None:
        """Expand environment variables in configuration values."""
        import re
        
        def expand_value(value):
            if isinstance(value, str):
                # Pattern: ${VAR_NAME:default_value} or ${VAR_NAME}
                pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'
                
                def replace_env_var(match):
                    var_name = match.group(1)
                    default_value = match.group(2) if match.group(2) is not None else ""
                    return os.getenv(var_name, default_value)
                
                return re.sub(pattern, replace_env_var, value)
            return value
        
        # Expand environment variables in all configuration objects
        for config_obj in [self.database, self.api, self.security, self.logging]:
            for attr_name in dir(config_obj):
                if not attr_name.startswith('_'):
                    attr_value = getattr(config_obj, attr_name)
                    if isinstance(attr_value, (str, int, float, bool)):
                        expanded_value = expand_value(attr_value)
                        # Convert back to original type
                        if isinstance(attr_value, int) and isinstance(expanded_value, str):
                            try:
                                expanded_value = int(expanded_value)
                            except ValueError:
                                pass
                        elif isinstance(attr_value, float) and isinstance(expanded_value, str):
                            try:
                                expanded_value = float(expanded_value)
                            except ValueError:
                                pass
                        elif isinstance(attr_value, bool) and isinstance(expanded_value, str):
                            expanded_value = expanded_value.lower() in ('true', '1', 'yes', 'on')
                        
                        setattr(config_obj, attr_name, expanded_value)
    
    def get_database_url(self) -> str:
        """Get database connection URL."""
        return f"postgresql://{self.database.username}:{self.database.password}@{self.database.host}:{self.database.port}/{self.database.database}"
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == 'production'
    
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment == 'development'
    
    def is_testing(self) -> bool:
        """Check if running in testing environment."""
        return self.environment == 'testing'


# Global configuration instance
config = ConfigManager()