# 量化交易系统配置文件

database:
  host: localhost
  port: 5432
  database: trading_system
  username: postgres
  password: ""
  pool_size: 10
  echo: false

risk:
  max_position_size: 0.1  # 单个仓位最大占比 10%
  max_portfolio_risk: 0.02  # 组合最大日风险 2%
  stop_loss_pct: 0.05  # 止损比例 5%
  max_drawdown_pct: 0.2  # 最大回撤 20%
  max_correlation: 0.8  # 最大相关性
  position_limit_per_symbol: 0.05  # 单个标的最大仓位 5%

backtest:
  initial_capital: 100000.0  # 初始资金
  commission_rate: 0.001  # 手续费率 0.1%
  slippage_rate: 0.0005  # 滑点率 0.05%
  benchmark_symbol: "SPY"  # 基准指数
  cash_interest_rate: 0.02  # 现金利率 2%

api:
  host: "0.0.0.0"
  port: 8000
  debug: false
  cors_origins: ["*"]
  rate_limit: "100/minute"
  auth_enabled: true
  jwt_secret: "your-secret-key-change-in-production"

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/trading_system.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5