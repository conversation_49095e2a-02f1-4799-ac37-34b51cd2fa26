# 开发环境配置
environment: development

database:
  host: localhost
  port: 5432
  database: trading_system_dev
  username: postgres
  password: ""
  pool_size: 5
  echo: true  # 开发环境显示SQL
  ssl_mode: disable

risk:
  max_position_size: 0.2  # 开发环境更宽松
  max_portfolio_risk: 0.05
  stop_loss_pct: 0.1
  max_drawdown_pct: 0.3
  max_correlation: 0.9
  position_limit_per_symbol: 0.1

backtest:
  initial_capital: 100000.0
  commission_rate: 0.001
  slippage_rate: 0.0005
  benchmark_symbol: "SPY"
  cash_interest_rate: 0.02

api:
  host: "127.0.0.1"
  port: 8000
  debug: true
  cors_origins: ["*"]
  rate_limit: "1000/minute"
  auth_enabled: false  # 开发环境可以关闭认证
  jwt_secret: "dev-secret-key"
  docs_enabled: true
  max_request_size: 16777216  # 16MB
  request_timeout: 30

security:
  secret_key: "dev-secret-key"
  algorithm: "HS256"
  access_token_expire_minutes: 60
  refresh_token_expire_days: 7
  password_min_length: 6
  max_login_attempts: 10
  lockout_duration_minutes: 5

logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
  file_path: "logs/trading_system_dev.log"
  max_file_size: 10485760  # 10MB
  backup_count: 3
  enable_syslog: false
  enable_json_format: false

monitoring:
  enable_metrics: false
  health_check_interval: 60
  enable_performance_monitoring: false

cache:
  redis_url: "redis://localhost:6379/1"
  default_timeout: 300
  key_prefix: "trading_system_dev:"

worker:
  concurrency: 2
  max_tasks_per_child: 100
  task_soft_time_limit: 60
  task_time_limit: 120