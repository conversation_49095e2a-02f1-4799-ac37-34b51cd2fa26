# 生产环境配置
environment: production

database:
  host: ${DB_HOST:localhost}
  port: ${DB_PORT:5432}
  database: ${DB_NAME:trading_system_prod}
  username: ${DB_USER:postgres}
  password: ${DB_PASSWORD}
  pool_size: 20
  echo: false
  ssl_mode: require
  connection_timeout: 30
  pool_timeout: 30
  pool_recycle: 3600

risk:
  max_position_size: 0.05  # 更保守的5%
  max_portfolio_risk: 0.015  # 1.5%日风险
  stop_loss_pct: 0.03  # 3%止损
  max_drawdown_pct: 0.15  # 15%最大回撤
  max_correlation: 0.7
  position_limit_per_symbol: 0.03

backtest:
  initial_capital: 1000000.0  # 100万初始资金
  commission_rate: 0.0008  # 更低的手续费
  slippage_rate: 0.0003
  benchmark_symbol: "SPY"
  cash_interest_rate: 0.025

api:
  host: "0.0.0.0"
  port: ${API_PORT:8000}
  debug: false
  cors_origins: 
    - "https://yourdomain.com"
    - "https://api.yourdomain.com"
  rate_limit: "1000/minute"
  auth_enabled: true
  jwt_secret: ${JWT_SECRET}
  docs_enabled: false  # 生产环境关闭文档
  max_request_size: 33554432  # 32MB
  request_timeout: 60

security:
  secret_key: ${SECRET_KEY}
  algorithm: "HS256"
  access_token_expire_minutes: 15  # 更短的过期时间
  refresh_token_expire_days: 1
  password_min_length: 12
  max_login_attempts: 3
  lockout_duration_minutes: 30

logging:
  level: "WARNING"  # 生产环境只记录警告和错误
  format: "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
  file_path: "/var/log/trading_system/app.log"
  max_file_size: 52428800  # 50MB
  backup_count: 10
  enable_syslog: true
  syslog_address: "/dev/log"
  enable_json_format: true

monitoring:
  enable_metrics: true
  metrics_port: 9090
  health_check_interval: 30
  alert_webhook_url: ${ALERT_WEBHOOK_URL}
  enable_performance_monitoring: true

cache:
  redis_url: ${REDIS_URL:redis://localhost:6379/0}
  default_timeout: 3600
  key_prefix: "trading_system:"

worker:
  concurrency: 4
  max_tasks_per_child: 1000
  task_soft_time_limit: 300
  task_time_limit: 600