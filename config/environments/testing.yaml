# 测试环境配置
environment: testing

database:
  host: localhost
  port: 5432
  database: trading_system_test
  username: postgres
  password: ""
  pool_size: 2
  echo: false
  ssl_mode: disable

risk:
  max_position_size: 0.5  # 测试环境允许更大仓位
  max_portfolio_risk: 0.1
  stop_loss_pct: 0.2
  max_drawdown_pct: 0.5
  max_correlation: 1.0
  position_limit_per_symbol: 0.5

backtest:
  initial_capital: 10000.0  # 较小的测试资金
  commission_rate: 0.0
  slippage_rate: 0.0
  benchmark_symbol: "SPY"
  cash_interest_rate: 0.0

api:
  host: "127.0.0.1"
  port: 8001
  debug: true
  cors_origins: ["*"]
  rate_limit: "10000/minute"
  auth_enabled: false
  jwt_secret: "test-secret-key"
  docs_enabled: true
  max_request_size: 1048576  # 1MB
  request_timeout: 10

security:
  secret_key: "test-secret-key"
  algorithm: "HS256"
  access_token_expire_minutes: 5
  refresh_token_expire_days: 1
  password_min_length: 4
  max_login_attempts: 100
  lockout_duration_minutes: 1

logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/trading_system_test.log"
  max_file_size: 1048576  # 1MB
  backup_count: 1
  enable_syslog: false
  enable_json_format: false

monitoring:
  enable_metrics: false
  health_check_interval: 300
  enable_performance_monitoring: false

cache:
  redis_url: "redis://localhost:6379/2"
  default_timeout: 60
  key_prefix: "trading_system_test:"

worker:
  concurrency: 1
  max_tasks_per_child: 10
  task_soft_time_limit: 30
  task_time_limit: 60