# 设计文档

## 概述

量化交易系统采用模块化、事件驱动的架构设计，支持多策略并行回测和分析。系统基于Python生态系统构建，利用pandas、numpy等库进行高效的数据处理和计算。整体架构分为数据层、策略层、执行层、分析层和展示层。

## 架构

### 系统架构图

```mermaid
graph TB
    subgraph "展示层"
        UI[Web界面]
        API[REST API]
    end
    
    subgraph "业务逻辑层"
        SM[策略管理器]
        BE[回测引擎]
        RM[风险管理器]
        PA[绩效分析器]
    end
    
    subgraph "数据处理层"
        DM[数据管理器]
        TI[技术指标库]
        DC[数据清洗器]
    end
    
    subgraph "数据存储层"
        TDB[(时间序列数据库)]
        MDB[(元数据库)]
        RDB[(结果数据库)]
    end
    
    UI --> API
    API --> SM
    API --> BE
    API --> PA
    
    SM --> DM
    BE --> DM
    BE --> TI
    BE --> RM
    
    DM --> DC
    DM --> TDB
    
    PA --> RDB
    SM --> MDB
    
    BE --> RDB
```

### 核心组件

1. **数据管理器 (DataManager)** - 负责数据的导入、存储、查询和缓存
2. **策略管理器 (StrategyManager)** - 管理策略的注册、配置和生命周期
3. **回测引擎 (BacktestEngine)** - 核心回测逻辑和事件处理
4. **风险管理器 (RiskManager)** - 实时风险控制和限制检查
5. **绩效分析器 (PerformanceAnalyzer)** - 策略绩效计算和报告生成

## 组件和接口

### 数据管理组件

```python
class DataManager:
    def load_data(self, symbol: str, start_date: datetime, end_date: datetime) -> pd.DataFrame
    def save_data(self, data: pd.DataFrame, symbol: str) -> bool
    def get_available_symbols(self) -> List[str]
    def validate_data(self, data: pd.DataFrame) -> ValidationResult
    def clean_data(self, data: pd.DataFrame) -> pd.DataFrame
    def add_data_source(self, source: DataSourceAdapter) -> None
    def sync_data_from_source(self, source_name: str, symbols: List[str]) -> None
```

### 多数据源适配器架构

```python
class DataSourceAdapter:
    def __init__(self, source_name: str, config: Dict[str, Any])
    def fetch_historical_data(self, symbol: str, start_date: datetime, end_date: datetime, interval: str) -> pd.DataFrame
    def get_available_symbols(self) -> List[str]
    def validate_symbol(self, symbol: str) -> bool
    def normalize_data(self, raw_data: Any) -> pd.DataFrame
    def get_market_info(self, symbol: str) -> MarketInfo

# 美股数据适配器 (Yahoo Finance)
class YahooFinanceAdapter(DataSourceAdapter):
    def fetch_historical_data(self, symbol: str, start_date: datetime, end_date: datetime, interval: str) -> pd.DataFrame
    def get_available_symbols(self) -> List[str]  # S&P 500, NASDAQ等
    def get_market_hours(self) -> Dict[str, str]
    def fetch_dividends(self, symbol: str, start_date: datetime, end_date: datetime) -> pd.DataFrame
    def fetch_splits(self, symbol: str, start_date: datetime, end_date: datetime) -> pd.DataFrame

# A股数据适配器 (Tushare)
class TushareAdapter(DataSourceAdapter):
    def __init__(self, token: str)
    def fetch_historical_data(self, symbol: str, start_date: datetime, end_date: datetime, interval: str) -> pd.DataFrame
    def get_stock_list(self, exchange: str) -> List[str]  # SSE, SZSE
    def fetch_index_data(self, index_code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame
    def get_trading_calendar(self, start_date: datetime, end_date: datetime) -> List[datetime]
    def fetch_financial_data(self, symbol: str, period: str) -> pd.DataFrame

# 币安加密货币适配器
class BinanceAdapter(DataSourceAdapter):
    def __init__(self, api_key: str = None, api_secret: str = None)
    def fetch_historical_data(self, symbol: str, start_date: datetime, end_date: datetime, interval: str) -> pd.DataFrame
    def get_exchange_info(self) -> Dict[str, Any]
    def get_trading_pairs(self) -> List[str]
    def fetch_24hr_ticker(self, symbol: str = None) -> Dict[str, Any]
    def get_kline_intervals(self) -> List[str]  # 1m, 5m, 1h, 1d等

# 美联储经济数据适配器 (FRED)
class FREDAdapter(DataSourceAdapter):
    def __init__(self, api_key: str)
    def fetch_economic_data(self, series_id: str, start_date: datetime, end_date: datetime) -> pd.DataFrame
    def search_series(self, search_text: str, limit: int = 100) -> List[Dict[str, Any]]
    def get_series_info(self, series_id: str) -> Dict[str, Any]
    def get_popular_series(self) -> List[str]  # GDP, CPI, 失业率等热门指标
    def fetch_multiple_series(self, series_ids: List[str], start_date: datetime, end_date: datetime) -> pd.DataFrame
    def get_release_dates(self, series_id: str) -> List[datetime]
    def fetch_real_time_data(self, series_id: str, real_time_start: datetime, real_time_end: datetime) -> pd.DataFrame
```

### 统一数据模型

```python
@dataclass
class MarketInfo:
    symbol: str
    name: str
    market: str  # 'US', 'CN', 'CRYPTO', 'ECONOMIC'
    exchange: str  # 'NASDAQ', 'SSE', 'BINANCE', 'FRED'
    currency: str
    lot_size: float
    tick_size: float
    trading_hours: Dict[str, str]
    timezone: str

@dataclass
class UnifiedMarketData:
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    market: str
    exchange: str
    currency: str
    adj_close: Optional[float] = None
    turnover: Optional[float] = None  # 成交额

@dataclass
class EconomicData:
    series_id: str
    timestamp: datetime
    value: float
    market: str  # 'ECONOMIC'
    source: str  # 'FRED'
    unit: str  # 数据单位
    frequency: str  # 数据频率 (Daily, Weekly, Monthly, Quarterly, Annual)
    seasonal_adjustment: str  # 季节性调整状态
    last_updated: datetime
    notes: Optional[str] = None
```

### 数据源配置管理

```python
@dataclass
class DataSourceConfig:
    name: str
    adapter_class: str
    enabled: bool
    priority: int  # 数据源优先级
    rate_limit: Dict[str, int]  # API调用限制
    credentials: Dict[str, str]
    default_params: Dict[str, Any]

class DataSourceManager:
    def __init__(self, config_file: str)
    def register_adapter(self, config: DataSourceConfig) -> None
    def get_adapter(self, source_name: str) -> DataSourceAdapter
    def sync_all_sources(self, symbols: List[str], start_date: datetime, end_date: datetime) -> None
    def get_best_source_for_symbol(self, symbol: str) -> str
    def handle_rate_limits(self, source_name: str) -> None
```

**数据存储格式：**
- 主键：(symbol, timestamp, market)
- 字段：open, high, low, close, volume, adj_close, turnover
- 索引：时间戳索引，市场索引，支持快速时间范围查询
- 分区：按市场和时间分区存储

### 策略框架

```python
class BaseStrategy:
    def __init__(self, params: Dict[str, Any])
    def initialize(self, context: StrategyContext) -> None
    def on_data(self, data: MarketData) -> List[Signal]
    def on_order_fill(self, fill: OrderFill) -> None
    def get_parameters(self) -> Dict[str, Parameter]
    
class StrategyContext:
    def get_historical_data(self, symbol: str, lookback: int) -> pd.DataFrame
    def get_portfolio(self) -> Portfolio
    def get_indicator(self, name: str, **kwargs) -> pd.Series
```

**策略信号格式：**
```python
@dataclass
class Signal:
    symbol: str
    action: str  # 'BUY', 'SELL', 'HOLD'
    quantity: float
    price: Optional[float]
    timestamp: datetime
    confidence: float
    metadata: Dict[str, Any]
```

### 回测引擎

```python
class BacktestEngine:
    def __init__(self, initial_capital: float, commission: float)
    def add_strategy(self, strategy: BaseStrategy, weight: float) -> None
    def set_data_source(self, data_manager: DataManager) -> None
    def run_backtest(self, start_date: datetime, end_date: datetime) -> BacktestResult
    def get_portfolio_history(self) -> pd.DataFrame
    
class BacktestResult:
    trades: List[Trade]
    portfolio_history: pd.DataFrame
    performance_metrics: Dict[str, float]
    drawdown_series: pd.Series
```

### 风险管理

```python
class RiskManager:
    def __init__(self, config: RiskConfig)
    def check_order(self, order: Order, portfolio: Portfolio) -> RiskCheckResult
    def update_position_limits(self, limits: Dict[str, float]) -> None
    def calculate_var(self, portfolio: Portfolio, confidence: float) -> float
    
@dataclass
class RiskConfig:
    max_position_size: float
    max_portfolio_risk: float
    stop_loss_pct: float
    max_drawdown_pct: float
    max_correlation: float
```

## 数据模型

### 核心数据结构

```python
@dataclass
class MarketData:
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    adj_close: Optional[float] = None

@dataclass
class Trade:
    id: str
    symbol: str
    side: str  # 'BUY' or 'SELL'
    quantity: float
    price: float
    timestamp: datetime
    commission: float
    strategy_id: str

@dataclass
class Portfolio:
    cash: float
    positions: Dict[str, Position]
    total_value: float
    unrealized_pnl: float
    realized_pnl: float

@dataclass
class Position:
    symbol: str
    quantity: float
    avg_price: float
    market_value: float
    unrealized_pnl: float
```

### 数据库设计

**时间序列数据表 (market_data):**
- symbol (VARCHAR) - 标的代码
- timestamp (TIMESTAMP) - 时间戳
- open, high, low, close, volume (FLOAT) - OHLCV数据
- market (VARCHAR) - 市场标识 ('US', 'CN', 'CRYPTO', 'ECONOMIC')
- exchange (VARCHAR) - 交易所 ('NASDAQ', 'SSE', 'BINANCE', 'FRED')
- currency (VARCHAR) - 计价货币
- adj_close (FLOAT) - 复权价格
- turnover (FLOAT) - 成交额
- 主键: (symbol, timestamp, market)
- 索引: timestamp, symbol, market, exchange
- 分区: 按market和年份分区

**经济数据表 (economic_data):**
- series_id (VARCHAR) - FRED序列ID
- timestamp (TIMESTAMP) - 数据时间戳
- value (FLOAT) - 数据值
- market (VARCHAR) - 固定为'ECONOMIC'
- source (VARCHAR) - 数据源 ('FRED')
- unit (VARCHAR) - 数据单位
- frequency (VARCHAR) - 数据频率
- seasonal_adjustment (VARCHAR) - 季节性调整状态
- last_updated (TIMESTAMP) - 最后更新时间
- notes (TEXT) - 备注信息
- 主键: (series_id, timestamp)
- 索引: timestamp, series_id, frequency

**市场信息表 (market_info):**
- symbol (VARCHAR)
- name (VARCHAR) - 标的名称
- market (VARCHAR)
- exchange (VARCHAR)
- currency (VARCHAR)
- lot_size (FLOAT) - 最小交易单位
- tick_size (FLOAT) - 最小价格变动
- trading_hours (JSON) - 交易时间
- timezone (VARCHAR)
- is_active (BOOLEAN)
- 主键: (symbol, market)

**数据源配置表 (data_sources):**
- id (UUID)
- name (VARCHAR) - 数据源名称
- adapter_class (VARCHAR) - 适配器类名
- enabled (BOOLEAN)
- priority (INTEGER)
- rate_limit (JSON) - API限制配置
- credentials (JSON) - 认证信息（加密存储）
- default_params (JSON)
- last_sync (TIMESTAMP)
- created_at (TIMESTAMP)

**策略配置表 (strategies):**
- id (UUID)
- name (VARCHAR)
- class_name (VARCHAR)
- parameters (JSON)
- supported_markets (JSON) - 支持的市场
- created_at (TIMESTAMP)

**回测结果表 (backtest_results):**
- id (UUID)
- strategy_id (UUID)
- start_date, end_date (DATE)
- markets (JSON) - 涉及的市场
- performance_metrics (JSON)
- created_at (TIMESTAMP)

## 错误处理

### 错误分类和处理策略

1. **数据错误**
   - 数据缺失：使用前向填充或插值
   - 数据异常：标记并提供清洗选项
   - 格式错误：拒绝导入并提供错误详情

2. **策略错误**
   - 参数错误：验证并提供默认值
   - 运行时错误：记录日志并跳过当前周期
   - 信号错误：忽略无效信号并记录警告

3. **系统错误**
   - 内存不足：分批处理数据
   - 计算超时：提供进度反馈和取消选项
   - 数据库连接：重试机制和降级处理

```python
class TradingSystemException(Exception):
    pass

class DataException(TradingSystemException):
    pass

class StrategyException(TradingSystemException):
    pass

class RiskException(TradingSystemException):
    pass
```

## 测试策略

### 单元测试

1. **数据管理测试**
   - 数据导入和验证功能
   - 数据查询和缓存机制
   - 数据清洗算法

2. **策略框架测试**
   - 策略基类功能
   - 信号生成逻辑
   - 参数验证机制

3. **回测引擎测试**
   - 事件处理流程
   - 订单执行模拟
   - 绩效计算准确性

### 集成测试

1. **端到端回测流程**
   - 完整的策略回测流程
   - 多策略组合测试
   - 大数据量性能测试

2. **API接口测试**
   - REST API功能测试
   - 错误处理测试
   - 并发访问测试

### 性能测试

1. **数据处理性能**
   - 大量历史数据加载速度
   - 技术指标计算效率
   - 内存使用优化

2. **回测性能**
   - 多年历史数据回测速度
   - 多策略并行处理能力
   - 实时绩效计算效率

### 测试数据

- 使用标准化的测试数据集（如SPY、QQQ等ETF数据）
- 包含各种市场条件（牛市、熊市、震荡市）
- 模拟数据异常情况（缺失值、异常值、停牌等）

## 图形走势分析界面

### 界面架构

图形分析界面采用现代化的Web技术栈，提供交互式的数据可视化和分析工具。

```mermaid
graph TB
    subgraph "前端界面层"
        DC[仪表板控制器]
        CC[图表组件]
        IC[指标组件]
        TC[交易组件]
    end
    
    subgraph "可视化引擎"
        CE[图表引擎]
        IE[指标引擎]
        AE[分析引擎]
    end
    
    subgraph "数据适配层"
        DA[数据适配器]
        RT[实时数据]
        HD[历史数据]
    end
    
    DC --> CC
    DC --> IC
    DC --> TC
    
    CC --> CE
    IC --> IE
    TC --> AE
    
    CE --> DA
    IE --> DA
    AE --> DA
    
    DA --> RT
    DA --> HD
```

### 核心图表组件

#### 1. 主图表区域 (MainChart)

```python
class MainChartComponent:
    def __init__(self, container_id: str)
    def render_candlestick(self, data: pd.DataFrame) -> None
    def add_indicator_overlay(self, indicator: Indicator) -> None
    def add_trade_markers(self, trades: List[Trade]) -> None
    def set_time_range(self, start: datetime, end: datetime) -> None
    def enable_crosshair(self) -> None
    def add_volume_subplot(self) -> None
```

**功能特性：**
- K线图显示（蜡烛图/线图切换）
- 成交量柱状图
- 技术指标叠加显示
- 交易信号标记
- 时间范围缩放和平移
- 十字光标和数据提示

#### 2. 技术指标面板 (IndicatorPanel)

```python
class IndicatorPanel:
    def __init__(self, chart_container: MainChartComponent)
    def add_moving_average(self, period: int, ma_type: str) -> None
    def add_bollinger_bands(self, period: int, std_dev: float) -> None
    def add_rsi(self, period: int) -> None
    def add_macd(self, fast: int, slow: int, signal: int) -> None
    def add_custom_indicator(self, indicator: CustomIndicator) -> None
    def remove_indicator(self, indicator_id: str) -> None
```

**支持的技术指标：**
- 移动平均线（SMA、EMA、WMA）
- 布林带
- RSI相对强弱指标
- MACD指标
- KDJ随机指标
- 威廉指标
- 自定义指标支持

#### 3. 策略分析面板 (StrategyAnalysisPanel)

```python
class StrategyAnalysisPanel:
    def render_equity_curve(self, portfolio_history: pd.DataFrame) -> None
    def render_drawdown_chart(self, drawdown_series: pd.Series) -> None
    def render_trade_distribution(self, trades: List[Trade]) -> None
    def render_monthly_returns(self, returns: pd.Series) -> None
    def render_correlation_heatmap(self, strategies: List[Strategy]) -> None
```

**分析图表类型：**
- 资金曲线图
- 回撤曲线图
- 交易分布直方图
- 月度收益热力图
- 策略相关性矩阵
- 风险收益散点图

#### 4. 实时监控面板 (MonitoringPanel)

```python
class MonitoringPanel:
    def render_position_pie_chart(self, positions: Dict[str, Position]) -> None
    def render_pnl_waterfall(self, pnl_data: pd.DataFrame) -> None
    def render_risk_gauge(self, risk_metrics: RiskMetrics) -> None
    def render_signal_timeline(self, signals: List[Signal]) -> None
```

### 界面布局设计

#### 主界面布局

```
┌─────────────────────────────────────────────────────────────┐
│ 导航栏 [策略管理] [回测] [分析] [监控] [设置]                    │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────┐ ┌─────────────────────────────────┐ │
│ │   策略选择面板        │ │        主图表区域                │ │
│ │ ☑ 移动平均策略       │ │                                 │ │
│ │ ☑ RSI策略           │ │      K线图 + 技术指标            │ │
│ │ ☐ MACD策略          │ │                                 │ │
│ │                     │ │                                 │ │
│ │   参数调整          │ │                                 │ │
│ │ MA周期: [20] [50]   │ │                                 │ │
│ │ RSI阈值: [30] [70]  │ │                                 │ │
│ └─────────────────────┘ └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────┐ ┌─────────────────────────────────┐ │
│ │   绩效指标面板        │ │        策略分析图表              │ │
│ │ 总收益: +15.6%      │ │                                 │ │
│ │ 夏普比率: 1.23      │ │     资金曲线 / 回撤分析          │ │
│ │ 最大回撤: -8.4%     │ │                                 │ │
│ │ 胜率: 58.3%         │ │                                 │ │
│ └─────────────────────┘ └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 交互功能设计

#### 1. 图表交互

```javascript
class ChartInteraction {
    // 时间范围选择
    onTimeRangeSelect(startTime, endTime) {
        this.updateChartData(startTime, endTime);
        this.updateIndicators();
    }
    
    // 指标参数调整
    onIndicatorParamChange(indicatorId, params) {
        this.recalculateIndicator(indicatorId, params);
        this.refreshChart();
    }
    
    // 交易信号点击
    onTradeSignalClick(signal) {
        this.showTradeDetails(signal);
        this.highlightRelatedData(signal);
    }
    
    // 图表缩放和平移
    onChartZoom(zoomLevel) {
        this.adjustTimeScale(zoomLevel);
        this.updateDataGranularity();
    }
}
```

#### 2. 策略对比功能

```python
class StrategyComparison:
    def compare_strategies(self, strategies: List[Strategy]) -> ComparisonResult
    def render_comparison_table(self, comparison: ComparisonResult) -> None
    def render_equity_curves_overlay(self, strategies: List[Strategy]) -> None
    def render_risk_return_scatter(self, strategies: List[Strategy]) -> None
```

#### 3. 自定义分析工具

```python
class CustomAnalysisTools:
    def create_custom_indicator(self, formula: str, params: Dict) -> CustomIndicator
    def save_chart_template(self, template_name: str, config: ChartConfig) -> None
    def export_analysis_report(self, format: str) -> bytes
    def create_alert_rule(self, condition: str, notification: NotificationConfig) -> None
```

### 技术实现

#### 前端技术栈

- **图表库**: TradingView Charting Library 或 D3.js + 自定义组件
- **UI框架**: React.js 或 Vue.js
- **状态管理**: Redux 或 Vuex
- **实时通信**: WebSocket
- **样式框架**: Ant Design 或 Element UI

#### 后端API设计

```python
# 图表数据API
@app.route('/api/chart/data')
def get_chart_data(symbol: str, start: str, end: str, interval: str):
    return jsonify({
        'data': market_data,
        'indicators': calculated_indicators,
        'trades': trade_signals
    })

# 策略绩效API
@app.route('/api/strategy/performance')
def get_strategy_performance(strategy_id: str, period: str):
    return jsonify({
        'metrics': performance_metrics,
        'equity_curve': equity_data,
        'drawdown': drawdown_data
    })
```

#### 性能优化

1. **数据分页加载**: 大量历史数据分批加载
2. **图表虚拟化**: 只渲染可见区域的数据点
3. **指标缓存**: 缓存计算结果避免重复计算
4. **WebSocket更新**: 实时数据推送而非轮询
5. **图表防抖**: 避免频繁的重绘操作