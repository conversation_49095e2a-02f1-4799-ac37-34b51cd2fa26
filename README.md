# 量化交易系统 (Quantitative Trading System)

一个基于Python的综合量化交易系统，支持多策略回测、风险管理和绩效分析。

## 特性

- 🏗️ **模块化架构**: 清晰的项目结构，易于扩展和维护
- 📊 **多数据源支持**: 支持美股(Yahoo Finance)、A股(Akshare)、加密货币(Binance)、经济数据(FRED)
- 🔄 **策略框架**: 灵活的策略开发框架，支持自定义策略
- 📈 **回测引擎**: 事件驱动的回测引擎，支持多策略并行测试
- ⚠️ **风险管理**: 内置风险控制机制，包括仓位限制、止损等
- 📊 **绩效分析**: 全面的绩效指标计算和报告生成
- 🌐 **Web界面**: 现代化的Web界面，支持实时监控和分析
- 🔧 **配置管理**: 灵活的配置系统，支持多环境部署

## 项目结构

```
├── 📁 src/                    # 源代码目录
│   ├── models/               # 数据模型
│   ├── data/                 # 数据管理模块
│   │   └── adapters/         # 外部数据源适配器
│   ├── strategies/           # 策略框架
│   ├── backtest/             # 回测引擎
│   ├── risk/                 # 风险管理
│   ├── analysis/             # 绩效分析
│   ├── indicators/           # 技术指标库
│   ├── ui/                   # 用户界面
│   ├── monitoring/           # 监控系统
│   └── main.py               # 系统主入口
├── 📁 tests/                 # 测试代码
├── 📁 docs/                  # 文档
│   └── completion_summaries/ # 开发记录
├── 📁 config/                # 配置文件
├── 📁 examples/              # 示例代码
├── 📁 tools/                 # 开发工具
├── 📁 scripts/               # 系统脚本
├── 📁 data/                  # 数据文件
├── 📁 logs/                  # 日志文件
└── requirements.txt          # 依赖包
```

详细结构说明请查看 [PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md)

## 🚀 快速开始

### 方法一：自动设置（推荐）

**Linux/Mac:**
```bash
chmod +x setup.sh
./setup.sh
```

**Windows:**
```cmd
setup.bat
```

### 方法二：手动设置

1. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **初始化系统**
```bash
python start.py --mode setup
```

4. **启动应用**
```bash
# 启动GUI应用
python start.py --mode gui

# 或运行示例
python start.py --mode example
```

### 配置系统（可选）

系统使用SQLite数据库，无需额外配置。如需自定义，可编辑 `.env` 文件：

```bash
# 数据库配置
DATABASE_URL=sqlite:///data/trading_system.db

# 数据源API密钥（可选）
TUSHARE_TOKEN=your_tushare_token
FRED_API_KEY=your_fred_api_key
```

## 核心组件

### 数据模型

系统提供了完整的数据模型，包括：

- **MarketData**: 市场数据（OHLCV）
- **Trade**: 交易记录
- **Portfolio**: 投资组合管理
- **Position**: 持仓管理
- **Signal**: 交易信号

```python
from src.models.market_data import MarketData
from src.models.portfolio import Portfolio
from src.models.trading import Trade, OrderSide

# 创建市场数据
data = MarketData(
    symbol="AAPL",
    timestamp=datetime.now(),
    open=150.0, high=155.0, low=149.0, close=154.0,
    volume=1000000
)

# 创建投资组合
portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)

# 执行交易
trade = Trade(
    id="trade_001",
    symbol="AAPL",
    side=OrderSide.BUY,
    quantity=100,
    price=150.0,
    timestamp=datetime.now()
)
portfolio.add_trade(trade)
```

### 配置管理

系统使用YAML配置文件和环境变量进行配置管理：

```python
from config.settings import config

# 访问配置
print(f"初始资金: {config.backtest.initial_capital}")
print(f"手续费率: {config.backtest.commission_rate}")
print(f"风险限制: {config.risk.max_position_size}")
```

### 异常处理

系统提供了完整的异常处理框架：

```python
from src.exceptions import DataException, StrategyException, RiskException

try:
    # 执行交易逻辑
    pass
except RiskException as e:
    print(f"风险控制错误: {e.message}")
    print(f"错误详情: {e.details}")
```

## 开发计划

- [x] **阶段1**: 项目结构和核心接口 ✅
- [ ] **阶段2**: 数据管理模块
- [ ] **阶段3**: 技术指标库
- [ ] **阶段4**: 策略框架
- [ ] **阶段5**: 回测引擎
- [ ] **阶段6**: 风险管理系统
- [ ] **阶段7**: 绩效分析模块
- [ ] **阶段8**: 多策略管理
- [ ] **阶段9**: Web API接口
- [ ] **阶段10**: 图形用户界面
- [ ] **阶段11**: 系统集成和测试

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License

## 📚 完整文档

本项目提供了完整的文档体系，包括：

### 📖 用户文档
- [📚 文档中心](docs/README.md) - 完整的文档导航和概览
- [📖 用户操作手册](docs/user_manual.md) - 从安装到高级功能的完整指南
- [🚀 部署指南](docs/deployment.md) - 系统配置、部署和运维

### 🔧 开发文档  
- [🏗️ 系统架构](docs/system_architecture.md) - 详细的系统设计和架构
- [🔌 API文档](docs/api_documentation.md) - 完整的REST API接口文档
- [📝 代码文档规范](docs/code_documentation_guide.md) - 代码注释和文档标准

### 📈 功能文档
- [📊 示例策略](docs/example_strategies.md) - 内置策略使用说明
- [⚠️ 风险管理](docs/risk_management.md) - 风险控制和监控系统
- [🔍 监控系统](docs/risk_monitoring_system.md) - 实时监控功能

### 🌍 数据源文档
- [🇺🇸 Yahoo Finance](docs/yahoo_finance_adapter.md) - 美股数据源
- [🇨🇳 Akshare](docs/akshare_adapter.md) - A股数据源  
- [🌐 多市场策略](docs/multi_market_strategy_implementation.md) - 跨市场策略
- [📊 经济数据策略](docs/economic_strategies.md) - 宏观经济策略

## 🎯 快速开始

1. **查看文档**: 访问[文档中心](docs/README.md)了解系统全貌
2. **安装系统**: 按照[用户手册](docs/user_manual.md)安装配置
3. **运行示例**: 尝试[示例策略](docs/example_strategies.md)
4. **开发集成**: 参考[API文档](docs/api_documentation.md)

## 联系方式

如有问题或建议，请通过以下方式联系：

- 📧 邮箱: <EMAIL>
- 🐛 GitHub Issues: [项目Issues页面](https://github.com/your-org/quantitative-trading-system/issues)
- 💬 讨论: [GitHub Discussions](https://github.com/your-org/quantitative-trading-system/discussions)
- 📚 文档反馈: 欢迎改进文档内容

---

**注意**: 本系统仅用于教育和研究目的，不构成投资建议。实际交易请谨慎评估风险。