#!/usr/bin/env python3
"""
全面系统测试 - 验证所有已知问题是否已修复
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_comprehensive_system():
    """全面系统测试"""
    print("🔧 量化交易系统全面验证")
    print("=" * 50)
    
    all_tests_passed = True
    
    # 1. 测试技术指标列表获取
    print("\n📊 测试技术指标列表获取...")
    try:
        from src.indicators.engine import IndicatorEngine
        
        engine = IndicatorEngine()
        
        # 测试 get_available_indicators 返回格式
        available_indicators = engine.get_available_indicators()
        if not isinstance(available_indicators, dict):
            print(f"❌ get_available_indicators() 返回类型错误: {type(available_indicators)}, 期望: dict")
            all_tests_passed = False
        else:
            print(f"✅ get_available_indicators() 返回正确格式: {len(available_indicators)} 个指标")
        
        # 测试 get_indicator_names 返回格式
        indicator_names = engine.get_indicator_names()
        if not isinstance(indicator_names, list):
            print(f"❌ get_indicator_names() 返回类型错误: {type(indicator_names)}, 期望: list")
            all_tests_passed = False
        else:
            print(f"✅ get_indicator_names() 返回正确格式: {len(indicator_names)} 个指标")
            
    except Exception as e:
        print(f"❌ 技术指标测试失败: {e}")
        all_tests_passed = False
    
    # 2. 测试风险管理器变量作用域
    print("\n⚠️  测试风险管理器变量作用域...")
    try:
        from src.risk.manager import RiskManager
        from src.models.portfolio import Portfolio
        
        risk_manager = RiskManager()
        portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        
        # 测试计算风险指标 - 这里之前有 position_values 变量作用域问题
        metrics = risk_manager.calculate_risk_metrics(portfolio)
        
        if metrics is None:
            print("❌ 风险指标计算返回 None")
            all_tests_passed = False
        else:
            print(f"✅ 风险指标计算成功: 投资组合价值 {metrics.portfolio_value}")
            
    except Exception as e:
        print(f"❌ 风险管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        all_tests_passed = False
    
    # 3. 测试数据源健康检查返回格式
    print("\n🔌 测试数据源健康检查...")
    try:
        from src.data.adapters.manager import DataSourceManager
        
        manager = DataSourceManager()
        
        # 测试健康检查返回格式
        health_results = manager.health_check_all()
        
        if not isinstance(health_results, dict):
            print(f"❌ health_check_all() 返回类型错误: {type(health_results)}, 期望: dict")
            all_tests_passed = False
        else:
            print(f"✅ health_check_all() 返回正确格式: {len(health_results)} 个数据源")
            
            # 检查每个数据源的返回格式
            for name, result in health_results.items():
                if not isinstance(result, dict):
                    print(f"❌ 数据源 {name} 返回格式错误: {type(result)}")
                    all_tests_passed = False
                elif 'healthy' not in result or 'message' not in result:
                    print(f"❌ 数据源 {name} 缺少必要字段: {result.keys()}")
                    all_tests_passed = False
                else:
                    print(f"  ✅ 数据源 {name}: {'健康' if result['healthy'] else '不健康'}")
            
    except Exception as e:
        print(f"❌ 数据源健康检查测试失败: {e}")
        import traceback
        traceback.print_exc()
        all_tests_passed = False
    
    # 4. 测试回测引擎信号处理
    print("\n🔄 测试回测引擎信号处理...")
    try:
        from src.backtest.engine import BacktestEngine
        from src.strategies.base import BaseStrategy
        from src.models.trading import Signal, SignalAction
        from src.models.market_data import UnifiedMarketData
        import pandas as pd
        import numpy as np
        
        # 创建简单测试策略
        from src.strategies.parameters import StrategyParameter, ParameterType
        
        class TestStrategy(BaseStrategy):
            def __init__(self):
                super().__init__("test_strategy", {"period": 10})
            
            def get_parameter_definitions(self):
                return {
                    "period": StrategyParameter(
                        name="period",
                        param_type=ParameterType.INTEGER,
                        default_value=10,
                        min_value=1,
                        max_value=100,
                        description="Period for calculation"
                    )
                }
            
            def initialize(self, context):
                pass
            
            def on_data(self, context, data):
                pass
            
            def generate_signals(self, data: pd.DataFrame) -> list:
                if len(data) > 0:
                    signal = Signal(
                        symbol="TEST",
                        action=SignalAction.BUY,  # 使用 action 而不是 signal_type
                        quantity=100,
                        price=data['close'].iloc[-1],
                        timestamp=datetime.now(),
                        confidence=0.8,
                        strategy_id=self.name
                    )
                    return [signal]
                return []
        
        # 创建回测引擎
        engine = BacktestEngine(initial_capital=100000.0)
        strategy = TestStrategy()
        engine.add_strategy(strategy)
        
        # 创建测试数据 (DataFrame格式)
        dates = pd.date_range(start='2023-01-01', periods=5, freq='D')
        test_data = pd.DataFrame({
            'timestamp': dates,
            'symbol': ['TEST'] * 5,
            'open': [100.0 + i for i in range(5)],
            'high': [105.0 + i for i in range(5)],
            'low': [95.0 + i for i in range(5)],
            'close': [102.0 + i for i in range(5)],
            'volume': [1000] * 5
        })
        
        # 设置数据源并运行回测
        engine.set_data_feed(test_data)
        result = engine.run_backtest()
        
        if result is None:
            print("❌ 回测结果为 None")
            all_tests_passed = False
        else:
            print(f"✅ 回测运行成功: 状态 {result.status}")
            
    except Exception as e:
        print(f"❌ 回测引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        all_tests_passed = False
    
    # 5. 测试信号验证
    print("\n📡 测试信号验证...")
    try:
        from src.models.trading import Signal, SignalAction
        
        # 创建测试信号
        signal = Signal(
            symbol="TEST",
            action=SignalAction.BUY,
            quantity=100,
            price=150.0,
            timestamp=datetime.now(),
            confidence=0.8,
            strategy_id="test_strategy"
        )
        
        # 测试验证方法
        is_valid = signal.validate()
        
        if not is_valid:
            print("❌ 信号验证失败")
            all_tests_passed = False
        else:
            print("✅ 信号验证成功")
            
    except Exception as e:
        print(f"❌ 信号验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        all_tests_passed = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 所有测试通过！系统运行正常。")
        return True
    else:
        print("❌ 发现问题需要修复。")
        return False

if __name__ == "__main__":
    success = test_comprehensive_system()
    sys.exit(0 if success else 1)