# 项目结构说明

## 📁 整理后的项目结构

```
quantitative-trading-system/
├── 📁 .kiro/                          # Kiro IDE配置
│   └── specs/quantitative-trading-system/
├── 📁 config/                         # 配置文件
│   ├── config.yaml                    # 主配置文件
│   ├── data_sources.yaml             # 数据源配置
│   └── settings.py                   # 配置管理
├── 📁 data/                          # 数据文件目录
│   ├── trading_system.db             # SQLite数据库
│   ├── cache/                        # 缓存文件
│   └── exports/                      # 导出文件
├── 📁 docs/                          # 文档目录
│   ├── completion_summaries/         # 任务完成总结
│   │   ├── TASK_*.md                 # 各任务完成报告
│   │   ├── API_REMOVAL_SUMMARY.md    # API移除总结
│   │   └── DOCKER_REMOVAL_SUMMARY.md # Docker移除总结
│   ├── user_manual.md                # 用户手册
│   ├── system_architecture.md        # 系统架构
│   ├── deployment.md                 # 部署指南
│   └── README.md                     # 文档导航
├── 📁 examples/                      # 示例代码
│   ├── basic_usage.py                # 基础使用示例
│   ├── strategy_examples/            # 策略示例
│   └── data_analysis/                # 数据分析示例
├── 📁 logs/                          # 日志文件
│   ├── app.log                       # 应用日志
│   ├── error.log                     # 错误日志
│   └── trading.log                   # 交易日志
├── 📁 scripts/                       # 系统脚本
│   ├── database/                     # 数据库相关脚本
│   ├── monitoring/                   # 监控脚本
│   ├── deploy.sh                     # 部署脚本
│   ├── start_system.py               # 系统启动脚本
│   ├── system_status.py              # 系统状态检查
│   └── validate_config.py            # 配置验证
├── 📁 src/                           # 源代码目录
│   ├── 📁 analysis/                  # 绩效分析模块
│   │   ├── metrics.py                # 绩效指标计算
│   │   ├── reporting.py              # 报告生成
│   │   └── comparison.py             # 策略对比
│   ├── 📁 backtest/                  # 回测引擎
│   │   ├── engine.py                 # 回测引擎核心
│   │   ├── events.py                 # 事件系统
│   │   ├── executor.py               # 订单执行器
│   │   └── result.py                 # 回测结果
│   ├── 📁 data/                      # 数据管理模块
│   │   ├── adapters/                 # 数据源适配器
│   │   │   ├── yahoo_finance.py      # Yahoo Finance
│   │   │   ├── akshare_adapter.py    # Akshare A股 (免费)
│   │   │   ├── binance_adapter.py    # Binance 加密货币
│   │   │   ├── fred_adapter.py       # FRED 经济数据
│   │   │   └── manager.py            # 适配器管理
│   │   ├── manager.py                # 数据管理器
│   │   ├── database.py               # 数据库管理
│   │   ├── repository.py             # 数据访问层
│   │   └── migrations.py             # 数据库迁移
│   ├── 📁 indicators/                # 技术指标库
│   │   ├── engine.py                 # 指标计算引擎
│   │   ├── basic.py                  # 基础指标
│   │   ├── momentum.py               # 动量指标
│   │   └── volatility.py             # 波动率指标
│   ├── 📁 models/                    # 数据模型
│   │   ├── base.py                   # 基础模型
│   │   ├── market_data.py            # 市场数据模型
│   │   ├── trading.py                # 交易模型
│   │   └── portfolio.py              # 投资组合模型
│   ├── 📁 monitoring/                # 监控系统
│   │   ├── system_monitor.py         # 系统监控
│   │   ├── health_check.py           # 健康检查
│   │   └── audit_logger.py           # 审计日志
│   ├── 📁 risk/                      # 风险管理
│   │   ├── manager.py                # 风险管理器
│   │   ├── models.py                 # 风险模型
│   │   ├── rules.py                  # 风险规则
│   │   └── monitors.py               # 风险监控
│   ├── 📁 strategies/                # 策略框架
│   │   ├── base.py                   # 策略基类
│   │   ├── signals.py                # 信号管理
│   │   ├── parameters.py             # 参数管理
│   │   ├── examples/                 # 示例策略
│   │   └── multi_strategy_manager.py # 多策略管理
│   ├── 📁 ui/                        # 用户界面
│   │   ├── main_app.py               # 主GUI应用
│   │   ├── charts/                   # 图表组件
│   │   ├── strategy_management/      # 策略管理界面
│   │   ├── data_source_management/   # 数据源管理界面
│   │   ├── analysis_reporting/       # 分析报告界面
│   │   └── monitoring/               # 监控界面
│   ├── main.py                       # 系统主入口
│   ├── exceptions.py                 # 异常处理
│   └── system_validator.py           # 系统验证器
├── 📁 tests/                         # 测试代码
│   ├── test_data_*.py                # 数据模块测试
│   ├── test_strategies_*.py          # 策略模块测试
│   ├── test_backtest_*.py            # 回测模块测试
│   └── test_risk_*.py                # 风险模块测试
├── 📁 tools/                         # 工具脚本
│   ├── verify_system.py              # 系统验证工具
│   ├── setup.py                      # 安装配置
│   └── generate_docs.py              # 文档生成工具
├── 📁 backup/                        # 备份目录
├── 📁 temp/                          # 临时文件目录
├── .env                              # 环境变量
├── .gitignore                        # Git忽略文件
├── README.md                         # 项目说明
├── requirements.txt                  # Python依赖
├── setup.sh                          # Linux/Mac设置脚本
├── setup.bat                         # Windows设置脚本
├── start.py                          # 启动脚本
└── PROJECT_STRUCTURE.md              # 本文件
```

## 📋 目录说明

### 🔧 **核心目录**
- **`src/`**: 所有源代码，按功能模块组织
- **`config/`**: 配置文件，支持多环境配置
- **`data/`**: 数据文件，包括数据库和缓存
- **`tests/`**: 单元测试和集成测试

### 📚 **文档目录**
- **`docs/`**: 完整的文档体系
- **`docs/completion_summaries/`**: 开发过程记录
- **`examples/`**: 使用示例和教程

### 🛠️ **工具目录**
- **`tools/`**: 开发和维护工具
- **`scripts/`**: 系统管理脚本
- **`logs/`**: 日志文件存储

### 🗂️ **辅助目录**
- **`backup/`**: 数据备份
- **`temp/`**: 临时文件
- **`.kiro/`**: IDE配置

## 🚀 **使用指南**

### 快速开始
```bash
# 1. 设置环境
./setup.sh  # Linux/Mac 或 setup.bat (Windows)

# 2. 启动系统
python start.py --mode gui

# 3. 验证系统
python tools/verify_system.py
```

### 开发使用
```bash
# 启动主系统
python src/main.py

# 运行示例
python examples/basic_usage.py

# 运行测试
python -m pytest tests/

# 生成文档
python tools/generate_docs.py
```

## 📊 **模块依赖关系**

```
UI界面 ←→ 核心业务逻辑 ←→ 外部数据API
   ↓           ↓              ↓
策略管理 ←→ 数据管理 ←→ 数据源适配器
   ↓           ↓
回测引擎 ←→ 风险管理
   ↓           ↓
绩效分析 ←→ 技术指标
```

## 🔄 **数据流向**

```
外部数据源 → 数据适配器 → 数据管理器 → 策略引擎
                                    ↓
GUI界面 ← 绩效分析 ← 回测引擎 ← 风险管理
```

## 📝 **文件命名规范**

- **模块文件**: 小写+下划线 (`data_manager.py`)
- **类名**: 大驼峰 (`DataManager`)
- **函数名**: 小写+下划线 (`get_market_data`)
- **常量**: 大写+下划线 (`MAX_POSITION_SIZE`)
- **配置文件**: 小写+下划线 (`config.yaml`)
- **文档文件**: 大写+下划线 (`README.md`)

## 🎯 **维护建议**

1. **定期清理**: 清理 `temp/` 和 `logs/` 目录
2. **备份数据**: 定期备份 `data/` 目录到 `backup/`
3. **更新文档**: 代码变更时同步更新文档
4. **运行测试**: 提交前运行完整测试套件
5. **版本管理**: 使用语义化版本号

## 📈 **扩展指南**

- **新增策略**: 在 `src/strategies/examples/` 添加
- **新增指标**: 在 `src/indicators/` 添加
- **新增数据源**: 在 `src/data/adapters/` 添加
- **新增测试**: 在 `tests/` 对应目录添加
- **新增文档**: 在 `docs/` 添加并更新导航

这个结构设计遵循了模块化、可扩展、易维护的原则，适合个人开发和长期维护。