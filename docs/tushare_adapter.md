# Tushare Adapter Documentation

## Overview

The TushareAdapter provides access to Chinese A-share market data through the Tushare Pro API. It supports fetching historical data for stocks and indices, market information, trading calendars, and financial data.

## Features

### Core Functionality
- **Historical Data**: Fetch OHLCV data for A-share stocks and major indices
- **Market Information**: Get detailed information about stocks and indices
- **Symbol Validation**: Validate stock codes and index codes
- **Trading Calendar**: Get A-share trading days and holidays
- **Financial Data**: Access fundamental financial indicators
- **Stock Search**: Search stocks by name or code
- **Exchange Filtering**: Get stocks by exchange (SSE/SZSE)

### Supported Markets
- **Shanghai Stock Exchange (SSE)**: Stocks ending with `.SH`
- **Shenzhen Stock Exchange (SZSE)**: Stocks ending with `.SZ`
- **Major Indices**: 上证指数, 深证成指, 创业板指, 沪深300, etc.

### Data Types
- **Stock Data**: Individual stock OHLCV data with volume and turnover
- **Index Data**: Market index data with constituent information
- **Financial Data**: Fundamental financial indicators and ratios
- **Calendar Data**: Trading days and market holidays

## Configuration

### Basic Configuration

```yaml
tushare:
  adapter_class: "src.data.adapters.tushare_adapter.TushareAdapter"
  enabled: true
  priority: 2
  rate_limit:
    requests_per_minute: 200
    requests_per_day: 10000
  credentials:
    token: "your_tushare_pro_token"
  default_params:
    adj: "qfq"  # 前复权
```

### Environment Variables

```bash
export TUSHARE_TOKEN="your_tushare_pro_token"
```

### Getting a Tushare Token

1. Visit [Tushare Pro](https://tushare.pro/register)
2. Register for a free account
3. Get your API token from the user center
4. Note: Some features require paid subscription

## Usage Examples

### Basic Usage

```python
from src.data.adapters.base import DataSourceConfig
from src.data.adapters.tushare_adapter import TushareAdapter
from datetime import datetime, timedelta

# Configuration
config = DataSourceConfig(
    name='tushare',
    adapter_class='src.data.adapters.tushare_adapter.TushareAdapter',
    enabled=True,
    priority=1,
    rate_limit={'requests_per_minute': 200},
    credentials={'token': 'your_token_here'},
    default_params={}
)

# Initialize adapter
adapter = TushareAdapter(config)

# Fetch historical data
end_date = datetime.now()
start_date = end_date - timedelta(days=30)
data = adapter.fetch_historical_data('000001.SZ', start_date, end_date)

print(f"Fetched {len(data)} records for 平安银行")
```

### Working with Indices

```python
# Fetch Shanghai Composite Index data
index_data = adapter.fetch_historical_data('000001.SH', start_date, end_date)

# Get index information
market_info = adapter.get_market_info('000001.SH')
print(f"Index: {market_info.name}")  # 上证指数
```

### Market Information

```python
# Get stock information
market_info = adapter.get_market_info('000001.SZ')
print(f"Stock: {market_info.name}")      # 平安银行
print(f"Exchange: {market_info.exchange}") # SZSE
print(f"Currency: {market_info.currency}") # CNY
print(f"Lot Size: {market_info.lot_size}") # 100.0 (1手)
```

### Trading Calendar

```python
# Get trading days
trading_days = adapter.get_trading_calendar(start_date, end_date)
print(f"Trading days: {len(trading_days)}")

# Check if a specific date is a trading day
is_trading = adapter.is_trading_day(datetime(2024, 1, 1))
print(f"2024-01-01 is trading day: {is_trading}")  # False (New Year)
```

### Stock Search

```python
# Search by name
banks = adapter.search_stocks('银行', limit=10)
for bank in banks:
    print(f"{bank['symbol']}: {bank['name']}")

# Search by code
results = adapter.search_stocks('000001')
```

### Financial Data

```python
# Get financial indicators
financial_data = adapter.fetch_financial_data('000001.SZ', period='annual')
if financial_data is not None:
    print("Financial indicators available")
```

### Exchange-Specific Data

```python
# Get all stocks from Shanghai Stock Exchange
sse_stocks = adapter.get_stock_list_by_exchange('SSE')
print(f"SSE stocks: {len(sse_stocks)}")

# Get all stocks from Shenzhen Stock Exchange  
szse_stocks = adapter.get_stock_list_by_exchange('SZSE')
print(f"SZSE stocks: {len(szse_stocks)}")
```

## Data Format

### UnifiedMarketData Structure

```python
@dataclass
class UnifiedMarketData:
    symbol: str          # Stock code (e.g., '000001.SZ')
    timestamp: datetime  # Trading date
    open: float         # Opening price
    high: float         # Highest price
    low: float          # Lowest price
    close: float        # Closing price
    volume: float       # Volume (shares, converted from 手)
    market: str         # 'CN'
    exchange: str       # 'SSE' or 'SZSE'
    currency: str       # 'CNY'
    adj_close: float    # Adjusted closing price
    turnover: float     # Turnover amount (CNY)
```

### MarketInfo Structure

```python
@dataclass
class MarketInfo:
    symbol: str              # Stock/Index code
    name: str               # Chinese name
    market: str             # 'CN'
    exchange: str           # 'SSE' or 'SZSE'
    currency: str           # 'CNY'
    lot_size: float         # 100.0 for stocks, 1.0 for indices
    tick_size: float        # 0.01 (minimum price increment)
    trading_hours: dict     # Market hours
    timezone: str           # 'Asia/Shanghai'
    is_active: bool         # Whether actively traded
```

## Major Indices Supported

| Code | Name | Description |
|------|------|-------------|
| 000001.SH | 上证指数 | Shanghai Composite Index |
| 399001.SZ | 深证成指 | Shenzhen Component Index |
| 399006.SZ | 创业板指 | ChiNext Index |
| 000300.SH | 沪深300 | CSI 300 Index |
| 000905.SH | 中证500 | CSI 500 Index |
| 000852.SH | 中证1000 | CSI 1000 Index |
| 000016.SH | 上证50 | SSE 50 Index |
| 000688.SH | 科创50 | STAR 50 Index |

## Trading Hours

A-share markets operate with the following schedule:

- **Morning Session**: 09:30 - 11:30 (Asia/Shanghai)
- **Afternoon Session**: 13:00 - 15:00 (Asia/Shanghai)
- **Weekends**: Closed
- **Holidays**: Closed (Chinese national holidays)

## Rate Limits

Tushare Pro has the following default limits:

- **Free Users**: 200 requests/minute, 10,000 requests/day
- **Paid Users**: Higher limits based on subscription level
- **Points System**: Some data requires points consumption

## Error Handling

The adapter handles various error conditions:

```python
try:
    data = adapter.fetch_historical_data('INVALID', start_date, end_date)
except DataException as e:
    print(f"Data error: {e}")
```

Common errors:
- Invalid token
- Rate limit exceeded
- Invalid symbol
- No data available
- Network connectivity issues

## Caching

The adapter implements intelligent caching:

- **Symbol Lists**: Cached for 24 hours
- **Market Info**: Cached for 4 hours
- **Trading Calendar**: Cached for 1 day
- **Symbol Validation**: Cached for 4 hours (positive) / shorter for negative

## Performance Tips

1. **Batch Requests**: Fetch data for multiple symbols in separate calls
2. **Use Caching**: Repeated calls for the same data use cached results
3. **Respect Rate Limits**: The adapter automatically handles rate limiting
4. **Choose Appropriate Date Ranges**: Smaller ranges fetch faster
5. **Use Indices for Market Overview**: Indices load faster than individual stocks

## Troubleshooting

### Common Issues

1. **Token Error**
   ```
   DataException: Tushare token is required in credentials
   ```
   Solution: Set valid token in configuration or environment variable

2. **Rate Limit Exceeded**
   ```
   Rate limit reached, waiting X seconds
   ```
   Solution: Wait or upgrade to paid subscription

3. **No Data Returned**
   ```
   No data returned for symbol
   ```
   Solution: Check symbol format, date range, and market holidays

4. **Invalid Symbol**
   ```
   Invalid symbol: XXXXX
   ```
   Solution: Use correct format (e.g., '000001.SZ', not '000001')

### Health Check

```python
is_healthy, message = adapter.health_check()
if not is_healthy:
    print(f"Adapter issue: {message}")
```

## Integration with Data Manager

```python
from src.data.adapters.manager import DataSourceManager

# The adapter integrates automatically with DataSourceManager
manager = DataSourceManager('config/data_sources.yaml')

# Fetch data through manager (will use best available adapter)
success, data = manager.fetch_historical_data(
    '000001.SZ', start_date, end_date, preferred_adapter='tushare'
)
```

## Limitations

1. **Real-time Data**: Requires premium subscription
2. **Historical Depth**: Free accounts have limited historical data
3. **Financial Data**: Some financial indicators require paid access
4. **Rate Limits**: Free accounts have strict rate limits
5. **Market Coverage**: Primarily A-share markets (no Hong Kong, US markets)

## Best Practices

1. **Store Token Securely**: Use environment variables or secure config
2. **Handle Rate Limits**: Implement proper retry logic
3. **Cache Results**: Don't repeatedly fetch the same data
4. **Validate Symbols**: Always validate symbols before fetching data
5. **Monitor Usage**: Track API usage to avoid limits
6. **Error Handling**: Implement comprehensive error handling
7. **Use Appropriate Intervals**: Choose the right data frequency for your needs

## Support

For issues with the Tushare API itself:
- [Tushare Documentation](https://tushare.pro/document/2)
- [Tushare Community](https://waditu.com/)

For adapter-specific issues:
- Check the test files for usage examples
- Review the adapter source code
- Run the example script to verify setup