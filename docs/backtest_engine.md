# 回测引擎文档

## 概述

回测引擎是量化交易系统的核心组件，提供事件驱动的历史数据回测功能。它能够模拟真实的交易环境，包括订单执行、滑点、手续费等，为策略开发和验证提供可靠的测试平台。

## 核心特性

### 1. 事件驱动架构
- 基于事件队列的处理机制
- 支持市场数据事件、信号事件、订单事件和成交事件
- 时间序列数据的逐步处理，确保无未来数据泄露

### 2. 真实交易模拟
- 订单执行模拟，包括滑点和市场冲击
- 可配置的手续费和交易成本
- 支持多种订单类型（市价单、限价单等）
- 资金管理和风险控制

### 3. 策略集成
- 支持多策略并行回测
- 策略生命周期管理
- 策略参数验证和配置
- 策略性能独立跟踪

### 4. 性能分析
- 全面的回测结果分析
- 风险指标计算（夏普比率、最大回撤等）
- 交易统计和盈亏分析
- 资金曲线和回撤序列

## 架构组件

### 1. BacktestEngine (回测引擎)
主要的回测控制器，负责：
- 数据流管理
- 事件处理循环
- 策略执行协调
- 结果收集和分析

### 2. Event System (事件系统)
包含以下事件类型：
- `MarketEvent`: 市场数据更新事件
- `SignalEvent`: 策略信号事件
- `OrderEvent`: 订单下单事件
- `FillEvent`: 订单成交事件

### 3. OrderExecutor (订单执行器)
模拟真实的订单执行：
- 滑点计算
- 手续费计算
- 市场冲击模拟
- 订单验证和风险检查

### 4. BacktestResult (回测结果)
提供全面的回测分析：
- 基础统计指标
- 风险指标
- 交易分析
- 时间序列数据

## 使用方法

### 基本使用流程

```python
from src.backtest.engine import BacktestEngine
from src.backtest.executor import ExecutionConfig
from src.strategies.base import BaseStrategy

# 1. 创建回测引擎
engine = BacktestEngine(
    initial_capital=100000.0,
    execution_config=ExecutionConfig(
        commission_rate=0.001,
        slippage_rate=0.0005
    )
)

# 2. 添加策略
strategy = MyStrategy(name="TestStrategy")
engine.add_strategy(strategy)

# 3. 设置数据源
engine.set_data_feed(market_data_df)

# 4. 运行回测
result = engine.run_backtest()

# 5. 分析结果
print(f"总收益率: {result.total_return:.2%}")
print(f"夏普比率: {result.sharpe_ratio:.2f}")
print(f"最大回撤: {result.max_drawdown:.2%}")
```

### 数据格式要求

市场数据DataFrame必须包含以下列：
- `symbol`: 交易标的代码
- `open`: 开盘价
- `high`: 最高价
- `low`: 最低价
- `close`: 收盘价
- `volume`: 成交量
- 索引必须是时间戳

### 策略开发

策略必须继承`BaseStrategy`类并实现以下方法：

```python
class MyStrategy(BaseStrategy):
    def get_parameter_definitions(self):
        # 返回策略参数定义
        pass
    
    def initialize(self, context):
        # 策略初始化
        pass
    
    def on_data(self, data):
        # 处理市场数据，返回信号列表
        return signals
```

## 配置选项

### ExecutionConfig (执行配置)

```python
ExecutionConfig(
    commission_rate=0.001,      # 手续费率 (0.1%)
    slippage_rate=0.0005,       # 滑点率 (0.05%)
    market_impact_rate=0.0001,  # 市场冲击率 (0.01%)
    partial_fill_probability=0.0,  # 部分成交概率
    rejection_probability=0.0      # 订单拒绝概率
)
```

## 性能指标

### 基础指标
- `total_return`: 总收益率
- `annualized_return`: 年化收益率
- `volatility`: 波动率（年化）
- `sharpe_ratio`: 夏普比率
- `max_drawdown`: 最大回撤

### 交易统计
- `total_trades`: 总交易次数
- `win_rate`: 胜率
- `profit_factor`: 盈亏比
- `avg_win`: 平均盈利
- `avg_loss`: 平均亏损

## 进度跟踪

回测引擎支持进度跟踪和回调：

```python
def progress_callback(progress):
    print(f"进度: {progress.get_progress_pct():.1f}%")

engine.set_progress_callback(progress_callback)
```

## 最佳实践

### 1. 数据质量
- 确保数据完整性和准确性
- 处理缺失数据和异常值
- 使用适当的数据频率

### 2. 策略设计
- 避免前瞻偏差
- 实现合理的风险管理
- 考虑交易成本和滑点

### 3. 回测设置
- 使用足够长的历史数据
- 设置合理的交易成本参数
- 进行样本外测试

### 4. 结果分析
- 关注风险调整后的收益
- 分析回撤期间和恢复时间
- 检查交易频率和成本影响

## 扩展功能

### 自定义订单执行
可以继承`OrderExecutor`类实现自定义的订单执行逻辑：

```python
class CustomExecutor(OrderExecutor):
    def execute_order(self, order, market_data, portfolio):
        # 自定义执行逻辑
        pass
```

### 多资产回测
引擎支持多资产组合的回测：

```python
# 数据包含多个标的
multi_asset_data = pd.DataFrame({
    'symbol': ['AAPL', 'GOOGL', 'MSFT', ...],
    'open': [...],
    'high': [...],
    # ...
})
```

## 注意事项

1. **内存使用**: 大量历史数据可能消耗较多内存
2. **计算性能**: 复杂策略可能影响回测速度
3. **数据对齐**: 确保多资产数据的时间对齐
4. **参数敏感性**: 测试不同参数组合的稳定性

## 故障排除

### 常见问题

1. **导入错误**: 确保正确设置Python路径
2. **数据格式错误**: 检查DataFrame列名和数据类型
3. **策略错误**: 验证策略逻辑和参数设置
4. **内存不足**: 减少数据量或优化策略代码

### 调试技巧

1. 使用小数据集进行初步测试
2. 启用详细日志记录
3. 检查中间结果和状态
4. 使用单元测试验证组件功能

## 示例代码

完整的使用示例请参考 `examples/backtest_example.py` 文件。