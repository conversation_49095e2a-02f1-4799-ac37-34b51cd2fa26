# Risk Monitoring System Documentation

## Overview

The Risk Monitoring System provides comprehensive real-time risk oversight for the quantitative trading system. It implements continuous monitoring of risk metrics, threshold-based alerting, event logging, and audit trail functionality to ensure portfolio safety and regulatory compliance.

## Key Features

### 1. Real-time Risk Indicator Monitoring
- Continuous monitoring of portfolio risk metrics
- Configurable risk thresholds with warning and critical levels
- Multi-threaded monitoring loop for real-time updates
- Support for custom risk indicators

### 2. Risk Threshold Alert Mechanisms
- Configurable threshold-based alerting system
- Multiple severity levels (INFO, WARNING, ERROR, CRITICAL)
- Alert cooldown mechanisms to prevent spam
- Alert suppression capabilities
- Automatic alert escalation

### 3. Risk Event Logging and Audit Functionality
- Comprehensive event logging for all risk-related activities
- Persistent storage in SQLite database
- Audit trail for compliance and investigation
- Event categorization and filtering
- Historical event analysis

### 4. Risk Control Decision Recording
- Recording of all risk management decisions
- User attribution and timestamps
- Decision metadata and context
- Integration with alert resolution workflow

## Architecture

### Core Components

```
RiskMonitoringSystem
├── AlertManager (Alert generation and management)
├── EventLogger (Event logging and audit trail)
├── ThresholdManager (Threshold configuration and checking)
├── DatabaseManager (Persistent storage)
└── CallbackManager (External integrations)
```

### Data Models

#### RiskAlert
```python
@dataclass
class RiskAlert:
    id: str
    timestamp: datetime
    severity: AlertSeverity
    status: AlertStatus
    title: str
    message: str
    source: str
    symbol: Optional[str] = None
    strategy_id: Optional[str] = None
    violation: Optional[RiskViolation] = None
    threshold_value: Optional[float] = None
    current_value: Optional[float] = None
    metadata: Dict[str, Any] = None
```

#### RiskEvent
```python
@dataclass
class RiskEvent:
    id: str
    timestamp: datetime
    event_type: str
    source: str
    description: str
    severity: AlertSeverity
    symbol: Optional[str] = None
    strategy_id: Optional[str] = None
    user_id: Optional[str] = None
    decision_taken: Optional[str] = None
    metadata: Dict[str, Any] = None
```

#### RiskThreshold
```python
@dataclass
class RiskThreshold:
    name: str
    metric_name: str
    warning_threshold: float
    critical_threshold: float
    comparison_operator: str  # 'gt', 'lt', 'eq', 'gte', 'lte'
    enabled: bool = True
    cooldown_minutes: int = 5
    description: str = ""
```

## Usage

### Basic Setup

```python
from src.risk.monitoring_system import RiskMonitoringSystem
from src.risk.manager import RiskManager
from src.risk.models import RiskConfig

# Create risk configuration
risk_config = RiskConfig(
    max_position_size_pct=0.10,
    max_drawdown_pct=0.15,
    daily_risk_budget=0.02
)

# Create risk manager
risk_manager = RiskManager(risk_config)

# Create monitoring system
monitoring_system = RiskMonitoringSystem(
    risk_manager=risk_manager,
    config=risk_config,
    db_path="data/risk_monitoring.db"
)

# Start monitoring
monitoring_system.start_monitoring()
```

### Alert Callbacks

```python
def alert_callback(alert):
    """Handle risk alerts."""
    if alert.severity == AlertSeverity.CRITICAL:
        # Send immediate notification
        send_emergency_notification(alert)
    elif alert.severity == AlertSeverity.ERROR:
        # Log to monitoring system
        log_high_priority_alert(alert)

# Add callback
monitoring_system.add_alert_callback(alert_callback)
```

### Custom Thresholds

```python
# Create custom threshold
custom_threshold = RiskThreshold(
    name="Sector Concentration",
    metric_name="tech_sector_pct",
    warning_threshold=60.0,
    critical_threshold=80.0,
    comparison_operator="gt",
    description="Technology sector concentration limit"
)

# Add to monitoring system
monitoring_system.add_threshold(custom_threshold)
```

### Alert Management

```python
# Get active alerts
active_alerts = monitoring_system.get_active_alerts()

# Acknowledge alert
monitoring_system.acknowledge_alert(
    alert_id="alert-123",
    user_id="risk_manager",
    notes="Investigating position concentration issue"
)

# Resolve alert
monitoring_system.resolve_alert(
    alert_id="alert-123",
    user_id="risk_manager",
    resolution_notes="Reduced position size to acceptable level"
)
```

### Decision Recording

```python
# Record risk management decision
monitoring_system.record_decision(
    decision_type="emergency_stop",
    description="Activated emergency stop due to excessive drawdown",
    user_id="risk_manager",
    symbol="AAPL",
    metadata={
        "drawdown_pct": 18.5,
        "trigger_threshold": 15.0,
        "action_taken": "halt_all_trading"
    }
)
```

## Default Risk Thresholds

The system comes with pre-configured thresholds for common risk metrics:

| Threshold Name | Metric | Warning | Critical | Description |
|----------------|--------|---------|----------|-------------|
| Portfolio Drawdown | current_drawdown_pct | 10% | 15% | Portfolio drawdown from peak |
| Daily Loss Limit | daily_loss_pct | 3% | 5% | Daily portfolio loss percentage |
| Position Concentration | largest_position_pct | 15% | 25% | Largest single position percentage |
| Leverage Ratio | gross_leverage | 1.5 | 2.0 | Portfolio gross leverage ratio |
| VaR 1-Day | portfolio_var_1d | $50k | $100k | 1-day Value at Risk |
| Risk Budget Utilization | daily_risk_used_pct | 80% | 95% | Daily risk budget utilization |
| Cash Level | cash_pct | 5% | 2% | Available cash percentage |

## Database Schema

### risk_alerts Table
```sql
CREATE TABLE risk_alerts (
    id TEXT PRIMARY KEY,
    timestamp TEXT NOT NULL,
    severity TEXT NOT NULL,
    status TEXT NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    source TEXT NOT NULL,
    symbol TEXT,
    strategy_id TEXT,
    threshold_value REAL,
    current_value REAL,
    metadata TEXT,
    acknowledged_by TEXT,
    acknowledged_at TEXT,
    resolved_at TEXT
);
```

### risk_events Table
```sql
CREATE TABLE risk_events (
    id TEXT PRIMARY KEY,
    timestamp TEXT NOT NULL,
    event_type TEXT NOT NULL,
    source TEXT NOT NULL,
    description TEXT NOT NULL,
    severity TEXT NOT NULL,
    symbol TEXT,
    strategy_id TEXT,
    user_id TEXT,
    portfolio_value REAL,
    risk_metrics TEXT,
    decision_taken TEXT,
    metadata TEXT
);
```

### risk_metrics_log Table
```sql
CREATE TABLE risk_metrics_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp TEXT NOT NULL,
    portfolio_value REAL NOT NULL,
    metrics_data TEXT NOT NULL
);
```

## Monitoring Reports

### System Status Report
```python
status = monitoring_system.get_monitoring_status()
# Returns:
{
    'status': 'running',
    'start_time': '2025-01-01T10:00:00',
    'last_update': '2025-01-01T10:30:00',
    'uptime_seconds': 1800,
    'active_alerts': 3,
    'active_alerts_by_severity': {
        'warning': 2,
        'critical': 1
    },
    'total_alerts_generated': 15,
    'total_events_logged': 45,
    'violations_detected': 8,
    'decisions_recorded': 3
}
```

### Comprehensive Monitoring Report
```python
report = monitoring_system.generate_monitoring_report(hours=24)
# Returns detailed report with:
# - System status and uptime
# - Alert summary by severity
# - Event summary by type
# - Threshold breach analysis
# - Performance statistics
# - Configuration summary
```

## Integration with Risk Manager

The monitoring system integrates seamlessly with the existing RiskManager:

```python
# Risk manager automatically sends violations to monitoring system
risk_manager.add_alert_callback(monitoring_system._handle_risk_violation)

# Monitoring system can trigger risk manager actions
def critical_alert_handler(alert):
    if alert.severity == AlertSeverity.CRITICAL:
        risk_manager.activate_emergency_stop()

monitoring_system.add_alert_callback(critical_alert_handler)
```

## Performance Considerations

### Memory Management
- Alert history limited to 10,000 entries
- Event history limited to 10,000 entries
- Risk metrics history limited to 1,000 entries
- Automatic cleanup of old database records

### Threading
- Monitoring runs in separate daemon thread
- Thread-safe operations with locks
- Graceful shutdown handling

### Database Optimization
- Indexed tables for fast queries
- Automatic cleanup of old records
- Efficient storage of JSON metadata

## Error Handling

### Monitoring Loop Resilience
```python
# Monitoring loop continues despite errors
try:
    self._perform_monitoring_cycle()
except Exception as e:
    self.logger.error(f"Error in monitoring cycle: {e}")
    self.status = MonitoringStatus.ERROR
    self._stop_event.wait(5.0)  # Wait longer on error
```

### Database Error Recovery
```python
# Graceful handling of database errors
try:
    self._persist_alert(alert)
except Exception as e:
    self.logger.error(f"Error persisting alert: {e}")
    # Continue operation without failing
```

## Configuration

### Environment Variables
- `RISK_MONITORING_DB_PATH`: Database file path
- `RISK_MONITORING_LOG_LEVEL`: Logging level
- `RISK_MONITORING_CLEANUP_DAYS`: Days to keep old records

### Configuration File Support
```yaml
# risk_monitoring.yaml
thresholds:
  portfolio_drawdown:
    warning: 10.0
    critical: 15.0
    enabled: true
  position_concentration:
    warning: 15.0
    critical: 25.0
    enabled: true

alerts:
  cooldown_minutes: 5
  max_active_alerts: 100

database:
  path: "data/risk_monitoring.db"
  cleanup_days: 30
```

## Testing

### Unit Tests
- Complete test coverage for all components
- Mock database operations for fast testing
- Threading and concurrency tests

### Integration Tests
- End-to-end monitoring workflow tests
- Database persistence tests
- Alert callback tests

### Performance Tests
- High-frequency monitoring tests
- Memory usage tests
- Database performance tests

## Compliance and Audit

### Regulatory Requirements
- Complete audit trail of all risk events
- Immutable event logging
- User attribution for all actions
- Timestamp accuracy and timezone handling

### Audit Reports
- Comprehensive event history
- Decision tracking and justification
- Alert response times
- System availability metrics

## Best Practices

### Threshold Configuration
1. Start with conservative thresholds
2. Monitor false positive rates
3. Adjust based on market conditions
4. Document threshold changes

### Alert Management
1. Acknowledge alerts promptly
2. Provide detailed resolution notes
3. Track alert response times
4. Regular review of alert patterns

### System Maintenance
1. Regular database cleanup
2. Monitor system performance
3. Review and update thresholds
4. Test alert callbacks regularly

## Troubleshooting

### Common Issues

#### High Alert Volume
```python
# Check threshold sensitivity
status = monitoring_system.get_monitoring_status()
if status['total_alerts_generated'] > expected_threshold:
    # Review and adjust thresholds
    monitoring_system.suppress_alert_type("Portfolio Drawdown", 60)
```

#### Database Performance
```python
# Monitor database size and performance
import os
db_size = os.path.getsize("data/risk_monitoring.db")
if db_size > 100_000_000:  # 100MB
    # Trigger cleanup
    monitoring_system._cleanup_old_data()
```

#### Memory Usage
```python
# Monitor memory usage
import psutil
memory_usage = psutil.Process().memory_info().rss
if memory_usage > 500_000_000:  # 500MB
    # Reduce history limits
    monitoring_system.alert_history = deque(maxlen=5000)
```

## Future Enhancements

### Planned Features
1. Machine learning-based anomaly detection
2. Advanced correlation analysis
3. Real-time dashboard integration
4. Mobile alert notifications
5. Advanced reporting and analytics

### API Extensions
1. REST API for external integrations
2. WebSocket support for real-time updates
3. Webhook notifications
4. Third-party monitoring system integration

## Conclusion

The Risk Monitoring System provides comprehensive real-time risk oversight with robust alerting, logging, and audit capabilities. It integrates seamlessly with the existing risk management infrastructure while providing the flexibility to customize thresholds and responses based on specific requirements.

For additional support or questions, please refer to the example implementations in `examples/risk_monitoring_example.py` or contact the development team.