# Makefile for Sphinx documentation
#

# You can set these variables from the command line, and also
# from the environment for the first two.
SPHINXOPTS    ?=
SPHINXBUILD  ?= sphinx-build
SOURCEDIR    = .
BUILDDIR     = build

# Put it first so that "make" without argument is like "make help".
help:
	@$(SPHINXBUILD) -M help "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

.PHONY: help Makefile

# Custom targets for our documentation

# Generate API documentation
apidoc:
	@echo "生成API文档..."
	@sphinx-apidoc -o api ../src --separate --force --module-first
	@echo "API文档生成完成"

# Build HTML documentation
html: apidoc
	@echo "构建HTML文档..."
	@$(SPHINXBUILD) -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O)
	@echo "HTML文档构建完成: $(BUILDDIR)/html/index.html"

# Build PDF documentation
pdf: apidoc
	@echo "构建PDF文档..."
	@$(SPHINXBUILD) -b latex "$(SOURCEDIR)" "$(BUILDDIR)/latex" $(SPHINXOPTS) $(O)
	@cd $(BUILDDIR)/latex && make
	@echo "PDF文档构建完成: $(BUILDDIR)/latex/quantitative-trading-system.pdf"

# Check documentation coverage
coverage:
	@echo "检查文档覆盖率..."
	@python ../scripts/generate_docs.py --coverage
	@echo "文档覆盖率检查完成"

# Check links in documentation
linkcheck:
	@echo "检查文档链接..."
	@$(SPHINXBUILD) -b linkcheck "$(SOURCEDIR)" "$(BUILDDIR)/linkcheck" $(SPHINXOPTS) $(O)
	@echo "链接检查完成"

# Serve documentation locally
serve: html
	@echo "启动文档服务器..."
	@python ../scripts/generate_docs.py --serve

# Clean build directory
clean:
	@echo "清理构建目录..."
	@rm -rf $(BUILDDIR)/*
	@rm -rf api/
	@echo "清理完成"

# Full documentation build (HTML + PDF + Coverage)
full: clean html pdf coverage
	@echo "完整文档构建完成"

# Quick build for development
dev: apidoc
	@echo "快速构建开发文档..."
	@$(SPHINXBUILD) -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" -E $(SPHINXOPTS) $(O)
	@echo "开发文档构建完成"

# Watch for changes and rebuild automatically (requires sphinx-autobuild)
watch:
	@echo "启动自动重建模式..."
	@sphinx-autobuild "$(SOURCEDIR)" "$(BUILDDIR)/html" --ignore "$(BUILDDIR)" --ignore "api"

# Validate documentation
validate: linkcheck coverage
	@echo "文档验证完成"

# Deploy documentation (placeholder for CI/CD)
deploy: full
	@echo "部署文档..."
	@echo "注意: 请配置实际的部署脚本"

# Show documentation statistics
stats:
	@echo "文档统计信息:"
	@find . -name "*.md" -o -name "*.rst" | wc -l | xargs echo "文档文件数量:"
	@find ../src -name "*.py" | wc -l | xargs echo "Python文件数量:"
	@find . -name "*.md" -o -name "*.rst" | xargs wc -l | tail -1 | awk '{print "文档总行数: " $$1}'

# Help for custom targets
help-custom:
	@echo "自定义目标:"
	@echo "  apidoc      生成API文档"
	@echo "  html        构建HTML文档"
	@echo "  pdf         构建PDF文档"
	@echo "  coverage    检查文档覆盖率"
	@echo "  linkcheck   检查文档链接"
	@echo "  serve       启动文档服务器"
	@echo "  clean       清理构建目录"
	@echo "  full        完整文档构建"
	@echo "  dev         快速开发构建"
	@echo "  watch       自动重建模式"
	@echo "  validate    验证文档质量"
	@echo "  deploy      部署文档"
	@echo "  stats       显示文档统计"

# Catch-all target: route all unknown targets to Sphinx using the new
# "make mode" option.  $(O) is meant as a shortcut for $(SPHINXOPTS).
%: Makefile
	@$(SPHINXBUILD) -M $@ "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)