# 量化交易系统项目结构文档

## 项目概述

量化交易系统是一个基于Python的多策略回测平台，支持多市场数据源（美股、A股、加密货币、经济数据），提供完整的策略开发、回测、风险管理和绩效分析功能。

## 项目结构

```
quantitative-trading-system/
├── .kiro/                          # Kiro配置和规格文档
│   └── specs/
│       └── quantitative-trading-system/
│           ├── requirements.md      # 需求文档
│           ├── design.md           # 设计文档
│           └── tasks.md            # 任务列表
├── config/                         # 配置文件目录
│   ├── __init__.py
│   ├── config.yaml                 # 主配置文件
│   ├── data_sources.yaml          # 数据源配置
│   └── settings.py                 # 配置管理模块
├── docs/                           # 文档目录
│   ├── README.md                   # 项目说明
│   └── project_structure.md       # 项目结构文档
├── examples/                       # 示例代码
│   ├── __init__.py
│   └── basic_usage.py             # 基础使用示例
├── logs/                          # 日志文件目录
├── src/                           # 源代码目录
│   ├── __init__.py                # 主模块初始化
│   ├── exceptions.py              # 异常处理框架
│   ├── system_validator.py        # 系统验证模块
│   └── models/                    # 数据模型
│       ├── __init__.py
│       ├── base.py                # 基础模型类
│       ├── market_data.py         # 市场数据模型
│       ├── trading.py             # 交易相关模型
│       └── portfolio.py           # 投资组合模型
├── tests/                         # 测试代码
│   ├── __init__.py
│   └── test_models.py            # 模型测试
├── requirements.txt               # Python依赖
├── setup.py                      # 安装配置
└── README.md                     # 项目主说明
```

## 核心组件

### 1. 数据模型 (src/models/)

#### 基础模型 (base.py)
- `BaseModel`: 所有数据模型的基类，提供通用功能
  - `to_dict()`: 转换为字典
  - `to_json()`: 转换为JSON字符串
  - `from_dict()`: 从字典创建实例
  - `validate()`: 数据验证

#### 市场数据模型 (market_data.py)
- `MarketData`: 基础OHLCV市场数据
- `UnifiedMarketData`: 统一的多市场数据格式
- `EconomicData`: 经济数据模型（支持FRED等数据源）
- `MarketInfo`: 市场信息和交易规则

#### 交易模型 (trading.py)
- `Order`: 订单数据结构
- `OrderFill`: 订单成交记录
- `Trade`: 交易记录
- `Signal`: 交易信号
- 枚举类型：`OrderSide`, `OrderType`, `OrderStatus`, `SignalAction`

#### 投资组合模型 (portfolio.py)
- `Position`: 持仓数据结构
- `Portfolio`: 投资组合管理
  - 现金管理
  - 持仓跟踪
  - 盈亏计算
  - 交易记录

### 2. 异常处理框架 (exceptions.py)

#### 异常层次结构
```
TradingSystemException (基础异常)
├── DataException (数据相关异常)
│   ├── DataValidationException
│   ├── DataSourceException
│   └── DataFormatException
├── StrategyException (策略相关异常)
│   ├── StrategyConfigurationException
│   └── StrategyExecutionException
├── RiskException (风险管理异常)
│   ├── RiskLimitException
│   ├── InsufficientFundsException
│   └── PositionLimitException
├── BacktestException (回测异常)
│   ├── BacktestConfigurationException
│   └── BacktestExecutionException
├── PortfolioException (投资组合异常)
├── OrderException (订单异常)
│   ├── OrderValidationException
│   └── OrderExecutionException
├── ConfigurationException (配置异常)
├── APIException (API异常)
└── DatabaseException (数据库异常)
```

#### 错误处理工具
- `ErrorHandler`: 集中化错误处理工具
  - 错误转换和包装
  - 结构化日志记录
  - API错误响应生成

### 3. 配置管理系统 (config/)

#### 配置结构
- `DatabaseConfig`: 数据库配置
- `DataSourceConfig`: 数据源配置
- `RiskConfig`: 风险管理配置
- `BacktestConfig`: 回测配置
- `APIConfig`: API服务配置
- `LoggingConfig`: 日志配置

#### 配置管理器
- `ConfigManager`: 统一配置管理
  - YAML文件加载
  - 环境变量支持
  - 配置验证和保存
  - 数据源配置管理

### 4. 系统验证 (system_validator.py)

#### 验证功能
- 配置系统验证
- 数据模型验证
- 交易模型验证
- 投资组合模型验证
- 异常处理验证

#### 验证报告
- 详细的测试结果
- 成功率统计
- 错误详情记录

## 设计特点

### 1. 模块化设计
- 清晰的模块分离
- 松耦合架构
- 易于扩展和维护

### 2. 多市场支持
- 统一的数据模型
- 支持美股、A股、加密货币
- 经济数据集成

### 3. 强类型系统
- 使用dataclass和类型注解
- 枚举类型确保数据一致性
- 运行时数据验证

### 4. 异常处理
- 分层异常体系
- 结构化错误信息
- 集中化错误处理

### 5. 配置管理
- 分离的配置文件
- 环境变量支持
- 动态配置加载

## 使用示例

### 基础数据模型使用

```python
from src import MarketData, Portfolio, Trade, OrderSide
from datetime import datetime

# 创建市场数据
market_data = MarketData(
    symbol="AAPL",
    timestamp=datetime.now(),
    open=150.0,
    high=155.0,
    low=148.0,
    close=152.0,
    volume=1000000
)

# 创建投资组合
portfolio = Portfolio(
    initial_capital=100000.0,
    cash=100000.0
)

# 创建交易
trade = Trade(
    id="trade_001",
    symbol="AAPL",
    side=OrderSide.BUY,
    quantity=100,
    price=150.0,
    timestamp=datetime.now()
)

# 添加交易到投资组合
portfolio.add_trade(trade)
```

### 配置管理使用

```python
from config.settings import config

# 获取数据库配置
db_config = config.database
print(f"Database: {db_config.host}:{db_config.port}")

# 获取风险配置
risk_config = config.risk
print(f"Max position size: {risk_config.max_position_size}")

# 获取数据源配置
yahoo_config = config.get_data_source_config("yahoo_finance")
if yahoo_config:
    print(f"Yahoo Finance enabled: {yahoo_config.enabled}")
```

### 异常处理使用

```python
from src.exceptions import DataException, ErrorHandler

try:
    # 一些可能出错的操作
    pass
except Exception as e:
    # 使用错误处理器
    handler = ErrorHandler()
    data_error = handler.handle_data_error(e, "数据处理")
    
    # 记录错误
    handler.log_error(data_error)
    
    # 生成API响应
    response = handler.create_error_response(data_error)
```

## 系统验证

运行系统验证以确保所有组件正常工作：

```bash
python -m src.system_validator
```

验证包括：
- 配置管理系统
- 数据模型功能
- 交易模型功能
- 投资组合管理
- 异常处理机制

## 下一步开发

根据任务列表，接下来的开发重点：

1. **数据管理模块** (任务2)
   - 数据存储和访问层
   - 数据验证和清洗
   - 多数据源集成

2. **技术指标库** (任务3)
   - 基础技术指标实现
   - 指标计算引擎
   - 自定义指标框架

3. **策略框架** (任务4)
   - 策略基础类和接口
   - 信号生成和管理
   - 示例策略实现

4. **回测引擎** (任务5)
   - 核心回测逻辑
   - 订单执行模拟
   - 投资组合管理

## 总结

项目结构和核心接口已经建立完成，包括：

✅ **标准Python项目目录结构** - 完整的目录结构，包括src、tests、docs、config等

✅ **核心数据模型类** - 实现了MarketData、Trade、Portfolio、Position等核心模型

✅ **基础异常类和错误处理框架** - 完整的异常体系和错误处理工具

✅ **配置管理模块和环境设置** - 灵活的配置管理系统，支持多种配置方式

系统验证显示所有核心组件都正常工作，为后续开发奠定了坚实的基础。