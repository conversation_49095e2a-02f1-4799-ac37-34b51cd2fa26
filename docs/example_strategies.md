# 示例策略实现文档

本文档描述了量化交易系统中实现的四个示例策略，展示了策略框架的使用方法和最佳实践。

## 策略概览

| 策略名称 | 类型 | 主要指标 | 适用市场 | 复杂度 |
|---------|------|---------|---------|--------|
| 移动平均交叉策略 | 趋势跟踪 | SMA/EMA/WMA | 趋势性市场 | 简单 |
| RSI均值回归策略 | 均值回归 | RSI | 震荡性市场 | 简单 |
| MACD趋势跟踪策略 | 趋势跟踪 | MACD | 趋势性市场 | 中等 |
| 多因子组合策略 | 综合型 | MA+RSI+MACD+布林带 | 各种市场 | 复杂 |

## 1. 移动平均交叉策略 (MovingAverageCrossoverStrategy)

### 策略原理
基于快速移动平均线和慢速移动平均线的交叉信号进行交易：
- **金叉**：快速MA上穿慢速MA时产生买入信号
- **死叉**：快速MA下穿慢速MA时产生卖出信号

### 关键参数
- `fast_period`: 快速移动平均周期 (默认: 5)
- `slow_period`: 慢速移动平均周期 (默认: 20)
- `ma_type`: 移动平均类型 (SMA/EMA/WMA, 默认: SMA)
- `min_crossover_strength`: 最小交叉强度，避免假突破 (默认: 0.001)
- `stop_loss_pct`: 止损百分比 (默认: 5%)
- `take_profit_pct`: 止盈百分比 (默认: 10%)

### 适用场景
- 趋势明显的市场环境
- 中长期交易
- 适合初学者理解技术分析基础

### 使用示例
```python
from src.strategies.examples import MovingAverageCrossoverStrategy

# 创建策略实例
strategy = MovingAverageCrossoverStrategy(
    name="MA_5_20",
    parameters={
        'fast_period': 5,
        'slow_period': 20,
        'ma_type': 'EMA',
        'stop_loss_pct': 0.03
    }
)

# 初始化策略
strategy.initialize(context)

# 处理市场数据
signals = strategy.on_data(market_data)
```

## 2. RSI均值回归策略 (RSIMeanReversionStrategy)

### 策略原理
基于RSI指标的超买超卖信号进行均值回归交易：
- **超卖买入**：RSI < 超卖阈值时产生买入信号
- **超买卖出**：RSI > 超买阈值时产生卖出信号
- **中性平仓**：RSI回归到中性区域时平仓

### 关键参数
- `rsi_period`: RSI计算周期 (默认: 14)
- `oversold_threshold`: 超卖阈值 (默认: 30)
- `overbought_threshold`: 超买阈值 (默认: 70)
- `neutral_exit_lower`: 中性区域下限，多头平仓 (默认: 45)
- `neutral_exit_upper`: 中性区域上限，空头平仓 (默认: 55)
- `min_rsi_change`: 最小RSI变化幅度，避免噪音 (默认: 2.0)

### 适用场景
- 震荡性市场环境
- 短中期交易
- 适合区间交易和反转策略

### 使用示例
```python
from src.strategies.examples import RSIMeanReversionStrategy

# 创建策略实例
strategy = RSIMeanReversionStrategy(
    name="RSI_MeanRev",
    parameters={
        'rsi_period': 14,
        'oversold_threshold': 25,
        'overbought_threshold': 75,
        'stop_loss_pct': 0.03
    }
)
```

## 3. MACD趋势跟踪策略 (MACDTrendFollowingStrategy)

### 策略原理
基于MACD指标的趋势跟踪交易：
- **金叉买入**：MACD线上穿信号线时产生买入信号
- **死叉卖出**：MACD线下穿信号线时产生卖出信号
- **零轴过滤**：可选择只在MACD>0时买入，MACD<0时卖出
- **柱状图确认**：可选择要求柱状图方向与信号一致

### 关键参数
- `fast_period`: MACD快速EMA周期 (默认: 12)
- `slow_period`: MACD慢速EMA周期 (默认: 26)
- `signal_period`: MACD信号线EMA周期 (默认: 9)
- `require_zero_line_filter`: 是否启用零轴过滤 (默认: True)
- `require_histogram_confirmation`: 是否需要柱状图确认 (默认: True)
- `min_crossover_strength`: 最小交叉强度 (默认: 0.001)

### 适用场景
- 趋势性市场环境
- 中期交易
- 适合捕捉趋势的开始和结束

### 使用示例
```python
from src.strategies.examples import MACDTrendFollowingStrategy

# 创建策略实例
strategy = MACDTrendFollowingStrategy(
    name="MACD_Trend",
    parameters={
        'fast_period': 12,
        'slow_period': 26,
        'signal_period': 9,
        'require_zero_line_filter': True,
        'require_histogram_confirmation': True
    }
)
```

## 4. 多因子组合策略 (MultiFactorStrategy)

### 策略原理
结合多个技术指标的综合交易策略：
- **移动平均信号**：快慢MA交叉信号
- **RSI信号**：超买超卖信号
- **MACD信号**：趋势跟踪信号
- **布林带信号**：价格位置信号
- **加权综合**：根据权重计算综合信号强度
- **阈值判断**：信号强度超过阈值时产生交易信号

### 关键参数
#### 移动平均参数
- `ma_fast_period`: 快速MA周期 (默认: 10)
- `ma_slow_period`: 慢速MA周期 (默认: 30)
- `ma_weight`: MA指标权重 (默认: 0.3)

#### RSI参数
- `rsi_period`: RSI周期 (默认: 14)
- `rsi_oversold`: RSI超卖阈值 (默认: 30)
- `rsi_overbought`: RSI超买阈值 (默认: 70)
- `rsi_weight`: RSI指标权重 (默认: 0.25)

#### MACD参数
- `macd_fast`: MACD快线周期 (默认: 12)
- `macd_slow`: MACD慢线周期 (默认: 26)
- `macd_signal`: MACD信号线周期 (默认: 9)
- `macd_weight`: MACD指标权重 (默认: 0.25)

#### 布林带参数
- `bb_period`: 布林带周期 (默认: 20)
- `bb_std`: 布林带标准差倍数 (默认: 2.0)
- `bb_weight`: 布林带指标权重 (默认: 0.2)

#### 信号阈值
- `buy_threshold`: 买入信号阈值 (默认: 0.6)
- `sell_threshold`: 卖出信号阈值 (默认: -0.6)

### 适用场景
- 各种市场环境
- 中长期交易
- 适合追求稳定性和多样化的投资者

### 使用示例
```python
from src.strategies.examples import MultiFactorStrategy

# 创建策略实例
strategy = MultiFactorStrategy(
    name="MultiFactor",
    parameters={
        'ma_weight': 0.3,
        'rsi_weight': 0.25,
        'macd_weight': 0.25,
        'bb_weight': 0.2,
        'buy_threshold': 0.65,
        'sell_threshold': -0.65
    }
)
```

## 策略使用最佳实践

### 1. 参数优化
- 使用历史数据进行参数回测
- 避免过度拟合历史数据
- 考虑不同市场环境下的参数稳定性

### 2. 风险管理
- 设置合理的止损止盈比例
- 控制单笔交易的风险敞口
- 考虑最大回撤限制

### 3. 信号过滤
- 使用最小变化幅度避免噪音交易
- 结合多个确认条件提高信号质量
- 避免频繁的买卖切换

### 4. 组合使用
- 可以同时运行多个策略
- 通过权重分配管理策略组合
- 监控策略间的相关性

## 性能评估

所有示例策略都包含完整的测试用例，可以通过以下命令运行测试：

```bash
python -m pytest tests/test_example_strategies.py -v
```

测试覆盖了以下方面：
- 策略初始化和参数验证
- 信号生成逻辑
- 错误处理机制
- 元数据完整性
- 策略信息获取

## 扩展开发

基于现有的策略框架，可以轻松开发新的策略：

1. 继承 `BaseStrategy` 基类
2. 实现必要的抽象方法
3. 定义策略参数
4. 编写信号生成逻辑
5. 添加相应的测试用例

示例策略提供了良好的参考模板，展示了策略开发的最佳实践。