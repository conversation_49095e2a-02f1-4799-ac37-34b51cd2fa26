# Akshare Adapter Documentation

## Overview

The Akshare adapter provides access to Chinese A-share market data using the free and open-source [Akshare](https://github.com/akfamily/akshare) library. This adapter replaces the previously used Tushare adapter, which required a paid subscription.

## Features

- Free access to A-share stock and index data
- Support for both stocks and major indices
- Historical data retrieval with various time intervals
- Market information and symbol validation
- Trading calendar and holiday support
- Search functionality for stocks

## Installation

To use the Akshare adapter, you need to install the akshare package:

```bash
pip install akshare>=1.17.0
```

## Configuration

To enable the Akshare adapter in your configuration file (`config/data_sources.yaml`):

```yaml
akshare:
  adapter_class: src.data.adapters.akshare_adapter.AkshareAdapter
  credentials: {}
  default_params: {}
  enabled: true
  priority: 2
  rate_limit:
    requests_per_minute: 100
```

## Usage

### Initializing the Adapter

```python
from src.data.adapters.akshare_adapter import AkshareAdapter
from src.data.adapters.base import DataSourceConfig

config = DataSourceConfig(
    name='akshare_example',
    adapter_class='src.data.adapters.akshare_adapter.AkshareAdapter',
    enabled=True,
    priority=1,
    rate_limit={'requests_per_minute': 100},
    credentials={},
    default_params={}
)

adapter = AkshareAdapter(config)
```

### Fetching Historical Data

```python
from datetime import datetime, timedelta

# Get historical data for a stock
end_date = datetime.now()
start_date = end_date - timedelta(days=30)

data = adapter.fetch_historical_data("000001.SZ", start_date, end_date)
```

### Getting Available Symbols

```python
# Get list of all available symbols
symbols = adapter.get_available_symbols()
```

### Validating Symbols

```python
# Check if a symbol is valid
is_valid = adapter.validate_symbol("000001.SZ")
```

### Getting Market Information

```python
# Get detailed information about a symbol
market_info = adapter.get_market_info("000001.SZ")
```

### Searching for Stocks

```python
# Search for stocks by keyword
results = adapter.search_stocks("银行", limit=10)
```

## Supported Intervals

The Akshare adapter supports the following time intervals:
- `1d`: Daily data
- `1w`: Weekly data
- `1M`: Monthly data

## Limitations

- Rate limits apply to prevent overloading the data sources
- Some historical data may not be as comprehensive as paid services
- Data availability depends on the underlying data sources used by Akshare

## Troubleshooting

If you encounter issues with the Akshare adapter:

1. Make sure you have the latest version of akshare installed
2. Check your internet connection
3. Verify that the symbol you're requesting is valid
4. Check the logs for specific error messages

## Example

See `examples/akshare_adapter_example.py` for a complete working example.