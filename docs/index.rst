量化交易系统文档
==================

欢迎使用量化交易系统！这是一个基于Python的综合量化交易平台，支持多市场数据源、多策略回测、风险管理和绩效分析。

.. note::
   本系统仅用于教育和研究目的，不构成投资建议。实际交易请谨慎评估风险。

快速导航
--------

.. grid:: 2

    .. grid-item-card:: 🚀 快速开始
        :link: user_manual
        :link-type: doc

        从安装到运行第一个策略的完整指南

    .. grid-item-card:: 🔧 开发文档
        :link: api_documentation
        :link-type: doc

        API接口和开发者指南

    .. grid-item-card:: 🏗️ 系统架构
        :link: system_architecture
        :link-type: doc

        深入了解系统设计和架构

    .. grid-item-card:: 📈 示例策略
        :link: example_strategies
        :link-type: doc

        学习内置策略的使用方法

主要特性
--------

🏗️ **模块化架构**
   清晰的项目结构，易于扩展和维护

📊 **多数据源支持**
   支持美股(Yahoo Finance)、A股(Tushare)、加密货币(Binance)、经济数据(FRED)

🔄 **策略框架**
   灵活的策略开发框架，支持自定义策略

📈 **回测引擎**
   事件驱动的回测引擎，支持多策略并行测试

⚠️ **风险管理**
   内置风险控制机制，包括仓位限制、止损等

📊 **绩效分析**
   全面的绩效指标计算和报告生成

🌐 **Web界面**
   现代化的Web界面，支持实时监控和分析

🔧 **配置管理**
   灵活的配置系统，支持多环境部署

文档目录
--------

.. toctree::
   :maxdepth: 2
   :caption: 用户指南

   README
   user_manual
   deployment

.. toctree::
   :maxdepth: 2
   :caption: 开发文档

   project_structure
   system_architecture
   api_documentation
   code_documentation_guide

.. toctree::
   :maxdepth: 2
   :caption: 功能模块

   example_strategies
   backtest_engine
   risk_management
   risk_monitoring_system
   monitoring_api

.. toctree::
   :maxdepth: 2
   :caption: 数据源集成

   yahoo_finance_adapter
   tushare_adapter
   multi_market_strategy_implementation
   economic_strategies

.. toctree::
   :maxdepth: 2
   :caption: API参考

   api/modules

系统要求
--------

基础环境
~~~~~~~~

- **Python**: 3.8+
- **数据库**: PostgreSQL 12+ (推荐TimescaleDB)
- **缓存**: Redis (可选)
- **内存**: 8GB+ (推荐16GB)
- **存储**: 50GB+

快速安装
~~~~~~~~

.. code-block:: bash

   # 克隆项目
   git clone https://github.com/your-org/quantitative-trading-system.git
   cd quantitative-trading-system

   # 安装依赖
   pip install -r requirements.txt

   # 初始化数据库
   python scripts/database/init_db.py

   # 启动系统
   python run_web.py

版本信息
--------

- **当前版本**: v1.0.0
- **发布日期**: 2024-07-24
- **文档版本**: v1.0.0
- **最后更新**: 2024-07-24

支持和反馈
----------

如有问题或建议，请通过以下方式联系：

- 📧 **邮箱**: <EMAIL>
- 🐛 **问题反馈**: `GitHub Issues <https://github.com/your-org/quantitative-trading-system/issues>`_
- 💬 **讨论**: `GitHub Discussions <https://github.com/your-org/quantitative-trading-system/discussions>`_

索引和表格
----------

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`