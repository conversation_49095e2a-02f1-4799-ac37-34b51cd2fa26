# Risk Management System

The Risk Management System provides comprehensive risk control capabilities for the quantitative trading system. It implements multiple layers of protection to ensure portfolio safety and compliance with risk limits.

## Overview

The risk management system consists of four main components:

1. **Risk Rules** - Validate orders and portfolio states against risk limits
2. **Risk Monitors** - Continuously track risk metrics in real-time
3. **Risk Manager** - Coordinates all risk controls and decision making
4. **Risk Models** - Define data structures and configurations

## Key Features

### 1. Position Size Control
- **Maximum position size limits** - Prevent any single position from becoming too large
- **Portfolio risk per trade** - Limit the risk exposure of individual trades
- **Cash availability checks** - Ensure sufficient funds for buy orders
- **Short selling prevention** - Block attempts to sell positions not owned

### 2. Stop Loss and Take Profit Management
- **Fixed stop losses** - Set stop loss orders at specific price levels
- **Trailing stops** - Dynamic stop losses that follow favorable price movements
- **Take profit orders** - Automatically close positions at target profit levels
- **Automatic trigger detection** - Monitor market prices and execute stops when triggered

### 3. Drawdown Protection
- **Maximum drawdown limits** - Prevent portfolio losses from exceeding thresholds
- **Daily loss limits** - Control daily portfolio losses
- **Peak value tracking** - Monitor portfolio high-water marks
- **Emergency stop activation** - Halt trading during severe drawdowns

### 4. Risk Budget Management
- **Daily risk budgets** - Limit total risk exposure per day
- **Weekly and monthly budgets** - Longer-term risk allocation controls
- **Risk usage tracking** - Monitor how much risk budget has been consumed
- **Budget violation alerts** - Warn when approaching or exceeding limits

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Risk Rules    │    │  Risk Monitors  │    │  Risk Manager   │
│                 │    │                 │    │                 │
│ • Position Size │    │ • Drawdown      │    │ • Coordination  │
│ • Stop Loss     │◄───┤ • Position      │◄───┤ • Decision      │
│ • Drawdown      │    │ • Risk Budget   │    │ • Alerts        │
│ • Risk Budget   │    │                 │    │ • Reporting     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Risk Models    │
                    │                 │
                    │ • Configuration │
                    │ • Violations    │
                    │ • Metrics       │
                    │ • Orders        │
                    └─────────────────┘
```

## Usage Examples

### Basic Setup

```python
from src.risk.manager import RiskManager
from src.risk.models import RiskConfig

# Create risk configuration
config = RiskConfig(
    max_position_size_pct=0.10,     # 10% max position size
    max_drawdown_pct=0.15,          # 15% max drawdown
    daily_risk_budget=0.05,         # 5% daily risk budget
    default_stop_loss_pct=0.05      # 5% default stop loss
)

# Initialize risk manager
risk_manager = RiskManager(config)

# Add alert callback
def risk_alert(violation):
    print(f"Risk Alert: {violation.rule_name} - {violation.violation_type}")

risk_manager.add_alert_callback(risk_alert)
```

### Order Validation

```python
from src.models.trading import Order, OrderSide, OrderType

# Create an order
order = Order(
    id='order_1',
    symbol='AAPL',
    side=OrderSide.BUY,
    order_type=OrderType.MARKET,
    quantity=100,
    price=150.0
)

# Check if order is allowed
allowed, violations = risk_manager.check_order(order, portfolio)

if allowed:
    print("Order approved")
else:
    print(f"Order rejected: {len(violations)} violations")
    for violation in violations:
        print(f"- {violation.rule_name}: {violation.violation_type}")
```

### Stop Loss Management

```python
# Add fixed stop loss
risk_manager.add_stop_loss(
    symbol='AAPL',
    stop_price=140.0,
    quantity=100,
    stop_type='fixed'
)

# Add trailing stop loss
risk_manager.add_stop_loss(
    symbol='MSFT',
    stop_price=190.0,
    quantity=50,
    stop_type='trailing',
    trail_amount=10.0
)

# Add take profit
risk_manager.add_take_profit(
    symbol='AAPL',
    target_price=180.0,
    quantity=100
)

# Check for triggers
market_data = {'AAPL': 135.0, 'MSFT': 195.0}
risk_manager.update_monitors(portfolio, market_data)
```

### Risk Monitoring

```python
# Calculate risk metrics
metrics = risk_manager.calculate_risk_metrics(portfolio)

print(f"Portfolio value: ${metrics.portfolio_value:,.2f}")
print(f"Largest position: {metrics.largest_position_pct:.1%}")
print(f"Current drawdown: {metrics.current_drawdown_pct:.1%}")
print(f"Gross leverage: {metrics.gross_leverage:.2f}x")

# Get risk summary
summary = risk_manager.get_risk_summary()
print(f"Emergency stop active: {summary['emergency_stop_active']}")
print(f"Recent violations: {summary['recent_violations']}")
```

### Portfolio Risk Check

```python
# Check portfolio for violations
violations = risk_manager.check_portfolio(portfolio)

print(f"Portfolio violations: {len(violations)}")
for violation in violations:
    print(f"- {violation.rule_name}: {violation.violation_type}")
    print(f"  Current: {violation.current_value:.4f}")
    print(f"  Limit: {violation.limit_value:.4f}")
    print(f"  Severity: {violation.severity.value}")
```

## Risk Rules

### Position Size Rule
Controls position sizes and capital allocation:
- Maximum position size as percentage of portfolio
- Portfolio risk per trade limits
- Cash availability validation
- Short selling prevention

### Stop Loss Rule
Manages stop loss and take profit orders:
- Fixed stop losses at specific prices
- Trailing stops that follow price movements
- Take profit orders for profit taking
- Automatic trigger detection and execution

### Drawdown Rule
Protects against excessive losses:
- Maximum drawdown limits
- Daily loss limits
- Peak value tracking
- Emergency stop activation

### Risk Budget Rule
Controls risk allocation over time:
- Daily, weekly, and monthly risk budgets
- Risk usage tracking and recording
- Budget violation detection
- Period-based risk allocation

## Risk Monitors

### Drawdown Monitor
Tracks portfolio drawdown in real-time:
- Peak value tracking
- Current and maximum drawdown calculation
- Drawdown duration monitoring
- Daily loss tracking

### Position Monitor
Monitors position sizes and concentration:
- Individual position size tracking
- Concentration risk detection
- Position history maintenance
- Top positions analysis

### Risk Budget Monitor
Tracks risk budget utilization:
- Daily, weekly, monthly usage tracking
- Budget period management
- Usage history recording
- Violation detection

## Configuration Options

### RiskConfig Parameters

```python
@dataclass
class RiskConfig:
    # Position size limits
    max_position_size_pct: float = 0.1          # 10% max position size
    max_portfolio_risk_pct: float = 0.02        # 2% max portfolio risk per trade
    
    # Stop loss and take profit
    default_stop_loss_pct: float = 0.05         # 5% stop loss
    default_take_profit_pct: float = 0.10       # 10% take profit
    enable_trailing_stop: bool = True
    trailing_stop_pct: float = 0.03             # 3% trailing stop
    
    # Drawdown protection
    max_drawdown_pct: float = 0.15              # 15% max drawdown
    daily_loss_limit_pct: float = 0.05          # 5% daily loss limit
    enable_drawdown_protection: bool = True
    
    # Risk budget
    daily_risk_budget: float = 0.02             # 2% daily risk budget
    weekly_risk_budget: float = 0.08            # 8% weekly risk budget
    monthly_risk_budget: float = 0.20           # 20% monthly risk budget
    
    # Leverage and concentration
    max_leverage: float = 2.0                   # 2:1 max leverage
    max_sector_concentration_pct: float = 0.3   # 30% max sector concentration
    
    # Emergency controls
    enable_emergency_stop: bool = True
    emergency_stop_drawdown_pct: float = 0.25   # 25% emergency stop
```

## Risk Violation Severity Levels

- **LOW** - Minor violations that generate warnings
- **MEDIUM** - Moderate violations that may restrict trading
- **HIGH** - Serious violations that block orders
- **CRITICAL** - Severe violations that halt trading immediately

## Integration with Backtesting

The risk management system integrates seamlessly with the backtesting engine:

```python
from src.backtest.engine import BacktestEngine

# Create backtest engine with risk management
engine = BacktestEngine(initial_capital=100000)

# Add risk manager to engine
engine.risk_manager = risk_manager

# Risk checks will be performed automatically during backtesting
result = engine.run_backtest(start_date, end_date)
```

## Best Practices

### 1. Configuration
- Set conservative limits initially and adjust based on experience
- Use different risk budgets for different time periods
- Enable emergency stops for protection against extreme events

### 2. Monitoring
- Regularly review risk metrics and violations
- Set up alert callbacks for immediate notification
- Monitor drawdown and position concentration closely

### 3. Stop Loss Management
- Always set stop losses for new positions
- Use trailing stops for trending markets
- Adjust stop levels based on volatility

### 4. Risk Budget Management
- Allocate risk budgets based on strategy performance
- Monitor budget utilization throughout trading periods
- Reserve emergency capacity for exceptional opportunities

## Error Handling

The risk management system includes comprehensive error handling:
- Graceful degradation when components fail
- Fail-safe defaults that reject risky orders
- Detailed logging for troubleshooting
- Exception isolation to prevent system crashes

## Performance Considerations

- Risk checks are optimized for low latency
- Monitors use efficient data structures
- Historical data is automatically pruned
- Calculations are vectorized where possible

## Testing

Comprehensive unit tests cover all components:
- Rule validation logic
- Monitor functionality
- Manager coordination
- Configuration validation
- Error handling scenarios

Run tests with:
```bash
python -m pytest tests/test_risk_management.py -v
```

## Future Enhancements

Planned improvements include:
- Machine learning-based risk prediction
- Dynamic risk limit adjustment
- Cross-asset correlation analysis
- Real-time market regime detection
- Advanced portfolio optimization integration