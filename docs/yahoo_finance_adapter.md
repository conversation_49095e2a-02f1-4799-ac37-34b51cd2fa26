# Yahoo Finance Data Adapter

## Overview

The Yahoo Finance adapter provides access to US stock market data through the `yfinance` library. It supports historical data, real-time quotes, dividends, stock splits, and market information for US equities.

## Features

### Core Functionality
- **Historical Data**: OHLCV data with adjustable time intervals
- **Real-time Quotes**: Current market prices and statistics
- **Corporate Actions**: Dividend and stock split data
- **Market Information**: Company details, exchange info, trading hours
- **Symbol Search**: Find symbols by partial matches
- **Market Status**: Check if US markets are open/closed

### Supported Markets
- **Primary**: US Stock Market (NYSE, NASDAQ, AMEX)
- **Symbols**: S&P 500 and NASDAQ 100 components (expandable)
- **Currency**: USD
- **Time Zone**: US/Eastern

### Data Intervals
- Intraday: 1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h
- Daily and longer: 1d, 5d, 1wk, 1mo, 3mo

## Configuration

### Basic Configuration
```yaml
yahoo_finance:
  adapter_class: "src.data.adapters.yahoo_finance.YahooFinanceAdapter"
  enabled: true
  priority: 1
  rate_limit:
    requests_per_minute: 60
    requests_per_hour: 2000
  credentials: {}
  default_params:
    interval: "1d"
    auto_adjust: false
    prepost: false
```

### Rate Limiting
The adapter implements rate limiting to respect Yahoo Finance's usage policies:
- **Default**: 60 requests per minute, 2000 per hour
- **Automatic**: Built-in wait mechanisms when limits are approached
- **Configurable**: Adjustable through configuration

## Usage Examples

### Basic Usage
```python
from src.data.adapters.yahoo_finance import YahooFinanceAdapter
from src.data.adapters.base import DataSourceConfig
from datetime import datetime, timedelta

# Create configuration
config = DataSourceConfig(
    name='yahoo_finance',
    adapter_class='src.data.adapters.yahoo_finance.YahooFinanceAdapter',
    enabled=True,
    priority=1,
    rate_limit={'requests_per_minute': 60},
    credentials={},
    default_params={}
)

# Initialize adapter
adapter = YahooFinanceAdapter(config)

# Fetch historical data
end_date = datetime.now()
start_date = end_date - timedelta(days=30)
data = adapter.fetch_historical_data('AAPL', start_date, end_date)

print(f"Fetched {len(data)} records for AAPL")
```

### Advanced Features
```python
# Get market information
market_info = adapter.get_market_info('AAPL')
print(f"Company: {market_info.name}")
print(f"Exchange: {market_info.exchange}")

# Get real-time quote
quote = adapter.get_real_time_quote('AAPL')
print(f"Current price: ${quote['price']:.2f}")

# Fetch dividends
dividends = adapter.fetch_dividends('AAPL', start_date, end_date)
print(f"Found {len(dividends)} dividend payments")

# Check market status
is_open = adapter.is_market_open()
print(f"Market is {'open' if is_open else 'closed'}")
```

## Data Models

### UnifiedMarketData
```python
@dataclass
class UnifiedMarketData:
    symbol: str           # Stock symbol (e.g., 'AAPL')
    timestamp: datetime   # Data timestamp
    open: float          # Opening price
    high: float          # High price
    low: float           # Low price
    close: float         # Closing price
    volume: float        # Trading volume
    market: str          # 'US'
    exchange: str        # 'NASDAQ', 'NYSE', etc.
    currency: str        # 'USD'
    adj_close: float     # Adjusted closing price (optional)
    turnover: float      # Trading turnover (optional)
```

### MarketInfo
```python
@dataclass
class MarketInfo:
    symbol: str                    # Stock symbol
    name: str                     # Company name
    market: str                   # 'US'
    exchange: str                 # Exchange name
    currency: str                 # 'USD'
    lot_size: float              # Minimum trading unit (1.0)
    tick_size: float             # Minimum price increment (0.01)
    trading_hours: Dict[str, str] # Market hours
    timezone: str                # 'US/Eastern'
    is_active: bool              # Trading status
```

## Symbol Coverage

### Included Symbols
- **S&P 500 Components**: Major US companies
- **NASDAQ 100 Components**: Technology-focused companies
- **Total**: ~72 unique symbols (after deduplication)

### Sample Symbols
```
AAPL, MSFT, AMZN, GOOGL, GOOG, TSLA, BRK-B, UNH, JNJ, META,
NVDA, JPM, V, PG, HD, CVX, MA, BAC, ABBV, PFE, AVGO, KO, COST,
DIS, TMO, DHR, ABT, VZ, ADBE, CRM, NFLX, CMCSA, NKE, ACN, TXN
```

## Market Hours and Holidays

### Trading Hours
- **Regular Hours**: 9:30 AM - 4:00 PM ET
- **Time Zone**: US/Eastern
- **Weekends**: Closed (Saturday, Sunday)

### Recognized Holidays (2024-2025)
- New Year's Day
- Martin Luther King Jr. Day
- Presidents' Day
- Good Friday
- Memorial Day
- Juneteenth
- Independence Day
- Labor Day
- Thanksgiving
- Christmas Day

## Error Handling

### Common Errors
1. **Invalid Symbol**: Symbol not found or not supported
2. **Network Issues**: Connection problems with Yahoo Finance
3. **Rate Limiting**: Too many requests in short time
4. **Data Unavailable**: No data for requested time period

### Error Recovery
- **Automatic Retry**: Built-in retry mechanism for transient errors
- **Rate Limit Handling**: Automatic waiting when limits are reached
- **Graceful Degradation**: Returns empty results instead of crashing
- **Detailed Logging**: Comprehensive error logging for debugging

## Performance Considerations

### Caching
- **Symbol Validation**: Cached for 1 hour
- **Market Information**: Cached for 4 hours
- **Automatic Cleanup**: Old cache entries are automatically removed

### Optimization Tips
1. **Batch Requests**: Fetch multiple symbols in sequence rather than parallel
2. **Appropriate Intervals**: Use longer intervals for historical analysis
3. **Time Range Limits**: Avoid very large date ranges in single requests
4. **Cache Awareness**: Repeated requests for same data use cached results

## Testing

### Unit Tests
```bash
# Run all Yahoo Finance adapter tests
python -m pytest tests/test_yahoo_finance_adapter.py -v

# Run specific test categories
python -m pytest tests/test_yahoo_finance_adapter.py::TestYahooFinanceAdapter -v
```

### Integration Tests
```bash
# Run integration tests (requires network access)
python -m pytest tests/test_yahoo_finance_adapter.py::TestYahooFinanceIntegration -v
```

### Example Scripts
```bash
# Basic functionality demonstration
python examples/yahoo_finance_example.py

# Data source manager integration
python examples/data_source_manager_example.py
```

## Troubleshooting

### Common Issues

#### 1. "Invalid symbol" errors
```python
# Check if symbol is supported
if adapter.validate_symbol('SYMBOL'):
    print("Symbol is valid")
else:
    print("Symbol not supported")
```

#### 2. Empty data results
- Check if market was open during requested period
- Verify date range is not in the future
- Ensure symbol is actively traded

#### 3. Rate limiting issues
```python
# Check rate limit status
stats = adapter.get_performance_stats()
print(f"Can make request: {stats['can_make_request']}")
print(f"Wait time: {stats['wait_time']} seconds")
```

#### 4. Network connectivity
```python
# Perform health check
is_healthy, message = adapter.health_check()
print(f"Adapter health: {message}")
```

### Debug Mode
Enable detailed logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Limitations

### Data Limitations
- **Historical Range**: Limited by Yahoo Finance's data availability
- **Real-time Delay**: Quotes may have 15-20 minute delay
- **Symbol Coverage**: Limited to predefined symbol lists
- **Market Coverage**: US markets only

### Technical Limitations
- **Rate Limits**: Subject to Yahoo Finance's usage policies
- **API Changes**: Dependent on yfinance library updates
- **Network Dependency**: Requires internet connectivity
- **No Authentication**: Uses public Yahoo Finance data

## Future Enhancements

### Planned Features
1. **Extended Symbol Coverage**: Add more exchanges and symbol types
2. **Options Data**: Support for options chains and derivatives
3. **Fundamental Data**: Financial statements and ratios
4. **News Integration**: Company news and announcements
5. **Sector/Industry Data**: ETF and sector performance

### Configuration Enhancements
1. **Custom Symbol Lists**: User-defined symbol collections
2. **Flexible Rate Limits**: Per-endpoint rate limiting
3. **Data Quality Controls**: Configurable validation rules
4. **Caching Strategies**: Configurable cache policies

## Dependencies

### Required Packages
```
yfinance>=0.2.0    # Yahoo Finance data access
pandas>=1.5.0      # Data manipulation
pytz>=2023.3       # Timezone handling
```

### Optional Packages
```
requests>=2.28.0   # HTTP requests (used by yfinance)
```

## License and Disclaimer

### Usage Terms
- This adapter uses Yahoo Finance's public data
- Subject to Yahoo Finance's terms of service
- For educational and research purposes
- Not for commercial redistribution

### Data Disclaimer
- Market data may be delayed
- No guarantee of data accuracy
- Use at your own risk for trading decisions
- Always verify critical data from official sources