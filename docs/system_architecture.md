# 系统架构和设计文档

## 概述

量化交易系统采用现代化的微服务架构设计，基于Python生态系统构建，支持多市场数据源、多策略并行回测和实时风险监控。系统设计遵循高内聚、低耦合的原则，具备良好的可扩展性和可维护性。

## 整体架构

### 系统架构图

```mermaid
graph TB
    subgraph "用户接口层"
        WEB[Web界面]
        GUI[桌面GUI]
        API[REST API]
        WS[WebSocket]
    end
    
    subgraph "应用服务层"
        AUTH[认证服务]
        STRATEGY[策略服务]
        BACKTEST[回测服务]
        RISK[风险服务]
        ANALYSIS[分析服务]
        MONITOR[监控服务]
    end
    
    subgraph "业务逻辑层"
        SM[策略管理器]
        BE[回测引擎]
        RM[风险管理器]
        PA[绩效分析器]
        DM[数据管理器]
        IM[指标管理器]
    end
    
    subgraph "数据访问层"
        CACHE[缓存层]
        ORM[数据访问对象]
        CONN[连接池]
    end
    
    subgraph "数据存储层"
        TSDB[(时间序列数据库)]
        RDBMS[(关系数据库)]
        REDIS[(Redis缓存)]
        FILES[(文件存储)]
    end
    
    subgraph "外部数据源"
        YAHOO[Yahoo Finance]
        TUSHARE[Tushare]
        BINANCE[Binance]
        FRED[FRED]
    end
    
    WEB --> API
    GUI --> API
    API --> AUTH
    API --> STRATEGY
    API --> BACKTEST
    API --> RISK
    API --> ANALYSIS
    WS --> MONITOR
    
    STRATEGY --> SM
    BACKTEST --> BE
    RISK --> RM
    ANALYSIS --> PA
    
    SM --> DM
    BE --> DM
    BE --> IM
    RM --> DM
    PA --> DM
    
    DM --> CACHE
    DM --> ORM
    ORM --> CONN
    
    CONN --> TSDB
    CONN --> RDBMS
    CACHE --> REDIS
    
    DM --> YAHOO
    DM --> TUSHARE
    DM --> BINANCE
    DM --> FRED
```

### 架构特点

1. **分层架构**: 清晰的分层设计，每层职责明确
2. **微服务化**: 核心功能模块化，支持独立部署和扩展
3. **事件驱动**: 基于事件的异步处理机制
4. **插件化**: 支持策略、指标、数据源的插件式扩展
5. **高可用**: 支持集群部署和故障转移
6. **可观测**: 完整的日志、监控和追踪体系

## 核心组件设计

### 1. 数据管理子系统

#### 架构设计

```mermaid
graph TB
    subgraph "数据管理子系统"
        DSM[数据源管理器]
        DM[数据管理器]
        DV[数据验证器]
        DC[数据清洗器]
        DA[数据适配器]
    end
    
    subgraph "数据适配器层"
        YFA[Yahoo Finance适配器]
        TSA[Tushare适配器]
        BNA[Binance适配器]
        FRA[FRED适配器]
    end
    
    subgraph "数据存储"
        TSDB[(TimescaleDB)]
        CACHE[(Redis缓存)]
    end
    
    DSM --> DA
    DA --> YFA
    DA --> TSA
    DA --> BNA
    DA --> FRA
    
    DM --> DSM
    DM --> DV
    DM --> DC
    
    DM --> TSDB
    DM --> CACHE
```

#### 关键设计模式

**1. 适配器模式**
```python
class DataSourceAdapter(ABC):
    """数据源适配器基类"""
    
    @abstractmethod
    def fetch_data(self, symbol: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        pass
    
    @abstractmethod
    def get_available_symbols(self) -> List[str]:
        pass
    
    @abstractmethod
    def normalize_data(self, raw_data: Any) -> pd.DataFrame:
        pass

class YahooFinanceAdapter(DataSourceAdapter):
    """Yahoo Finance数据适配器"""
    
    def fetch_data(self, symbol: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        # 实现Yahoo Finance数据获取逻辑
        pass
```

**2. 工厂模式**
```python
class DataSourceFactory:
    """数据源工厂"""
    
    _adapters = {
        'yahoo_finance': YahooFinanceAdapter,
        'tushare': TushareAdapter,
        'binance': BinanceAdapter,
        'fred': FREDAdapter
    }
    
    @classmethod
    def create_adapter(cls, source_name: str, config: Dict[str, Any]) -> DataSourceAdapter:
        adapter_class = cls._adapters.get(source_name)
        if not adapter_class:
            raise ValueError(f"Unknown data source: {source_name}")
        return adapter_class(config)
```

**3. 观察者模式**
```python
class DataUpdateObserver(ABC):
    @abstractmethod
    def on_data_updated(self, symbol: str, data: pd.DataFrame):
        pass

class DataManager:
    def __init__(self):
        self._observers: List[DataUpdateObserver] = []
    
    def add_observer(self, observer: DataUpdateObserver):
        self._observers.append(observer)
    
    def notify_observers(self, symbol: str, data: pd.DataFrame):
        for observer in self._observers:
            observer.on_data_updated(symbol, data)
```

#### 数据模型设计

**统一数据模型**
```python
@dataclass
class UnifiedMarketData:
    """统一市场数据模型"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    market: str  # 'US', 'CN', 'CRYPTO', 'ECONOMIC'
    exchange: str
    currency: str
    adj_close: Optional[float] = None
    turnover: Optional[float] = None
    
    def __post_init__(self):
        self.validate()
    
    def validate(self):
        """数据验证"""
        if self.high < self.low:
            raise ValueError("High price cannot be less than low price")
        if self.open < 0 or self.close < 0:
            raise ValueError("Prices cannot be negative")
        if self.volume < 0:
            raise ValueError("Volume cannot be negative")
```

### 2. 策略框架子系统

#### 架构设计

```mermaid
graph TB
    subgraph "策略框架"
        SM[策略管理器]
        SC[策略容器]
        SE[策略执行器]
        SS[信号聚合器]
    end
    
    subgraph "策略类型"
        BS[基础策略]
        MS[多因子策略]
        MLS[机器学习策略]
        HFS[高频策略]
        EDS[事件驱动策略]
    end
    
    subgraph "支持组件"
        IM[指标管理器]
        PM[参数管理器]
        OM[优化管理器]
    end
    
    SM --> SC
    SC --> SE
    SE --> SS
    
    SC --> BS
    SC --> MS
    SC --> MLS
    SC --> HFS
    SC --> EDS
    
    SE --> IM
    SE --> PM
    SM --> OM
```

#### 策略生命周期管理

```python
class StrategyLifecycleManager:
    """策略生命周期管理器"""
    
    def __init__(self):
        self.strategies: Dict[str, BaseStrategy] = {}
        self.strategy_states: Dict[str, StrategyState] = {}
    
    def register_strategy(self, strategy: BaseStrategy):
        """注册策略"""
        self.strategies[strategy.id] = strategy
        self.strategy_states[strategy.id] = StrategyState.REGISTERED
        
    def initialize_strategy(self, strategy_id: str, context: StrategyContext):
        """初始化策略"""
        strategy = self.strategies[strategy_id]
        strategy.initialize(context)
        self.strategy_states[strategy_id] = StrategyState.INITIALIZED
        
    def start_strategy(self, strategy_id: str):
        """启动策略"""
        if self.strategy_states[strategy_id] != StrategyState.INITIALIZED:
            raise ValueError("Strategy must be initialized before starting")
        
        self.strategy_states[strategy_id] = StrategyState.RUNNING
        
    def stop_strategy(self, strategy_id: str):
        """停止策略"""
        strategy = self.strategies[strategy_id]
        strategy.cleanup()
        self.strategy_states[strategy_id] = StrategyState.STOPPED
```

#### 信号处理机制

```python
class SignalProcessor:
    """信号处理器"""
    
    def __init__(self):
        self.signal_filters: List[SignalFilter] = []
        self.signal_aggregators: List[SignalAggregator] = []
    
    def add_filter(self, filter: SignalFilter):
        """添加信号过滤器"""
        self.signal_filters.append(filter)
    
    def add_aggregator(self, aggregator: SignalAggregator):
        """添加信号聚合器"""
        self.signal_aggregators.append(aggregator)
    
    def process_signals(self, signals: List[Signal]) -> List[Signal]:
        """处理信号"""
        # 应用过滤器
        filtered_signals = signals
        for filter in self.signal_filters:
            filtered_signals = filter.filter(filtered_signals)
        
        # 应用聚合器
        aggregated_signals = filtered_signals
        for aggregator in self.signal_aggregators:
            aggregated_signals = aggregator.aggregate(aggregated_signals)
        
        return aggregated_signals
```

### 3. 回测引擎子系统

#### 事件驱动架构

```mermaid
graph TB
    subgraph "回测引擎"
        EQ[事件队列]
        EP[事件处理器]
        BE[回测引擎]
        OE[订单执行器]
    end
    
    subgraph "事件类型"
        ME[市场事件]
        SE[信号事件]
        OEV[订单事件]
        FE[成交事件]
    end
    
    subgraph "处理器"
        MH[市场处理器]
        SH[信号处理器]
        OH[订单处理器]
        PH[投资组合处理器]
    end
    
    BE --> EQ
    EQ --> EP
    EP --> MH
    EP --> SH
    EP --> OH
    EP --> PH
    
    ME --> EQ
    SE --> EQ
    OEV --> EQ
    FE --> EQ
    
    MH --> OE
    SH --> OE
    OH --> OE
```

#### 事件驱动实现

```python
class Event(ABC):
    """事件基类"""
    def __init__(self, timestamp: datetime):
        self.timestamp = timestamp
        self.event_id = str(uuid.uuid4())

class MarketEvent(Event):
    """市场数据事件"""
    def __init__(self, timestamp: datetime, market_data: MarketData):
        super().__init__(timestamp)
        self.market_data = market_data

class SignalEvent(Event):
    """交易信号事件"""
    def __init__(self, timestamp: datetime, signal: Signal):
        super().__init__(timestamp)
        self.signal = signal

class EventQueue:
    """事件队列"""
    def __init__(self):
        self.queue = queue.PriorityQueue()
    
    def put(self, event: Event):
        # 按时间戳排序
        self.queue.put((event.timestamp, event))
    
    def get(self) -> Event:
        _, event = self.queue.get()
        return event
    
    def empty(self) -> bool:
        return self.queue.empty()

class BacktestEngine:
    """回测引擎"""
    def __init__(self):
        self.event_queue = EventQueue()
        self.event_handlers = {}
        self.portfolio = Portfolio()
        
    def register_handler(self, event_type: Type[Event], handler: Callable):
        """注册事件处理器"""
        self.event_handlers[event_type] = handler
    
    def run_backtest(self):
        """执行回测"""
        while not self.event_queue.empty():
            event = self.event_queue.get()
            handler = self.event_handlers.get(type(event))
            if handler:
                handler(event)
```

### 4. 风险管理子系统

#### 多层风险控制架构

```mermaid
graph TB
    subgraph "风险管理层次"
        L1[订单级风险控制]
        L2[持仓级风险控制]
        L3[投资组合级风险控制]
        L4[系统级风险控制]
    end
    
    subgraph "风险指标"
        VAR[VaR计算]
        CVAR[CVaR计算]
        VOL[波动率监控]
        CORR[相关性分析]
        CONC[集中度监控]
    end
    
    subgraph "风险动作"
        ALERT[风险预警]
        LIMIT[限制交易]
        HEDGE[对冲操作]
        STOP[强制平仓]
    end
    
    L1 --> VAR
    L2 --> VOL
    L3 --> CORR
    L4 --> CONC
    
    VAR --> ALERT
    VOL --> LIMIT
    CORR --> HEDGE
    CONC --> STOP
```

#### 风险计算引擎

```python
class RiskCalculationEngine:
    """风险计算引擎"""
    
    def __init__(self, config: RiskConfig):
        self.config = config
        self.calculators = {
            'var': VaRCalculator(),
            'cvar': CVaRCalculator(),
            'volatility': VolatilityCalculator(),
            'correlation': CorrelationCalculator()
        }
    
    def calculate_portfolio_risk(self, portfolio: Portfolio, 
                               market_data: Dict[str, pd.DataFrame]) -> PortfolioRisk:
        """计算投资组合风险"""
        
        # 计算各项风险指标
        var_95 = self.calculators['var'].calculate(portfolio, market_data, 0.95)
        cvar_95 = self.calculators['cvar'].calculate(portfolio, market_data, 0.95)
        volatility = self.calculators['volatility'].calculate(portfolio, market_data)
        correlation_matrix = self.calculators['correlation'].calculate(portfolio, market_data)
        
        # 计算风险贡献
        risk_contributions = self._calculate_risk_contributions(
            portfolio, market_data, var_95
        )
        
        return PortfolioRisk(
            var_95=var_95,
            cvar_95=cvar_95,
            volatility=volatility,
            correlation_matrix=correlation_matrix,
            risk_contributions=risk_contributions,
            timestamp=datetime.now()
        )
    
    def _calculate_risk_contributions(self, portfolio: Portfolio, 
                                    market_data: Dict[str, pd.DataFrame], 
                                    portfolio_var: float) -> Dict[str, float]:
        """计算各持仓的风险贡献"""
        contributions = {}
        
        for symbol, position in portfolio.positions.items():
            # 计算边际VaR
            marginal_var = self._calculate_marginal_var(
                symbol, portfolio, market_data
            )
            
            # 风险贡献 = 持仓权重 × 边际VaR
            weight = position.market_value / portfolio.total_value
            contributions[symbol] = weight * marginal_var / portfolio_var
        
        return contributions
```

### 5. 绩效分析子系统

#### 分析框架设计

```mermaid
graph TB
    subgraph "绩效分析框架"
        PA[绩效分析器]
        MC[指标计算器]
        RA[收益归因]
        CA[比较分析]
        RG[报告生成器]
    end
    
    subgraph "分析维度"
        RET[收益分析]
        RISK[风险分析]
        ATTR[归因分析]
        BENCH[基准比较]
    end
    
    subgraph "输出格式"
        HTML[HTML报告]
        PDF[PDF报告]
        JSON[JSON数据]
        CHART[图表]
    end
    
    PA --> MC
    PA --> RA
    PA --> CA
    PA --> RG
    
    MC --> RET
    MC --> RISK
    RA --> ATTR
    CA --> BENCH
    
    RG --> HTML
    RG --> PDF
    RG --> JSON
    RG --> CHART
```

#### 绩效指标计算

```python
class PerformanceMetricsCalculator:
    """绩效指标计算器"""
    
    def calculate_comprehensive_metrics(self, 
                                      returns: pd.Series,
                                      benchmark_returns: pd.Series = None,
                                      risk_free_rate: float = 0.02) -> PerformanceMetrics:
        """计算综合绩效指标"""
        
        metrics = PerformanceMetrics()
        
        # 基础收益指标
        metrics.total_return = self._calculate_total_return(returns)
        metrics.annual_return = self._calculate_annual_return(returns)
        metrics.volatility = self._calculate_volatility(returns)
        
        # 风险调整收益指标
        metrics.sharpe_ratio = self._calculate_sharpe_ratio(returns, risk_free_rate)
        metrics.sortino_ratio = self._calculate_sortino_ratio(returns, risk_free_rate)
        metrics.calmar_ratio = self._calculate_calmar_ratio(returns)
        
        # 回撤指标
        metrics.max_drawdown = self._calculate_max_drawdown(returns)
        metrics.avg_drawdown = self._calculate_avg_drawdown(returns)
        metrics.drawdown_duration = self._calculate_drawdown_duration(returns)
        
        # 相对基准指标
        if benchmark_returns is not None:
            metrics.alpha = self._calculate_alpha(returns, benchmark_returns, risk_free_rate)
            metrics.beta = self._calculate_beta(returns, benchmark_returns)
            metrics.information_ratio = self._calculate_information_ratio(returns, benchmark_returns)
            metrics.tracking_error = self._calculate_tracking_error(returns, benchmark_returns)
        
        return metrics
    
    def _calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float) -> float:
        """计算夏普比率"""
        excess_returns = returns - risk_free_rate / 252  # 日化无风险利率
        return excess_returns.mean() / excess_returns.std() * np.sqrt(252)
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """计算最大回撤"""
        cumulative_returns = (1 + returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        return drawdown.min()
```

## 数据库设计

### 数据库架构

```mermaid
erDiagram
    MARKET_DATA {
        string symbol PK
        timestamp timestamp PK
        string market PK
        float open
        float high
        float low
        float close
        bigint volume
        float adj_close
        float turnover
        string exchange
        string currency
    }
    
    ECONOMIC_DATA {
        string series_id PK
        timestamp timestamp PK
        float value
        string unit
        string frequency
        string seasonal_adjustment
        timestamp last_updated
        text notes
    }
    
    STRATEGIES {
        uuid id PK
        string name
        string class_name
        json parameters
        json supported_markets
        timestamp created_at
        timestamp updated_at
        boolean is_active
    }
    
    BACKTEST_RESULTS {
        uuid id PK
        uuid strategy_id FK
        date start_date
        date end_date
        json symbols
        json markets
        json performance_metrics
        json configuration
        timestamp created_at
    }
    
    TRADES {
        uuid id PK
        uuid backtest_id FK
        string symbol
        string side
        float quantity
        float price
        float commission
        timestamp timestamp
        json metadata
    }
    
    PORTFOLIO_HISTORY {
        uuid id PK
        uuid backtest_id FK
        timestamp timestamp
        float total_value
        float cash
        float positions_value
        json positions
        json metrics
    }
    
    RISK_ALERTS {
        uuid id PK
        uuid portfolio_id FK
        string alert_type
        string severity
        string message
        json details
        timestamp timestamp
        boolean is_resolved
    }
    
    STRATEGIES ||--o{ BACKTEST_RESULTS : "has"
    BACKTEST_RESULTS ||--o{ TRADES : "contains"
    BACKTEST_RESULTS ||--o{ PORTFOLIO_HISTORY : "tracks"
```

### 时间序列数据优化

#### TimescaleDB配置

```sql
-- 创建超表
SELECT create_hypertable('market_data', 'timestamp', 
                        partitioning_column => 'market',
                        number_partitions => 4);

-- 创建索引
CREATE INDEX idx_market_data_symbol_time ON market_data (symbol, timestamp DESC);
CREATE INDEX idx_market_data_market_time ON market_data (market, timestamp DESC);

-- 数据保留策略
SELECT add_retention_policy('market_data', INTERVAL '5 years');

-- 压缩策略
ALTER TABLE market_data SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'symbol, market'
);

SELECT add_compression_policy('market_data', INTERVAL '30 days');
```

#### 分区策略

```python
class DatabasePartitionManager:
    """数据库分区管理器"""
    
    def __init__(self, db_connection):
        self.db = db_connection
    
    def create_monthly_partitions(self, table_name: str, start_date: datetime, end_date: datetime):
        """创建月度分区"""
        current_date = start_date.replace(day=1)
        
        while current_date <= end_date:
            next_month = (current_date + timedelta(days=32)).replace(day=1)
            partition_name = f"{table_name}_{current_date.strftime('%Y_%m')}"
            
            sql = f"""
            CREATE TABLE IF NOT EXISTS {partition_name} PARTITION OF {table_name}
            FOR VALUES FROM ('{current_date}') TO ('{next_month}');
            """
            
            self.db.execute(sql)
            current_date = next_month
    
    def optimize_partitions(self, table_name: str):
        """优化分区性能"""
        # 分析表统计信息
        self.db.execute(f"ANALYZE {table_name};")
        
        # 重建索引
        self.db.execute(f"REINDEX TABLE {table_name};")
```

## 缓存策略

### 多层缓存架构

```mermaid
graph TB
    subgraph "缓存层次"
        L1[应用内存缓存]
        L2[Redis缓存]
        L3[数据库查询缓存]
        L4[CDN缓存]
    end
    
    subgraph "缓存类型"
        MC[市场数据缓存]
        IC[指标计算缓存]
        RC[回测结果缓存]
        SC[策略配置缓存]
    end
    
    L1 --> MC
    L2 --> IC
    L3 --> RC
    L4 --> SC
```

### 缓存实现

```python
class CacheManager:
    """缓存管理器"""
    
    def __init__(self, redis_client, local_cache_size=1000):
        self.redis = redis_client
        self.local_cache = LRUCache(maxsize=local_cache_size)
        self.cache_stats = CacheStats()
    
    def get(self, key: str, cache_level: CacheLevel = CacheLevel.ALL) -> Any:
        """获取缓存数据"""
        
        # L1: 本地内存缓存
        if cache_level in [CacheLevel.LOCAL, CacheLevel.ALL]:
            if key in self.local_cache:
                self.cache_stats.record_hit('local')
                return self.local_cache[key]
        
        # L2: Redis缓存
        if cache_level in [CacheLevel.REDIS, CacheLevel.ALL]:
            data = self.redis.get(key)
            if data:
                self.cache_stats.record_hit('redis')
                # 回填本地缓存
                self.local_cache[key] = pickle.loads(data)
                return self.local_cache[key]
        
        self.cache_stats.record_miss()
        return None
    
    def set(self, key: str, value: Any, ttl: int = 3600, 
            cache_level: CacheLevel = CacheLevel.ALL):
        """设置缓存数据"""
        
        # 设置本地缓存
        if cache_level in [CacheLevel.LOCAL, CacheLevel.ALL]:
            self.local_cache[key] = value
        
        # 设置Redis缓存
        if cache_level in [CacheLevel.REDIS, CacheLevel.ALL]:
            self.redis.setex(key, ttl, pickle.dumps(value))
    
    def invalidate(self, pattern: str):
        """失效缓存"""
        # 清理本地缓存
        keys_to_remove = [k for k in self.local_cache.keys() if fnmatch.fnmatch(k, pattern)]
        for key in keys_to_remove:
            del self.local_cache[key]
        
        # 清理Redis缓存
        keys = self.redis.keys(pattern)
        if keys:
            self.redis.delete(*keys)

class CacheDecorator:
    """缓存装饰器"""
    
    def __init__(self, cache_manager: CacheManager, ttl: int = 3600):
        self.cache_manager = cache_manager
        self.ttl = ttl
    
    def __call__(self, func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = self._generate_cache_key(func.__name__, args, kwargs)
            
            # 尝试从缓存获取
            result = self.cache_manager.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            self.cache_manager.set(cache_key, result, self.ttl)
            
            return result
        
        return wrapper
    
    def _generate_cache_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        key_parts = [func_name]
        key_parts.extend(str(arg) for arg in args)
        key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
        return ":".join(key_parts)

# 使用示例
@CacheDecorator(cache_manager, ttl=1800)
def calculate_technical_indicator(symbol: str, indicator_type: str, period: int) -> pd.Series:
    """计算技术指标（带缓存）"""
    # 实际计算逻辑
    pass
```

## 监控和可观测性

### 监控架构

```mermaid
graph TB
    subgraph "监控组件"
        METRICS[指标收集]
        LOGS[日志收集]
        TRACES[链路追踪]
        ALERTS[告警系统]
    end
    
    subgraph "存储系统"
        PROM[Prometheus]
        ELK[ELK Stack]
        JAEGER[Jaeger]
    end
    
    subgraph "可视化"
        GRAFANA[Grafana]
        KIBANA[Kibana]
        DASH[自定义仪表板]
    end
    
    METRICS --> PROM
    LOGS --> ELK
    TRACES --> JAEGER
    
    PROM --> GRAFANA
    ELK --> KIBANA
    PROM --> ALERTS
    
    GRAFANA --> DASH
```

### 监控实现

```python
class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.metrics_collector = MetricsCollector()
        self.health_checker = HealthChecker()
        self.alert_manager = AlertManager()
    
    def start_monitoring(self):
        """启动监控"""
        # 启动指标收集
        self.metrics_collector.start()
        
        # 启动健康检查
        self.health_checker.start()
        
        # 启动告警监控
        self.alert_manager.start()
    
    def collect_system_metrics(self):
        """收集系统指标"""
        metrics = {
            'cpu_usage': psutil.cpu_percent(),
            'memory_usage': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent,
            'active_connections': len(psutil.net_connections()),
            'process_count': len(psutil.pids())
        }
        
        self.metrics_collector.record_metrics('system', metrics)
    
    def collect_application_metrics(self):
        """收集应用指标"""
        metrics = {
            'active_strategies': self._count_active_strategies(),
            'running_backtests': self._count_running_backtests(),
            'api_requests_per_minute': self._get_api_request_rate(),
            'cache_hit_rate': self._get_cache_hit_rate(),
            'database_connections': self._get_db_connection_count()
        }
        
        self.metrics_collector.record_metrics('application', metrics)

class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self.checks = [
            DatabaseHealthCheck(),
            RedisHealthCheck(),
            DataSourceHealthCheck(),
            APIHealthCheck()
        ]
    
    def run_health_checks(self) -> HealthStatus:
        """执行健康检查"""
        results = {}
        overall_status = HealthStatus.HEALTHY
        
        for check in self.checks:
            try:
                result = check.check()
                results[check.name] = result
                
                if result.status == HealthStatus.UNHEALTHY:
                    overall_status = HealthStatus.UNHEALTHY
                elif result.status == HealthStatus.DEGRADED and overall_status == HealthStatus.HEALTHY:
                    overall_status = HealthStatus.DEGRADED
                    
            except Exception as e:
                results[check.name] = HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message=f"Health check failed: {str(e)}"
                )
                overall_status = HealthStatus.UNHEALTHY
        
        return HealthStatus(
            overall_status=overall_status,
            component_results=results,
            timestamp=datetime.now()
        )
```

## 安全架构

### 安全层次

```mermaid
graph TB
    subgraph "安全层次"
        NET[网络安全]
        AUTH[认证授权]
        DATA[数据安全]
        APP[应用安全]
        AUDIT[审计日志]
    end
    
    subgraph "安全组件"
        WAF[Web应用防火墙]
        JWT[JWT令牌]
        ENC[数据加密]
        RBAC[角色权限控制]
        LOG[安全日志]
    end
    
    NET --> WAF
    AUTH --> JWT
    DATA --> ENC
    APP --> RBAC
    AUDIT --> LOG
```

### 安全实现

```python
class SecurityManager:
    """安全管理器"""
    
    def __init__(self, config: SecurityConfig):
        self.config = config
        self.jwt_manager = JWTManager(config.jwt_secret)
        self.encryption_manager = EncryptionManager(config.encryption_key)
        self.rbac_manager = RBACManager()
        self.audit_logger = AuditLogger()
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """用户认证"""
        # 密码哈希验证
        user = self._get_user_by_username(username)
        if not user or not self._verify_password(password, user.password_hash):
            self.audit_logger.log_failed_login(username)
            return None
        
        # 检查用户状态
        if not user.is_active:
            self.audit_logger.log_inactive_user_login(username)
            return None
        
        self.audit_logger.log_successful_login(username)
        return user
    
    def authorize_request(self, token: str, required_permission: str) -> bool:
        """请求授权"""
        try:
            # 验证JWT令牌
            payload = self.jwt_manager.decode_token(token)
            user_id = payload.get('user_id')
            
            # 检查权限
            has_permission = self.rbac_manager.check_permission(user_id, required_permission)
            
            if not has_permission:
                self.audit_logger.log_unauthorized_access(user_id, required_permission)
            
            return has_permission
            
        except JWTError as e:
            self.audit_logger.log_invalid_token(str(e))
            return False
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        return self.encryption_manager.encrypt(data)
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        return self.encryption_manager.decrypt(encrypted_data)

class RateLimiter:
    """请求限流器"""
    
    def __init__(self, redis_client):
        self.redis = redis_client
    
    def is_allowed(self, key: str, limit: int, window: int) -> bool:
        """检查是否允许请求"""
        current_time = int(time.time())
        window_start = current_time - window
        
        # 使用滑动窗口算法
        pipe = self.redis.pipeline()
        pipe.zremrangebyscore(key, 0, window_start)
        pipe.zcard(key)
        pipe.zadd(key, {str(current_time): current_time})
        pipe.expire(key, window)
        
        results = pipe.execute()
        current_requests = results[1]
        
        return current_requests < limit
```

## 部署架构

### 本地部署架构

```
量化交易系统本地部署
├── Python虚拟环境 (venv)
├── SQLite数据库 (data/trading_system.db)
├── 日志文件 (logs/)
├── 配置文件 (config/)
└── 数据文件 (data/)
```

#### 系统组件

1. **应用服务**
   - FastAPI Web服务 (端口: 8000)
   - PyQt5 GUI应用
   - 后台数据同步服务

2. **数据存储**
   - SQLite数据库 (本地文件)
   - 文件缓存系统
   - 日志文件存储

3. **配置管理**
   - YAML配置文件
   - 环境变量
   - 运行时配置

#### 目录结构

```
quantitative-trading-system/
├── venv/                    # Python虚拟环境
├── src/                     # 源代码
├── data/                    # 数据文件
│   ├── trading_system.db    # SQLite数据库
│   ├── cache/              # 缓存文件
│   └── exports/            # 导出文件
├── logs/                    # 日志文件
│   ├── app.log             # 应用日志
│   ├── error.log           # 错误日志
│   └── trading.log         # 交易日志
├── config/                  # 配置文件
│   ├── config.yaml         # 主配置
│   └── data_sources.yaml   # 数据源配置
└── .env                    # 环境变量
```

#### 启动流程

```bash
# 1. 激活虚拟环境
source venv/bin/activate

# 2. 启动API服务
python src/api/main.py &

# 3. 启动GUI应用
python src/ui/main_app.py

# 4. 或运行回测脚本
python examples/run_backtest.py
```
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 性能优化

### 性能优化策略

1. **数据库优化**
   - 索引优化
   - 查询优化
   - 连接池配置
   - 分区策略

2. **应用优化**
   - 异步处理
   - 缓存策略
   - 批量操作
   - 内存管理

3. **网络优化**
   - CDN加速
   - 压缩传输
   - 连接复用
   - 负载均衡

### 性能监控

```python
class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.metrics = defaultdict(list)
    
    def profile_function(self, func_name: str):
        """函数性能分析装饰器"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                start_memory = psutil.Process().memory_info().rss
                
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.time()
                    end_memory = psutil.Process().memory_info().rss
                    
                    execution_time = end_time - start_time
                    memory_delta = end_memory - start_memory
                    
                    self.metrics[func_name].append({
                        'execution_time': execution_time,
                        'memory_delta': memory_delta,
                        'timestamp': datetime.now()
                    })
            
            return wrapper
        return decorator
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        report = {}
        
        for func_name, measurements in self.metrics.items():
            execution_times = [m['execution_time'] for m in measurements]
            memory_deltas = [m['memory_delta'] for m in measurements]
            
            report[func_name] = {
                'call_count': len(measurements),
                'avg_execution_time': np.mean(execution_times),
                'max_execution_time': np.max(execution_times),
                'min_execution_time': np.min(execution_times),
                'avg_memory_delta': np.mean(memory_deltas),
                'max_memory_delta': np.max(memory_deltas)
            }
        
        return report
```

这个系统架构文档详细描述了量化交易系统的整体设计思路、核心组件实现、数据库设计、缓存策略、监控体系、安全架构和部署方案，为系统的开发、维护和扩展提供了完整的技术指导。