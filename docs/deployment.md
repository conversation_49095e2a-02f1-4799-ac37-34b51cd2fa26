# 系统配置和部署指南

## 概述

本文档描述了量化交易系统的配置管理和部署流程，包括环境配置、数据库初始化、系统启动和监控等。

## 配置管理

### 环境配置

系统支持三种环境配置：

- **development**: 开发环境
- **testing**: 测试环境  
- **production**: 生产环境

环境通过 `TRADING_ENV` 环境变量指定，默认为 `development`。

### 配置文件结构

```
config/
├── config.yaml              # 基础配置
├── data_sources.yaml        # 数据源配置
├── market_config.yaml       # 市场配置
├── settings.py              # 配置管理器
└── environments/            # 环境特定配置
    ├── development.yaml
    ├── testing.yaml
    └── production.yaml
```

### 配置优先级

配置加载优先级（从低到高）：

1. 基础配置 (`config.yaml`)
2. 环境配置 (`environments/{env}.yaml`)
3. 环境变量

### 环境变量

支持的环境变量：

```bash
# 环境设置
TRADING_ENV=production

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=trading_system_prod
DB_USER=postgres
DB_PASSWORD=your_password

# API配置
API_HOST=0.0.0.0
API_PORT=8000
JWT_SECRET=your_jwt_secret
SECRET_KEY=your_secret_key

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 告警配置
ALERT_WEBHOOK_URL=https://hooks.slack.com/...
```

## 数据库管理

### 初始化数据库

```bash
# 初始化数据库（创建表、索引、默认数据）
python scripts/database/init_db.py
```

### 数据库迁移

```bash
# 执行所有待应用的迁移
python scripts/database/migrate.py migrate

# 查看迁移状态
python scripts/database/migrate.py status

# 回滚到指定版本
python scripts/database/migrate.py rollback 001
```

### 数据库架构

主要数据表：

- `market_data`: 市场数据（支持TimescaleDB超表）
- `economic_data`: 经济数据
- `strategies`: 策略配置
- `backtest_results`: 回测结果
- `trades`: 交易记录
- `system_logs`: 系统日志

## 系统部署

### 手动部署

1. **检查系统要求**
   ```bash
   # Python 3.8+
   python3 --version
   
   # PostgreSQL
   psql --version
   
   # Redis (可选)
   redis-cli --version
   ```

2. **安装依赖**
   ```bash
   # 创建虚拟环境
   python3 -m venv venv
   source venv/bin/activate
   
   # 安装依赖
   pip install -r requirements.txt
   ```

3. **配置环境**
   ```bash
   # 复制环境变量模板
   cp .env.example .env
   
   # 编辑配置
   vim .env
   ```

4. **初始化数据库**
   ```bash
   python scripts/database/init_db.py
   ```

5. **验证配置**
   ```bash
   python scripts/validate_config.py
   ```

6. **启动系统**
   ```bash
   python scripts/start_system.py
   ```

### 自动部署

使用部署脚本进行自动化部署：

```bash
# 生产环境部署
./scripts/deploy.sh production

# 开发环境部署
./scripts/deploy.sh development

# 跳过依赖安装
./scripts/deploy.sh production true
```

部署脚本会自动：

- 检查系统要求
- 安装依赖
- 初始化数据库
- 创建systemd服务
- 配置日志轮转
- 设置监控任务
- 配置防火墙
- 创建备份脚本

### 本地部署

1. **创建虚拟环境**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # 或 venv\Scripts\activate  # Windows
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **环境变量配置**
   ```bash
   # 创建环境变量文件
   cat > .env << EOF
   # 数据库配置（使用SQLite）
   DATABASE_URL=sqlite:///data/trading_system.db
   
   # API配置
   JWT_SECRET=$(openssl rand -hex 32)
   SECRET_KEY=$(openssl rand -hex 32)
   
   # 数据源API密钥（可选）
   TUSHARE_TOKEN=your_tushare_token
   FRED_API_KEY=your_fred_api_key
   EOF
   ```

4. **初始化数据库**
   ```bash
   python -c "from src.data.manager import DataManager; DataManager(use_sqlite=True)"
   ```

5. **启动系统**
   ```bash
   # 启动API服务
   python src/api/main.py
   
   # 或启动GUI界面
   python src/ui/main_app.py
   ```

## 系统监控

### 健康检查

```bash
# 完整健康检查
python scripts/monitoring/health_check.py

# 保存结果到文件
python scripts/monitoring/health_check.py health_report.json
```

健康检查项目：

- 数据库连接
- API服务状态
- Redis连接
- 磁盘空间
- 内存使用
- CPU使用
- 日志文件状态
- 数据新鲜度

### 系统状态

```bash
# 综合状态
python scripts/system_status.py

# 特定组件状态
python scripts/system_status.py database
python scripts/system_status.py api
python scripts/system_status.py processes
```

### 日志管理

系统使用分层日志管理：

1. **文件日志**: 轮转日志文件
2. **数据库日志**: 结构化日志存储（生产环境）
3. **Syslog**: 系统日志集成（生产环境）
4. **JSON格式**: 便于日志分析

日志配置：

```python
# 设置日志系统
from src.monitoring.logging_manager import setup_logging
setup_logging()

# 获取日志器
from src.monitoring.logging_manager import get_logger
logger = get_logger(__name__)

# 错误追踪
from src.monitoring.logging_manager import track_error
try:
    # 业务逻辑
    pass
except Exception as e:
    track_error(e, {'context': 'additional_info'})
```

### 服务管理

使用systemd管理系统服务：

```bash
# 服务状态
sudo systemctl status trading-system

# 启动/停止/重启
sudo systemctl start trading-system
sudo systemctl stop trading-system
sudo systemctl restart trading-system

# 查看日志
sudo journalctl -u trading-system -f

# 启用/禁用自启动
sudo systemctl enable trading-system
sudo systemctl disable trading-system
```

## 配置验证

### 验证脚本

```bash
# 验证所有配置
python scripts/validate_config.py

# 退出码说明：
# 0: 验证通过
# 1: 有错误
# 2: 有警告但无错误
```

### 验证项目

- 数据库配置有效性
- API配置安全性
- 安全配置强度
- 日志配置可用性
- 风险管理参数
- 数据源配置
- 环境变量完整性
- 连接测试

## 备份和恢复

### 自动备份

系统会自动创建备份任务：

```bash
# 数据库备份（每天3点执行）
0 3 * * * /path/to/project/scripts/backup.sh

# 日志清理（每天2点执行）
0 2 * * * /path/to/project/venv/bin/python -c "from src.monitoring.logging_manager import logging_manager; logging_manager.cleanup_old_logs(30)"
```

### 手动备份

```bash
# 数据库备份
pg_dump trading_system_prod | gzip > backup_$(date +%Y%m%d).sql.gz

# 配置备份
tar -czf config_backup_$(date +%Y%m%d).tar.gz config/
```

### 恢复

```bash
# 恢复数据库
gunzip -c backup_20240724.sql.gz | psql trading_system_prod

# 恢复配置
tar -xzf config_backup_20240724.tar.gz
```

## 性能优化

### 数据库优化

1. **索引优化**: 自动创建性能索引
2. **TimescaleDB**: 时间序列数据优化
3. **连接池**: 配置合适的连接池大小
4. **分区**: 按时间和市场分区

### 应用优化

1. **缓存**: Redis缓存热点数据
2. **异步处理**: Celery任务队列
3. **批量操作**: 批量数据库操作
4. **内存管理**: 合理的内存使用

### 监控指标

- 响应时间
- 吞吐量
- 错误率
- 资源使用率
- 数据库性能

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 检查网络连接

2. **API服务无响应**
   - 检查进程状态
   - 查看错误日志
   - 验证端口占用

3. **内存不足**
   - 检查内存使用
   - 优化数据处理
   - 增加交换空间

4. **磁盘空间不足**
   - 清理旧日志
   - 压缩数据文件
   - 扩展存储空间

### 日志分析

```bash
# 查看错误日志
grep -i error /var/log/trading_system/app.log

# 查看最近的警告
tail -f /var/log/trading_system/app.log | grep WARNING

# 分析API访问日志
grep "POST\|GET" /var/log/trading_system/app.log | tail -100
```

### 性能分析

```bash
# 系统资源使用
htop

# 数据库性能
psql -c "SELECT * FROM pg_stat_activity;"

# API性能测试
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:8000/health"
```

## 安全配置

### 生产环境安全

1. **密钥管理**: 使用强密钥，定期轮换
2. **网络安全**: 配置防火墙，限制访问
3. **认证授权**: 启用API认证
4. **数据加密**: 数据库连接加密
5. **审计日志**: 记录所有操作

### 安全检查清单

- [ ] 更改默认密码
- [ ] 配置SSL/TLS
- [ ] 限制网络访问
- [ ] 启用审计日志
- [ ] 定期安全更新
- [ ] 备份加密
- [ ] 访问控制

## 扩展和集群

### 水平扩展

1. **负载均衡**: Nginx反向代理
2. **数据库集群**: PostgreSQL主从复制
3. **缓存集群**: Redis Cluster
4. **应用集群**: 多实例部署

### 监控和告警

1. **Prometheus**: 指标收集
2. **Grafana**: 可视化监控
3. **AlertManager**: 告警管理
4. **日志聚合**: ELK Stack

这个配置和部署系统提供了完整的生产级部署能力，包括环境管理、数据库初始化、系统监控、日志管理和错误追踪等功能。