#!/usr/bin/env python3
"""
系统启动脚本
"""

import sys
import os
import time
import signal
import logging
import subprocess
import threading
from pathlib import Path
from typing import List, Dict, Optional
import psutil

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import config


class ProcessManager:
    """进程管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.processes: Dict[str, subprocess.Popen] = {}
        self.shutdown_event = threading.Event()
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，开始关闭系统...")
        self.shutdown_event.set()
        self.stop_all_processes()
        sys.exit(0)
    
    def start_process(self, name: str, command: List[str], cwd: str = None, env: Dict[str, str] = None) -> bool:
        """启动进程"""
        try:
            self.logger.info(f"启动进程: {name}")
            self.logger.debug(f"命令: {' '.join(command)}")
            
            # 合并环境变量
            process_env = os.environ.copy()
            if env:
                process_env.update(env)
            
            # 启动进程
            process = subprocess.Popen(
                command,
                cwd=cwd or str(project_root),
                env=process_env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            self.processes[name] = process
            
            # 启动日志监控线程
            threading.Thread(
                target=self._monitor_process_output,
                args=(name, process),
                daemon=True
            ).start()
            
            # 检查进程是否成功启动
            time.sleep(1)
            if process.poll() is None:
                self.logger.info(f"进程 {name} 启动成功 (PID: {process.pid})")
                return True
            else:
                self.logger.error(f"进程 {name} 启动失败")
                return False
                
        except Exception as e:
            self.logger.error(f"启动进程 {name} 失败: {e}")
            return False
    
    def _monitor_process_output(self, name: str, process: subprocess.Popen):
        """监控进程输出"""
        try:
            while process.poll() is None and not self.shutdown_event.is_set():
                # 读取stdout
                if process.stdout:
                    line = process.stdout.readline()
                    if line:
                        self.logger.info(f"[{name}] {line.strip()}")
                
                # 读取stderr
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        self.logger.warning(f"[{name}] {line.strip()}")
                
                time.sleep(0.1)
                
        except Exception as e:
            self.logger.error(f"监控进程 {name} 输出失败: {e}")
    
    def stop_process(self, name: str, timeout: int = 10) -> bool:
        """停止进程"""
        if name not in self.processes:
            return True
        
        process = self.processes[name]
        
        try:
            self.logger.info(f"停止进程: {name}")
            
            # 发送SIGTERM信号
            process.terminate()
            
            # 等待进程结束
            try:
                process.wait(timeout=timeout)
                self.logger.info(f"进程 {name} 已正常停止")
            except subprocess.TimeoutExpired:
                # 强制杀死进程
                self.logger.warning(f"进程 {name} 未在 {timeout} 秒内停止，强制杀死")
                process.kill()
                process.wait()
            
            del self.processes[name]
            return True
            
        except Exception as e:
            self.logger.error(f"停止进程 {name} 失败: {e}")
            return False
    
    def stop_all_processes(self):
        """停止所有进程"""
        for name in list(self.processes.keys()):
            self.stop_process(name)
    
    def is_process_running(self, name: str) -> bool:
        """检查进程是否运行"""
        if name not in self.processes:
            return False
        
        process = self.processes[name]
        return process.poll() is None
    
    def get_process_status(self) -> Dict[str, Dict[str, any]]:
        """获取所有进程状态"""
        status = {}
        
        for name, process in self.processes.items():
            try:
                if process.poll() is None:
                    # 进程正在运行
                    ps_process = psutil.Process(process.pid)
                    status[name] = {
                        'status': 'running',
                        'pid': process.pid,
                        'cpu_percent': ps_process.cpu_percent(),
                        'memory_percent': ps_process.memory_percent(),
                        'create_time': ps_process.create_time()
                    }
                else:
                    # 进程已停止
                    status[name] = {
                        'status': 'stopped',
                        'exit_code': process.returncode
                    }
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                status[name] = {
                    'status': 'unknown'
                }
        
        return status


class SystemStarter:
    """系统启动器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.process_manager = ProcessManager()
        
        # 设置日志
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志"""
        # 创建日志目录
        log_dir = Path(config.logging.file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置日志格式
        formatter = logging.Formatter(config.logging.format)
        
        # 文件处理器
        file_handler = logging.FileHandler(
            log_dir / "system_startup.log",
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
    
    def check_dependencies(self) -> bool:
        """检查系统依赖"""
        self.logger.info("检查系统依赖...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            self.logger.error("需要Python 3.8或更高版本")
            return False
        
        # 检查必要的包
        required_packages = [
            'pandas', 'numpy', 'sqlalchemy', 'psycopg2', 
            'fastapi', 'uvicorn', 'redis', 'celery'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            self.logger.error(f"缺少必要的包: {', '.join(missing_packages)}")
            return False
        
        # 检查数据库连接
        try:
            from sqlalchemy import create_engine
            engine = create_engine(config.get_database_url())
            with engine.connect():
                pass
            self.logger.info("数据库连接正常")
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
        
        # 检查Redis连接（如果配置了）
        if hasattr(config, 'cache') and config.cache.redis_url:
            try:
                import redis
                r = redis.from_url(config.cache.redis_url)
                r.ping()
                self.logger.info("Redis连接正常")
            except Exception as e:
                self.logger.warning(f"Redis连接失败: {e}")
        
        self.logger.info("系统依赖检查完成")
        return True
    
    def start_database_services(self) -> bool:
        """启动数据库相关服务"""
        self.logger.info("启动数据库服务...")
        
        # 运行数据库迁移
        migrate_cmd = [
            sys.executable, 
            str(project_root / "scripts" / "database" / "migrate.py"),
            "migrate"
        ]
        
        if not self.process_manager.start_process("db_migrate", migrate_cmd):
            return False
        
        # 等待迁移完成
        time.sleep(5)
        
        return True
    
    def start_api_server(self) -> bool:
        """启动API服务器"""
        self.logger.info("启动API服务器...")
        
        api_cmd = [
            sys.executable,
            "-m", "uvicorn",
            "src.api.app:app",
            "--host", config.api.host,
            "--port", str(config.api.port),
            "--reload" if config.is_development() else "--no-reload"
        ]
        
        env = {
            "TRADING_ENV": config.environment
        }
        
        return self.process_manager.start_process("api_server", api_cmd, env=env)
    
    def start_worker_processes(self) -> bool:
        """启动工作进程"""
        if not hasattr(config, 'worker'):
            return True
        
        self.logger.info("启动工作进程...")
        
        # 启动Celery worker
        worker_cmd = [
            sys.executable,
            "-m", "celery",
            "worker",
            "-A", "src.tasks.celery_app",
            "--loglevel=info",
            f"--concurrency={config.worker.concurrency}"
        ]
        
        env = {
            "TRADING_ENV": config.environment
        }
        
        return self.process_manager.start_process("celery_worker", worker_cmd, env=env)
    
    def start_monitoring_services(self) -> bool:
        """启动监控服务"""
        if not hasattr(config, 'monitoring') or not config.monitoring.enable_metrics:
            return True
        
        self.logger.info("启动监控服务...")
        
        # 启动指标收集器
        metrics_cmd = [
            sys.executable,
            str(project_root / "scripts" / "monitoring" / "metrics_collector.py")
        ]
        
        env = {
            "TRADING_ENV": config.environment
        }
        
        return self.process_manager.start_process("metrics_collector", metrics_cmd, env=env)
    
    def start_system(self) -> bool:
        """启动整个系统"""
        self.logger.info(f"启动量化交易系统 - 环境: {config.environment}")
        
        # 检查依赖
        if not self.check_dependencies():
            return False
        
        # 启动数据库服务
        if not self.start_database_services():
            return False
        
        # 启动API服务器
        if not self.start_api_server():
            return False
        
        # 启动工作进程
        if not self.start_worker_processes():
            return False
        
        # 启动监控服务
        if not self.start_monitoring_services():
            return False
        
        self.logger.info("系统启动完成!")
        return True
    
    def monitor_system(self):
        """监控系统状态"""
        self.logger.info("开始监控系统状态...")
        
        while not self.process_manager.shutdown_event.is_set():
            try:
                status = self.process_manager.get_process_status()
                
                # 检查关键进程
                critical_processes = ['api_server']
                for process_name in critical_processes:
                    if process_name in status:
                        process_status = status[process_name]
                        if process_status['status'] != 'running':
                            self.logger.error(f"关键进程 {process_name} 已停止，尝试重启...")
                            # 这里可以添加重启逻辑
                
                # 记录系统状态
                running_count = sum(1 for s in status.values() if s.get('status') == 'running')
                self.logger.debug(f"系统状态: {running_count}/{len(status)} 个进程运行中")
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                self.logger.error(f"监控系统状态失败: {e}")
                time.sleep(10)
    
    def run(self):
        """运行系统"""
        if self.start_system():
            try:
                self.monitor_system()
            except KeyboardInterrupt:
                self.logger.info("收到中断信号，正在关闭系统...")
            finally:
                self.process_manager.stop_all_processes()
        else:
            self.logger.error("系统启动失败")
            sys.exit(1)


def main():
    """主函数"""
    starter = SystemStarter()
    starter.run()


if __name__ == "__main__":
    main()