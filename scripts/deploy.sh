#!/bin/bash
# 系统部署脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查Python版本
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 未安装"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    if [[ $(echo "$python_version < 3.8" | bc -l) -eq 1 ]]; then
        log_error "需要Python 3.8或更高版本，当前版本: $python_version"
        exit 1
    fi
    
    # 检查PostgreSQL
    if ! command -v psql &> /dev/null; then
        log_warn "PostgreSQL客户端未安装，请确保数据库服务可用"
    fi
    
    # 检查Redis（可选）
    if ! command -v redis-cli &> /dev/null; then
        log_warn "Redis客户端未安装，缓存功能可能不可用"
    fi
    
    log_info "系统要求检查完成"
}

# 设置环境变量
setup_environment() {
    local env=${1:-production}
    
    log_info "设置环境: $env"
    
    # 创建环境变量文件
    cat > .env << EOF
# 量化交易系统环境配置
TRADING_ENV=$env

# 数据库配置
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-trading_system_prod}
DB_USER=${DB_USER:-postgres}
DB_PASSWORD=${DB_PASSWORD}

# API配置
API_HOST=${API_HOST:-0.0.0.0}
API_PORT=${API_PORT:-8000}
JWT_SECRET=${JWT_SECRET:-$(openssl rand -hex 32)}
SECRET_KEY=${SECRET_KEY:-$(openssl rand -hex 32)}

# Redis配置
REDIS_URL=${REDIS_URL:-redis://localhost:6379/0}

# 告警配置
ALERT_WEBHOOK_URL=${ALERT_WEBHOOK_URL}
EOF
    
    log_info "环境变量文件已创建: .env"
}

# 安装依赖
install_dependencies() {
    log_info "安装Python依赖..."
    
    # 创建虚拟环境
    if [[ ! -d "venv" ]]; then
        python3 -m venv venv
        log_info "虚拟环境已创建"
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    if [[ -f "requirements.txt" ]]; then
        pip install -r requirements.txt
        log_info "依赖安装完成"
    else
        log_error "requirements.txt 文件不存在"
        exit 1
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    source venv/bin/activate
    
    # 运行数据库初始化脚本
    python scripts/database/init_db.py
    
    # 运行数据库迁移
    python scripts/database/migrate.py migrate
    
    log_info "数据库初始化完成"
}

# 创建系统服务
create_systemd_service() {
    local service_name="trading-system"
    local working_dir=$(pwd)
    local user=$(whoami)
    
    log_info "创建systemd服务..."
    
    # 创建服务文件
    sudo tee /etc/systemd/system/${service_name}.service > /dev/null << EOF
[Unit]
Description=Quantitative Trading System
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=simple
User=$user
Group=$user
WorkingDirectory=$working_dir
Environment=PATH=$working_dir/venv/bin
ExecStart=$working_dir/venv/bin/python scripts/start_system.py
ExecReload=/bin/kill -HUP \$MAINPID
KillMode=mixed
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=trading-system

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=$working_dir

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd配置
    sudo systemctl daemon-reload
    
    # 启用服务
    sudo systemctl enable ${service_name}
    
    log_info "systemd服务已创建: ${service_name}"
}

# 创建日志轮转配置
create_logrotate_config() {
    local log_dir="/var/log/trading_system"
    
    log_info "创建日志轮转配置..."
    
    # 创建日志目录
    sudo mkdir -p $log_dir
    sudo chown $(whoami):$(whoami) $log_dir
    
    # 创建logrotate配置
    sudo tee /etc/logrotate.d/trading-system > /dev/null << EOF
$log_dir/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 $(whoami) $(whoami)
    postrotate
        systemctl reload trading-system || true
    endscript
}
EOF
    
    log_info "日志轮转配置已创建"
}

# 创建监控脚本
create_monitoring_cron() {
    log_info "创建监控定时任务..."
    
    # 添加健康检查定时任务
    (crontab -l 2>/dev/null; echo "*/5 * * * * $(pwd)/venv/bin/python $(pwd)/scripts/monitoring/health_check.py > /dev/null 2>&1") | crontab -
    
    # 添加日志清理定时任务
    (crontab -l 2>/dev/null; echo "0 2 * * * $(pwd)/venv/bin/python -c \"from src.monitoring.logging_manager import logging_manager; logging_manager.cleanup_old_logs(30)\"") | crontab -
    
    log_info "监控定时任务已创建"
}

# 设置防火墙
setup_firewall() {
    log_info "配置防火墙..."
    
    # 检查ufw是否安装
    if command -v ufw &> /dev/null; then
        # 允许SSH
        sudo ufw allow ssh
        
        # 允许API端口
        sudo ufw allow ${API_PORT:-8000}/tcp
        
        # 启用防火墙
        sudo ufw --force enable
        
        log_info "防火墙配置完成"
    else
        log_warn "ufw未安装，跳过防火墙配置"
    fi
}

# 创建备份脚本
create_backup_script() {
    log_info "创建备份脚本..."
    
    cat > scripts/backup.sh << 'EOF'
#!/bin/bash
# 数据库备份脚本

BACKUP_DIR="/var/backups/trading_system"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME=${DB_NAME:-trading_system_prod}

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
pg_dump $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# 删除30天前的备份
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete

echo "数据库备份完成: $BACKUP_DIR/db_backup_$DATE.sql.gz"
EOF
    
    chmod +x scripts/backup.sh
    
    # 添加备份定时任务
    (crontab -l 2>/dev/null; echo "0 3 * * * $(pwd)/scripts/backup.sh") | crontab -
    
    log_info "备份脚本已创建"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查服务状态
    if systemctl is-active --quiet trading-system; then
        log_info "系统服务运行正常"
    else
        log_error "系统服务未运行"
        return 1
    fi
    
    # 检查API健康状态
    sleep 5  # 等待服务启动
    
    local api_url="http://localhost:${API_PORT:-8000}/health"
    if curl -f -s $api_url > /dev/null; then
        log_info "API服务健康检查通过"
    else
        log_error "API服务健康检查失败"
        return 1
    fi
    
    # 运行系统健康检查
    source venv/bin/activate
    if python scripts/monitoring/health_check.py > /dev/null; then
        log_info "系统健康检查通过"
    else
        log_warn "系统健康检查发现问题，请查看详细报告"
    fi
    
    log_info "部署验证完成"
}

# 显示部署后信息
show_post_deployment_info() {
    log_info "部署完成！"
    echo
    echo "系统信息:"
    echo "  - 环境: ${TRADING_ENV:-production}"
    echo "  - API地址: http://localhost:${API_PORT:-8000}"
    echo "  - 日志目录: /var/log/trading_system"
    echo "  - 服务名称: trading-system"
    echo
    echo "常用命令:"
    echo "  - 查看服务状态: sudo systemctl status trading-system"
    echo "  - 启动服务: sudo systemctl start trading-system"
    echo "  - 停止服务: sudo systemctl stop trading-system"
    echo "  - 查看日志: sudo journalctl -u trading-system -f"
    echo "  - 健康检查: ./venv/bin/python scripts/monitoring/health_check.py"
    echo
}

# 主函数
main() {
    local environment=${1:-production}
    local skip_deps=${2:-false}
    
    log_info "开始部署量化交易系统..."
    
    # 检查用户权限
    check_root
    
    # 检查系统要求
    check_requirements
    
    # 设置环境
    setup_environment $environment
    
    # 安装依赖
    if [[ "$skip_deps" != "true" ]]; then
        install_dependencies
    fi
    
    # 初始化数据库
    init_database
    
    # 创建系统服务
    create_systemd_service
    
    # 创建日志轮转配置
    create_logrotate_config
    
    # 创建监控定时任务
    create_monitoring_cron
    
    # 设置防火墙
    setup_firewall
    
    # 创建备份脚本
    create_backup_script
    
    # 启动服务
    sudo systemctl start trading-system
    
    # 验证部署
    verify_deployment
    
    # 显示部署后信息
    show_post_deployment_info
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [environment] [skip_deps]"
    echo
    echo "参数:"
    echo "  environment  部署环境 (production|development|testing) [默认: production]"
    echo "  skip_deps    跳过依赖安装 (true|false) [默认: false]"
    echo
    echo "示例:"
    echo "  $0                    # 生产环境部署"
    echo "  $0 development        # 开发环境部署"
    echo "  $0 production true    # 生产环境部署，跳过依赖安装"
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac