#!/usr/bin/env python3
"""
配置验证脚本
"""

import sys
import os
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import config


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.errors = []
        self.warnings = []
    
    def validate_database_config(self) -> bool:
        """验证数据库配置"""
        valid = True
        
        # 检查必需字段
        required_fields = ['host', 'port', 'database', 'username']
        for field in required_fields:
            if not hasattr(config.database, field) or not getattr(config.database, field):
                self.errors.append(f"数据库配置缺少必需字段: {field}")
                valid = False
        
        # 检查端口范围
        if hasattr(config.database, 'port'):
            port = config.database.port
            if not isinstance(port, int) or port < 1 or port > 65535:
                self.errors.append(f"数据库端口无效: {port}")
                valid = False
        
        # 检查连接池配置
        if hasattr(config.database, 'pool_size'):
            pool_size = config.database.pool_size
            if not isinstance(pool_size, int) or pool_size < 1:
                self.errors.append(f"数据库连接池大小无效: {pool_size}")
                valid = False
        
        # 生产环境特殊检查
        if config.is_production():
            if not config.database.password:
                self.errors.append("生产环境必须设置数据库密码")
                valid = False
            
            if not hasattr(config.database, 'ssl_mode') or config.database.ssl_mode != 'require':
                self.warnings.append("生产环境建议启用SSL连接")
        
        return valid
    
    def validate_api_config(self) -> bool:
        """验证API配置"""
        valid = True
        
        # 检查端口
        if hasattr(config.api, 'port'):
            port = config.api.port
            if not isinstance(port, int) or port < 1 or port > 65535:
                self.errors.append(f"API端口无效: {port}")
                valid = False
        
        # 检查JWT密钥
        if hasattr(config.api, 'jwt_secret'):
            jwt_secret = config.api.jwt_secret
            if not jwt_secret or len(jwt_secret) < 32:
                self.errors.append("JWT密钥长度不足，建议至少32个字符")
                valid = False
        
        # 生产环境特殊检查
        if config.is_production():
            if hasattr(config.api, 'debug') and config.api.debug:
                self.warnings.append("生产环境不应启用调试模式")
            
            if hasattr(config.api, 'docs_enabled') and config.api.docs_enabled:
                self.warnings.append("生产环境建议关闭API文档")
            
            if hasattr(config.api, 'cors_origins') and '*' in config.api.cors_origins:
                self.warnings.append("生产环境不应允许所有CORS来源")
        
        return valid
    
    def validate_security_config(self) -> bool:
        """验证安全配置"""
        valid = True
        
        # 检查密钥强度
        if hasattr(config.security, 'secret_key'):
            secret_key = config.security.secret_key
            if not secret_key or len(secret_key) < 32:
                self.errors.append("安全密钥长度不足，建议至少32个字符")
                valid = False
            
            # 检查是否使用默认密钥
            default_keys = [
                'your-secret-key-change-in-production',
                'dev-secret-key',
                'test-secret-key'
            ]
            if secret_key in default_keys:
                self.errors.append("不应使用默认安全密钥")
                valid = False
        
        # 检查密码策略
        if hasattr(config.security, 'password_min_length'):
            min_length = config.security.password_min_length
            if min_length < 8:
                self.warnings.append("密码最小长度建议至少8个字符")
        
        # 检查登录限制
        if hasattr(config.security, 'max_login_attempts'):
            max_attempts = config.security.max_login_attempts
            if max_attempts > 10:
                self.warnings.append("最大登录尝试次数过高，建议不超过10次")
        
        return valid
    
    def validate_logging_config(self) -> bool:
        """验证日志配置"""
        valid = True
        
        # 检查日志级别
        if hasattr(config.logging, 'level'):
            level = config.logging.level.upper()
            valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if level not in valid_levels:
                self.errors.append(f"无效的日志级别: {level}")
                valid = False
        
        # 检查日志文件路径
        if hasattr(config.logging, 'file_path'):
            log_path = Path(config.logging.file_path)
            log_dir = log_path.parent
            
            # 检查目录是否可写
            try:
                log_dir.mkdir(parents=True, exist_ok=True)
                test_file = log_dir / '.write_test'
                test_file.touch()
                test_file.unlink()
            except (PermissionError, OSError) as e:
                self.errors.append(f"日志目录不可写: {log_dir} - {e}")
                valid = False
        
        # 检查文件大小限制
        if hasattr(config.logging, 'max_file_size'):
            max_size = config.logging.max_file_size
            if max_size < 1024 * 1024:  # 1MB
                self.warnings.append("日志文件大小限制过小，可能导致频繁轮转")
        
        return valid
    
    def validate_risk_config(self) -> bool:
        """验证风险管理配置"""
        valid = True
        
        # 检查仓位限制
        if hasattr(config.risk, 'max_position_size'):
            max_pos = config.risk.max_position_size
            if not isinstance(max_pos, (int, float)) or max_pos <= 0 or max_pos > 1:
                self.errors.append(f"最大仓位比例无效: {max_pos}，应在0-1之间")
                valid = False
        
        # 检查止损设置
        if hasattr(config.risk, 'stop_loss_pct'):
            stop_loss = config.risk.stop_loss_pct
            if not isinstance(stop_loss, (int, float)) or stop_loss <= 0 or stop_loss > 1:
                self.errors.append(f"止损比例无效: {stop_loss}，应在0-1之间")
                valid = False
        
        # 检查最大回撤
        if hasattr(config.risk, 'max_drawdown_pct'):
            max_dd = config.risk.max_drawdown_pct
            if not isinstance(max_dd, (int, float)) or max_dd <= 0 or max_dd > 1:
                self.errors.append(f"最大回撤比例无效: {max_dd}，应在0-1之间")
                valid = False
        
        return valid
    
    def validate_data_sources_config(self) -> bool:
        """验证数据源配置"""
        valid = True
        
        if not config.data_sources:
            self.warnings.append("未配置任何数据源")
            return valid
        
        for name, ds_config in config.data_sources.items():
            # 检查必需字段
            if not hasattr(ds_config, 'adapter_class') or not ds_config.adapter_class:
                self.errors.append(f"数据源 {name} 缺少适配器类")
                valid = False
            
            # 检查优先级
            if hasattr(ds_config, 'priority'):
                priority = ds_config.priority
                if not isinstance(priority, int) or priority < 1:
                    self.errors.append(f"数据源 {name} 优先级无效: {priority}")
                    valid = False
        
        return valid
    
    def validate_environment_variables(self) -> bool:
        """验证环境变量"""
        valid = True
        
        # 生产环境必需的环境变量
        if config.is_production():
            required_env_vars = [
                'DB_PASSWORD',
                'JWT_SECRET',
                'SECRET_KEY'
            ]
            
            for var in required_env_vars:
                if not os.getenv(var):
                    self.errors.append(f"生产环境缺少必需的环境变量: {var}")
                    valid = False
        
        return valid
    
    def test_database_connection(self) -> bool:
        """测试数据库连接"""
        try:
            from sqlalchemy import create_engine, text
            
            engine = create_engine(config.get_database_url())
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            self.logger.info("数据库连接测试成功")
            return True
            
        except Exception as e:
            self.errors.append(f"数据库连接测试失败: {e}")
            return False
    
    def test_redis_connection(self) -> bool:
        """测试Redis连接"""
        if not hasattr(config, 'cache') or not config.cache.redis_url:
            return True  # Redis是可选的
        
        try:
            import redis
            
            r = redis.from_url(config.cache.redis_url)
            r.ping()
            
            self.logger.info("Redis连接测试成功")
            return True
            
        except Exception as e:
            self.warnings.append(f"Redis连接测试失败: {e}")
            return False
    
    def validate_all(self) -> Tuple[bool, List[str], List[str]]:
        """执行所有验证"""
        self.logger.info(f"开始验证配置 - 环境: {config.environment}")
        
        validators = [
            self.validate_database_config,
            self.validate_api_config,
            self.validate_security_config,
            self.validate_logging_config,
            self.validate_risk_config,
            self.validate_data_sources_config,
            self.validate_environment_variables
        ]
        
        all_valid = True
        for validator in validators:
            if not validator():
                all_valid = False
        
        # 连接测试
        self.test_database_connection()
        self.test_redis_connection()
        
        return all_valid, self.errors, self.warnings
    
    def print_results(self, valid: bool, errors: List[str], warnings: List[str]):
        """打印验证结果"""
        if valid and not errors:
            print("✅ 配置验证通过")
        else:
            print("❌ 配置验证失败")
        
        if errors:
            print(f"\n错误 ({len(errors)}):")
            for i, error in enumerate(errors, 1):
                print(f"  {i}. {error}")
        
        if warnings:
            print(f"\n警告 ({len(warnings)}):")
            for i, warning in enumerate(warnings, 1):
                print(f"  {i}. {warning}")
        
        print(f"\n环境: {config.environment}")
        print(f"数据库: {config.database.host}:{config.database.port}/{config.database.database}")
        print(f"API: {config.api.host}:{config.api.port}")


def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    validator = ConfigValidator()
    valid, errors, warnings = validator.validate_all()
    
    validator.print_results(valid, errors, warnings)
    
    # 设置退出码
    if errors:
        sys.exit(1)
    elif warnings:
        sys.exit(2)  # 有警告但无错误
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()