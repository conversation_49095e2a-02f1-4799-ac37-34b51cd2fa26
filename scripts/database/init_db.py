#!/usr/bin/env python3
"""
数据库初始化脚本
"""

import sys
import os
import logging
from pathlib import Path
from typing import Optional

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from config.settings import config


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def create_database_if_not_exists(logger: logging.Logger) -> bool:
    """创建数据库（如果不存在）"""
    try:
        # 连接到默认的postgres数据库
        conn = psycopg2.connect(
            host=config.database.host,
            port=config.database.port,
            user=config.database.username,
            password=config.database.password,
            database='postgres'
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        
        cursor = conn.cursor()
        
        # 检查数据库是否存在
        cursor.execute(
            "SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s",
            (config.database.database,)
        )
        
        if cursor.fetchone():
            logger.info(f"数据库 '{config.database.database}' 已存在")
        else:
            # 创建数据库
            cursor.execute(f'CREATE DATABASE "{config.database.database}"')
            logger.info(f"数据库 '{config.database.database}' 创建成功")
        
        cursor.close()
        conn.close()
        return True
        
    except psycopg2.Error as e:
        logger.error(f"创建数据库失败: {e}")
        return False


def create_extensions(logger: logging.Logger) -> bool:
    """创建必要的数据库扩展"""
    try:
        engine = create_engine(config.get_database_url())
        
        with engine.connect() as conn:
            # 创建TimescaleDB扩展（如果可用）
            try:
                conn.execute(text("CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE"))
                logger.info("TimescaleDB扩展创建成功")
            except SQLAlchemyError:
                logger.warning("TimescaleDB扩展不可用，将使用标准PostgreSQL")
            
            # 创建其他有用的扩展
            extensions = [
                "uuid-ossp",  # UUID生成
                "pg_stat_statements",  # 查询统计
                "btree_gin",  # GIN索引支持
                "pg_trgm"  # 文本相似度搜索
            ]
            
            for ext in extensions:
                try:
                    conn.execute(text(f"CREATE EXTENSION IF NOT EXISTS \"{ext}\""))
                    logger.info(f"扩展 '{ext}' 创建成功")
                except SQLAlchemyError as e:
                    logger.warning(f"扩展 '{ext}' 创建失败: {e}")
            
            conn.commit()
        
        return True
        
    except SQLAlchemyError as e:
        logger.error(f"创建扩展失败: {e}")
        return False


def create_tables(logger: logging.Logger) -> bool:
    """创建数据表"""
    try:
        engine = create_engine(config.get_database_url())
        
        # 读取SQL脚本
        sql_file = Path(__file__).parent / "schema.sql"
        if not sql_file.exists():
            logger.error(f"SQL脚本文件不存在: {sql_file}")
            return False
        
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 执行SQL脚本
        with engine.connect() as conn:
            # 分割SQL语句并执行
            statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            for stmt in statements:
                try:
                    conn.execute(text(stmt))
                    logger.debug(f"执行SQL: {stmt[:50]}...")
                except SQLAlchemyError as e:
                    logger.error(f"执行SQL失败: {stmt[:50]}... - {e}")
                    return False
            
            conn.commit()
        
        logger.info("数据表创建成功")
        return True
        
    except Exception as e:
        logger.error(f"创建数据表失败: {e}")
        return False


def create_indexes(logger: logging.Logger) -> bool:
    """创建索引"""
    try:
        engine = create_engine(config.get_database_url())
        
        indexes_sql = """
        -- 市场数据索引
        CREATE INDEX IF NOT EXISTS idx_market_data_symbol_timestamp 
        ON market_data (symbol, timestamp);
        
        CREATE INDEX IF NOT EXISTS idx_market_data_timestamp 
        ON market_data (timestamp);
        
        CREATE INDEX IF NOT EXISTS idx_market_data_market 
        ON market_data (market);
        
        -- 经济数据索引
        CREATE INDEX IF NOT EXISTS idx_economic_data_series_timestamp 
        ON economic_data (series_id, timestamp);
        
        CREATE INDEX IF NOT EXISTS idx_economic_data_timestamp 
        ON economic_data (timestamp);
        
        -- 交易记录索引
        CREATE INDEX IF NOT EXISTS idx_trades_strategy_timestamp 
        ON trades (strategy_id, timestamp);
        
        CREATE INDEX IF NOT EXISTS idx_trades_symbol 
        ON trades (symbol);
        
        -- 回测结果索引
        CREATE INDEX IF NOT EXISTS idx_backtest_results_strategy 
        ON backtest_results (strategy_id);
        
        CREATE INDEX IF NOT EXISTS idx_backtest_results_created 
        ON backtest_results (created_at);
        """
        
        with engine.connect() as conn:
            conn.execute(text(indexes_sql))
            conn.commit()
        
        logger.info("索引创建成功")
        return True
        
    except SQLAlchemyError as e:
        logger.error(f"创建索引失败: {e}")
        return False


def setup_timescale_hypertables(logger: logging.Logger) -> bool:
    """设置TimescaleDB超表（如果可用）"""
    try:
        engine = create_engine(config.get_database_url())
        
        with engine.connect() as conn:
            # 检查TimescaleDB是否可用
            result = conn.execute(text(
                "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'timescaledb')"
            ))
            
            if not result.scalar():
                logger.info("TimescaleDB不可用，跳过超表设置")
                return True
            
            # 创建超表
            hypertables = [
                ("market_data", "timestamp"),
                ("economic_data", "timestamp"),
                ("trades", "timestamp")
            ]
            
            for table_name, time_column in hypertables:
                try:
                    conn.execute(text(
                        f"SELECT create_hypertable('{table_name}', '{time_column}', "
                        f"if_not_exists => TRUE)"
                    ))
                    logger.info(f"超表 '{table_name}' 创建成功")
                except SQLAlchemyError as e:
                    logger.warning(f"超表 '{table_name}' 创建失败: {e}")
            
            conn.commit()
        
        return True
        
    except SQLAlchemyError as e:
        logger.error(f"设置TimescaleDB超表失败: {e}")
        return False


def create_default_data(logger: logging.Logger) -> bool:
    """创建默认数据"""
    try:
        engine = create_engine(config.get_database_url())
        
        default_data_sql = """
        -- 插入默认数据源配置
        INSERT INTO data_sources (id, name, adapter_class, enabled, priority, rate_limit, default_params, created_at)
        VALUES 
            (gen_random_uuid(), 'yahoo_finance', 'YahooFinanceAdapter', true, 1, 
             '{"requests_per_minute": 60}', '{"interval": "1d"}', NOW()),
            (gen_random_uuid(), 'tushare', 'TushareAdapter', true, 2, 
             '{"requests_per_minute": 200}', '{"adj": "qfq"}', NOW()),
            (gen_random_uuid(), 'binance', 'BinanceAdapter', true, 3, 
             '{"requests_per_minute": 1200}', '{"interval": "1d"}', NOW()),
            (gen_random_uuid(), 'fred', 'FREDAdapter', true, 4, 
             '{"requests_per_minute": 120}', '{"frequency": "d"}', NOW())
        ON CONFLICT (name) DO NOTHING;
        
        -- 插入默认市场信息
        INSERT INTO market_info (symbol, name, market, exchange, currency, lot_size, tick_size, trading_hours, timezone, is_active)
        VALUES 
            ('SPY', 'SPDR S&P 500 ETF', 'US', 'NYSE', 'USD', 1, 0.01, 
             '{"open": "09:30", "close": "16:00"}', 'America/New_York', true),
            ('QQQ', 'Invesco QQQ Trust', 'US', 'NASDAQ', 'USD', 1, 0.01, 
             '{"open": "09:30", "close": "16:00"}', 'America/New_York', true),
            ('000001.SZ', '平安银行', 'CN', 'SZSE', 'CNY', 100, 0.01, 
             '{"open": "09:30", "close": "15:00"}', 'Asia/Shanghai', true),
            ('BTCUSDT', 'Bitcoin/USDT', 'CRYPTO', 'BINANCE', 'USDT', 0.00001, 0.01, 
             '{"open": "00:00", "close": "23:59"}', 'UTC', true)
        ON CONFLICT (symbol, market) DO NOTHING;
        """
        
        with engine.connect() as conn:
            conn.execute(text(default_data_sql))
            conn.commit()
        
        logger.info("默认数据创建成功")
        return True
        
    except SQLAlchemyError as e:
        logger.error(f"创建默认数据失败: {e}")
        return False


def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info(f"开始初始化数据库 - 环境: {config.environment}")
    logger.info(f"数据库: {config.database.host}:{config.database.port}/{config.database.database}")
    
    # 步骤1: 创建数据库
    if not create_database_if_not_exists(logger):
        sys.exit(1)
    
    # 步骤2: 创建扩展
    if not create_extensions(logger):
        sys.exit(1)
    
    # 步骤3: 创建表
    if not create_tables(logger):
        sys.exit(1)
    
    # 步骤4: 创建索引
    if not create_indexes(logger):
        sys.exit(1)
    
    # 步骤5: 设置TimescaleDB超表
    if not setup_timescale_hypertables(logger):
        sys.exit(1)
    
    # 步骤6: 创建默认数据
    if not create_default_data(logger):
        sys.exit(1)
    
    logger.info("数据库初始化完成!")


if __name__ == "__main__":
    main()